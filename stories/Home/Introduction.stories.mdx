import { Meta } from '@storybook/addon-docs';
import Code from '../assets/code-brackets.svg';
import Colors from '../assets/colors.svg';
import Comments from '../assets/comments.svg';
import Direction from '../assets/direction.svg';
import Flow from '../assets/flow.svg';
import Plugin from '../assets/plugin.svg';
import Repo from '../assets/repo.svg';
import StackAlt from '../assets/stackalt.svg';

<Meta title="Home/Overview" />

# Antsomi CDP components - Overview

Antsomi CDP components is a library of React UI components that implements Material UI.

## Introduction
Antsomi CDP components is an open-source React component library that implements Material UI.

It includes a comprehensive collection of prebuilt components that are ready for use in production right out of the box.

Antsomi CDP components is beautiful by design and features a suite of customization options that make it easy to implement your own custom design system on top of our components.

## Features
- 🌈 Enterprise-class UI designed for web applications.
- 📦 A set of high-quality React components out of the box.
- 🛡 Written in TypeScript with predictable static types.
- ⚙️ Whole package of design resources and development tools.
- 🌍 Internationalization support for dozens of languages.
- 🎨 Powerful theme customization in every detail.
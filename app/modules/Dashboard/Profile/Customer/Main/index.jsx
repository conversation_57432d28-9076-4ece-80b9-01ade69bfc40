/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
/* eslint-disable import/order */
import React, { useEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, withRouter } from 'react-router-dom';
import { UILoading as Loading, TabPanel } from '@xlab-team/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

import { compose } from 'redux';

import reducer from './reducer';
import saga from './saga';

import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';

import { MODULE_CONFIG } from './config';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import { init, reset, updateValue } from '../../../../../redux/actions';
import { makeSelectCustomerMainCommon } from './selectors';
import { setActiveMenu } from '../../../utils';
import { MENU_CODE } from '../../../menu.config';
import AuthenticationPage from '../../../../Authentication';
import { WrapperDetail, StyleWrapper, StyleTabs } from '../styles';
import LayoutLoading from '../../../../../components/Templates/LayoutContent/LayoutLoading';
import ListCustomerPage from '../List/Loadable';
import APP from '../../../../../appConfig';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../utils/web/cookie';
import { isProduction, updateUrl } from '../../../../../utils/common';
import { buildProfileURL, checkActiveTabExist } from './utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import OverviewPage from '../Overview';
import { getReportInfo } from '../../../MarketingHub/Journey/Detail/Dashdoard/config.report';
import DropdownViewAccess from '../../../../../components/common/DropdownViewAccess';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { makeSelectIsDisplayRequestAccess } from '../List/selectors';
import { BREADCRUMDATA } from '../List/config';
import { DATA_ACCESS_OBJECT } from '../../../../../utils/constants';
import { RequestAccessV2 } from '../../../../../components/Molecules/RequestAccessV2';
import InsightIframe from '../../components/InsightIframe';
import { isLocalhost } from '../../../../../utils/web/utils';

// import { makeSelectDashboardLoading } from '../../selector';
const MAP_TITLE = {
  tab: {
    customer: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_CUSTOMER, 'Customer'),
  },
  overview: getTranslateMessage(TRANSLATE_KEY._TAB_STORY_OVERVIEW, 'Overview'),
};
function CustomerCommonPage(props) {
  const { main, isDisplayRequestAccess } = props;
  const { tab = 'list' } = useParams();

  /* -------------------------------- OVER AREA ------------------------------- */
  const reportOverviewConfig = getReportInfo('customer-overview');

  const isShowOverviewTab = reportOverviewConfig !== null;

  useEffect(() => {
    const activeTabDefault = checkActiveTabExist(tab, isShowOverviewTab); // nhập tạp trên url không đúng sẽ trả về tab default list
    props.init({ tab: activeTabDefault });

    if (!isShowOverviewTab && tab === 'overview') {
      const newUrl = `${APP.PREFIX}/${getPortalId()}/profile/customers/list`;
      updateUrl(newUrl);
    }

    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
    };
  }, []);

  const onChangeTab = value => {
    props.onChangeTab(value);

    const newUrl = `${APP.PREFIX}/${getPortalId()}/profile/customers/${value}`;
    updateUrl(newUrl);
  };

  // console.log('props.main.isInitDone', props.main.isInitDone);
  if (!props.main.isInitDone) {
    return <Loading isLoading isWhite />;
  }

  if (isDisplayRequestAccess) {
    const styleWrapper = {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      display: 'flex',
      backgroundColor: '#fff',
    };
    const serviceFn = ({ defaultService, message }) =>
      defaultService({
        body: {
          message,
          objectId: -1003,
          objectType: DATA_ACCESS_OBJECT.BO,
        },
      });

    const breadcrumbs = BREADCRUMDATA.map(breadcrumb => {
      if (breadcrumb.type === 'main-customer') {
        const newBreadcrumbData = {
          ...breadcrumb,
          isShowArrow: true,
        };
        newBreadcrumbData.display = 'Choose customer view';
        return newBreadcrumbData;
      }
      return breadcrumb;
    });

    return (
      <div style={styleWrapper}>
        <DropdownViewAccess
          value={{}}
          viewAccessInfo={{}}
          callback={() => {}}
          stylePaper={{
            top: '45px !important',
            left: '245px !important',
          }}
          containerStyles={{
            position: 'absolute',
          }}
        >
          <CustomHeader breadcrums={breadcrumbs} />
        </DropdownViewAccess>
        <RequestAccessV2 requestService={serviceFn} />
      </div>
    );
  }

  return (
    <AuthenticationPage menuCode={MENU_CODE.CUSTOMER}>
      {/* <Loading isLoading={props.isLoading} isWhite /> */}
      <StyleWrapper>
        <LayoutContent padding="0" overflow="hidden" height="100%">
          <WrapperDetail>
            {/* <Loading isLoading={props.main.isLoading} isWhite /> */}
            <LayoutLoading isLoading={false}>
              {isShowOverviewTab && (
                <StyleTabs
                  // isRenderChildren={false}
                  // noBorderBottom
                  activeTab={main.activeTab}
                  onChange={onChangeTab}
                  className="tab-box-shadow"
                >
                  <TabPanel
                    marginTop
                    label={MAP_TITLE.overview}
                    eventKey="overview"
                  >
                    <OverviewPage reportConfig={reportOverviewConfig} />
                  </TabPanel>
                  <TabPanel
                    marginTop
                    label={MAP_TITLE.tab.customer}
                    eventKey="list"
                  />
                </StyleTabs>
              )}
              <ListCustomerPage activeTab={main.activeTab} />
            </LayoutLoading>
          </WrapperDetail>
        </LayoutContent>
      </StyleWrapper>
      <InsightIframe
        src={buildProfileURL()}
        module="CUSTOMER"
        itemTypeId={-1003}
      />
    </AuthenticationPage>
  );
}

const mapStateToProps = createStructuredSelector({
  // isLoading: makeSelectDashboardLoading(),
  main: makeSelectCustomerMainCommon(),
  isDisplayRequestAccess: makeSelectIsDisplayRequestAccess(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB@@`, params));
    },
    onChangeChannel: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANNEL_ACTIVE@@`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

// export default compose(
//   withRouter,
//   withConnect,
// )(CustomerCommonPage);
export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(CustomerCommonPage);

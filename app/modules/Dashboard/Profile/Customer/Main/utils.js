import React from 'react';

import { ACTIVE_TAB_ARRAY, BREADCRUMDATA } from './config';

export const checkActiveTabExist = (activeTab, isShowOverviewTab) => {
  let activeTabOutput = 'list';

  if (activeTab === 'overview' && isShowOverviewTab) {
    activeTabOutput = activeTab;
  } else if (activeTab === 'overview' && !isShowOverviewTab) {
    activeTabOutput = 'list';
  } else if (ACTIVE_TAB_ARRAY.includes(activeTab)) {
    activeTabOutput = activeTab;
  }

  return activeTabOutput;
};

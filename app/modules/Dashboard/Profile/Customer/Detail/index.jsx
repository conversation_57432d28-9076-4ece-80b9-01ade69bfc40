/* eslint-disable react/prop-types */

import React, {
  useEffect,
  memo,
  useState,
  useMemo,
  useLayoutEffect,
} from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import Grid from '@material-ui/core/Grid';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { UILoading as Loading, TabPanel } from '@xlab-team/ui-components';

import CustomerInfo from 'components/Organisms/CustomerInfo/index';

import queryString from 'query-string';
import reducer from './reducer';
import saga from './saga';
import makeSelectCustomerDetail, {
  makeSelectEventsPeformance,
  makeSelectEventTracking,
  makeSelectCustomerName,
  // makeSelectCustomerDetailCustomerListFilter,
} from './selectors';

import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_LIST } from './widgets/CustomerList/config';
// import { MODULE_CONFIG as MODULE_COMMON_CONFIG } from '../config';
import { init, updateValue, reset } from '../../../../../redux/actions';
import {
  getPortalFormatNumber,
  getPortalFormatCurrency,
} from '../../../../../utils/web/portalSetting';

import {
  // WapperContainerCustomerInfo,
  ContainerCustomerInfo,
  WrapperCustomerList,
  TabsExtend,
  WrapperTabContent,
  Flexbox,
  // TimeLineContent,
  Mt16,
  // BranchChart,
  WrapperDashboardCustom,
  WrraperCustomerNotFound,
  HackStyleLayoutContent,
  WrapperContentDrawer,
  LabelModule,
} from './styles';

import ActivityViewer from './widgets/ActivityViewer';
// import ActivityViewer from '../../../../../containers/Widgets/ActivityViewer';
import CustomerList from './widgets/CustomerList';
import IdentityGraph from './widgets/IdentityGraph';
import OverviewTimeline from '../../../../../containers/Widgets/OverviewTimeline';

import WidgetTableJourney from '../../../../../containers/Widgets/TableJourney';
// import AffinitesChart from './widgets/AffinitesChart';
import AffinitesChart from '../../../../../containers/Widgets/AffinitesChart';
import WidgetDayHourChartTime from '../../../../../containers/Widgets/DayHourChartTime';
// import WidgetProductRecommendation from './widgets/ProductRecommendation';
import WidgetProductRecommendation from '../../../../../containers/Widgets/ProductRecommendation';
import WidgetSquareNumber from '../../../../../containers/Widgets/SquareNumber';

import WidgetDayHourChartDevice from '../../../../../containers/Widgets/DayHourChartDevice';
import WidgetInfo from '../../../../../containers/Widgets/WidgetInfo';
import WorkCloud from '../../../../../containers/Widgets/WorkCloud';

// import AudienceSegment from '../../../../../containers/Widgets/AudienceSegment';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
// import makeSelectCustomerCommon from '../selectors';
// import ButtonCollapse from './widgets/_UI/ButtonCollapse';
import CustomerService from '../../../../../services/CustomerV2';
import DataViewService from '../../../../../services/DataView';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { getBreadcrums } from './utils';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import { isUrlEmbedded } from '../../../../../utils/web/utils';
import APP from '../../../../../appConfig';
import { getPortalId } from '../../../../../utils/web/cookie';

const MAP_TITLE = {
  customer: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_CUSTOMER, 'Customers'),
  overview: getTranslateMessage(
    TRANSLATE_KEY._TAB_CUSTOMER_OVERVIEW,
    'Overview',
  ),
  activity: getTranslateMessage(
    TRANSLATE_KEY._TAB_CUSTOMER_ACTIVITY,
    'Activity',
  ),
};

const widthStyle = {
  width: '100vh',
};

const encryptConfig = {
  prefixes: [
    `${MODULE_CONFIG.key}@@OBJECT_INFO@@`,
    `${MODULE_CONFIG_LIST.key}`,
  ],
};

function StyleWrapperDashboard(props) {
  const { isCollapse } = props;
  // return (
  //   <WrapperDashboardCustom>
  //     <div className="container-loading">
  //       <Loading isLoading iswhite />
  //     </div>

  //     {/* <Loading isLoading={props.isLoading} /> */}
  //   </WrapperDashboardCustom>
  // );
  // return null;
  if (props.isLoading) {
    return (
      <WrapperDashboardCustom>
        <div className="container-loading">
          <Loading isLoading={props.isLoading} iswhite />
        </div>

        {/* <Loading isLoading={props.isLoading} /> */}
      </WrapperDashboardCustom>
    );
  }
  if (props.code !== 200) {
    return (
      <WrapperDashboardCustom>
        <WrraperCustomerNotFound>Customer not found</WrraperCustomerNotFound>
      </WrapperDashboardCustom>
    );
  }
  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Customer/Detail/index.jsx">
      <WrapperDashboardCustom isCollapse={isCollapse}>
        {props.children}
      </WrapperDashboardCustom>
    </ErrorBoundary>
  );
}

export function CustomerDetail(props) {
  const { customer, eventTracking, history } = props;

  const queryParams = queryString.parse(history.location.search);

  const { customerId, tab } = props.match.params;
  const { path } = props.match;

  const { main } = customer;
  const { isCollapse } = main;

  const [dataViewInfo, setDataViewInfo] = useState({});

  useLayoutEffect(() => {
    // redirect to new layout detail customer v2
    if (customerId && main.activeTab) {
      let newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/profile/customers/list?id=${customerId}&tab=${
        main.activeTab
      }`;

      if (path && path.includes('public')) {
        newUrl = `${
          APP.PREFIX
        }/${getPortalId()}/profile/public/customers/list?id=${customerId}&tab=${tab ||
          main.activeTab}`;
      }
      history.push(newUrl);
    }
  }, []);

  useEffect(() => {
    if (queryParams.view_id) {
      (async () => {
        const {
          code,
          data: dataViewData,
        } = await DataViewService.dataView.getDetailInfo({
          viewId: queryParams.view_id,
        });

        if (code === 200) {
          setDataViewInfo(dataViewData);
        }
      })();
    }
  }, [queryParams.view_id]);

  useEffect(() => {
    props.init({
      customerId: customerId || queryParams.id,
      mode: 'detail',
      queryParams,
    });

    return () => {
      if (props.isDisplayContentDrawer) {
        // redirect to origin url listing
        history.push(history.location.pathname);
      }
      props.reset();
    };
  }, [customerId, queryParams.id]);

  useEffect(() => {
    if (props.activeTab) {
      onChangeTab(props.activeTab);
    }
  }, [props.activeTab]);

  const onChangeTab = tab => {
    if (tab !== main.activity) {
      props.onChangeTab({ tab, queryParams });
      if (tab === 'activity') {
        // props.handleInitActivities();
      }
    }
  };

  const showMoreActivity = tab => {
    const el = document.getElementById('main-content');

    el.scrollTo({
      top: 330,
      left: 0,
      behavior: 'smooth', // or can get `auto` variable
    });
    props.onChangeTab({ tab, queryParams });
  };

  const onToggleCollapse = () => {
    props.onToggleCollapse();
  };

  // const data = tableDataMultiColumns;
  const pageTitle = `${MAP_TITLE.customer} - ${MAP_TITLE[main.activeTab]}`;

  const breadcrums = useMemo(() => {
    const name = props.customer.main.info.properties.name.value;

    return getBreadcrums(name, dataViewInfo.viewDisplay);
  }, [
    props.customer.main.info.properties.name.value,
    dataViewInfo.viewDisplay,
  ]);

  const renderContent = activeTab => {
    let content = null;

    if (activeTab === 'overview') {
      content = (
        <WrapperTabContent className="p-bottom-4">
          <Grid container spacing={2}>
            <Grid item xs={4}>
              {main.activeTab === 'overview' ? (
                <WidgetInfo
                  isCollapse={isCollapse}
                  activeTab={main.activeTab}
                  encryptConfig={encryptConfig}
                  isLoading={main.widgets.customerInfo.isLoading}
                  info={main.info.properties}
                  information={
                    main.widgets.customerInfo.groups.customerInformation
                  }
                  others={main.widgets.customerInfo.groups.others}
                  ComponentHeader={CustomerInfo}
                  groups={main.groupAttributes.groups}
                  properties={main.widgets.customerInfo.properties}
                  isDisplayContentDrawer={props.isDisplayContentDrawer}
                  // callback={callback}
                />
              ) : null}
              <Mt16>
                <WidgetDayHourChartTime
                  objectId={main.customerId}
                  id={main.widgets.total_revenue}
                  dateRange={main.dateRange}
                  eventsPerfomance={props.eventsPerfomance}
                  ServiceFn={CustomerService.cards.performance}
                />
              </Mt16>
              {/* <Mt16>
                <WidgetDayHourChartDevice
                  objectId={main.customerId}
                  id={main.widgets.total_revenue}
                  dateRange={main.dateRange}
                  eventsPerfomance={props.eventsPerfomance}
                  ServiceFn={CustomerService.cards.performance}
                />
              </Mt16>
              <Mt16>
                <WorkCloud
                  objectId={main.customerId}
                  id
                  dateRange={main.dateRange}
                  ServiceFn={CustomerService.cards.performance}
                />
              </Mt16> */}
            </Grid>
            <Grid item xs={8} style={widthStyle}>
              {!props.isDisplayContentDrawer && (
                <>
                  <WidgetProductRecommendation
                    use="customer"
                    objectId={main.customerId}
                    dateRange={main.dateRange}
                  />
                  <Mt16>
                    <WidgetTableJourney
                      objectId={main.customerId}
                      dateRange={main.dateRange}
                      id
                      eventsPerfomance={props.eventsPerfomance}
                      ServiceFn={CustomerService.cards.performance}
                    />
                  </Mt16>
                </>
              )}
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Mt16
                    style={{ marginTop: props.isDisplayContentDrawer ? 0 : '' }}
                  >
                    <OverviewTimeline
                      customerId={main.customerId}
                      dateRange={main.dateRange}
                      id
                      eventTracking={eventTracking}
                      customerName={props.customerName}
                      // onClickMore={onChangeTab}
                      onClickMore={showMoreActivity}
                    />
                  </Mt16>
                </Grid>

                <Grid
                  item
                  xs={6}
                  style={
                    props.isDisplayContentDrawer
                      ? {
                          display: 'flex',
                          flexDirection: 'column-reverse',
                          justifyContent: 'flex-end',
                        }
                      : {}
                  }
                >
                  {props.isDisplayContentDrawer && (
                    <Mt16>
                      <WidgetProductRecommendation
                        use="customer"
                        objectId={main.customerId}
                        dateRange={main.dateRange}
                        isDisplayContentDrawer={props.isDisplayContentDrawer}
                      />
                    </Mt16>
                  )}
                  <Mt16>
                    <AffinitesChart
                      objectId={main.customerId}
                      id={main.widgets.total_revenue}
                      dateRange={main.dateRange}
                      eventsPerfomance={props.eventsPerfomance}
                      dimensions="product_main_category"
                      label={getTranslateMessage(
                        TRANSLATE_KEY._BLOCK_INTEREST_CUS,
                        'Interests',
                      )}
                      ServiceFn={CustomerService.cards.performance}
                      isDisplayContentDrawer={props.isDisplayContentDrawer}
                    />
                  </Mt16>
                  <Mt16>
                    <AffinitesChart
                      objectId={main.customerId}
                      id={main.widgets.total_revenue}
                      dateRange={main.dateRange}
                      eventsPerfomance={props.eventsPerfomance}
                      dimensions="product_brand"
                      label={getTranslateMessage(
                        TRANSLATE_KEY._BLOCK_BRAND_AFFINITIES_CUS,
                        'Author affinities',
                      )}
                      ServiceFn={CustomerService.cards.performance}
                      isDisplayContentDrawer={props.isDisplayContentDrawer}
                    />
                  </Mt16>
                  <Flexbox
                    style={{ marginTop: props.isDisplayContentDrawer ? 0 : '' }}
                  >
                    <WidgetSquareNumber
                      objectId={main.customerId}
                      id={main.widgets.purchases}
                      dateRange={main.dateRange}
                      metric="revenues"
                      className="flex1 mr8 h100"
                      label={getTranslateMessage(
                        TRANSLATE_KEY._BLOCK_REVENUE_CUS,
                        'Revenue',
                      )}
                      displayFormat={{
                        type: 'CURRENCY',
                        config: {
                          ...getPortalFormatCurrency(),
                          decimalPlace: 2,
                        },
                      }}
                      ServiceFn={CustomerService.cards.performance}
                    />
                    <WidgetSquareNumber
                      objectId={main.customerId}
                      id={main.widgets.total_revenue}
                      dateRange={main.dateRange}
                      metric="purchases"
                      className="flex1 ml8 h100"
                      label={getTranslateMessage(
                        TRANSLATE_KEY._BLOCK_SUCCESS_ORDER_CUS,
                        'Success orders',
                      )}
                      displayFormat={{
                        type: 'CURRENCY',
                        config: {
                          ...getPortalFormatNumber,
                          decimalPlace: 0,
                        },
                      }}
                      ServiceFn={CustomerService.cards.performance}
                    />
                  </Flexbox>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </WrapperTabContent>
      );
    } else if (activeTab === 'activity') {
      content = (
        <Grid container spacing={2}>
          <Grid item xs={4}>
            {main.activeTab === 'activity' ? (
              <WidgetInfo
                isCollapse={isCollapse}
                activeTab={main.activeTab}
                encryptConfig={encryptConfig}
                isLoading={main.widgets.customerInfo.isLoading}
                info={main.info.properties}
                // groups={main.widgets.customerInfo.groups}
                information={
                  main.widgets.customerInfo.groups.customerInformation
                }
                others={main.widgets.customerInfo.groups.others}
                ComponentHeader={CustomerInfo}
                showOtherGroup
                groups={main.groupAttributes.groups}
                properties={main.widgets.customerInfo.properties}
                isDisplayContentDrawer={props.isDisplayContentDrawer}
              />
            ) : null}
            <Mt16>
              <IdentityGraph
                activeTab={main.activity}
                componentId={customer.main.userAttributesComponentId}
                customerId={main.customerId}
                userAttributes={customer.main.userAttributes}
                isDisplayContentDrawer={props.isDisplayContentDrawer}
              />
            </Mt16>
            {/* <AudienceSegment
          limit={5}
          objectId={main.customerId}
          ServiceFn={CustomerService.cards.segments}
        /> */}
          </Grid>
          <Grid item xs={8} style={widthStyle}>
            {!props.isDisplayContentDrawer && (
              <WidgetProductRecommendation
                use="customer"
                objectId={main.customerId}
                dateRange={main.dateRange}
              />
            )}
            <Mt16 style={{ margin: props.isDisplayContentDrawer ? 0 : '' }}>
              <ActivityViewer
                customerId={main.customerId}
                isDisplayContentDrawer={props.isDisplayContentDrawer}
                // eventTracking={eventTracking}
                // customerName={safeParse(customerName, 'N/A')}
              />
            </Mt16>
          </Grid>
        </Grid>
      );
    }

    return content;
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Customer/Detail/index.jsx">
      <UIHelmet title={pageTitle} />
      {!isUrlEmbedded() && !props.isDisplayContentDrawer && (
        <CustomHeader breadcrums={breadcrums} />
      )}
      <ContainerCustomerInfo className={`${isUrlEmbedded() ? 'm-top-0' : ''}`}>
        {/* <Loading isLoading={main.isLoading} iswhite /> */}
        {!props.isDisplayContentDrawer ? (
          <>
            <WrapperCustomerList isCollapse={isCollapse}>
              <CustomerList
                isCollapse={isCollapse}
                onToggleCollapse={onToggleCollapse}
              />
            </WrapperCustomerList>
            <StyleWrapperDashboard
              isCollapse={isCollapse}
              isLoading={main.isLoading}
              code={main.code}
            >
              <HackStyleLayoutContent isCollapse={isCollapse} />
              <TabsExtend
                // isRenderChildren={false}
                noBorderBottom
                activeTab={main.activeTab}
                onChange={onChangeTab}
                className="tab-box-shadow tab-nav-tab-cus-detail"
              >
                <TabPanel
                  marginTop
                  label={getTranslateMessage(
                    TRANSLATE_KEY._TAB_CUSTOMER_OVERVIEW,
                    'Overview',
                  )}
                  eventKey="overview"
                >
                  {renderContent(main.activeTab)}
                </TabPanel>
                <TabPanel
                  marginTop
                  label={getTranslateMessage(
                    TRANSLATE_KEY._TAB_CUSTOMER_ACTIVITY,
                    'Activity',
                  )}
                  eventKey="activity"
                >
                  {renderContent(main.activeTab)}
                </TabPanel>
              </TabsExtend>
            </StyleWrapperDashboard>
          </>
        ) : (
          <WrapperContentDrawer>
            <div className="content">{renderContent(main.activeTab)}</div>
          </WrapperContentDrawer>
        )}
      </ContainerCustomerInfo>
      {/* </WapperContainerCustomerInfo> */}
    </ErrorBoundary>
  );
}

// CustomerDetail.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  customer: makeSelectCustomerDetail(),
  eventsPerfomance: makeSelectEventsPeformance(),
  eventTracking: makeSelectEventTracking(),
  customerName: makeSelectCustomerName(),
});

function mapDispatchToProps(dispatch) {
  return {
    reset: () => {
      dispatch(reset(MODULE_CONFIG.key));
    },
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@TAB`, params));
    },
    // handleInitActivities: () => {
    //   dispatch(init(`${MODULE_CONFIG.key}@@TAB_aCTIVITIES`));
    // },
    onToggleCollapse: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@COLLAPSE`));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(CustomerDetail);

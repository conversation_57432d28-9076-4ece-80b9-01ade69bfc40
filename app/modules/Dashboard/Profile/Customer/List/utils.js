import {
  CellText,
  CellCustomerName,
  CellPortalDate,
  CellCurrency,
  CellNumber,
  CellOwner,
  CellArray,
  CellCustomFunction,
  CellEnumeration,
  CellRadioSelect,
} from '../../../../../containers/Table/Cell';
import { MODULE_CONFIG } from './config';
import { safeParse } from '../../../../../utils/common';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

export function serializeData(list, portalId) {
  const data = [];
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.customer_id.value,
      portal_id: {
        value: portalId,
      },
    };
    data.push(tempt);
  });
  return data;
}

export function buildTableGroupColumns(columnName, columnsActive) {
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: `${columnName.value}.value`,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellCustomerName,
      isEncrypt: columnName.isEncrypt,
      itemTypeId: columnName.itemTypeId,
      className: '',
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: `${property.value}.value`,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      isEncrypt: property.isEncrypt,
      itemTypeId: MODULE_CONFIG.objectId,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'owner_id') {
      column.accessor = `properties.${property.value}`;
      column.Cell = CellOwner;
    } else if (
      codeType === 'computation' &&
      property.computeType === 'custom'
    ) {
      column.Cell = CellCustomFunction;
      column.property = property;
    } else if (codeType === 'dropdown_select') {
      column.Cell = CellEnumeration;
    } else if (codeType === 'multiple_checkboxes') {
      column.Cell = CellEnumeration;
    } else if (codeType === 'single_checkbox') {
      column.Cell = CellEnumeration;
    } else if (codeType === 'date_picker') {
      column.Cell = CellPortalDate;
    } else if (codeType === 'radio_select') {
      column.Cell = CellRadioSelect;
    } else if (dataType === 'number') {
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellPortalDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    columns.push(column);
  });
  return columns;
}

export function getIsEncryptSuggestionSearch() {
  const array = safeParse(
    APP_CACHE_PARAMS.decryptPermission.encryptFields.itemAttributes[
      MODULE_CONFIG.objectId
    ],
    [],
  );
  return array.includes('name');
}

/* eslint-disable consistent-return */
/* eslint-disable react/prop-types */
/* eslint-disable no-undef */
/* eslint-disable import/order */

import React, { useState, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { intersection, get } from 'lodash';

import { createStructuredSelector } from 'reselect';

import reducer from './reducer';
import saga from './saga';
import makeSelectCustomer, {
  makeSelectCustomerFilter,
  makeSelectCustomerColumn,
} from './selectors';

// Hooks
import useNotificationBar from 'hooks/useNotificationBar';

import ErrorBoundary from 'components/common/ErrorBoundary';
import TableContainer from 'containers/Table';
import ModalProcessing from 'containers/modals/ModalProcessing';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { UIIconButton as IconButton } from '@xlab-team/ui-components';
import ModalRemoveData from 'containers/modals/ModalRemoveData';
import { MODULE_CONFIG, BREADCRUMDATA } from './config';
// import { MODULE_CONFIG as MODULE_COMMON_CONFIG } from '../config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  init,
  getList,
  updateValue,
  reset,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { TableWrapper, TableRelative, WrapperDataView } from './styles';
import makeSelectCustomerCommon from '../selectors';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
// import TryVersion from '../../../../../containers/TryVersion';
// import { getLabelFilterBy } from '../../Segment/List/utils';
// import { useWhyDidYouUpdate } from '../../../../../utils/web/useHooks';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import { isDecryptFields } from '../../../../../utils/web/attribute';
import { getIsEncryptSuggestionSearch } from './utils';
import useToggle from '../../../../../hooks/useToggle';
import Upload from '../../../../../containers/Upload';
import ModalExport from '../../../../../containers/modals/ModalExport';
import { OBJECT_NAME } from '../../utils';
import { DrawerInsightExplore } from '@antscorp/cdp-explore-package';
import {
  getCurrentOwnerId,
  getCurrentUserId,
  getPortalId,
  getToken,
} from '../../../../../utils/web/cookie';
import { getDomainInsight } from '../../../MarketingHub/Journey/utils';
import DropdownViewAccess from '../../../../../components/common/DropdownViewAccess';
import CustomerDetailModule from '../Detail/Loadable';
import { DrawerDetail, Icon } from '@antscorp/antsomi-ui';
import UIIconXlab from '../../../../../components/common/UIIconXlab';
import { LabelModule } from '../Detail/styles';
import ModalRemoveProfile from '../../../../../containers/modals/ModalRemoveProfile';
import ControlTable from './ControlTable';
import { RequestAccessV2 } from '../../../../../components/Molecules/RequestAccessV2';
import { PROFILE_TEMPLATE_AVAILABLE_PORTAL_IDS } from '../../constants';

const MAP_TITLE = {
  navBar: {
    title: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_CUSTOMER, 'Customers'),
    name: getTranslateMessage(
      TRANSLATE_KEY._FILTER_SEGMENT_CUSTOMER,
      'Customer',
    ),
  },
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    // download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
    explore: getTranslateMessage(TRANSLATE_KEY._, 'EXPLORE'),
  },
};

const encryptConfig = { prefixes: [MODULE_CONFIG.key] };

export function Customer(props) {
  const { common, customer, filter, column } = props;
  const { main, table } = customer;
  const {
    viewData,
    viewInfoMapping,
    isEditPermission,
    isDisplayRequestAccess,
  } = main;
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalRemove, toggleModalRemove] = useToggle(false);
  const { isShow: isShowExplore } = useNotificationBar();

  const [isEncryptSuggestionSearch] = useState(getIsEncryptSuggestionSearch());
  const [activeTab, setActiveTab] = useState('overview');
  const [isOpenDetail, setIsOpenDetail] = useState(false);

  const searchParams = new URLSearchParams(window.location.search);

  const isShowOldVersion =
    searchParams.has('isOldMode') ||
    !PROFILE_TEMPLATE_AVAILABLE_PORTAL_IDS.includes(+getPortalId());
  // useEffect(() => {
  //   const resizeListener = () => {
  //     setWidth(window.innerWidth);
  //   };
  //   window.addEventListener('resize', resizeListener);

  //   return () => {
  //     window.removeEventListener('resize', resizeListener);
  //   };
  // }, []);

  useEffect(() => {
    props.init();
    return () => {
      props.reset();
    };
  }, []);

  useEffect(() => {
    if (!searchParams.has('id')) return;
    const tab = searchParams.get('tab');
    setActiveTab(tab);

    if (isShowOldVersion) {
      setIsOpenDetail(true);
    }

    return () => {
      setIsOpenDetail(false);
    };
  }, [window.location.search]);

  const callback = (type, data) => {
    if (type === 'FETCH_DATA') {
      props.fetchData();
    }
    if (type === 'ACTION_TABLE_DELETE') {
      toggleModalRemove();
    }

    if (type === 'APPLY_CONFIRM_DELETE') {
      const { isUpload } = data;
      if (isUpload) {
        toggleModalProcessing();
      } else {
        props.onRemove();
      }
    }

    if (type === 'DATA_ACCESS_ITEM') {
      const { item } = data;
      props.reset();
      props.onChangeDataView({
        viewData: item,
      });
    }
  };

  const breadcrumbs = useMemo(() => {
    const { viewId, viewDisplay } = viewData;
    const dataBreadcrumbs = BREADCRUMDATA.map(breadcrumb => {
      if (breadcrumb.type === 'main-customer') {
        const newBreadcrumbData = {
          ...breadcrumb,
          isShowArrow: true,
        };
        if (!Object.keys(viewData).length) {
          newBreadcrumbData.display = 'Choose customer view';
        } else if (viewId) {
          newBreadcrumbData.display = viewDisplay;
        } else if (viewData && viewData.itemTypeDisplay) {
          newBreadcrumbData.display = viewData.itemTypeDisplay;
        }
        return newBreadcrumbData;
      }
      return breadcrumb;
    });
    return dataBreadcrumbs;
  }, [viewData]);

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const columnsExport = React.useMemo(() => {
    const columnsAlias = get(column, 'columnObj.columns.columnsAlias', []);
    const attributeKeys = Object.keys(
      get(common, 'main.groupAttributes.map', {}),
    );

    return intersection(columnsAlias, attributeKeys);
  }, [common, main]);

  const toggleModalProcessing = () =>
    props.toggleModalProcessing({ isOpen: !main.isOpenModalProcessing });

  if (isDisplayRequestAccess) {
    const styleWrapper = {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      display: 'flex',
      backgroundColor: '#fff',
    };
    const serviceFn = ({ defaultService, message }) =>
      defaultService({
        body: {
          message,
          objectId: -1003,
          objectType: DATA_ACCESS_OBJECT.BO,
        },
      });

    return (
      <div style={styleWrapper}>
        <DropdownViewAccess
          value={viewData}
          viewAccessInfo={viewInfoMapping}
          callback={callback}
          stylePaper={{
            top: '45px !important',
            left: '245px !important',
          }}
          containerStyles={{
            position: 'absolute',
          }}
          existId="viewId"
        >
          <CustomHeader breadcrums={breadcrumbs} isFitContent />
        </DropdownViewAccess>
        <RequestAccessV2 requestService={serviceFn} />
      </div>
    );
  }

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Customer/List/index.jsx">
      <UIHelmet title={MAP_TITLE.navBar.title} />
      <LayoutContent
        padding="0px 16px"
        margin="-16px -16px 0"
        height={`calc(100vh - ${isShowExplore ? '132px' : '84px'})`}
      >
        {/* <NavBar
          label={getTranslateMessage(
            TRANSLATE_KEY._MENU_SUB_CUSTOMER,
            'Customers',
          )}
        /> */}
        <TableRelative>
          <TableWrapper>
            {/* <Table
          columns={columns}
          data={props.customer.data}
          sizeCheckbox={width > 1600 ? 50 : 48}
        /> */}
            <TableContainer
              columnActive={column.columnObj}
              table={table}
              encryptConfig={encryptConfig}
              isLoading={main.isLoading}
              moduleConfig={MODULE_CONFIG}
              // paging={table.paging}
              // sort={table.sort}
              selectedIds={table.selectedIds}
              selectedRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              isSelectedAllPage={table.isSelectedAllPage}
              columns={tableColumns}
              data={main.data}
              callback={callback}
              resizeColName="name"
              noCheckboxAction={viewData.viewId ? true : !isEditPermission}
              ComponentControlTable={ControlTable}
              // noDataText="No customer found"
              // widthFirstColumns={48}
              // initialWidthColumns={width > 1600 ? 291 : 218}
            >
              <>
                <Filters
                  encryptConfig={encryptConfig}
                  use="list"
                  rules={filter.rules}
                  moduleConfig={MODULE_CONFIG}
                  filterActive={filter.config.filterObj}
                  filterCustom={filter.config.library.filterCustom}
                  libraryFilters={filter.config.library.filters}
                  groups={common.main.groupAttributes.filterGroups}
                  isFilter={filter.config.design.isFilter}
                  isLoading={filter.config.isLoading}
                  content={
                    <DropdownViewAccess
                      existId="viewId"
                      value={viewData}
                      viewAccessInfo={viewInfoMapping}
                      callback={callback}
                      stylePaper={{
                        top: '110px !important',
                        left: '365px !important',
                      }}
                      containerStyles={{
                        position: '',
                      }}
                      existId="viewId"
                    >
                      {/* <CustomHeader breadcrums={breadcrumbs} isFitContent /> */}
                      <WrapperDataView>
                        <span>Data view: </span>
                        <span className="data-view">
                          {breadcrumbs[0].display}
                        </span>
                      </WrapperDataView>
                    </DropdownViewAccess>
                  }
                />
                <WrapActionTable
                  show={!filter.config.design.isFilter}
                  className="p-x-4"
                >
                  <div className="actionTable__inner">
                    <Search
                      moduleConfig={MODULE_CONFIG}
                      config={{
                        limit: 10,
                        page: 1,
                        sd: 'asc',
                        scope: 3,
                        propertyCode: 'customer_id',
                        itemTypeId: MODULE_CONFIG.objectId,
                        itemTypeName: 'name',
                        itemPropertyName: 'customer_id',
                        isPk: 1,
                        systemDefined: 0,
                        decryptFields:
                          isEncryptSuggestionSearch &&
                          isDecryptFields({
                            itemTypeId: MODULE_CONFIG.objectId,
                            attributeCode: 'name',
                          })
                            ? ['name']
                            : [],
                      }}
                      suggestionType="suggestion"
                      moduleLabel={MAP_TITLE.navBar.title}
                      isShowAvatar
                    />
                    <ModifyColumn
                      sort={table.sort}
                      moduleConfig={MODULE_CONFIG}
                      columnActive={column.columnObj}
                      columnCustom={column.library.columnCustom}
                      libraryColumns={column.library.columns}
                      defaults={MODULE_CONFIG.columnsDefault}
                      defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                      columns={column.columnObj.columns.columnsAlias}
                      groups={common.main.groupAttributes.groups}
                      isLoading={column.isLoading}

                      // handleConfirm={handleConfirmCustomizeColumn}
                    />
                    {!viewData?.viewId && (
                      <DrawerInsightExplore
                        use="all"
                        token={getToken()}
                        ownerId={getCurrentOwnerId()}
                        userId={getCurrentUserId()}
                        domain={getDomainInsight()}
                        styledWrapper={{
                          marginLeft: '5px',
                          ...(viewData.viewId || !Object.keys(viewData).length
                            ? { opacity: 0.5, pointerEvents: 'none' }
                            : {}),
                        }}
                        portalId={getPortalId()}
                        nameBO="Customer"
                        itemTypeName="customers"
                        itemTypeId={MODULE_CONFIG.objectId}
                        dataSourceId={-6666}
                        nameExplore="bo"
                        CustomButton={btnProps => (
                          <IconButton
                            {...btnProps}
                            iconName="explorer"
                            size="24px"
                            isVertical
                          >
                            {MAP_TITLE.action.explore.toUpperCase()}
                          </IconButton>
                        )}
                      />
                    )}
                    {!viewData.viewId
                      ? isEditPermission && (
                          <Upload itemTypeId={MODULE_CONFIG.objectId} />
                        )
                      : null}
                    <IconButton
                      iconName="file_download"
                      size="24px"
                      isVertical
                      onClick={
                        table.paging.totalRecord === 0
                          ? () => {}
                          : toggleModalDownload
                      }
                      disabled={table.paging.totalRecord === 0}
                      // disabled
                    >
                      {MAP_TITLE.action.actExport.toUpperCase()}
                    </IconButton>
                  </div>
                  {/* <div className="downBlock">
                    <IconButton
                      iconName="arrow-down"
                      size="24px"
                      onClick={() => {}}
                      disabled
                    />
                  </div> */}
                </WrapActionTable>
              </>
            </TableContainer>
          </TableWrapper>
        </TableRelative>
      </LayoutContent>
      {/* <ModalDownload
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        filters={filter}
        itemTypeId={MODULE_CONFIG.objectId}
        itemTypeName="customer"
        properties={column.columnObj.columns.columnsAlias}
      /> */}
      <ModalExport
        isOpen={isOpenModalDownload}
        objectName={OBJECT_NAME.CUSTOMER}
        toggle={toggleModalDownload}
        paging={table.paging}
        object_type={MODULE_CONFIG.objectType}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        filters={filter}
        itemTypeId={+MODULE_CONFIG.itemTypeId || MODULE_CONFIG.objectId}
        viewId={viewData.viewId}
        itemTypeName="customer"
        columns={columnsExport}
      />
      {/* <ModalRemoveData
        isOpen={isOpenModalRemove}
        toggle={toggleModalRemove}
        name={MAP_TITLE.navBar.name}
        objectType={MODULE_CONFIG.objectType}
        filter={filter}
        selectedRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        callback={callback}
        itemTypeId={MODULE_CONFIG.objectId}
      /> */}

      <ModalRemoveProfile
        isOpen={isOpenModalRemove}
        callback={callback}
        toggle={toggleModalRemove}
        totalRecord={table.paging.totalRecord}
        selectedRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        onClose={() => toggleModalRemove(false)}
        moduleType="CUSTOMERS"
      />

      <ModalProcessing
        isOpen={main.isOpenModalProcessing}
        toggle={toggleModalProcessing}
      />

      {/* Drawer detail customer */}
      <DrawerDetail
        open={isOpenDetail}
        onClose={() => setIsOpenDetail(false)}
        menuProps={{
          items: [
            {
              icon: <UIIconXlab name="account" fontSize="24px" />,
              label: 'Overview',
              key: 'overview',
            },
            {
              icon: <Icon type="icon-ants-clock" />,
              label: 'Activity',
              key: 'activity',
            },
          ],
          selectedKeys: [activeTab],
          onClick: item => {
            setActiveTab(item.key);
            searchParams.set('tab', item.key);
            const newUrl = `${
              window.location.href.split('?')[0]
            }?${searchParams.toString()}`;
            window.history.pushState({ path: newUrl }, '', newUrl);
          },
        }}
        anchor="right"
        destroyOnClose
        headerProps={{
          children: <LabelModule className="label">Customer 360</LabelModule>,
          height: 60,
          showBorderBottom: true,
          style: { padding: '0 15px' },
        }}
      >
        <CustomerDetailModule activeTab={activeTab} isDisplayContentDrawer />
      </DrawerDetail>
      {/* <TryVersion module="customers" /> */}
    </ErrorBoundary>
  );
}

// Customer.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  customer: makeSelectCustomer(),
  common: makeSelectCustomerCommon(),
  filter: makeSelectCustomerFilter(),
  column: makeSelectCustomerColumn(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },
    toggleModalProcessing: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL_PROCESSING`, params),
      );
    },
    onChangeDataView: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_DATAVIEW`, params));
    },
    onRemove: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@REMOVE_CUSTOMER`));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(Customer);

// export default compose(withConnect)(Customer);

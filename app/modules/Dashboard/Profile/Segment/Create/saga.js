/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable no-nested-ternary */

// Libraries
import {
  takeLatest,
  call,
  put,
  select,
  delay,
  race,
  take,
} from 'redux-saga/effects';
import { push } from 'react-router-redux';
import { OrderedMap } from 'immutable';
import { get, pick, isEmpty, cloneDeep, concat, isNull } from 'lodash';
import moment from 'moment';

// Services
import SegmentServices from 'services/Segment';
import SQLService from 'services/SqlService';
import PortalSettingServices from 'services/PortalSetting';
import ColumnsServices from 'services/ColumnsV2';
import NotificationServices from '../../../../../services/Notification';

// Actions
import {
  initRules,
  resetRules,
} from 'containers/Segment/Content/Condition/action';
import {
  validateLoading,
  updateValidateQuerySql,
  changeSql,
  initQuerySql,
} from 'containers/Segment/Content/QuerySQL/action';

// Constants
import { MAP_SEGMENT_TYPES } from 'containers/Segment/Content/Condition/constants';
import { getDefaultEndTime } from 'components/Organisms/ComputationSchedule/constant';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_WORKSPACE_SEGMENT } from 'containers/Drawer/DrawerSegment/config';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../Detail/config';
import { MODULE_CONFIG as MODULE_CONFIG_JOURNEY_COMMON } from '../../../MarketingHub/Journey/config';
import APP from '../../../../../appConfig';

// Selectors
import { makeSelectSegmentPreviewColumn } from 'containers/Segment/Preview/selectors';
import {
  getListDone,
  updateValue,
  updateDone,
  addNotification,
  init,
  reset,
  initDone,
  resetData,
} from '../../../../../redux/actions';
import { makeSelectPortal, selectSearch } from '../../../selector';
import selectMainCreateSegment, {
  makeSelectConfigCreateSegment,
} from './selectors';

// Sagas
import {
  dashboardGetGroupItemAttributes,
  dashboardLookupItemAttributes,
  dashboardLookupEventSchema,
} from '../../../saga';

// Utils
import {
  mapDataPlansToFE,
  buildDisableHours,
  getSuggestedTime,
  FORMAT_DATE_API,
} from 'components/Organisms/CalendarTable/utils';
import { safeParse, updateUrl, getObjectPropSafely } from 'utils/common';
import {
  toConditionUI,
  getInputLookupFromRule,
} from 'containers/Segment/Content/Condition/utils';
import {
  bindValidateResultToCondition,
  validateRulesCondition,
  validateCircularSegment,
  validateScheduleTypeDynamic,
} from 'containers/Segment/Content/Condition/utils.conditions';
import { getModuleConfig } from 'modules/Dashboard/Profile/Segment/Detail/utils';
import { isJsonString } from '../../../../../utils/web/json';
import {
  toAPI,
  onValidateScreenCreateSegment,
  converCammelCaseToSnakeCase,
  trimData,
} from './utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import {
  getCurrentOwnerId,
  getCurrentUserId,
  getPortalId,
  setLocalStorage,
} from '../../../../../utils/web/cookie';
import { toMatchingFileFE } from '../../../../../containers/Segment/Content/MatchingFile/utils';
import { isModifiedRules } from '../../../../../containers/Segment/util';
import {
  getBrowser,
  getClientOS,
} from '../../../MarketingHub/Journey/Create/utils.version';
import { makeSelectSegmentDetailDomainMain } from '../Detail/selectors';
import { makeSelectSegmentWorkspace } from 'containers/Drawer/DrawerSegment/selectors';
import {
  ITEM_TYPE_ID,
  JOURNEY,
  USE_SEGMENT_IN,
} from '../../../../../containers/Drawer/DrawerSegment/constants';
import { ACTION_LIMIT_TYPES } from './_reducer/utils';
import { CONDITIONS, QUERY_SQL } from './constants';
import { convertShareAccessToFE } from '../../../../../components/common/ShareAccessAntsomiUI/utils';

const PREFIX = MODULE_CONFIG.key;
const PREFIX_DETAIL = MODULE_CONFIG_DETAIL.key;
const PREFIX_WORKSPACE = MODULE_CONFIG_WORKSPACE_SEGMENT.key;
const PREFIX_JOURNEY_COMMON = MODULE_CONFIG_JOURNEY_COMMON.key;

// const getReducer = state => state.get(PREFIX);
const getStoreKey = id => `sql-recommend-data-userId:${id}`;
const userId = getCurrentUserId();

export default function* workerCustomerSaga(_args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}${ReduxTypes.UPDATE}`, handleSave);
  yield takeLatest(
    `${MODULE_CONFIG.key}@@VALIDATE${ReduxTypes.UPDATE_VALUE}`,
    handleOnValidate,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@VALIDATE_SCHEDULE_TYPE_DYNAMIC@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    handleValidateScheduleTypeDynamic,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@SEGMENT_CHANGE_COMPUTE_TYPE@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    handleResetSettings,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@SEGMENT_RESET_CONDITIONS${ReduxTypes.RESET}`,
    resetCondition,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@GET_PLANS@@${ReduxTypes.UPDATE_VALUE}`,
    handleGetPlans,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@RESET_SUGGESTION_TIME@@${ReduxTypes.UPDATE_VALUE}`,
    handleSuggestedTime,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@VALIDATE_QUERY_SQL@@${ReduxTypes.VALIDATE_VALUE}`,
    handleValidateSQL,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@AUTOCOMPLETE_DATA@@${ReduxTypes.INIT}`,
    handleInitAutoCompleteData,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@FUNCTION_DATA@@${ReduxTypes.INIT}`,
    handleInitFunctionData,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@ACTION_SEGMENT_DONE@@${ReduxTypes.UPDATE_DONE}`,
    handleSegmentActionDone,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@UPDATE_VIEW_ACCESS@@${ReduxTypes.UPDATE_VALUE}`,
    handleGetListGroupAttrs,
  );
  // yield takeLatest(`${PREFIX}@@CLONE_SEGMENT${ReduxTypes.INIT}`, handleInitClone);
}

function* handleInit(action, _args) {
  const search = yield select(selectSearch);
  const notificationId = search.get('notification_id');

  yield call(handleGetListGroupAttrs, action);
  yield call(handleGetListEvent);
  yield put(reset(`${PREFIX}@@RESET_FORECAST_SESSION`));

  let { activeRow = {} } = action.payload;
  const { design, itemTypeId, copyId, name, dataStorage } = action.payload;

  let notiObjectType = null;
  let inputMethodType = null;
  if (notificationId) {
    const notiDetail = yield call(handleGetNotificationDetail, {
      notificationId,
    });

    const { alert_id, properties } = notiDetail;
    notiObjectType = get(notiDetail, 'object', '');
    inputMethodType = get(properties, 'type', null);
    yield put(updateValue(`${PREFIX}@@INIT_RULE_ID`, { initRuleId: alert_id }));

    if (+inputMethodType === QUERY_SQL) {
      yield put(
        init(`${PREFIX}@@INIT_QUERY_SQL`, {
          initQuery: pick(properties.conditions, ['raw', 'formattedQuery']),
        }),
      );
    }

    activeRow = { ...activeRow, ...properties };
  }

  yield call(handleFetchListViewAccess, action.payload);

  if (design === 'update') {
    const {
      accessInfo = { general: {}, listAccess: [] },
    } = convertShareAccessToFE(activeRow.share_access);

    yield put(
      updateValue(`${PREFIX}@@UPDATE_SHARE_ACCESS_INFO@@`, { accessInfo }),
    );

    yield call(initConfigure, activeRow);
    yield call(handleBuildCalendar);
    yield race([
      call(intervalHandleGetData),
      take(`${PREFIX}${ReduxTypes.RESET}`),
    ]);
  }

  if (design === 'create') {
    if (copyId) {
      yield call(handelCreateCopy, copyId, name);
    } else {
      yield call(resetCondition);

      if (!notificationId) {
        yield put(reset(`${PREFIX}@@RESET_INIT_QUERY_SQL`));
      }

      if (dataStorage) {
        yield call(initConfigure, {
          ...dataStorage,
          type: dataStorage.segmentType,
        });
      }
    }

    if (notificationId) {
      yield call(initConfigure, activeRow);

      if (notiObjectType === 'forecast') {
        const configure = yield select(makeSelectConfigCreateSegment());

        const notiViewId = get(activeRow, 'viewId', null);
        if (notiViewId) {
          const viewInfoMapping = get(configure, 'main.viewInfoMapping', {});
          const { owner = [], shareWithMe = [] } = viewInfoMapping;
          const combineView = concat([], owner, shareWithMe);

          const chosenView = combineView.find(
            view => +view.viewId === +notiViewId,
          );
          if (chosenView && !isNull(notiViewId)) {
            yield put(
              updateValue(`${PREFIX}@@UPDATE_VIEW_ACCESS@@`, {
                viewAccess: {
                  ...chosenView,
                  type: 'VIEW',
                },
              }),
            );
          }
        }

        if (inputMethodType) {
          yield put(updateValue(`${PREFIX}@@INPUT_METHOD`, +inputMethodType));
          yield put(
            updateValue(`${PREFIX}@@UPDATE_PREVIEW_DISABLED_STT`, {
              disabled: false,
            }),
          );
        }
      }
    }

    yield put(updateValue(`${PREFIX}@@IS_LOADING@@`, false));

    yield call(handleBuildCalendar);
    yield call(handleSuggestedTime);
  }
}

function* handleFetchListViewAccess(data) {
  const reducer = yield select(selectMainCreateSegment());
  const { main } = reducer;
  const { itemTypeId = '', design = '', activeRow = {} } = main;
  const currentUserId = getCurrentUserId();
  let currentOwnerId = getCurrentOwnerId();
  const cUserId = getObjectPropSafely(() =>
    get(activeRow, 'owner_id', main.activeRow?.share_access?.owner_id),
  );

  if (currentOwnerId === 'all') {
    currentOwnerId = currentUserId;
  }

  const res = yield call(SegmentServices.fetch.getListViewAccess, {
    body: {
      itemTypeId: +itemTypeId || +data.itemTypeId,
      ownerId: design !== 'create' ? +cUserId : +currentOwnerId,
    },
  });

  if (res && res.code === 200 && res.data && Object.keys(res.data).length > 0) {
    const { businessObject = {}, owner = [], shareWithMe = [] } = res.data;
    let preSelect = {};

    if (design === 'create') {
      if (!isEmpty(businessObject)) {
        preSelect = { type: 'BO', ...cloneDeep(businessObject) };
      } else if (Array.isArray(owner) || Array.isArray(shareWithMe)) {
        [preSelect] = [...owner, ...shareWithMe];
        preSelect = { ...preSelect, type: 'VIEW' };
      }
      yield put(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_VIEW_ACCESS@@`, {
          viewAccess: preSelect,
        }),
      );
    }
    yield put(
      updateValue(`${PREFIX}@@UPDATE_DATA_ACCESS_VIEW@@`, {
        design,
        activeRow,
        preSelect,
        owner,
        shareWithMe,
        BOInfo: businessObject,
      }),
    );
  }
}

function* handleGetNotificationDetail(action) {
  try {
    const { notificationId } = action;

    const { data } = yield call(NotificationServices.detail, {
      notificationId,
    });

    const { info } = data;

    if (!isJsonString(info.properties)) {
      throw new Error('data.info.properties is invalid json string');
    }

    info.properties = JSON.parse(info.properties);

    return info;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Detail/saga.js',
      func: 'handleGetActiveSegmentFromNotiDetail',
      data: error.stack,
    });
    console.log(error);
    return null;
  }
}

function* intervalHandleGetData() {
  try {
    while (true) {
      yield delay(10000);
      yield call(handleGetDataInterval);
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/List/saga.js',
      func: 'intervalHandleGetData',
      data: err.stack,
    });
    console.error(err);
  }
}
function* handleGetDataInterval() {
  const reducer = yield select(selectMainCreateSegment());
  const { main } = reducer;
  const { activeRow } = main;
  const { segmentId } = activeRow;
  // console.log('intervallllllllllllllllllll', segmentId, reducer);
  if (segmentId) {
    const res = yield call(SegmentServices.data.getDetail, {
      objectId: segmentId,
    });
    let activeSegment = res.data;
    const itemTypeId = parseInt(get(res, 'data.itemTypeId', ''));
    if (itemTypeId !== -1003 && itemTypeId !== -1007) {
      activeSegment = {};
    }

    // console.log({ res, segmentId, PREFIX });
    yield put(
      updateValue(`${PREFIX}@@UPDATE_STATUS_INTERVAL`, {
        data: activeSegment,
      }),
    );
  }
}

function* handelCreateCopy(copyId, name) {
  try {
    const activeRow = yield call(SegmentServices.data.getDetail, {
      objectId: copyId,
    });

    const convertedActiveRow = converCammelCaseToSnakeCase(activeRow.data);
    convertedActiveRow.segmentId = convertedActiveRow.segment_id;

    yield put(
      init(`${PREFIX}@@CLONE_SEGMENT`, { activeRow: convertedActiveRow, name }),
    );
    yield call(initConfigure, convertedActiveRow, true);

    const searchParams = new URLSearchParams(window.location.search);
    searchParams.delete('name');
    searchParams.delete('copyId');
    updateUrl(
      `${
        APP.PREFIX
      }/${getPortalId()}/profile/segments/create?${searchParams.toString()}`,
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handelCreateCopy',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* initConfigure(item, isClone) {
  // init screen target user
  const reducer = yield select(selectMainCreateSegment());
  const {
    main: { design },
  } = reducer;
  let configure = yield select(makeSelectConfigCreateSegment());
  const {
    main: { type },
  } = configure;
  // const { configure } = reducer;
  const conditions = safeParse(item.conditions, {});

  const rulesOR = safeParse(conditions.OR, []);
  const inputLookup = getInputLookupFromRule(rulesOR);
  // console.log('inputLookup', inputLookup);
  const dataSourceID = '-1';

  yield call(dashboardLookupItemAttributes, MODULE_CONFIG.key, {
    dataPost: { attributes: inputLookup.comp_attr },
  });

  // lookup call eventSchema
  if (inputLookup.perf_event.length > 0) {
    yield call(dashboardLookupEventSchema, MODULE_CONFIG.key, {
      insightPropertyId: dataSourceID,
      dataPost: {
        eventIds: inputLookup.perf_event,
      },
      // label: 'eventTrackingName',
    });
  }
  // console.log({ type });

  configure = yield select(makeSelectConfigCreateSegment());
  if (type === 1) {
    // console.log(conditions);
    const rules = toConditionUI(
      safeParse(conditions, {}),
      {
        map: {
          conditionType: MAP_SEGMENT_TYPES,
          itemAttribute: configure.main.groupItemAttrs.map,
          eventSchema: configure.main.eventSchema.map,
        },
        info: configure.main.mapInfo,
      },
      false,
      true,
    );
    // console.log(rules);
    yield put(initRules(MODULE_CONFIG, { data: rules }));
  } else if (type === 2) {
    yield put(
      init(`${PREFIX}@@MATCHING_FILE`, {
        data: toMatchingFileFE(conditions, isClone ? 'clone' : design),
      }),
    );
  } else if (type === 6) {
    yield put(initQuerySql(MODULE_CONFIG, { ...conditions }));
  }
  // yield put(initRules(MODULE_CONFIG, { data: rules }));
  yield put(updateValue(`${PREFIX}@@IS_LOADING@@`, false));
  yield call(handleValidateScheduleTypeDynamic);
}

function* resetCondition(_action) {
  yield put(initRules(MODULE_CONFIG, { data: OrderedMap({}) }));
  yield put(init(`${PREFIX}@@MATCHING_FILE`, { data: {} }));
}

function* handleOnValidate() {
  const reducer = yield select(selectMainCreateSegment());
  let isPreviewDisabled = false;
  const inputMethodType = Number(get(reducer, 'configure.main.type', null));

  switch (inputMethodType) {
    case QUERY_SQL: {
      const querySqlError = get(reducer, 'configure.querySql.error', null);

      isPreviewDisabled = Boolean(querySqlError);
      break;
    }
    case CONDITIONS:
    default: {
      isPreviewDisabled = reducer.configure.condition.size === 0;
      break;
    }
  }
  // console.log('reducer', reducer);
  const isDisabled = onValidateScreenCreateSegment(reducer);
  yield put(updateValue(`${PREFIX}@@VALIDATE_BUTTON_BUILD`, isDisabled));
  yield put(
    updateValue(`${PREFIX}@@VALIDATE_BUTTON_PREVIEW`, isPreviewDisabled),
  );
}

function* handleGetListEvent() {
  const res = yield call(SegmentServices.fetch.getListEvent);
  if (res.code === 200) {
    yield put(getListDone(`${PREFIX}_EVENT_TRACKING`, res.data));
  } else {
    const notification = {
      id: 'error',
      message: 'Get list event failed, please try again.',
      timeout: 1500,
      timestamp: new Date().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
  }
}

function* handleSuggestedTime() {
  try {
    const reducer = yield select(selectMainCreateSegment());
    const { configure } = reducer;
    const { main: mainConfigure = {} } = configure;
    const { computeSchedule = {}, modalCalendarTable = {} } = mainConfigure;
    const { timeRange, disableTimes } = modalCalendarTable;

    const { day, hour, suggestedTime } = getSuggestedTime(
      disableTimes,
      timeRange,
    );

    const startTime = {
      day,
      hour,
    };

    const endTime = getDefaultEndTime(startTime);

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        selectedTime: suggestedTime,
      }),
    );

    // update start time for computetationSchedule
    yield put(
      updateValue(`${PREFIX}@@SEGMENT_COMPUTE_SCHEDULE@@`, {
        ...computeSchedule,
        repeatStartTime: startTime,
        endTime,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleSuggestedTime',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleGetLimitation() {
  try {
    const { data: dataLimitation } = yield call(
      PortalSettingServices.portalSetting.getDetailLimitation,
    );
    const limitHour = get(
      dataLimitation,
      'segment.quantity_limit.limit_num_segment_1_hour.value',
      9999,
    );

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        limitHour,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleGetPlans',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleGetPlansGroupByDate() {
  try {
    const reducer = yield select(selectMainCreateSegment());
    const timeRange = get(
      reducer,
      'configure.main.modalCalendarTable.timeRange',
      {},
    );

    const { data } = yield call(SegmentServices.getPlans, {
      data: {
        from: timeRange.start,
        to: timeRange.end,
        group_by: 'date-time',
      },
    });

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        dataEventsGroupByDate: data,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleGetPlans',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleGetPlans(action) {
  try {
    const { timeRange } = action.payload;
    const reducer = yield select(selectMainCreateSegment());
    const modalCalendarTable = get(
      reducer,
      'configure.main.modalCalendarTable',
      {},
    );

    const endCalendar = moment(
      modalCalendarTable.timeRange.end,
      FORMAT_DATE_API,
    );
    const endTimeRange = moment(timeRange.end, FORMAT_DATE_API);

    if (endTimeRange > endCalendar) {
      timeRange.end = modalCalendarTable.timeRange.end;
    }

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        isLoading: true,
      }),
    );

    const { data: dataPlans } = yield call(SegmentServices.getPlans, {
      data: {
        from: timeRange.start,
        to: timeRange.end,
      },
    });

    const dataEvents = mapDataPlansToFE(dataPlans);

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        dataEvents,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleGetPlans',
      data: err.stack,
    });
    console.log('error', err);
  } finally {
    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        isLoading: false,
      }),
    );
  }
}

function* handleBuildDisableHours() {
  try {
    const reducer = yield select(selectMainCreateSegment());
    const { configure } = reducer;
    const { main: mainConfigure = {} } = configure;
    const { computeSchedule, modalCalendarTable = {} } = mainConfigure;
    const { dataEventsGroupByDate, limitHour, timeRange } = modalCalendarTable;

    const disableTimes = buildDisableHours({
      dataEventsGroupByDate,
      computeSchedule,
      limitHour,
      timeRange,
    });

    yield put(
      updateValue(`${PREFIX}@@MODAL_CALENDAR_TABLE@@`, {
        disableTimes,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleBuildDisableHours',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleBuildCalendar() {
  try {
    yield put(
      updateValue(`${PREFIX}@@SEGMENT_COMPUTE_SCHEDULE@@`, {
        isLoading: true,
      }),
    );

    yield call(handleGetPlansGroupByDate);
    yield call(handleGetLimitation);
    yield call(handleBuildDisableHours);
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleBuildCalendar',
      data: err.stack,
    });
    console.log('error', err);
  } finally {
    yield put(
      updateValue(`${PREFIX}@@SEGMENT_COMPUTE_SCHEDULE@@`, {
        isLoading: false,
      }),
    );
  }
}

function* handleGetListGroupAttrs(action, args) {
  const viewInfo = action.payload?.viewAccess;

  const reducer = yield select(selectMainCreateSegment());
  const { main } = reducer;
  const { activeRow, design } = main;
  const {
    segmentId,
    view_id: viewIdDetail,
    item_type_id: itemTypeIdDetail,
  } = activeRow;

  const params = {
    itemTypeIds: '-1007,-1003',
    isSupportGroupAttr: false,
    isFilter: 1,
    segmentId,
  };

  const viewId = viewInfo?.viewId || viewIdDetail;
  const itemTypeId = viewInfo?.itemTypeId || itemTypeIdDetail;

  if (viewId && itemTypeId) {
    params.itemTypeIds = itemTypeId;
    params.viewId = viewId;
  }

  yield call(dashboardGetGroupItemAttributes, `${PREFIX}_GROUP_ATTRS`, params);
}

function* handleGoToList() {
  const portal = yield select(makeSelectPortal);
  yield put(push(`${APP.PREFIX}/${portal.portalId}/profile/segments`));
}

export function* handleValidateScheduleTypeDynamic(action) {
  try {
    const reducer = yield select(selectMainCreateSegment());
    const { configure } = reducer;

    const validate = validateScheduleTypeDynamic(configure.condition);

    yield put(updateValue(`${PREFIX}@@VALIDATE_DYNAMIC_SCHEDULE@@`, validate));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'validateScheduleTypeDynamic',
      data: err.stack,
    });
    console.log('error', err);
  }
  return true;
}

export function* handleValidateCondition(action) {
  try {
    const reducer = yield select(selectMainCreateSegment());
    const { main, configure } = reducer;
    const { activeRow } = main;
    const { segmentId } = activeRow;
    const validate = validateRulesCondition(configure.condition);
    if (validate.errors.length > 0) {
      // initCondition;
      const tmpCondition = bindValidateResultToCondition(
        configure.condition,
        validate.errors,
      );

      // console.log(('tmpCondition', tmpCondition));
      // props.initRules(tmpCondition);
      yield put(initRules(MODULE_CONFIG, { data: tmpCondition }));
      yield put(updateDone(PREFIX));
      return false;
    }

    const circularValidate = yield call(
      validateCircularSegment,
      configure.condition,
      segmentId,
    );

    if (circularValidate.errors.length > 0) {
      // initCondition;
      const tmpCondition = bindValidateResultToCondition(
        configure.condition,
        circularValidate.errors,
      );

      yield put(initRules(MODULE_CONFIG, { data: tmpCondition }));
      yield put(updateDone(PREFIX));
      return false;
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleValidateCondition',
      data: err.stack,
    });
    console.log('error', err);
  }
  return true;
}

export function* handleValidateMatchingFile(action) {
  try {
    yield put(updateValue(`${PREFIX}@@HANDLE_ERRORS`));
    // select matchingFile after HANDLE_ERRORS
    const {
      configure: { matchingFile },
    } = yield select(selectMainCreateSegment());
    // console.log({ matchingFile, errors: matchingFile.errors });
    if (
      matchingFile.mappingAttributes.length === 0 ||
      Object.keys(matchingFile.errors.headerValue).length > 0
    ) {
      // console.log(
      //   'errors: ',
      //   `${PREFIX}@@HANDLE_ERRORS${ReduxTypes.UPDATE_VALUE}`,
      // );
      yield put(updateDone(PREFIX));
      return false;
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleValidateCondition',
      data: err.stack,
    });
    console.log('error', err);
  }
  return true;
}

function* handleSegmentActionDone(action) {
  try {
    const design = get(action, 'payload.design', '');

    const workspaceSegment = yield select(makeSelectSegmentWorkspace());
    const useSegmentInto = get(workspaceSegment, 'useSegmentInto', '');

    if (useSegmentInto === JOURNEY) {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.delete(USE_SEGMENT_IN);
      searchParams.delete(ITEM_TYPE_ID);

      yield put(updateValue(`${PREFIX}@@TOGGLE_WORKSPACE`, false));
      yield put(
        resetData(`${PREFIX_WORKSPACE}@@RESET_WORKSPACE`, {
          isGoToListing: false,
        }),
      );
      yield put(
        updateValue(`${PREFIX_JOURNEY_COMMON}@@IS_OPEN_SUB_DRAWER`, false),
      );

      let url = window.location.pathname;
      if (searchParams.size) {
        url += `?${searchParams.toString()}`;
      }

      yield call(resetCondition);
      yield put(push(url));
    } else {
      if (design === 'update') {
        yield put(reset(`${PREFIX_DETAIL}@@RESET@@`));
      }

      yield call(resetCondition);
      yield call(handleGoToList);
      yield put(
        resetData(`${PREFIX_WORKSPACE}@@RESET_WORKSPACE`, {
          isGoToListing: true,
        }),
      );
      yield put(updateValue(`${PREFIX_WORKSPACE}@@RE_INIT_DASHBOARD`));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleSegmentActionDone',
      data: err.stack,
    });
  }
}

export function* handleValidateEmptySegment(action) {
  try {
    const errors = false;
    if (errors) {
      yield put(updateDone(PREFIX));
      return false;
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleValidateCondition',
      data: err.stack,
    });
    console.log('error', err);
  }
  return true;
}

function* handleSave(action) {
  const {
    isUploadStep,
    itemTypeId,
    isBuild,
    mappingAttributes,
    importId,
    // computeSchedule,
    isTypeApiV21,
    importType,
    afterSave,
  } = action.payload;
  try {
    const reducer = yield select(selectMainCreateSegment());
    const { main, configure } = reducer;
    const { design, activeRow } = main;
    const { main: mainConfigure = {}, querySql } = configure;
    const { type, computeSchedule } = mainConfigure;
    // const { dataEvents, limitHour, timeRange } = modalCalendarTable;
    const statusValiate =
      type === 1
        ? yield call(handleValidateCondition)
        : type === 2
        ? yield call(handleValidateMatchingFile)
        : type === 3
        ? yield call(handleValidateEmptySegment)
        : type === 4
        ? yield call(handleValidateEmptySegment)
        : type === 6
        ? yield call(handleValidateSQL)
        : type === 7
        ? yield call(handleValidateEmptySegment)
        : type === 5;
    if (statusValiate === false) {
      console.log('validate ', statusValiate);
      yield put(updateDone(PREFIX));
      return;
    }

    // if (design === 'create' && computeSchedule.type === 'dynamic') {
    //   // rebuild disableHours for current settings
    //   const disableTimes = buildDisableHours({
    //     computeSchedule,
    //     dataEvents,
    //     timeRange,
    //     limitHour,
    //   });

    //   const scheduleValidate = validateComputeScheduleInHour({
    //     computeSchedule,
    //     disableTimes,
    //     timeRange,
    //   });

    //   if (scheduleValidate === false) {
    //     yield put(
    //       updateValue(`${PREFIX}@@ERRORS`, {
    //         name: 'limit_schedule',
    //         message: getTranslateMessage(
    //           TRANSLATE_KEY._004,
    //           'In this hour, the limit on the number of segments to be built has been reached.',
    //         ),
    //       }),
    //     );

    //     yield put(updateDone(PREFIX));
    //     return;
    //   }
    // }

    const data = toAPI(
      main,
      configure,
      design,
      'segment',
      itemTypeId,
      isBuild,
      mappingAttributes,
      importId,
      computeSchedule,
      importType,
      querySql,
    );
    const objectId = main.activeRow.activeId;
    //
    const versionInfo = {
      os: getClientOS(),
      browser: getBrowser(),
      description: 'has created this segment',
    };
    let res;
    if (design === 'create') {
      const moduleConfig = getModuleConfig(
        main.itemTypeId,
        MODULE_CONFIG,
        design,
        activeRow.segment_id,
      );
      data.versionInfo = versionInfo;
      data.isNewVersion = true;

      const { objectType } = moduleConfig;
      // create Collection
      if (isTypeApiV21) {
        res = yield call(SegmentServices.createV21, data);
      } else {
        res = yield call(SegmentServices.create, data);
      }
      // console.log({ createAPI: data });

      if (type === 1) {
        // get modColumnId của collection vừa tạo
        const lastModifyColumn = yield call(ColumnsServices.getLast, {
          objectType:
            main.itemTypeId == '-1003'
              ? 'CUS_SEGM_MEMBERS'
              : 'USR_SEGM_MEMBERS',
          objectId: res.data,
        });

        // create lastModifyColumn with current columns
        const { columnObj = {} } = yield select(
          makeSelectSegmentPreviewColumn(),
        );
        const { columns = {} } = columnObj;
        const paramsFirstModifyColumns = {
          objectType:
            main.itemTypeId == '-1003'
              ? 'CUS_SEGM_MEMBERS'
              : 'USR_SEGM_MEMBERS',
          objectId: res.data,
          modColumnId: lastModifyColumn.data.modColumnId,
          body: { columns },
        };
        yield call(ColumnsServices.update, paramsFirstModifyColumns);
      }
    } else {
      const { versionId, activeRow } = yield select(
        makeSelectSegmentDetailDomainMain(),
      );
      const excludeCheckedModified = [
        'activeId',
        'isBuild',
        'item_type_id',
        'description',
        'segment_display',
        'status',
        'importId',
        'importTypes',
      ];
      if (Number(activeRow.segment_type) === 5) {
        excludeCheckedModified.push('conditions');
      }
      const dataTmp = converCammelCaseToSnakeCase(data);
      dataTmp.segmentId = dataTmp.segment_id;
      const isModified = isModifiedRules(activeRow, dataTmp, {
        excludeProperty: excludeCheckedModified,
      });
      versionInfo.description = 'has changed this segment';
      data.versionInfo = versionInfo;
      data.versionId = activeRow.segment_version.version;
      data.isNewVersion = versionId || activeRow.isSaveAs ? true : isModified;
      res = yield call(SegmentServices.update, {
        objectId,
        data,
      });
    }

    if (res.code === 200) {
      let isGoToList = true;

      const notification = {
        id: 'success',
        message: design === 'create' ? 'Created' : 'Updates saved!',
        translateCode:
          design === 'create'
            ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
            : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
        timeout: 1500,
        timestamp: new Date().getTime(),
        type: 'success',
      };

      if (afterSave) {
        yield call(afterSave, data, {
          preventGoToList: () => {
            isGoToList = false;
          },
        });
      }

      yield put(addNotification(notification));
      yield put(updateDone(PREFIX, true));
      // yield delay(1000);

      if (res.codeMessage === '_NOTIFICATION_BUILD_THE_NEXT_VALID_TIME') {
        yield put(
          updateValue(`${MODULE_CONFIG.key}@@MESSAGE_WARNING_LIMITATIONS@@`, {
            type: ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT,
            message: getTranslateMessage(
              TRANSLATE_KEY._WARN_SEG_COMPUTATIONAL_LIMIT,
              `Currently, the computation limit has been reached, the system will compute this segment in the next valid time.`,
            ),
          }),
        );

        yield put(
          updateValue(
            `${MODULE_CONFIG.key}@@TOGGLE_MODEL_WARNING_LIMITATIONS@@`,
            {
              isOpen: true,
              type: ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT,
            },
          ),
        );

        yield call(handleSegmentActionDone, { payload: { design } });
        if (design === 'create') {
          yield put(initRules(MODULE_CONFIG, { data: OrderedMap({}) }));
        }
      } else if (design === 'create') {
        // yield call(handleGoDetail, res.data); no use
        // yield put(createDone(MODULE_CONFIG_CREATE, res.data));
        // yield call(handleSaveModifyColumns, res.data);

        yield call(handleSegmentActionDone, { payload: { design } });
        yield put(initRules(MODULE_CONFIG, { data: OrderedMap({}) }));
      } else {
        // yield call(handleSaveModifyColumns, objectId);
        // eslint-disable-next-line no-lonely-if
        if (!isUploadStep && isGoToList) {
          yield call(handleSegmentActionDone, { payload: { design } });
        }
      }
    } else if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
      yield put(
        updateValue(`${PREFIX}@@ERRORS`, {
          name: 'name',
          message: getTranslateMessage(
            res.codeMessage,
            'This name already existed',
          ),
        }),
      );
    } else {
      const notification = {
        id: 'error',
        // message: 'Fail to update the campaign, please try again.',
        // message: res.message,
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 5000,
        timestamp: new Date().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
    }
    yield put(updateDone(PREFIX));
  } catch (err) {
    const reducer = yield select(selectMainCreateSegment());
    const { main } = reducer;
    const { design } = main;

    const notification = {
      id: 'error',
      message: getTranslateMessage(
        design === 'create'
          ? TRANSLATE_KEY._NOTI_FAIL_CREATE_SEGMENT
          : TRANSLATE_KEY._NOTI_FAIL_UPDATE_SEGMENT,
        design === 'create'
          ? 'Fail to create the segment, please try again.'
          : 'Fail to update the segment, please try again.',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 5000,
      timestamp: new Date().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
    // yield put(updateDone(MODULE_CONFIG_CREATE));
    console.log(err);
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleSave',
      data: err.stack,
    });
  }
}
// export function resetRules2(moduleConfig, payload) {
//   return { type: `${moduleConfig.key}_${Types.RESET_RULES}`, payload };
// }
export function* handleResetSettings(action) {
  try {
    // reset Condition
    yield put(resetRules(MODULE_CONFIG));
    // reset MatchinFile
    yield put(reset(`${PREFIX}@@RESET_MATCHING_FILES`));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleResetSettings',
      data: err.stack,
    });
    console.log('error', err);
  }
  return true;
}

export function* handleValidateSQL(_action) {
  try {
    yield put(validateLoading(MODULE_CONFIG, true));
    const selectCreateSegment = yield select(selectMainCreateSegment());
    const { main, configure } = selectCreateSegment;

    if (configure?.querySql?.error) {
      yield put(
        updateValidateQuerySql(MODULE_CONFIG, {
          show: true,
          status: false,
          message:
            configure?.querySql?.error?.message ||
            'SQL syntax validation failed',
        }),
      );
      yield put(validateLoading(MODULE_CONFIG, false));
      return false;
    }

    // Call api validate sql
    const res = yield call(SQLService.validateSQL, {
      body: {
        item_type_id: main.itemTypeId,
        sql: configure?.querySql?.formattedQuery,
      },
    });
    yield put(
      updateValidateQuerySql(MODULE_CONFIG, {
        show: true,
        status: res.status,
        message: res?.status ? 'SQL syntax validation successful' : res.message,
      }),
    );
    yield put(validateLoading(MODULE_CONFIG, false));

    return res.status;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleValidateSQL',
      data: error.stack,
    });
    yield put(
      updateValidateQuerySql(MODULE_CONFIG, {
        show: true,
        status: false,
        message:
          error.message || error.msg || error || 'SQL syntax validation failed',
      }),
    );
    yield put(validateLoading(MODULE_CONFIG, false));
    return false;
  }
}
function* handleInitAutoCompleteData(_action) {
  try {
    /** Business Objects & Sources */
    const tables = yield call(SQLService.getTableSqlWorkspace);
    const { data } = tables;
    const { businessObjects = [], sources = [] } = data || {};

    // BO
    const boItem = {
      label: 'BO',
      code: 'bos',
      tables: [],
    };

    businessObjects.forEach(bo => {
      const boTable = {
        label: trimData(bo.itemTypeDisplay),
        code: trimData(bo.itemTypeName),
        fields: [],
      };

      bo.properties.forEach(field => {
        boTable.fields.push({
          label: trimData(field.propertyDisplay),
          code: trimData(field.fieldCode),
        });
      });
      boItem.tables.push(boTable);
    });

    // SOURCE
    const sourceItems = [];

    sources.forEach(source => {
      const boSource = {
        label: trimData(source.insightPropertyDisplay),
        code: trimData(source.insightPropertyCode),
        tables: [],
      };

      source.events.forEach(event => {
        const boTable = {
          label: trimData(event.eventNameDisplay),
          code: trimData(event.eventTrackingName),
          fields: [],
        };

        event.properties.forEach(field => {
          boTable.fields.push({
            label: trimData(field.propertyDisplay),
            code: trimData(field.fieldCode),
          });
        });

        boSource.tables.push(boTable);
      });

      sourceItems.push(boSource);
    });

    /** Analytic Tables */
    const analyticTables = yield call(SQLService.getAllRecommendAnalyticTables);
    const analyticData = getObjectPropSafely(() => analyticTables.data.rows);
    const analyticTableItem = {
      label: 'Analytics Table',
      code: 'ats',
      tables: [],
    };
    if (analyticData && analyticData.length) {
      analyticData.forEach(table => {
        const atTable = {
          label: trimData(table.tableName),
          code: trimData(table.tableCode),
          fields: [],
        };

        table.columns.forEach(field => {
          atTable.fields.push({
            label: trimData(field.columnName),
            code: trimData(field.columnCode),
          });
        });
        analyticTableItem.tables.push(atTable);
      });
    }

    setLocalStorage(
      getStoreKey(userId),
      JSON.stringify([boItem, ...sourceItems, analyticTableItem]),
    );

    yield put(
      initDone(`${MODULE_CONFIG.key}@@AUTOCOMPLETE_DATA@@`, {
        autoCompleteData: [boItem, ...sourceItems, analyticTableItem],
      }),
    );
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleInitAutoCompleteData',
      data: error.stack,
    });
    console.log('error', error);
  }
}

function* handleInitFunctionData(_action) {
  try {
    const functions = yield call(SQLService.getAllRecommendFunctions);
    yield put(
      initDone(`${MODULE_CONFIG.key}@@FUNCTION_DATA@@`, {
        functionData: functions.data,
      }),
    );
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Create/saga.js',
      func: 'handleGetListFunctionData',
      data: error.stack,
    });
    console.log('error', error);
  }
}

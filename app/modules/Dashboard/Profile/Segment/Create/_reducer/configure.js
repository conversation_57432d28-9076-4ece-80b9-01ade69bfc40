/* eslint-disable no-restricted-syntax */
/* eslint-disable indent */
/* eslint-disable dot-notation */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { combineReducers } from 'redux';

import conditionReducerFor from 'containers/Segment/Content/Condition/reducer';
import querySqlReducerFor from 'containers/Segment/Content/QuerySQL/reducer';
import { toUIComputeSchedule } from 'containers/Segment/Content/GeneralSettings/utils';
import { toUISegmentMember } from 'containers/Segment/Content/SegmentMember/utils';
import { cloneDeep, has, isEmpty, isNull } from 'lodash';
import ReduxTypes from '../../../../../../redux/constants';
import { MODULE_CONFIG } from '../config';
import {
  mapGroupItemAttributeUniqueWithItemTypeId,
  autoMapDataWithLookup,
} from '../../../../../../services/map';
import {
  toEventTrackingFE,
  initialStateConfigure,
  isValidName,
  labelCantBlank,
  labelInvalidName,
  labelMaxLength,
  initValueComputeSchedule,
  ACTION_LIMIT_TYPES,
} from './utils';
import { getUntitledName } from '../../../../../../utils/web/properties';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { safeParse } from '../../../../../../utils/common';
import matchingFileReducerFor from '../../../../../../containers/Segment/Content/MatchingFile/reducer';
import {
  getNotificationDetailDefault,
  getNotificationDefault,
} from '../../../../../../components/Organisms/NotificationSetup/constant';
import { EMPTY_SEGMENT, UNSUBSCRIBE_SEGMENT } from '../constants';

const MAX_LENGTH = 255;

const configureReducerFor = moduleConfig => {
  const PREFIX = moduleConfig.key;
  const configureReducer = (state = initialStateConfigure(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT}`: {
          // const { item, param } = action.payload;
          const { itemTypeId, activeRow, design, dataStorage } = action.payload;
          // fake activeRow
          // const activeRow = { ...action.payload.activeRow, segmentType: 2 };
          const segmentMemberStore = toUISegmentMember(
            safeParse(dataStorage && dataStorage.refine, {}),
          );
          const segmentTypeStore = safeParse(
            dataStorage && dataStorage.segmentType,
            1,
          );
          draft.validate = {
            total: 0,
            miss: 0,
          };
          draft.name = getUntitledName(
            getTranslateMessage(
              TRANSLATE_KEY._UNTITLED_SEGMENT,
              'Untitled Segment',
            ),
          );
          draft.description = '';
          draft.type = 1;
          draft.computeSchedule = initValueComputeSchedule();
          draft.notificationSetup = getNotificationDefault();
          draft.initDataComputeSchedule = {};
          draft.segmentMember = {};
          draft.initDataSegmentMember = {};
          draft.tmpComputeScheduleType = '';
          draft.errors = { name: [] };
          draft.isOpenModal = false;
          draft.isOpenModalComputationLimit = false;
          draft.isOpenModalWarningTotalLimit = false;
          draft.isValidTotalLimit = false;
          draft.messageWarningLimit = '';
          draft.messageWarningComputationLimit = '';
          draft.nextPage = '';
          if (design === 'update') {
            const computeSchedule = toUIComputeSchedule(
              safeParse(activeRow.compute_schedule, {}),
            );
            const segmentMember = toUISegmentMember(
              safeParse(activeRow.refine, {}),
            );
            draft.name = activeRow.segment_display;
            draft.description = safeParse(activeRow.description, '');
            draft.computeSchedule = computeSchedule;
            draft.cacheComputeSchedule = computeSchedule;
            // console.log(
            //   activeRow.alertSetting ||
            //     getNotificationDetailDefault(activeRow.c_user_id),
            // );
            draft.notificationSetup =
              cloneDeep(activeRow.alert_setting) ||
              getNotificationDetailDefault(activeRow.c_user_id);
            draft.initDataComputeSchedule = computeSchedule;

            draft.segmentMember = segmentMember;
            draft.initDataSegmentMember = segmentMember;
            draft.type = parseInt(activeRow.segment_type);
          }
          draft.notSaveRuleWhenUpdateCondition = false;
          if (dataStorage) {
            draft.initDataSegmentMember = segmentMemberStore;
            draft.type = segmentTypeStore;
          }
          return;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          draft.initRuleId = null;
          draft.accessInfo = { general: {}, listAccess: [] };
          draft.viewData = {};
          draft.viewErrors = [];
          draft.viewInfoMapping = {};

          return;
        }
        case `${PREFIX}@@RESET_INIT_DEFAULT@@${ReduxTypes.RESET}`: {
          return initialStateConfigure();
        }
        case `${PREFIX}@@INIT_RULE_ID${ReduxTypes.UPDATE_VALUE}`: {
          const { initRuleId } = action.payload;

          if (initRuleId) {
            draft.initRuleId = initRuleId;
          }
          return;
        }
        case `${PREFIX}@@CLONE_SEGMENT${ReduxTypes.INIT}`: {
          const { itemTypeId, activeRow = {}, design, name } = action.payload;
          // fake activeRow
          // console.log('init clone: ', action.payload);
          draft.validate = {
            total: 0,
            miss: 0,
          };
          draft.errors = { name: [] };
          const computeSchedule = toUIComputeSchedule(
            safeParse(activeRow.compute_schedule, {}),
          );
          const segmentMember = toUISegmentMember(
            safeParse(activeRow.refine, {}),
          );
          draft.type = parseInt(activeRow.segment_type);
          draft.name =
            name ||
            getUntitledName(
              getTranslateMessage(
                TRANSLATE_KEY._UNTITLED_SEGMENT,
                'Untitled Segment',
              ),
            );
          draft.description = safeParse(activeRow.description, '');
          draft.computeSchedule = computeSchedule;
          draft.initDataComputeSchedule = computeSchedule;

          draft.segmentMember = segmentMember;
          draft.initDataSegmentMember = segmentMember;
          // }
          draft.notSaveRuleWhenUpdateCondition = false;
          return;
        }

        case `${PREFIX}_EVENT_TRACKING${ReduxTypes.GET_LIST_DONE}`: {
          draft.eventSchema = toEventTrackingFE(action.payload);
          return;
        }

        case `${PREFIX}@@MODAL_CALENDAR_TABLE@@${ReduxTypes.UPDATE_VALUE}`: {
          if (action.payload && Object.keys(action.payload).length) {
            draft.modalCalendarTable = {
              ...state.modalCalendarTable,
              ...action.payload,
            };
          }
          return;
        }

        case `${PREFIX}@@UPDATE_DATA_ACCESS_VIEW@@${ReduxTypes.UPDATE_VALUE}`: {
          if (action.payload && Object.keys(action.payload).length) {
            const {
              design = '',
              preSelect = {},
              activeRow = {},
              ...viewInfo
            } = action.payload;

            if (design === 'create') {
              draft.viewData = preSelect;
              draft.viewErrors =
                isEmpty(preSelect) ||
                (has(preSelect, 'type') && Object.keys(preSelect).length === 1)
                  ? [
                      getTranslateMessage(
                        TRANSLATE_KEY._NOTI_SEGMENT_NO_PERMISSION,
                        'You do not have permission for this object',
                      ),
                    ]
                  : [];
            } else if (design) {
              const { view_id: viewId = {} } = activeRow || {};
              const { BOInfo = {}, owner = [], shareWithMe = [] } = viewInfo;
              const viewIndex = []
                .concat(owner, shareWithMe)
                .findIndex(each => +each.viewId === +viewId);
              const noPermissionView = !isEmpty(viewId) && viewIndex === -1;
              let viewData = viewId;

              if (isNull(viewId) && !isEmpty(BOInfo)) {
                viewData = { ...BOInfo, type: 'BO' };
              }

              if (viewId && typeof viewId !== 'object') {
                const combineView = [].concat(owner, shareWithMe);
                const viewSelected = combineView.find(
                  view => +view?.viewId === +viewId,
                );

                viewData = { ...viewSelected, type: 'VIEW' };
              }

              if (noPermissionView) {
                viewData = null; // Reset view when has no permission anymore
              }

              draft.viewData = viewData;
              draft.viewErrors =
                (isEmpty(BOInfo) && isEmpty(viewId)) || noPermissionView
                  ? [
                      getTranslateMessage(
                        TRANSLATE_KEY._NOTI_SEGMENT_NO_PERMISSION,
                        'You do not have permission for this object',
                      ),
                    ]
                  : [];
            }

            draft.viewInfoMapping = viewInfo;
          }
          return;
        }

        case `${PREFIX}_GROUP_ATTRS${ReduxTypes.GET_LIST_DONE}`: {
          // const data  = action.payload;
          const data = mapGroupItemAttributeUniqueWithItemTypeId(
            action.payload,
          );
          // draft.groupItemAttrs.list = data.groups;
          // const itemTypeData = data[state.]
          // console.log('tmp', tmp, mapData);

          // const tmp = mapData.mapByItemTypeId[itemTypeId];
          // console.log('groupItemAttrs.map===>', data.filterGroups);
          draft.groupItemAttrs.list = data.filterGroups;
          draft.groupItemAttrs.map = data.map;
          return;
        }

        case `${PREFIX}DATA_LOOKUP${ReduxTypes.UPDATE_VALUE}`: {
          const { type, data } = action.payload;
          // draft.dataLookup[type] = data;
          if (type === 'dataSource') {
            //
            draft.mapInfo.dataSource = data.map;
          } else if (type === 'eventSchema') {
            // console.log('type, data', type, data);
            // const dataLookup = toEventTrackingFE(state.eventSchema);
            const temptMap = autoMapDataWithLookup(state.eventSchema, data);
            draft.mapInfo.eventSchema = temptMap.map;
          } else if (type === 'itemAttribute') {
            draft.mapInfo.itemAttribute = data.map;
          }
          return;
        }

        case `${PREFIX}@@SEGMENT_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          if (action.payload.length === 0) {
            draft.errors['name'] = [labelCantBlank];
          } else if (
            action.payload.length > 0 &&
            isValidName(action.payload) === false
          ) {
            draft.errors['name'] = [labelInvalidName];
          } else if (action.payload.trim().length > MAX_LENGTH) {
            draft.errors['name'] = [labelMaxLength];
          } else {
            draft.errors['name'] = [];
          }
          draft.name = action.payload;
          return;
        }

        case `${PREFIX}@@SEGMENT_DESCRIPTION@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.description = action.payload;
          return;
        }

        case `${PREFIX}@@SEGMENT_COMPUTE_SCHEDULE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // if update dynamic => set EmptySegment or UnsubscribeSegment to Condition
          if (
            action.payload.type === 'dynamic' &&
            [EMPTY_SEGMENT, UNSUBSCRIBE_SEGMENT].includes(state.type)
          ) {
            draft.type = 1;
          }
          draft.computeSchedule = {
            ...state.computeSchedule,
            ...action.payload,
          };
          return;
        }
        case `${PREFIX}@@SEGMENT_CHANGE_COMPUTE_TYPE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // draft.computeSchedule = action.payload;
          draft.computeSchedule.type = state.tmpComputeScheduleType;
          if (state.tmpComputeScheduleType === 'static') {
            draft.type = 0;
          } else if (state.tmpComputeScheduleType === 'dynamic') {
            draft.type = 1;
          }
          return;
        }
        case `${PREFIX}@@TOGGLE_MODAL_RESET@@${ReduxTypes.UPDATE_VALUE}`: {
          const { open, type } = action.payload;
          draft.isOpenModalReset = open;
          if (type) {
            draft.tmpComputeScheduleType = type;
          }
          return;
        }
        case `${PREFIX}@@TOGGLE_MODEL_WARNING_LIMITATIONS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isOpen, type } = action.payload;

          if (type === ACTION_LIMIT_TYPES.TOTAL_LIMIT) {
            draft.isOpenModalWarningTotalLimit = isOpen;
          } else if (type === ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT) {
            draft.isOpenModalComputationLimit = isOpen;
          }

          return;
        }

        case `${PREFIX}@@UPDATE_SHARE_ACCESS_INFO@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { accessInfo = {} } = action.payload;

          if (!isEmpty(accessInfo)) {
            draft.accessInfo = accessInfo;
          }
          return;
        }

        case `${PREFIX}@@UPDATE_VIEW_ACCESS@@${ReduxTypes.UPDATE_VALUE}`: {
          const { viewAccess = {} } = action.payload;

          draft.viewData = viewAccess;
          draft.viewErrors = isEmpty(viewAccess)
            ? [
                getTranslateMessage(
                  TRANSLATE_KEY._NOTI_SEGMENT_NO_PERMISSION,
                  'You do not have permission for this object',
                ),
              ]
            : [];

          if (viewAccess.type !== 'BO' && draft.type === 6) {
            draft.type = 1;
          }
          return;
        }

        case `${PREFIX}@@MESSAGE_WARNING_LIMITATIONS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { message = '', type = '' } = action.payload;

          if (type === ACTION_LIMIT_TYPES.TOTAL_LIMIT) {
            draft.messageWarningLimit = message;
          } else if (type === ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT) {
            draft.messageWarningComputationLimit = message;
          }

          return;
        }

        case `${PREFIX}@@PAGING_RELATIONSHIP@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.pagingRelationship = {
            ...state.pagingRelationship,
            ...action.payload,
          };
          return;
        }

        case `${PREFIX}@@SEARCH_RELATIONSHIP@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.searchRelationship = action.payload;
          return;
        }

        case `${PREFIX}@@FILTER_TYPE_RELATIONSHIP@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.filterTypeRelationship = action.payload;
          return;
        }

        case `${PREFIX}@@UPDATE_STATUS_VALID_TOTAL_LIMIT@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isValid } = action.payload;

          draft.isValidTotalLimit = isValid;

          return;
        }

        case `${PREFIX}@@UPDATE_STATUS_VALID_TOTAL_LIMIT_SHARE_ACCESS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.isValidTotalLimitTabShareAccess = action.payload;

          return;
        }

        case `${PREFIX}@@SEGMENT_MEMBER@@${ReduxTypes.UPDATE_VALUE}`: {
          // console.log('action.payload', action.payload);
          draft.segmentMember = action.payload;
          return;
        }

        case `${PREFIX}@@ERRORS${ReduxTypes.UPDATE_VALUE}`: {
          const { name, message } = action.payload;
          draft.errors[name] = [message];
          return;
        }
        case `${MODULE_CONFIG.key}@@TOGGLE_MODAL${ReduxTypes.UPDATE_VALUE}`: {
          const { isOpen, nextPage } = action.payload;
          draft.isOpenModal = isOpen;
          draft.nextPage = nextPage;
          return;
        }
        case `${MODULE_CONFIG.key}@@INPUT_METHOD${ReduxTypes.UPDATE_VALUE}`: {
          const type = action.payload;
          draft.type = type;

          // Không cần nữa vì chỉ được chọn type khi là computeSchedule là static
          // if (type === 2) {
          //   // disable GeneralSettings
          //   const computeSchedule = toUIComputeSchedule({ type: 'static' });
          //   draft.computeSchedule = computeSchedule;
          //   draft.initDataComputeSchedule = computeSchedule;
          // }

          return;
        }
        // Dùng cho button Or bulk select (hiện không dùng nữa)
        case `${MODULE_CONFIG.key}@@MATCHING_FILE${ReduxTypes.UPDATE_VALUE}`: {
          const { type } = action.payload;
          draft.type = type;

          if (type === 2) {
            // disable GeneralSettings
            const computeSchedule = toUIComputeSchedule({ type: 'static' });
            draft.computeSchedule = computeSchedule;
            draft.initDataComputeSchedule = computeSchedule;
          }
          return;
        }
        case `${PREFIX}@@NOTIFICATION_SETUP@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.notificationSetup = action.payload;
          return;
        }
        default:
          return state;
      }
    });
  return configureReducer;
};

const configureReducer = combineReducers({
  main: configureReducerFor(MODULE_CONFIG),
  //   condition: conditionReducerFor(MODULE_CONFIG_CONFIGURE),
  condition: conditionReducerFor(MODULE_CONFIG),
  querySql: querySqlReducerFor(MODULE_CONFIG),
  matchingFile: matchingFileReducerFor(MODULE_CONFIG),
});

export default configureReducer;

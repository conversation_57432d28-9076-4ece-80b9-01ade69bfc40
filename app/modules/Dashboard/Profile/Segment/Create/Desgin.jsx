/* eslint-disable no-restricted-globals */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useHistory, useLocation, withRouter } from 'react-router-dom';
import { getPortalId } from 'utils/web/cookie';
import moment from 'moment';
import {
  FORMAT_DATE_CALENDAR,
  buildDisableHours,
  getTimeRangeCurrentDate,
  getDateFromComputeSchedule,
} from 'components/Organisms/CalendarTable/utils';
// import { Prompt } from 'react-router';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import UIModalCommon from 'components/common/UIModalCommon';
import {
  defaultComputationSchedule,
  FORMAT_DATE_HOUR as FORMAT_FULL_DATE_SCHEDULE,
  FORMAT_DATE as FORMAT_DATE_SCHEDULE,
  FORMAT_HOUR as FORMAT_HOUR_SCHEDULE,
} from 'components/Organisms/ComputationSchedule/constant';

// Components
import ModalConfirmReset from 'containers/Segment/Content/ModalConfirmReset';
import injectSaga from 'utils/injectSaga';
import injectReducer from 'utils/injectReducer';
import Preview from 'containers/Segment/Preview';
import { SEGMENT_TYPES } from 'containers/Segment/Content/Condition/constants';
import ModalCalendarTable from 'containers/modals/ModalCalendarTable';
import SegmentServices from 'services/Segment';
import { TabPanel, UIButton, UIWrapperDisable } from '@xlab-team/ui-components';
import { isEqual, get } from 'lodash';
import ShareAccessView from './_UI/ShareAccessView';
import reducer from './reducer';
import saga, { handleValidateCondition } from './saga';
// import { makeSelectListSegmentDomainMain } from './selectors';
import { MODULE_CONFIG, getBreadcrumbs } from './config';
import { init, reset, update, updateValue } from '../../../../../redux/actions';
import Content from './Content';
// import Preview from './Preview';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
// import CustomerDetail from '../../Customer/Detail/Preview';
import {
  makeSelectConfigCreateSegment,
  makeSelectCreateSegmentCondition,
  makeSelectMainCreateSegment,
} from './selectors';
import ModalConfirmExit from '../../../../../containers/modals/ModalConfirmExit';
import ModalProgressBuildForecastSession from '../../../../../containers/modals/ModalProgressBuildForecastSession';
import ModalConfirm from '../../../../../containers/modals/ModalConfirm';
import DrawerArchive from '../../../../../components/common/DrawerArchive';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import {
  columnsRelationship,
  getWarningLimitMessages,
  isJsonString,
} from './utils';
import TRANSLATE_KEY from '../../../../../messages/constant';
import APP from '../../../../../appConfig';
// Constants
import { TAB_SEGMENT } from './constants';
import { ARCHIVE_STATUS } from '../../../../../utils/constants';
import { SEGMENT } from '../../../../../components/common/contants';

// Styled
import { TabsStyled, WrapButtonFooter, WrapperMessageWarning } from './styles';

// Utils
import { ACTION_LIMIT_TYPES } from './_reducer/utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { getObjectPropSafely, safeParse } from '../../../../../utils/common';
import useUpdateEffect from '../../../../../hooks/useUpdateEffect';

const PATH = 'app/modules/Dashboard/Profile/Segment/Create/Desgin.jsx';

const MAP_TITLE = {
  labelSeeSample: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SEE_SAMPLE_USER,
    'See a sample of users in this audience',
  ),
  labelGoToSegmentList: getTranslateMessage(
    TRANSLATE_KEY._ACT_WARN_SEG_GO_TO_LIST,
    'Go To Segment List',
  ),
  labelTotal: x =>
    getTranslateMessage(
      TRANSLATE_KEY._INFO_PREVIEW_RESULT,
      `Found ${x} matching result(s)`,
      { x },
    ),
  resetScheduleContent: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Your current input settings will also be reset if you update method option',
  ),
  warningTotalLimit: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_SEG_WARN_TOTAL,
    'Warning Total Segment Limit',
  ),
  warningComputationLimit: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_SEG_COMPUTATIONAL_LIMIT,
    'Warning Segment Computational Limit',
  ),
  labelTabSettings: getTranslateMessage(TRANSLATE_KEY._, 'Segment Settings'),
  labelTabShareAccess: getTranslateMessage(TRANSLATE_KEY._, 'Share Access'),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
  labelCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
};

export function CreateSegment(props) {
  const {
    activeRow,
    isShowHeader = true,
    isShowFooter = true,
    files,
    isNumSave,
    setOpenDrawer,
    setOpenPopover,
    nameSegment,
    isSegmentJourney = false,
    isHideScheduleTime = false,
    isSegment = false,
    isSegmentOption1 = false,
    isJourney = false,
    boUploadData,
    isDisableUpload = false,
    importType,
    isVersion = false,
    hasEditPermission = false,
    isDisplayExplore = true,
    onBeforeCancel = () => {},
  } = props;
  // const [selectedRows, setSelectedRows] = useState([]);
  const [width, setWidth] = React.useState(props.width * 0.3);
  const [activeItem, setActiveItem] = useState({});
  const [isOpenDetail, setIsOpenDetail] = useState(false);
  const { design, configure, main, match } = props;
  const { forecastSessionDetail = {} } = main;
  const { params = {} } = match;
  const { mappingAttributes } = configure.matchingFile;
  const {
    computeSchedule,
    modalCalendarTable = {},
    accessInfo = {},
    viewErrors = [],
  } = configure.main;
  const [itemTypeId, setItemTypeId] = useState(null);
  const [isCheckOnChange, setIsCheckOnChange] = useState(false);
  const [isLoadingRelationship, setIsLoadingRelationship] = useState(false);
  const [isOpenDrawerArchive, setIsOpenDrawerArchive] = useState(false);
  const [isCheckChangeSegmentName, setIsCheckChangeSegmentName] = useState(0);
  const [isOpenResetSchedule, setIsOpenResetSchedule] = useState(false);
  const [isOpenSegmentJourney, setIsOpenSegmentJourney] = useState(false);
  const [dataRelationship, setDataRelationship] = useState([]);
  const [limitRelationship, setLimitRelationship] = useState(null);
  const [activeTab, setActiveTab] = useState(TAB_SEGMENT.SETTINGS);
  const debounceIsNumSave = useRef(isNumSave);
  const location = useLocation();
  const history = useHistory();

  const isViewError = Array.isArray(viewErrors) && viewErrors.length > 0;
  const isDisabledShareAccess = getObjectPropSafely(
    () =>
      isVersion ||
      main.isDoing ||
      (design !== 'create' && !hasEditPermission) ||
      (activeRow && activeRow.status === ARCHIVE_STATUS),
  );

  const disabledForecastSession = useMemo(() => {
    if (
      !forecastSessionDetail ||
      !forecastSessionDetail.sessionName ||
      !forecastSessionDetail.id
    )
      return true;

    return get(forecastSessionDetail, 'sessionName', '').trim() === '';
  }, [forecastSessionDetail]);

  useEffect(() => {
    if (setOpenDrawer && setOpenPopover && props.main.isJourneySuccess) {
      setOpenDrawer(false);
      setOpenPopover(false);
      if (props.setCompletedStep && props.setActiveStep) {
        props.setCompletedStep({ isCompleted: false });
        props.setActiveStep(0);
      }
    }
  }, [props.main.isJourneySuccess]);

  const isCheckCustomerOrUserIdAttribute = useMemo(
    () =>
      mappingAttributes &&
      mappingAttributes.some(item =>
        ['user_id', 'customer_id'].includes(item.currentValue.itemPropertyName),
      ),
    [mappingAttributes],
  );
  useEffect(() => {
    if (isNumSave !== debounceIsNumSave.current) {
      debounceIsNumSave.current = isNumSave;
      if (
        (mappingAttributes.length !== 0 &&
          mappingAttributes.findIndex(
            item => item.headerValue.headerIndex === -1,
          ) === -1) ||
        configure.main.type !== 2
      ) {
        if (
          nameSegment !== SEGMENT.CUSTOMERS_AND_SEGMENT &&
          nameSegment !== SEGMENT.VISITORS_AND_SEGMENT
        ) {
          if (
            computeSchedule.type === 'dynamic' &&
            computeSchedule.repeatType !== 'none'
          ) {
            setIsOpenSegmentJourney(true);
          } else {
            callback('SAVE_SEGMENT', {
              design,
              itemTypeId: props.itemTypeId,
              isBuild: true,
            });
          }
        } else if (computeSchedule.type === 'static' && design === 'create') {
          callback('SAVE_SEGMENT', {
            design,
            itemTypeId: props.itemTypeId,
            computeSchedule: { ...computeSchedule },
            importId: boUploadData && boUploadData.importId,
            mappingAttributes: boUploadData && boUploadData.mappingAttributes,
            isBuild: true,
            isTypeApiV21: true,
            importType,
          });
        } else if (
          mappingAttributes.length === 1 &&
          isCheckCustomerOrUserIdAttribute &&
          isSegmentJourney
        ) {
          if (configure.main.type === 2) {
            callback('SAVE_SEGMENT', {
              design,
              itemTypeId: props.itemTypeId,
              computeSchedule: { ...computeSchedule, repeatType: 'none' },
              importId: boUploadData && boUploadData.importId,
              mappingAttributes: boUploadData && boUploadData.mappingAttributes,
              isBuild: true,
              isTypeApiV21: true,
              importType,
            });
          } else {
            // eslint-disable-next-line no-unused-expressions
            props.onSaveFirstStep && props.onSaveFirstStep();
            callback('SAVE_SEGMENT', {
              design,
              itemTypeId: props.itemTypeId,
              computeSchedule: { ...computeSchedule, repeatType: 'none' },
              importId: boUploadData && boUploadData.importId,
              // mappingAttributes : boUploadData && boUploadData.mappingAttributes,
              isBuild: true,
              // importType: importType
            });
          }
        } else if (configure.main.type === 2) {
          callback('SAVE_SEGMENT', {
            design,
            itemTypeId: props.itemTypeId,
            computeSchedule: {
              ...computeSchedule,
              repeatType: 'hours',
              repeatValue: 3,
              endTime: {
                ...computeSchedule.endTime,
                type: 'after_num_running',
                numRunning: 1,
              },
            },
            importId: boUploadData && boUploadData.importId,
            mappingAttributes: boUploadData && boUploadData.mappingAttributes,
            isBuild: false,
            isTypeApiV21: true,
            importType,
          });
        } else {
          // eslint-disable-next-line no-unused-expressions
          props.onSaveFirstStep && props.onSaveFirstStep();
          callback('SAVE_SEGMENT', {
            design,
            itemTypeId: props.itemTypeId,
            computeSchedule: {
              ...computeSchedule,
              repeatType: 'hours',
              repeatValue: 3,
              endTime: {
                ...computeSchedule.endTime,
                type: 'after_num_running',
                numRunning: 1,
              },
            },
            importId: boUploadData && boUploadData.importId,
            // mappingAttributes : boUploadData && boUploadData.mappingAttributes,
            isBuild: false,
            // importType: importType
          });
        }
      } else {
        const data = { design, itemTypeId: props.itemTypeId };
        let newData = { ...data };
        if (props.isUploadStep) {
          newData = { ...data, isUploadStep: props.isUploadStep };
        }
        props.onSave(newData);
      }
    }
  }, [isNumSave]);

  useUpdateEffect(() => {
    handleFetchListingRelationship();
  }, [configure.main.pagingRelationship, configure.main.searchRelationship]);

  useEffect(() => {
    // document.body.classList.add('xlab-desgin-full-layout');
    const searchParams = new URLSearchParams(location.search);

    const copyId = searchParams.get('copyId');
    const name = searchParams.get('name');
    const segmentKey = searchParams.get('key');

    let itemTypeId = 0;
    let dataStorage;

    if (segmentKey) {
      dataStorage = localStorage.getItem(segmentKey);
      if (dataStorage && isJsonString(dataStorage)) {
        dataStorage = JSON.parse(dataStorage);
      }
    }
    if (design === 'create') {
      const splitUrl = window.location.pathname.split('/');
      const value = splitUrl[splitUrl.length - 1];
      itemTypeId = params.itemTypeId || value;

      if (isNaN(Number(itemTypeId))) {
        itemTypeId = props.itemTypeId;
      }

      if (copyId) {
        props.init({
          itemTypeId,
          param: '',
          design,
          copyId,
          name,
        });
      } else {
        props.init({
          itemTypeId: props.itemTypeIdStepMode
            ? props.itemTypeIdStepMode
            : itemTypeId,
          param: '',
          design,
          dataStorage,
        });
      }
    } else if (design === 'update') {
      itemTypeId = activeRow.item_type_id;
      props.init({
        itemTypeId: activeRow.item_type_id,
        activeRow,
        design,
      });
    }
    setItemTypeId(itemTypeId);
    if (segmentKey) {
      localStorage.removeItem(segmentKey);
    }
    return () => {
      // document.body.classList.remove('xlab-desgin-full-layout');
      props.reset();
    };
  }, []);

  // Effect Init AutoComplete & FunctionData SQL
  useEffect(() => {
    if (typeof props.initAutoCompleteData === 'function') {
      props.initAutoCompleteData();
    }
  }, []);

  // console.log('isCheckOnChange', isCheckOnChange);

  const handleValidateTotalLimit = async data => {
    try {
      const { type = '' } = data;

      const params = {
        data: {
          type,
        },
      };

      const res = await SegmentServices.data.checkCreateLimitation(params);
      // const res = {
      //   code: 500,
      //   message: 'reached the limit of segments',
      //   data: {
      //     type: 'static',
      //   },
      //   codeMessage: '_NOTIFICATION_REACHED_LIMIT',
      //   duration: '0.746 ms',
      // };

      if (res.code === 200) {
        const { canCreate } = safeParse(res.data);

        if (props.activeTab === TAB_SEGMENT.SHARE_ACCESS) {
          props.onValidTotalLimitTabShareAccess(canCreate);
        } else {
          props.onValidateTotalLimit({
            isValid: canCreate,
          });
        }
      } else if (res.codeMessage === '_NOTIFICATION_REACHED_LIMIT') {
        const limitTotalMessage = getWarningLimitMessages(
          safeParse(res.data.type, ''),
        );

        if (limitTotalMessage) {
          props.onUpdateFilterTypeRelationship(safeParse(res.data.type, ''));
          props.onUpdateMessageWarningLimitations({
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
            message: limitTotalMessage,
          });
          props.toggleModalWarningLimitations({
            isOpen: true,
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
          });
        }

        if (props.activeTab === TAB_SEGMENT.SHARE_ACCESS) {
          props.onValidTotalLimitTabShareAccess(false);
        } else {
          props.onValidateTotalLimit({
            isValid: false,
          });
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleValidateTotalLimit',
        data: error.stack,
      });
    }
  };

  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'SAVE_SEGMENT': {
        let newData = { ...data };
        if (props.isUploadStep) {
          newData = { ...data, isUploadStep: props.isUploadStep };
        }
        props.onSave(newData);
        // Validate handle error mappingAttribute in MappingFile
        // and check props function before save step 1

        // if (configure.main.type === 2) {
        //   if (
        //     props.onSaveFirstStep &&
        //     typeof props.onSaveFirstStep === 'function' &&
        //     mappingAttributes.length !== 0 &&
        //     mappingAttributes.findIndex(
        //       item => item.headerValue.headerIndex === -1,
        //     ) === -1
        //   ) {
        //     props.onSaveFirstStep();
        //   }
        // } else if (props.onSaveFirstStep) {
        //   props.onSaveFirstStep();
        // }
        break;
      }
      case 'CHANGE_SEGMENT_NAME': {
        props.onChangeName(data);
        props.onValidate();
        setIsCheckOnChange(true);

        if (typeof props.setIsCheckOnChangeOuter === 'function') {
          props.setIsCheckOnChangeOuter(true);
        }
        break;
      }
      case 'CHANGE_SEGMENT_DESCRIPTION': {
        props.onChangeDescription(data);
        props.onValidate();
        setIsCheckOnChange(true);
        if (typeof props.setIsCheckOnChangeOuter === 'function') {
          props.setIsCheckOnChangeOuter(true);
        }
        break;
      }
      case 'CHANGE_SEGMENT_COMPUTE_SCHEDULE': {
        const { repeatStartTime = {} } = data;
        const { day, hour } = repeatStartTime;

        props.onChangeComputeSchedule(data);
        props.onValidate();
        setIsCheckOnChange(true);
        if (typeof props.setIsCheckOnChangeOuter === 'function') {
          props.setIsCheckOnChangeOuter(true);
        }

        // delete errors
        props.onChangeErrors({ name: 'limit_schedule', message: '' });

        // set time calendar
        const selectedTime = moment(
          `${day} ${hour}`,
          FORMAT_FULL_DATE_SCHEDULE,
        ).format(FORMAT_DATE_CALENDAR);
        props.onChangeModalCalendarTable({
          selectedTime,
        });
        break;
      }
      case 'RESET_SUGGESTION_TIME': {
        if (typeof props.onResetSuggestionTime === 'function') {
          props.onResetSuggestionTime();
        }

        break;
      }
      case 'CHANGE_SEGMENT_MEMBER': {
        let count = isCheckChangeSegmentName;
        count += 1;
        // console.log('count', count);
        if (count < 2) {
          setIsCheckOnChange(false);
        } else {
          setIsCheckOnChange(true);
        }
        if (typeof props.setIsCheckOnChangeOuter === 'function') {
          props.setIsCheckOnChangeOuter(!(count < 2));
        }

        setIsCheckChangeSegmentName(count);
        props.onChangeSegmentMember(data);
        props.onValidate();
        break;
      }
      case 'UPDATE_CONDITIONS': {
        props.onValidate();
        setIsCheckOnChange(true);
        if (typeof props.setIsCheckOnChangeOuter === 'function') {
          props.setIsCheckOnChangeOuter(true);
        }
        break;
      }
      case 'ON_OPEN_DETAIL': {
        setActiveItem(data.row);
        setIsOpenDetail(true);
        break;
      }
      case 'ON_CLOSE': {
        setActiveItem({});
        setIsOpenDetail(false);
        break;
      }
      case 'ON_CANCEL': {
        let needGoToList = true;

        onBeforeCancel({
          preventGoToList: () => {
            needGoToList = false;
          },
        });

        if (!isCheckOnChange || props.main.disabled) {
          if (props.onCancel) props.onCancel();

          if (needGoToList) props.goToList();
          break;
        }

        const nextPage = needGoToList
          ? `${APP.PREFIX}/${getPortalId()}/profile/segments`
          : null;

        props.toggleModal({ isOpen: data, nextPage });

        break;
      }
      case 'ADD_MATCHING_FILE': {
        props.addMatchingFile(data);
        props.onValidate();
        break;
      }
      case 'INPUT_METHOD': {
        props.onChangeMethod(data);
        props.onValidate();
        break;
      }
      case 'SEGMENT_CHANGE_COMPUTE_TYPE': {
        props.onChangeComputeType(data);
        props.onValidate();
        break;
      }
      case 'TOGGLE_MODAL_RESET': {
        props.toggleModalReset(data);
        break;
      }
      case 'OPEN_MODAL_RESET_SCHEDULE': {
        setIsOpenResetSchedule(true);
        break;
      }
      case 'ON_PAGING_TABLE': {
        props.onChangePagingRelationship(data);
        break;
      }
      case 'GET_VALUE_SEARCH': {
        props.onChangeSearchRelationship(data);
        break;
      }
      case 'VALIDATE_TOTAL_LIMIT': {
        handleValidateTotalLimit(data);
        break;
      }
      case 'TOGGLE_MODAL_CALENDAR_TABLE': {
        toggleCalendarTable();
        break;
      }
      case 'ON_CLICK_CELL': {
        props.onChangeModalCalendarTable({
          selectedTime: data,
        });
        break;
      }
      case 'CURRENT_DATE': {
        const { currentDate } = data;

        props.onChangeModalCalendarTable({
          currentDate,
        });
        const timeRange = getTimeRangeCurrentDate(currentDate);
        const currentTimeRange = getTimeRangeCurrentDate(
          modalCalendarTable.currentDate,
        );

        if (!isEqual(currentTimeRange, timeRange)) {
          props.onGetPlans({
            timeRange,
          });
        }
        break;
      }
      case 'UPDATE_SHARE_ACCESS': {
        props.onUpdateShareAccessInfo(data);
        break;
      }
      default:
        break;
    }
  };

  const onConfirm = () => {
    props.toggleModal({ isOpen: false, nextPage: '' });

    if (configure.main.nextPage) {
      history.push(configure.main.nextPage);
    }

    if (props.onCancel) {
      props.onCancel();
    }
  };

  const onToggleModal = () => {
    props.toggleModal({ isOpen: false, nextPage: '' });
  };

  const onConfirmReset = () => {
    props.toggleModalReset({ open: false });
    props.confirmReset();
  };

  const onToggleModalReset = () => {
    props.toggleModalReset({ open: false });
  };

  const toggleResetSchedule = () => {
    setIsOpenResetSchedule(!isOpenResetSchedule);
  };

  const toggleCalendarTable = () => {
    props.onChangeModalCalendarTable({
      isOpen: !modalCalendarTable.isOpen,
    });

    // call khi open modal
    if (!modalCalendarTable.isOpen) {
      const { selectedTime, currentDate } = getDateFromComputeSchedule(
        computeSchedule,
      );

      // rebuild disableTimes for current computeSchedule
      const disableTimes = buildDisableHours({
        computeSchedule,
        dataEventsGroupByDate: modalCalendarTable.dataEventsGroupByDate,
        limitHour: modalCalendarTable.limitHour,
        timeRange: modalCalendarTable.timeRange,
      });

      props.onChangeModalCalendarTable({
        disableTimes,
        currentDate,
        selectedTime,
      });

      const timeRange = getTimeRangeCurrentDate(currentDate);
      props.onGetPlans({ timeRange });
    }
  };

  const toggleResetSegmentJourney = () => {
    setIsOpenSegmentJourney(!isOpenSegmentJourney);
  };

  const onConfirmSegmentJourney = () => {
    toggleResetSegmentJourney();
    if (isJourney) {
      callback('SAVE_SEGMENT', {
        design,
        itemTypeId: props.itemTypeId,
        isBuild: true,
      });
    } else {
      callback('SAVE_SEGMENT', { design: 'create', isBuild: true });
    }
  };

  const onCancelSegmentJourney = () => {
    toggleResetSegmentJourney();
    if (isJourney) {
      callback('SAVE_SEGMENT', {
        design,
        itemTypeId: props.itemTypeId,
        isBuild: false,
      });
    } else {
      callback('SAVE_SEGMENT', { design: 'create', isBuild: false });
    }
  };

  const onConfirmResetSchedule = () => {
    props.onChangeComputeSchedule({
      type: 'static',
      ...defaultComputationSchedule,
    });
    toggleResetSchedule();
  };

  const handleConfirmWarningTotalLimit = type => {
    const isOpen = handleFetchListingRelationship();

    if (isOpen) {
      props.toggleModalWarningLimitations({ isOpen: false, type });
      setIsOpenDrawerArchive(true);
    }
  };

  const handleFetchListingRelationship = async () => {
    try {
      setIsLoadingRelationship(true);

      const page = safeParse(configure.main.pagingRelationship.page, 1);
      const limit = safeParse(configure.main.pagingRelationship.limit, 25);
      const search = safeParse(configure.main.searchRelationship, '');
      const filterType = safeParse(configure.main.filterTypeRelationship, '');

      const columns = [
        'status',
        'segment_id',
        'segment_display',
        'item_type_id',
        'c_user_id',
      ];

      const itemTypeIds = '-1003,-1007';

      const filters = {
        OR: [
          {
            AND: [
              {
                type: 1,
                column: 'status',
                data_type: 'number',
                operator: 'matches',
                value: [1, 2],
              },
              {
                type: 1,
                column: 'update_method',
                data_type: 'string',
                operator: 'matches',
                value: [filterType],
              },
              {
                type: 1,
                column: 'segment_display',
                data_type: 'string',
                operator: 'contains',
                value: search,
              },
            ],
          },
        ],
      };

      const params = {
        itemTypeIds,
        data: {
          page,
          limit,
          // search,
          sort: 'last_processed_date',
          sd: 'desc',
          columns,
          filters,
        },
      };

      const res = await SegmentServices.data.getListRelationship(params);
      // console.log({ res });
      if (res.code === 200) {
        const temp = safeParse(res.data, []);
        const limitTemp = safeParse(res.meta.limitNumberObject, {});

        const mapToUI = temp.map(item => ({
          id: safeParse(item.segment_id, ''),
          ...item,
        }));

        setDataRelationship(mapToUI);
        setLimitRelationship(limitTemp);

        setIsLoadingRelationship(false);
        return true;
      }

      setIsLoadingRelationship(false);
      return false;
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleFetchListingRelationship',
        data: error.stack,
      });
      console.log(error);
    }
  };

  const handleChangeTab = dataTab => {
    setActiveTab(dataTab);
  };

  const handleCloseComputationLimit = () => {
    props.toggleModalWarningLimitations({
      isOpen: false,
      type: ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT,
    });

    if (!props.isUploadStep) {
      props.goToList();
    }
  };

  const handleToggleDrawer = () => {
    setIsOpenDrawerArchive(false);

    // Reset Paging & search
    props.onChangePagingRelationship({
      page: 1,
      limit: 25,
    });
    props.onChangeSearchRelationship('');
  };

  const onConfirmCalendarTable = () => {
    const selectedTime = moment(
      modalCalendarTable.selectedTime,
      FORMAT_DATE_CALENDAR,
    );

    const startDay = moment(selectedTime).format(FORMAT_DATE_SCHEDULE);
    const startHour = moment(selectedTime).format(FORMAT_HOUR_SCHEDULE);

    props.onChangeComputeSchedule({
      ...computeSchedule,
      repeatStartTime: { day: startDay, hour: startHour },
    });

    // delete errors
    props.onChangeErrors({ name: 'limit_schedule', message: '' });

    toggleCalendarTable();
  };

  // const data = tableDataMultiColumns;
  // console.log('configure ===>', configure);
  const isLoading = main.isLoading;
  // const isLoading = true;
  // const shouldBlockNavigation = true;
  const breadcrumbs = useMemo(() => {
    const onClickBreadCrumbs = nextPage => {
      if (!isCheckOnChange || props.main.disabled) {
        props.goToList();
      } else {
        props.toggleModal({
          isOpen: true,
          nextPage,
        });
      }
    };

    return getBreadcrumbs(onClickBreadCrumbs, props.titleSegment);
  }, [isCheckOnChange, props.main.disabled]);

  const renderContentSettings = () => {
    if (!props.isUploadStep && design === 'create') return renderContentTab();

    if (props.isShareAccessView)
      return (
        <ShareAccessView
          moduleConfig={MODULE_CONFIG}
          isViewError={isViewError}
          accessInfo={accessInfo}
          disabled={isDisabledShareAccess}
          hasEditPermission={hasEditPermission}
          isShareAccessView={props.isShareAccessView}
          isValidTotalLimitTabShareAccess={
            configure.main.isValidTotalLimitTabShareAccess
          }
          design={design}
          isSegment={isSegment}
          isSegmentOption1={isSegmentOption1}
          setIsOpenSegmentJourney={setIsOpenSegmentJourney}
          boUploadData={boUploadData}
          importType={importType}
          onSaveFirstStep={props.onSaveFirstStep}
          callback={callback}
        />
      );

    return renderBaseView();
  };

  const renderContentTab = () => (
    <TabsStyled
      activeTab={activeTab}
      isBoxShadow={false}
      className="tab-segment"
      height="50px"
      onChange={handleChangeTab}
      classNameTabNav={activeTab}
    >
      <TabPanel
        label={MAP_TITLE.labelTabSettings}
        eventKey={TAB_SEGMENT.SETTINGS}
        className={`${
          activeTab === TAB_SEGMENT.SETTINGS ? 'active-tab' : ''
        } tab-panel-segment`}
        styleCss={{ height: '100%' }}
      >
        <div
          style={{
            display: 'flex',
            position: 'relative',
            overflow: 'hidden',
            height: '100%',
          }}
        >
          {renderBaseView()}
        </div>
      </TabPanel>
      <TabPanel
        label={MAP_TITLE.labelTabShareAccess}
        eventKey={TAB_SEGMENT.SHARE_ACCESS}
        className={`${
          activeTab === TAB_SEGMENT.SHARE_ACCESS ? 'active-tab' : ''
        } tab-panel-segment`}
        styleCss={{ height: '100%' }}
      >
        <ShareAccessView
          moduleConfig={MODULE_CONFIG}
          isViewError={isViewError}
          accessInfo={accessInfo}
          disabled={isDisabledShareAccess}
          hasEditPermission={hasEditPermission}
          isValidTotalLimit={configure.main.isValidTotalLimit}
          design={design}
          isSegment={isSegment}
          isSegmentOption1={isSegmentOption1}
          setIsOpenSegmentJourney={setIsOpenSegmentJourney}
          boUploadData={boUploadData}
          importType={importType}
          onSaveFirstStep={props.onSaveFirstStep}
          callback={callback}
          disabledFooter={activeTab !== TAB_SEGMENT.SHARE_ACCESS}
        />
      </TabPanel>
    </TabsStyled>
  );

  const renderBaseView = () => (
    <>
      <div
        style={{ width: '100%', height: '100%', backgroundColor: '#ffffff' }}
      >
        <Content
          files={files}
          isLoading={isLoading}
          callback={callback}
          design={design}
          isUploadStep={props.isUploadStep}
          hasEditPermission={hasEditPermission}
          activeItem={activeItem}
          isOpenDetail={isOpenDetail}
          isValidTotalLimit={configure.main.isValidTotalLimit}
          conditionOptions={SEGMENT_TYPES}
          moduleConfig={MODULE_CONFIG}
          isShowFooter={isShowFooter}
          activeRow={activeRow}
          isShowHeader={isShowHeader}
          itemTypeId={props.itemTypeId}
          setOpenDrawer={props.setOpenDrawer}
          setOpenPopover={props.setOpenPopover}
          isSegmentJourney={isSegmentJourney}
          isHideScheduleTime={isHideScheduleTime}
          isSegment={isSegment}
          isSegmentOption1={isSegmentOption1}
          setIsOpenSegmentJourney={setIsOpenSegmentJourney}
          boUploadData={boUploadData}
          isDisableUpload={isDisableUpload}
          importType={importType}
          onSaveFirstStep={props.onSaveFirstStep}
          isVersion={isVersion}
          isDisplayExplore={isDisplayExplore}
          disabledFooter={activeTab !== TAB_SEGMENT.SETTINGS}
        />
      </div>
      {(configure.main.type === 1 || configure.main.type === 6) &&
        !isVersion &&
        props.isShowPreview && (
          <UIWrapperDisable
            disabled={design !== 'create' && !hasEditPermission}
          >
            <Preview
              isSegmentPreview
              usePie
              useForecastSession
              isLoading={isLoading}
              width={width}
              setWidth={setWidth}
              design={design}
              callback={callback}
              main={main}
              isHideMargin={!props.isUploadStep}
              moduleConfigCreate={MODULE_CONFIG}
              makeSelectConfigCreateSegment={makeSelectConfigCreateSegment}
              makeSelectMainCreateSegment={makeSelectMainCreateSegment}
              feServicePreview={SegmentServices.preview}
              handleValidateCondition={handleValidateCondition}
              labels={MAP_TITLE}
            />
          </UIWrapperDisable>
        )}
    </>
  );

  return (
    <ErrorBoundary path={PATH}>
      {/* <Prompt
        when={shouldBlockNavigation}
        message="You have unsaved changes, are you sure you want to leave?"
      /> */}
      {design === 'create' && isShowHeader && (
        <CustomHeader showCancel breadcrums={breadcrumbs} callback={callback} />
      )}
      {/* <Box className="row width-100 m-x-0">
        <CustomerDetail activeItemId={'149'} />
      </Box> */}
      {renderContentSettings()}
      <ModalConfirmExit
        isOpen={configure.main.isOpenModal}
        toggle={onToggleModal}
        design={design}
        title={
          design === 'create'
            ? getTranslateMessage(
                TRANSLATE_KEY._TITL_CANCEL_CREATE_SEGMENT,
                'Cancel creating of this segment',
              )
            : getTranslateMessage(
                TRANSLATE_KEY._TITL_CANCEL_EDIT_SEGMENT,
                'Change will be lost',
              )
        }
        onConfirm={onConfirm}
      >
        {design === 'create'
          ? getTranslateMessage(
              TRANSLATE_KEY._WARN_CANCEL_CREATE_SEGMENT,
              'Once you confirm cancel, this segment will NOT be saved',
            )
          : getTranslateMessage(
              TRANSLATE_KEY._WARN_CANCEL_EDIT_SEGMENT,
              "Change you created won't be saved if you leave this site",
            )}
      </ModalConfirmExit>
      <ModalConfirmReset
        isOpen={configure.main.isOpenModalReset}
        toggle={onToggleModalReset}
        onConfirm={onConfirmReset}
      />
      <ModalConfirm
        isOpen={configure.main.isOpenModalWarningTotalLimit}
        isLoading={isLoadingRelationship}
        // isFooterV2
        title={MAP_TITLE.warningTotalLimit}
        labelConfirmBtn={MAP_TITLE.labelGoToSegmentList}
        toggle={() => {
          props.toggleModalWarningLimitations({
            isOpen: false,
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
          });
        }}
        onConfirm={() => {
          handleConfirmWarningTotalLimit(ACTION_LIMIT_TYPES.TOTAL_LIMIT);
        }}
      >
        <WrapperMessageWarning>
          {configure.main.messageWarningLimit}
        </WrapperMessageWarning>
      </ModalConfirm>
      <ModalConfirm
        isOpen={configure.main.isOpenModalComputationLimit}
        isFooterV2
        title={MAP_TITLE.warningComputationLimit}
        toggle={() => {
          handleCloseComputationLimit();
        }}
      >
        <WrapperMessageWarning>
          {configure.main.messageWarningComputationLimit}
        </WrapperMessageWarning>
      </ModalConfirm>
      <UIModalCommon
        isOpen={isOpenResetSchedule}
        toggle={toggleResetSchedule}
        header="Warning"
        onConfirm={onConfirmResetSchedule}
      >
        {MAP_TITLE.resetScheduleContent}
      </UIModalCommon>
      <DrawerArchive
        isOpen={isOpenDrawerArchive}
        isLoading={isLoadingRelationship}
        data={dataRelationship}
        labelNotiLimit={configure.main.messageWarningLimit}
        title={MAP_TITLE.warningTotalLimit}
        typePopup="Segment"
        objectType="BO_SEGMENTS"
        callback={callback}
        column={columnsRelationship}
        limit={limitRelationship}
        labelButtonAction="Archived"
        ServicesRelationShip={SegmentServices.data.archiveBOSegment}
        ServicesAction={SegmentServices.data.updateListStatus}
        togglePopup={() => handleToggleDrawer()}
        filterType={safeParse(configure.main.filterTypeRelationship, '')}
      />
      <ModalCalendarTable
        isOpen={modalCalendarTable.isOpen}
        toggle={toggleCalendarTable}
        onConfirm={onConfirmCalendarTable}
        // isLoading={modalCalendarTable.isLoading}
        selectedTime={modalCalendarTable.selectedTime}
        dataEvents={modalCalendarTable.dataEvents}
        disableTimes={modalCalendarTable.disableTimes}
        callback={callback}
      />
      <ModalCalendarTable
        isOpen={modalCalendarTable.isOpen}
        toggle={toggleCalendarTable}
        onConfirm={onConfirmCalendarTable}
        isLoading={modalCalendarTable.isLoading}
        selectedTime={modalCalendarTable.selectedTime}
        dataEvents={modalCalendarTable.dataEvents}
        disableTimes={modalCalendarTable.disableTimes}
        currentDate={modalCalendarTable.currentDate}
        timeRange={modalCalendarTable.timeRange}
        callback={callback}
      />
      <ModalProgressBuildForecastSession
        open={forecastSessionDetail.isShow}
        name={forecastSessionDetail.sessionName}
        disabled={disabledForecastSession}
        inputProps={{
          disabled: false,
        }}
        onConfirm={() => {
          props.onSeeResultLaterForecast({
            itemTypeId,
            type: +configure.main.type,
          });
        }}
        onChangeName={props.onChangeForecastSessionName}
        progress={forecastSessionDetail.progressInPercent}
      />
      <UIModalCommon
        isOpen={isOpenSegmentJourney}
        toggle={toggleResetSegmentJourney}
        header="Create success"
        onConfirm={onConfirmResetSchedule}
        // isHideLineBreak
        footer={
          <WrapButtonFooter>
            <UIButton
              style={{
                height: '29px',
                color: '#005fb8',
                fontSize: '12px',
                borderRadius: '3px',
                borderColor: '#edeef7',
              }}
              theme="outline"
              onClick={onCancelSegmentJourney}
            >
              No
            </UIButton>
            <UIButton
              style={{
                height: '29px',
                color: '#fff',
                fontSize: '12px',
                borderRadius: '3px',
              }}
              theme="primary"
              onClick={onConfirmSegmentJourney}
            >
              Yes
            </UIButton>
          </WrapButtonFooter>
        }
      >
        Do you want to compute this segment immediately?
      </UIModalCommon>
    </ErrorBoundary>
  );
}

// CreateSegment.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigCreateSegment(),
  main: makeSelectMainCreateSegment(),
  rules: makeSelectCreateSegmentCondition(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onSave: data => {
      dispatch(
        update(MODULE_CONFIG.key, {
          ...data,
          afterSave: props.afterSave,
        }),
      );
    },
    onChangeName: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@SEGMENT_NAME@@`, value)),
    onChangeDescription: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SEGMENT_DESCRIPTION@@`, value),
      ),
    onChangeComputeSchedule: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SEGMENT_COMPUTE_SCHEDULE@@`, value),
      ),
    onChangeModalCalendarTable: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@MODAL_CALENDAR_TABLE@@`, value),
      ),
    onGetPlans: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GET_PLANS@@`, value)),
    onChangeErrors: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ERRORS`, value)),
    onChangeSegmentMember: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@SEGMENT_MEMBER@@`, value)),
    onValidate: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@VALIDATE`, data)),
    toggleModal: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL`, data)),
    goToList: () => dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_LIST`)),
    addMatchingFile: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@MATCHING_FILE`, data)),
    onChangeMethod: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@INPUT_METHOD`, data)),
    onChangeComputeType: data =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@SEGMENT_CHANGE_COMPUTE_TYPE@@`,
          data,
        ),
      ),
    toggleModalReset: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL_RESET@@`, data)),
    toggleModalWarningLimitations: data =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_MODEL_WARNING_LIMITATIONS@@`,
          data,
        ),
      ),
    onChangePagingRelationship: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@PAGING_RELATIONSHIP@@`, data),
      );
    },
    onChangeSearchRelationship: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SEARCH_RELATIONSHIP@@`, data),
      );
    },
    onValidateTotalLimit: data => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@UPDATE_STATUS_VALID_TOTAL_LIMIT@@`,
          data,
        ),
      );
    },
    onValidTotalLimitTabShareAccess: data => {
      dispatch(
        updateValue(
          `${
            MODULE_CONFIG.key
          }@@UPDATE_STATUS_VALID_TOTAL_LIMIT_SHARE_ACCESS@@`,
          data,
        ),
      );
    },
    onUpdateFilterTypeRelationship: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@FILTER_TYPE_RELATIONSHIP@@`, data),
      );
    },
    onUpdateMessageWarningLimitations: data => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@MESSAGE_WARNING_LIMITATIONS@@`,
          data,
        ),
      );
    },
    onChangeForecastSessionName: name => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_FORECAST_SESSION_NAME`, name),
      );
    },
    onSeeResultLaterForecast: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@PREVIEW_FORECAST_LATER`, data),
      );
    },
    confirmReset: data =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@SEGMENT_CHANGE_COMPUTE_TYPE@@`,
          data,
        ),
      ),
    onResetSuggestionTime: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@RESET_SUGGESTION_TIME@@`));
    },
    onUpdateShareAccessInfo: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_SHARE_ACCESS_INFO@@`, data),
      );
    },
    onUpdatePreviewDisabled: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@VALIDATE_BUTTON_PREVIEW`, data),
      );
    },
    initAutoCompleteData: () =>
      dispatch(init(`${MODULE_CONFIG.key}@@AUTOCOMPLETE_DATA@@`, {})),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

// export default compose(
//   withConnect,
//   memo,
// )(CreateSegment);
const withReducer = injectReducer({
  key: MODULE_CONFIG.key,
  reducer,
});
const withSaga = injectSaga({
  key: MODULE_CONFIG.key,
  saga,
});

export default compose(
  withReducer,
  withRouter,
  withConnect,
  withSaga,
)(CreateSegment);

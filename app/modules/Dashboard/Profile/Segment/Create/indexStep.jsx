/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React, { Fragment, useMemo, useRef, useState } from 'react';

import ErrorBoundary from 'components/common/ErrorBoundary';
import { Step } from '@material-ui/core';
import { useParams, useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { TabPanel } from '@xlab-team/ui-components';
import {
  DisplayContent,
  StepWrapper,
  StyledStepButton,
  StyledStepper,
  StyleWrapper,
  TabsStyled,
  WrapperDesign,
} from './styles';
import Design from './Desgin';
import { getBreadcrumbs } from './config';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { addNotification } from '../../../../../redux/actions';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import Content from '../../../Settings/Uploads/BO/Uploads/ContentV0';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { getPortalId } from '../../../../../utils/web/cookie';
import APP from '../../../../../appConfig';
import { makeSelectMainCreateSegment } from './selectors';

// Constants
import { TAB_SEGMENT } from './constants';

export const MAP_TITLE = {
  navBar: {
    titleCustomerSegment: getTranslateMessage(
      TRANSLATE_KEY._TITL_CUSTOMER_SEGMENT,
      'Customers and Segment',
    ),
    titleVisitorSegment: getTranslateMessage(
      TRANSLATE_KEY._TITL_VISTOR_SEGMENT,
      'Visitors and Segment',
    ),
  },
  visitorSegment: getTranslateMessage(
    TRANSLATE_KEY._ACT_VISITOR_SEGMENT,
    'Visitor Segment',
  ),
  customerSegment: getTranslateMessage(
    TRANSLATE_KEY._ACT_CUSTOMER_SEGMENT,
    'Customer Segment',
  ),
  labelTabSettings: getTranslateMessage(TRANSLATE_KEY._, 'Segment Settings'),
  labelTabShareAccess: getTranslateMessage(TRANSLATE_KEY._, 'Share Access'),
};

const mapTranslateMessage = {
  createCustomer: getTranslateMessage(
    TRANSLATE_KEY._ACT_CREATE_CUSTOMER,
    'Create Customer',
  ),
  createVisitor: getTranslateMessage(
    TRANSLATE_KEY._ACT_CREATE_VISITOR,
    'Create Visitor',
  ),
  createSegment: getTranslateMessage(
    TRANSLATE_KEY._ACT_CREATE_SEGMENT,
    'Create Segment',
  ),
};

export function CreateSegment(props) {
  const { main } = props;
  const [width, setWidth] = React.useState(window.innerWidth * 0.7);
  const [activeStep, setActiveStep] = useState(0);
  const [files, setFiles] = useState([]);
  const [titleSegment, setTitleSegment] = useState('');
  const [completedStep, setCompletedStep] = useState({
    isCompleted: false,
    isCompletedFirst: false,
    isCompletedLast: false,
  });
  const { itemTypeId } = useParams();
  const childRef = useRef(null);
  const stepRef = useRef(null);
  const history = useHistory();
  const [boUploadData, setBoUploadData] = useState({
    importId: '',
    mappingAttributes: [],
  });
  const [importType, setInportType] = useState('');
  const [activeTab, setActiveTab] = useState(TAB_SEGMENT.SETTINGS);

  useMemo(() => {
    if (itemTypeId === '-1003') {
      setTitleSegment(MAP_TITLE.customerSegment);
    }
    if (itemTypeId === '-1007') {
      setTitleSegment(MAP_TITLE.visitorSegment);
    }
  }, [itemTypeId]);
  const steps = [
    props.title === MAP_TITLE.navBar.titleCustomerSegment
      ? mapTranslateMessage.createCustomer
      : mapTranslateMessage.createVisitor,
    mapTranslateMessage.createSegment,
  ];
  const handleStep = step => {
    if (step === 1) {
      if (childRef.current.onValidate() && !childRef.current.isFileUploading) {
        setActiveStep(step);
        setCompletedStep(prev => ({ ...prev, isCompletedFirst: true }));
      }
    } else {
      setCompletedStep(prev => ({
        ...prev,
        isCompletedLast: !main.disabled && !main.isDoing,
      }));
      setActiveStep(step);
    }
  };

  const goToList = () =>
    history.push(`${APP.PREFIX}/${getPortalId()}/profile/segments`);

  // eslint-disable-next-line no-undef
  const breadcrumbs = useMemo(() => getBreadcrumbs(goToList, props.title), []);

  const callback = (type, data) => {
    if (type === 'ON_CANCEL') {
      goToList();
    }
  };

  const handleChangeTab = dataTab => {
    setActiveTab(dataTab);
  };

  const renderCreateCustomerOrVisitor = step => (
    <DisplayContent isShow={step === 0}>
      <CustomHeader showCancel breadcrums={breadcrumbs} callback={callback} />
      <Content
        isUploadStep
        ref={childRef}
        itemTypeId={itemTypeId}
        setActiveStep={setActiveStep}
        setFiles={setFiles}
        setCompletedStep={setCompletedStep}
        addNotification={props.addNotification}
        setBoUploadData={setBoUploadData}
        setInportType={setInportType}
      />
    </DisplayContent>
  );
  const renderCreateSegment = step => (
    <DisplayContent isShow={step === 1}>
      <Design
        isUploadStep
        width={width}
        titleSegment={titleSegment}
        design="create"
        files={files}
        onSaveFirstStep={
          childRef && childRef.current && childRef.current.onSaveStepMode
        }
        isSegment
        isSegmentJourney
        boUploadData={boUploadData}
        isDisableUpload
        importType={importType}
      />
    </DisplayContent>
  );

  const renderSettings = () => (
    <Fragment>
      <StepWrapper>
        <StyledStepper
          nonLinear
          activeStep={activeStep}
          ref={stepRef}
          className={activeStep === 1 && 'final-step'}
        >
          {steps.map((label, index) => (
            <Step key={label} className={index === 0 ? 'p-left-0' : ''}>
              <StyledStepButton
                className={
                  (completedStep.isCompletedFirst &&
                    index === 0 &&
                    'completed-step-color') ||
                  (completedStep.isCompletedLast &&
                    index === 1 &&
                    'completed-step-color')
                }
                onClick={() => handleStep(index)}
                disableRipple
                disableTouchRipple
              >
                {label}
              </StyledStepButton>
            </Step>
          ))}
        </StyledStepper>
      </StepWrapper>
      {renderCreateCustomerOrVisitor(activeStep)}
      {renderCreateSegment(activeStep)}
    </Fragment>
  );

  const renderContentTab = () => (
    <TabsStyled
      activeTab={activeTab}
      isBoxShadow={false}
      className="tab-segment"
      height="50px"
      classNameTabNav={activeTab}
      onChange={handleChangeTab}
    >
      <TabPanel
        label={MAP_TITLE.labelTabSettings}
        eventKey={TAB_SEGMENT.SETTINGS}
        className={`${
          activeTab === TAB_SEGMENT.SETTINGS ? 'active-tab' : ''
        } tab-panel-segment`}
        styleCss={{ height: '100%' }}
      >
        <div style={{ height: '100%' }}>{renderSettings()}</div>
      </TabPanel>
      <TabPanel
        label={MAP_TITLE.labelTabShareAccess}
        eventKey={TAB_SEGMENT.SHARE_ACCESS}
        className={`${
          activeTab === TAB_SEGMENT.SHARE_ACCESS ? 'active-tab' : ''
        } tab-panel-segment`}
        styleCss={{ height: '100%' }}
      >
        <CustomHeader showCancel breadcrums={breadcrumbs} callback={callback} />
        <Design
          isUploadStep
          isShareAccessView
          activeTab={activeTab}
          width={width}
          titleSegment={titleSegment}
          design="create"
          files={files}
          onSaveFirstStep={
            childRef && childRef.current && childRef.current.onSaveStepMode
          }
          isSegment
          isSegmentJourney
          boUploadData={boUploadData}
          isDisableUpload
          importType={importType}
        />
      </TabPanel>
    </TabsStyled>
  );

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Segment/Create/indexStep.jsx">
      <UIHelmet title={props.title} />
      <StyleWrapper
        style={{
          flexWrap: 'wrap',
          overflowY: 'auto',
          backgroundColor: '#EDEEF7',
        }}
      >
        <WrapperDesign>{renderContentTab()}</WrapperDesign>
      </StyleWrapper>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectMainCreateSegment(),
});

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CreateSegment);

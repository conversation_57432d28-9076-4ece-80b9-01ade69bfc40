/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';

import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import CalendarSelection from 'components/Templates/CalendarSelection';

import { useImmer } from 'use-immer';
import { UILoading } from '@xlab-team/ui-components';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectOverviewDomainMain,
  makeSelectListOverviewDateRange,
} from './selectors';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import {
  init,
  updateValue,
  update,
  reset,
  addNotification,
} from '../../../../../redux/actions';
import { StyleWrapper } from './styles';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../components/Templates/LayoutContent';
import { makeSelectSegmentActiveTab } from '../Main/selectors';
import { formatDate } from '../../../../../utils/date';
import Iframe from '../../../MarketingHub/Journey/Detail/Dashdoard/_UI/Iframe';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';

function OverviewPage(props) {
  const { main, dateRange, activeTab, reportConfig } = props;

  const { isInitDone, componentRenderKey: componentKey } = main;

  const [input, setInput] = useImmer({
    src: '',
    width: '100%',
    height: '100%',
    title: 'Data dashboard',
    isLoading: true,
  });
  console.log('input', input);

  useEffect(() => {
    if (activeTab === 'overview') {
      props.init({});
    }
    return () => {
      props.reset();
    };
  }, [activeTab]);

  useEffect(() => {
    if (isInitDone) {
      const filter = [];
      const masterDaterange = {
        type: 'masterDaterange',
        value: {
          key: 'custom',
          fromDate: formatDate(
            new Date(dateRange.value.fromDate),
            'dd/MM/yyyy',
          ),
          toDate: formatDate(new Date(dateRange.value.toDate), 'dd/MM/yyyy'),
        },
      };
      filter.push(masterDaterange);
      console.log('masterDaterange', masterDaterange);

      const src = `${reportConfig.url}?filter=${window.encodeURI(
        JSON.stringify(filter),
      )}`;
      setInput(draft => {
        draft.src = src;
        draft.isLoading = true;
      });
    }
  }, [componentKey, isInitDone]);

  const onLoad = () => {
    setInput(draft => {
      draft.isLoading = false;
    });
  };

  const initDateRangeData = useMemo(() => dateRange, [isInitDone]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'ON_CHANGE_DATERANGE': {
        props.onChangeDateRange(dataIn);
        props.onSaveDateRange(dataIn);
        break;
      }
      default:
        break;
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Zone/index.jsx">
      <NavBarRightContent top="0px" right="14px" className="p-right-4">
        <CustomHeader breadcrums={BREADCRUMDATA} />

        {/* {isInitDone && (
          <CalendarSelection
            initData={initDateRangeData}
            callback={callback}
            maxWidth="150px"
            hiddenLabel
          />
        )} */}
      </NavBarRightContent>
      <StyleWrapper>
        {input.isLoading && <UILoading isLoading />}
        <LayoutContent
          padding="8px 16px 16px 16px"
          margin="-16px"
          height="calc(100vh - 90px)"
        >
          <LayoutContentLoading isLoading={!isInitDone}>
            <div
              id={`div-sca-${componentKey}-0`}
              style={{
                position: 'position',
                width: '100%',
              }}
            >
              <Iframe
                componentKey={componentKey}
                title={input.title}
                src={input.src}
                width={input.width}
                height={input.height}
                onLoad={onLoad}
              />
            </div>
          </LayoutContentLoading>
        </LayoutContent>
      </StyleWrapper>
    </ErrorBoundary>
  );
}
const mapStateToProps = createStructuredSelector({
  main: makeSelectOverviewDomainMain(),
  dateRange: makeSelectListOverviewDateRange(),
  activeTab: makeSelectSegmentActiveTab(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(OverviewPage);

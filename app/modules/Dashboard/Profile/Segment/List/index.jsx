/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useCallback, memo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import queryString from 'query-string';

import { createStructuredSelector } from 'reselect';

// Hooks
import useNotificationBar from 'hooks/useNotificationBar';

import ErrorBoundary from 'components/common/ErrorBoundary';
import NavBar from 'components/Organisms/NavBar/index';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { UIIconButton as IconButton } from '@xlab-team/ui-components';
import reducer from './reducer';
import saga from './saga';
import SegmentServices from '../../../../../services/Segment';
import {
  makeSelectListSegmentTable,
  makeSelectListSegmentFilter,
  makeSelectListSegmentColumn,
  makeSelectListSegmentDomainMain,
  makeSelectListSegmentDomainMainData,
} from './selectors';
import { MODULE_CONFIG, BREADCRUMDATA } from './config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  init,
  getList,
  updateValue,
  update,
  reset,
  addNotification,
  updateDone,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { TableWrapper, TableRelative } from './styles';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
// import TryVersion from '../../../../../containers/TryVersion';
import ControlTable from './ControlTable';
import ModalClone from '../../../../../containers/modals/ModalClone';
import ModalDeleteMulti from '../../../../../containers/modals/ModalDeleteMulti';
import ModalConfirm from '../../../../../containers/modals/ModalConfirm';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  getOwnerIds,
  MAP_JOURNEY_ACCEPTED_ACTIONS as MAA,
  makeUrlPermisison,
} from '../../../../../utils/web/permission';
import {
  ACTION_LIMIT_TYPES,
  getLabelFilterBy,
  getWarningLimitMessages,
} from './utils';
import { toConditionAPI } from '../../../../../containers/Filters/utils';
import AddComponent from './AddComponent';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
// import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import NoDataComponent from '../../../../../components/common/NoDataFound';
import APP from '../../../../../appConfig';
import {
  getCurrentAccessUserId,
  getCurrentUserId,
  getPortalId,
} from '../../../../../utils/web/cookie';
import { getUntitledName } from '../../../../../utils/web/properties';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import ModalArchive from '../../../../../containers/modals/ModalArchive';
import useToggle from '../../../../../hooks/useToggle';
import ModalRecover from '../../../../../containers/modals/ModalRecover';
import ModalExport from '../../../../../containers/modals/ModalExport';
import ModalDowload from '../../../../../containers/modals/ModalDownloadBO';
import ModalForceRun from '../../../../../containers/modals/ModalForceRun';
import { safeParse, trackEvent } from '../../../../../utils/web/utils';
import { OBJECT_NAME } from '../../utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import ModalRebuild from '../../../../../containers/modals/ModalRebuild';
import { makeSelectDashboardMenuCodeActive } from '../../../selector';
import { makeSelectReInitDashboard } from 'containers/Drawer/DrawerSegment/selectors';
import {
  ITEM_TYPE_ID,
  TAB,
  SETTINGS,
  DESIGN,
  NON_STEP,
  UI,
  DEFAULT_USE_SEGMENT_INTO,
  USE_SEGMENT_IN,
} from 'containers/Drawer/DrawerSegment/constants';

const MAP_TITLE = {
  navBar: {
    title: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_SEGMENT, 'Segments'),
  },
  labelArchive: getTranslateMessage(
    TRANSLATE_KEY._,
    'By default, the data with Archived status are hidden from the datagrid',
  ),
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  },
  itemNameSegment: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'segment(s)',
  ),
  untitledSegment: getTranslateMessage(
    TRANSLATE_KEY._UNTITLED_SEGMENT,
    'Untitled Segment',
  ),
  guideNoDestYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_SEGMENT,
    "You haven't created any Segment yet",
  ),
  guideNoDestYetShared: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_SHARED_SEGMENT,
    "You haven't been shared any Segment",
  ),
};

const NOTI = {
  toggle: {
    fail: res => ({
      id: 'toggle-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
  },
};
export function Customer(props) {
  const [isOpenModalClone, setIsOpenModalClone] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalArchive, toggleModalArchive] = useToggle(false);
  const [isOpenModalRecover, toggleModalRecover] = useToggle(false);
  const [isOpenModalRebuild, toggleModalRebuild] = useToggle(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalForceRun, toggleModalForceRun] = useToggle(false);
  const [width, setWidth] = useState(window.innerWidth);
  // const [selectedRows, setSelectedRows] = useState([]);
  const { main, table, filter, column, activeTab, menuCodeActive } = props;

  if (activeTab === 'list' || activeTab === 'shared-with-me') {
    const newObjectType =
      activeTab === 'shared-with-me'
        ? 'AUDIENCE_SEGMENTS_SHARED'
        : 'BO_SEGMENTS';

    MODULE_CONFIG.objectType = newObjectType;
  }

  useEffect(() => {
    const object = {
      type: 'segment',
      name: 'segment',
    };
    trackEvent('object', 'listing', object);
    if (activeTab === 'list' || activeTab === 'shared-with-me') {
      const { segmentId } = queryString.parse(window.location.search);
      props.init({ segmentId, activeTab });
      props.updateListType({
        listType: activeTab,
      });
    }
    return () => {
      props.reset();
    };
  }, [activeTab, props.reInitDashboard]);

  // useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  // useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });

  const handleValidateTotalLimit = async data => {
    try {
      const segmentList = [];
      data.forEach(each => {
        segmentList.push({
          id: safeParse(each.segment_id, ''),
          type: safeParse(each.update_method, '').toLowerCase(),
          status: safeParse(each.status, ''),
        });
      });

      const params = {
        data: {
          segments: segmentList,
        },
      };

      const res = await SegmentServices.data.checkUpateStatusLimitation(params);

      if (res.code === 200) {
        const { canUpdate } = safeParse(res.data);

        if (canUpdate) {
          toggleModalRecover();
        }
      } else if (res.codeMessage === '_NOTIFICATION_REACHED_LIMIT') {
        const limitTotalMessage = getWarningLimitMessages(
          safeParse(res.data.type, ''),
        );

        if (limitTotalMessage) {
          props.onUpdateMessageWarningLimitations({
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
            message: limitTotalMessage,
          });
          props.toggleModalWarningLimitations({
            isOpen: true,
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
          });
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: 'app/modules/Dashboard/Profile/Segment/List/index.jsx',
        func: 'handleValidateTotalLimit',
        data: error.stack,
      });
    }
  };

  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData({ ...(data || {}), activeTab });
        break;
      }
      case 'ACTION_TABLE_CLONE': {
        // setSelectedRows(data);
        setIsOpenModalClone(true);
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        // setSelectedRows(data);
        setIsOpenModalDelete(true);
        break;
      }
      case 'ACTION_TABLE_DISABLED_ENABLED': {
        if (data.status === 1) {
          toggleModalForceRun();
        } else {
          props.onChangeActionTable(data);
        }
        break;
      }
      case 'ACTION_TABLE_ARCHIVE': {
        // setSelectedRows(data);
        toggleModalArchive();
        break;
      }
      case 'ACTION_TABLE_RECOVER': {
        // setSelectedRows(data);
        handleValidateTotalLimit(data);
        // toggleModalRecover();
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(data);
        break;
      }
      case 'UPDATE_TOGGLE_CELL_DONE': {
        const { isSuccessed, res } = data;
        if (!isSuccessed) {
          const noti = NOTI.toggle.fail(res);
          props.addNotification(noti);
        }
        props.onChangeStatusDone(data);
        break;
      }
      case `ACTION_TABLE_FORCE_RUN`: {
        props.onChangeActionTable(data);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(data);
        break;
      }
      case 'ACTION_TABLE_REBUILD': {
        if (data.status === 1) {
          toggleModalRebuild();
        } else {
          props.rebuildSegment(data);
        }
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const mapParamsFnDelete = useCallback(
    (oldData, newData) => ({
      data: {
        totalSelected: oldData.isSelectedAll
          ? oldData.totalRecord
          : newData.accepted.length,
        objectIds: newData.accepted.map(each => each.segment_id),
        isCheckUsedStatus: 0,
        filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
      },
    }),
    [],
  );

  const isHasEditEverything = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.UPDATE,
    APP_ROLE_SCOPE.EVERYTHING,
    true,
  );
  const isHasCreateSegment = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.CREATED_BY_USER,
  );

  const activeRow = table.selectedRows.values().next().value;

  const cloneUrl = React.useMemo(() => {
    const searchParams = new URLSearchParams();
    searchParams.set(TAB, SETTINGS);
    searchParams.set(DESIGN, 'create');
    searchParams.set('copyId', (activeRow && activeRow.id) || 'not-found');
    searchParams.set(UI, NON_STEP);
    searchParams.set(USE_SEGMENT_IN, DEFAULT_USE_SEGMENT_INTO);
    searchParams.set(
      ITEM_TYPE_ID,
      (activeRow && activeRow.item_type_id) || 'not-found',
    );

    return makeUrlPermisison(
      `${
        APP.PREFIX
      }/${getPortalId()}/profile/segments/create?${searchParams.toString()}`,
    );
  }, [activeRow]);

  const { isShow: isShowExplore } = useNotificationBar();
  // const data = tableDataMultiColumns;
  // console.log('filter', props.data);
  // console.log('tableColumns', tableColumns, main.data);
  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Segment/List/index.jsx">
      <UIHelmet title={MAP_TITLE.navBar.title} />
      {/* <CustomHeader breadcrums={BREADCRUMDATA} /> */}
      <LayoutContent
        padding="0px"
        // margin="-16px"
        height={`calc(100vh - ${isShowExplore ? '215px' : '155px'})`}
      >
        {/* <NavBar label={MAP_TITLE.navBar.title} /> */}
        <TableRelative className="segment-list-abc">
          <TableWrapper>
            {/* <Table
          columns={columns}
          data={props.customer.data}
          sizeCheckbox={width > 1600 ? 50 : 48}
        /> */}
            <TableContainer
              columnActive={column.columnObj}
              table={table}
              isLoading={main.isLoading}
              // version={MODULE_CONFIG.key}
              moduleConfig={MODULE_CONFIG}
              selectedIds={table.selectedIds}
              selectedRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              isSelectedAllPage={table.isSelectedAllPage}
              columns={tableColumns}
              data={!main.isLoading ? props.data : []}
              callback={callback}
              labelArchive={MAP_TITLE.labelArchive}
              noDataText="No data"
              resizeColName="segment_display"
              widthFirstColumns={143}
              initialWidthColumns={423}
              ComponentControlTable={ControlTable}
              NoDataComponent={() => (
                <NoDataComponent
                  AddComponent={() => <AddComponent className="m-left-0" />}
                  guideNoYet={
                    activeTab === 'list'
                      ? MAP_TITLE.guideNoDestYet
                      : MAP_TITLE.guideNoDestYetShared
                  }
                />
              )}
              disabledHeaderCheckbox={!isHasEditEverything}
            >
              <>
                <Filters
                  use="list"
                  isEdit={isHasCreateSegment}
                  rules={filter.rules}
                  moduleConfig={MODULE_CONFIG}
                  filterActive={filter.config.filterObj}
                  filterCustom={filter.config.library.filterCustom}
                  libraryFilters={filter.config.library.filters}
                  groups={main.groupAttributes.groupsFilter}
                  // callback={callback}
                  isFilter={filter.config.design.isFilter}
                  isLoading={filter.config.isLoading}
                  AddComponent={activeTab === 'list' ? AddComponent : undefined}
                />
                <WrapActionTable
                  show={!filter.config.design.isFilter}
                  className="p-x-4"
                >
                  <div className="actionTable__inner">
                    <Search
                      moduleConfig={MODULE_CONFIG}
                      config={{
                        objectType: MODULE_CONFIG.objectType,
                        limit: 20,
                        page: 1,
                        sort: 'asc',
                        filters: {
                          OR: [
                            {
                              AND: [
                                {
                                  column: 'item_type_id',
                                  data_type: 'number',
                                  operator: 'matches',
                                  value: [-1003, -1007],
                                },
                              ],
                            },
                          ],
                        },
                      }}
                      suggestionType="suggestionMultilang"
                      moduleLabel={getTranslateMessage(
                        TRANSLATE_KEY._MENU_SUB_SEGMENT,
                        'Segments',
                      )}
                    />
                    <ModifyColumn
                      sort={table.sort}
                      moduleConfig={MODULE_CONFIG}
                      columnActive={column.columnObj}
                      columnCustom={column.library.columnCustom}
                      libraryColumns={column.library.columns}
                      defaults={MODULE_CONFIG.columnsDefault}
                      defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                      columns={column.columnObj.columns.columnsAlias}
                      groups={main.groupAttributes.groups}
                      isLoading={column.isLoading}
                    />
                    <IconButton
                      iconName="file_download"
                      size="24px"
                      onClick={
                        table.paging.totalRecord === 0
                          ? () => {}
                          : toggleModalDownload
                      }
                      isVertical
                      data-test="export-file"
                      disabled={table.paging.totalRecord === 0}
                    >
                      {MAP_TITLE.action.actExport.toUpperCase()}
                    </IconButton>
                  </div>
                  {/* <div className="downBlock">
                    <IconButton
                      iconName="arrow-down"
                      size="24px"
                      onClick={() => {}}
                      disabled
                    />
                  </div> */}
                </WrapActionTable>
              </>
            </TableContainer>
          </TableWrapper>
        </TableRelative>
      </LayoutContent>
      {/* <TryVersion module="segments" /> */}
      <ModalClone
        activeRow={table.selectedRows.values().next().value}
        label={getTranslateMessage(
          TRANSLATE_KEY._BOX_TITL_SEGMENT_CLONE,
          'Segment Clone',
        )}
        placeHolderName=""
        ObjectServicesFn={SegmentServices.data.clone}
        nameKey="segment_display"
        isOpenModal={isOpenModalClone}
        setOpenModal={setIsOpenModalClone}
        fetchData={props.fetchData}
        mapParamsFn={useCallback(
          (oldData, newData) => ({
            objectId: oldData.segment_id,
            data: {
              newName: newData.name.value,
            },
          }),
          [],
        )}
        cloneUrl={cloneUrl}
        // defaultName={getUntitledName(MAP_TITLE.untitledSegment)}
      />
      <ModalDowload
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        objectName={OBJECT_NAME.SEGMENT}
        object_type={MODULE_CONFIG.objectType}
        ObjectServicesFn={SegmentServices.data.downloadSegment}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        sortDefault="utime"
        filters={filter}
        itemTypeId={MODULE_CONFIG.objectId}
        itemTypeName="user"
        columns={column.columnObj.columns.columnsAlias}
        getListType={activeTab === 'list' ? 1 : 2}
      />
      <ModalArchive
        code="segment"
        activeRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'Segment')}
        placeHolderName=""
        callback={callback}
        moduleName={MAP_TITLE.itemNameSegment}
        objectType={MODULE_CONFIG.objectType}
        ObjectServicesFn={SegmentServices.data.archiveBOSegment}
        isOpen={isOpenModalArchive}
        toggle={toggleModalArchive}
      />
      <ModalRecover
        activeRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'Segment')}
        placeHolderName=""
        callback={callback}
        moduleName={MAP_TITLE.itemNameSegment}
        isOpen={isOpenModalRecover}
        toggle={toggleModalRecover}
      />
      <ModalConfirm
        isOpen={main.isOpenModalWarningTotalLimit}
        isFooterV2
        toggle={() => {
          props.toggleModalWarningLimitations({
            isOpen: false,
            type: ACTION_LIMIT_TYPES.TOTAL_LIMIT,
          });
        }}
        title={getTranslateMessage(
          TRANSLATE_KEY._WARN_TITLE_SEG_WARN_TOTAL,
          'Warning Total Segment Limit',
        )}
      >
        {main.messageWarningLimit}
      </ModalConfirm>
      <ModalConfirm
        isOpen={main.isOpenModalComputationLimit}
        isFooterV2
        toggle={() => {
          props.toggleModalWarningLimitations({
            isOpen: false,
            type: ACTION_LIMIT_TYPES.COMPUTATIONS_LIMIT,
          });
        }}
        title={getTranslateMessage(
          TRANSLATE_KEY._WARN_TITLE_SEG_COMPUTATIONAL_LIMIT,
          'Warning Segment Computational Limit',
        )}
      >
        {main.messageWarningComputationLimit}
      </ModalConfirm>
      <ModalForceRun
        activeRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'Segment')}
        placeHolderName=""
        callback={callback}
        moduleName="Action success"
        isOpen={isOpenModalForceRun}
        toggle={toggleModalForceRun}
      />
      {isOpenModalDelete && (
        <ModalDeleteMulti
          activeRows={table.selectedRows}
          isSelectedAll={table.isSelectedAll}
          totalRecord={table.paging.totalRecord}
          rules={filter.rules}
          label={getTranslateMessage(
            TRANSLATE_KEY._BOX_TITL_DELETE_SEGMENT,
            'Delete segment',
          )}
          placeHolderName=""
          moduleName={MAP_TITLE.itemNameSegment}
          ObjectServicesFn={SegmentServices.data.deleteWithCondition}
          criteriaFn={each => (each.accepted_actions || {})[MAA.DELETE]}
          isOpenModal={isOpenModalDelete}
          setOpenModal={setIsOpenModalDelete}
          fetchData={props.fetchData}
          mapParamsFn={mapParamsFnDelete}
        />
      )}
      <ModalRebuild
        activeRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'Segment')}
        placeHolderName=""
        callback={callback}
        moduleName={MAP_TITLE.itemNameSegment}
        isOpen={isOpenModalRebuild}
        toggle={toggleModalRebuild}
        isSegment
      />
    </ErrorBoundary>
  );
}

// Customer.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  main: makeSelectListSegmentDomainMain(),
  // common: makeSelectCustomerCommon(),
  table: makeSelectListSegmentTable(),
  filter: makeSelectListSegmentFilter(),
  column: makeSelectListSegmentColumn(),
  data: makeSelectListSegmentDomainMainData(),
  menuCodeActive: makeSelectDashboardMenuCodeActive(),
  reInitDashboard: makeSelectReInitDashboard(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    toggleModalWarningLimitations: data => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_MODEL_WARNING_LIMITATIONS@@`,
          data,
        ),
      );
    },
    onUpdateMessageWarningLimitations: data => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@MESSAGE_WARNING_LIMITATIONS@@`,
          data,
        ),
      );
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    onChangeStatusDone: params => {
      dispatch(updateDone(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeActionTable: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ACTION_STATUS`, params));
    },
    rebuildSegment: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@REBUILD_SEGMENT`, params));
    },
    updateListType: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@LIST_TYPE`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(Customer);

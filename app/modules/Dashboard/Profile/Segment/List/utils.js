/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
import { format } from 'date-fns';
import moment from 'moment';
import {
  getLabelStatusSegment,
  getLabelStatusArchive,
} from '../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellSegmentName,
  CellProcessStatusSegment,
  CellDate,
  CellCurrency,
  CellNumber,
  CellOwner,
  CellStatusArchiveSegment,
  CellArray,
  CellToggleAPI,
  CellArrayObject,
} from '../../../../../containers/Table/Cell';
import {
  getLabelSegmentErrorInfo,
  getSegmentTypeLabel,
  getStorageTypeLabel,
  getUpdateMethodLabel,
} from '../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
import SegmentServices from '../../../../../services/Segment';
import {
  initNumberWithoutDecimal,
  initNumberId,
} from '../../../../../utils/web/portalSetting';

import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { getCellDisplayDate } from '../../../../../containers/Table/utils';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

export const ACTION_LIMIT_TYPES = {
  TOTAL_LIMIT: 'TOTAL_LIMIT',
  COMPUTATIONS_LIMIT: 'COMPUTATIONS_LIMIT',
};

const ARR_SOURCES = [
  {
    name: 'Customer attributes',
    translateCode: TRANSLATE_KEY._ATTRIBUTE_TYPE_CUSTOMER,
  },
  {
    name: 'User attributes',
    translateCode: TRANSLATE_KEY._ATTRIBUTE_TYPE_USER,
  },
  {
    name: 'Matching file',
    translateCode: TRANSLATE_KEY._FILTER_MATCHING_FILE,
  },
];

const translateLabelSources = arr => {
  const validateArr = safeParse(arr, []);

  if (validateArr.length === 0) {
    return arr;
  }
  ARR_SOURCES.forEach(source => {
    const pos = validateArr.indexOf(source.name);
    if (pos !== -1) {
      validateArr.splice(pos, 1);
      const translatedName = getTranslateMessage(
        source.translateCode,
        source.name,
      );
      validateArr.splice(pos, 0, translatedName);
    }
  });
  return validateArr;
};

export const getWarningLimitMessages = type => {
  let label = '';
  let dynamicSegments = '';
  let staticSegments = '';

  switch (type) {
    case 'static':
      label = staticSegments = 'static segments';

      break;
    case 'dynamic':
      label = dynamicSegments = 'dynamic segments';
      break;
    default:
      staticSegments = 'static segments';
      dynamicSegments = 'dynamic segments';
      label = 'dynamic segments, static segments';
  }

  function getLabel() {
    return getTranslateMessage(
      TRANSLATE_KEY._WARN_SEG_WARN_TOTAL_CONTENT_RECOVER,
      `The number of ${label} has reached the limit. If you want to recover segments, please archived unused segments.`,
      { dynamic_segments: dynamicSegments, static_segments: staticSegments },
    );
  }

  return getLabel();
};

export function serializeData(list, portalId, groupAttributes) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.segment_id,
      portal_id: {
        value: portalId,
      },
      process_status: getLabelStatusSegment(
        parseInt(tmp.status),
        parseInt(tmp.process_status),
      ),
      segment_status: getLabelStatusArchive(Number(tmp.status)),
      original_process_status: tmp.process_status,
      segmentTypeDisplay: getSegmentTypeLabel(`${tmp.item_type_id}`),
      source_names: translateLabelSources(tmp.source_names),
      storageTypeDisplay: getStorageTypeLabel(tmp.storage_type),
      update_method: getUpdateMethodLabel(tmp.update_method),
      response_code: getLabelSegmentErrorInfo(tmp.response_code),
      disabledCheckbox: !tmp.isEdit,
      // compute_schedule_end:
      //   tmp.compute_schedule_end === 'None'
      //     ? 'Never' : tmp.compute_schedule_end,
      // : getCellDisplayDate(column[0], tmp.compute_schedule_end),
      // format(tmp.compute_schedule_end, 'dd MMMM yyyy HH:mm:ss', { awareOfUnicodeTokens: true })
    };

    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggleAPI,
      placement: 'center',
      className: 'tbl-border-left padding-left-right-10',
      isForce: row => row.update_method !== 'Static',
      getIsDisabledToggle: row =>
        !row.accepted_actions[MAA.DISABLE] ||
        // row.update_method === 'Static' ||
        row.status === 4,
      mapParamsFn: ({ oldData, newData, isBuild = true }) => ({
        objectId: oldData.segment_id,
        itemTypeId: oldData.item_type_id,
        data: {
          columns: ['status'],
          status: newData._newStatus ? 1 : 2,
          segmentType: oldData.segment_type,
          isBuild,
        },
      }),
      ServiceToggleFn: SegmentServices.data.updateListStatus,
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellSegmentName,
      getIsDisabledEditName: row =>
        !(row.accepted_actions || {})[MAA.EDIT_NAME],
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat, propertyCode } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'label_ids') {
      column.Cell = CellArrayObject;
    } else if (property.value === 'process_status') {
      column.Cell = CellProcessStatusSegment;
      column.placement = 'status';
      // column.className = `${dataType} ${property.value} ${'status'}`;
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'item_type_id') {
      column.accessor = `segmentTypeDisplay`;
      column.placement = 'left';
    } else if (property.value === 'segment_status') {
      column.Cell = CellStatusArchiveSegment;
      column.placement = 'status';
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (property.value === 'storage_type') {
      column.accessor = 'storageTypeDisplay';
      column.placement = 'left';
    } else if (dataType === 'number') {
      if (property.value === 'segment_size') {
        column.displayFormat = initNumberWithoutDecimal();
      } else if (property.value === 'segment_id') {
        column.displayFormat = initNumberId();
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    columns.push(column);
  });
  return columns;
}

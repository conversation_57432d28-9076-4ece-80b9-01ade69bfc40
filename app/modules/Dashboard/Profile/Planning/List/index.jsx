/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React, { useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';

import ErrorBoundary from 'components/common/ErrorBoundary';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import useNotificationBar from 'hooks/useNotificationBar';
import socket, { NOTIFY } from 'services/Socket';
import { UIIconButton as IconButton } from '@xlab-team/ui-components';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListSegmentTable,
  makeSelectListSegmentFilter,
  makeSelectListSegmentColumn,
  makeSelectListSegmentDomainMain,
  makeSelectListSegmentDomainMainData,
  makeSelectExploreInfo,
  selectBuildPlanProgress,
} from './selectors';
import { MODULE_CONFIG, BREADCRUMDATA, COLUMNS } from './config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  init,
  getList,
  updateValue,
  update,
  reset,
  addNotification,
  updateDone,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { TableWrapper, TableRelative } from './styles';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
// import TryVersion from '../../../../../containers/TryVersion';
import AddComponent from './AddComponent';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import NoDataComponent from '../../../../../components/common/NoDataFound';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import ModalProgressBuildPlanning from '../../../../../containers/modals/ModalProgressBuildPlanning';
import DrawerInsightExplorePlanning from '../../../../../containers/Drawer/DrawerInsightExplorePlanning';

const MAP_TITLE = {
  navBar: {
    title: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Plannings'),
  },
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  },
  itemNameSegment: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'segment(s)',
  ),
  untitledSegment: getTranslateMessage(
    TRANSLATE_KEY._UNTITLED_SEGMENT,
    'Untitled Segment',
  ),
};

const NOTI = {
  toggle: {
    fail: res => ({
      id: 'toggle-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
  },
};
export function PlanListing(props) {
  const { main, table, filter, exploreInfo, column, activeTab } = props;
  const prevExploreInfo = useRef();
  const { isShow } = useNotificationBar();

  useEffect(() => {
    if (activeTab === 'list') {
      props.init();
    }
    return () => props.reset();
  }, [activeTab]);

  useEffect(() => {
    // join room
    socket.emit('notification');

    socket.on(NOTIFY, message => {
      if (message && message.data) {
        const notiInfo = message.data.find(
          noti => +noti.alert_id === +prevExploreInfo.current.ruleId,
        );

        // success or fail noti
        if (notiInfo && [2, 3].includes(+notiInfo.build_status)) {
          props.toggleModalProgressBuild(false);
        }
      }
    });

    return () => {
      socket.off(NOTIFY);
    };
  }, []);

  useEffect(() => {
    prevExploreInfo.current = exploreInfo;
  }, [exploreInfo]);

  // useEffect(() => {
  //   window.addEventListener('message', event => {
  //     console.log(event.data);
  //   });
  // }, []);

  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(data);
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(data);
        break;
      }
      case 'UPDATE_TOGGLE_CELL_DONE': {
        const { isSuccessed, res } = data;
        if (!isSuccessed) {
          const noti = NOTI.toggle.fail(res);
          props.addNotification(noti);
        }
        props.onChangeStatusDone(data);
        break;
      }
      case `ACTION_TABLE_FORCE_RUN`: {
        props.onChangeActionTable(data);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(data);
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Segment/List/index.jsx">
      <UIHelmet title={MAP_TITLE.navBar.title} />
      <CustomHeader breadcrums={BREADCRUMDATA} />
      <LayoutContent
        padding="0"
        // margin="-16px"
        height={isShow ? 'calc(100vh - 142px - 54px)' : 'calc(100vh - 142px)'}
      >
        {/* <NavBar label={MAP_TITLE.navBar.title} /> */}
        <TableRelative className="segment-list-abc">
          <TableWrapper>
            <TableContainer
              columnActive={column.columnObj}
              table={table}
              isLoading={main.isLoading}
              // version={MODULE_CONFIG.key}
              moduleConfig={MODULE_CONFIG}
              selectedIds={table.selectedIds}
              selectedRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              isSelectedAllPage={table.isSelectedAllPage}
              columns={tableColumns}
              data={props.data}
              callback={callback}
              noDataText="No data"
              resizeColName={COLUMNS.planName}
              widthFirstColumns={143}
              initialWidthColumns={423}
              NoDataComponent={() => (
                <NoDataComponent
                  AddComponent={() => <AddComponent className="m-left-0" />}
                  guideNoYet={MAP_TITLE.guideNoDestYet}
                />
              )}
            >
              <>
                <Filters
                  use="list"
                  rules={filter.rules}
                  moduleConfig={MODULE_CONFIG}
                  filterActive={filter.config.filterObj}
                  filterCustom={filter.config.library.filterCustom}
                  libraryFilters={filter.config.library.filters}
                  groups={main.groupAttributes.groupsFilter}
                  // callback={callback}
                  isFilter={filter.config.design.isFilter}
                  isLoading={filter.config.isLoading}
                  AddComponent={AddComponent}
                />
                <WrapActionTable
                  show={!filter.config.design.isFilter}
                  className="p-x-4"
                >
                  <div className="actionTable__inner">
                    <Search
                      moduleConfig={MODULE_CONFIG}
                      config={{
                        objectType: MODULE_CONFIG.objectType,
                        limit: 20,
                        page: 1,
                        sort: 'asc',
                      }}
                      suggestionType="suggestionMultilang"
                      moduleLabel={getTranslateMessage(
                        TRANSLATE_KEY._MENU_SUB_SEGMENT,
                        'Segments',
                      )}
                    />
                    <ModifyColumn
                      sort={table.sort}
                      moduleConfig={MODULE_CONFIG}
                      columnActive={column.columnObj}
                      columnCustom={column.library.columnCustom}
                      libraryColumns={column.library.columns}
                      defaults={MODULE_CONFIG.columnsDefault}
                      defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                      columns={column.columnObj.columns.columnsAlias}
                      groups={main.groupAttributes.groups}
                      isLoading={column.isLoading}
                    />
                    <IconButton
                      iconName="file_download"
                      size="24px"
                      // onClick={
                      //   table.paging.totalRecord === 0
                      //     ? () => {}
                      //     : toggleModalDownload
                      // }
                      isVertical
                      // disabled={table.paging.totalRecord === 0}
                    >
                      {MAP_TITLE.action.download.toUpperCase()}
                    </IconButton>
                  </div>
                </WrapActionTable>
              </>
            </TableContainer>
          </TableWrapper>
        </TableRelative>
      </LayoutContent>

      <ModalProgressBuildPlanning
        open={main.isOpenModalProgessBuild}
        onClose={() => props.onCancelModalProgessBuildPlanning()}
        progress={props.buildPlanProgress}
      />

      <DrawerInsightExplorePlanning
        open={main.isOpenDrawerExplore}
        // open
        exploreInfo={exploreInfo}
        onToggle={isOpen => props.onToggleDrawerExplore(isOpen)}
      />
    </ErrorBoundary>
  );
}

// Customer.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  main: makeSelectListSegmentDomainMain(),
  // common: makeSelectCustomerCommon(),
  table: makeSelectListSegmentTable(),
  filter: makeSelectListSegmentFilter(),
  column: makeSelectListSegmentColumn(),
  data: makeSelectListSegmentDomainMainData(),
  exploreInfo: makeSelectExploreInfo(),
  buildPlanProgress: selectBuildPlanProgress,
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    onChangeStatusDone: params => {
      dispatch(updateDone(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeActionTable: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ACTION_STATUS`, params));
    },
    onCancelModalProgessBuildPlanning: () =>
      dispatch({
        type: `${MODULE_CONFIG.key}@@CANCEL_POLLING_GET_BUILD_PLANNING_S`,
      }),
    onToggleDrawerExplore: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_DRAWER_EXPLORE`, value),
      ),
    toggleModalProgressBuild: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL_PROGESS_BUILD`, value),
      ),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(PlanListing);

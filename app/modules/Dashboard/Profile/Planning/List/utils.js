/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
import {
  CellDate,
  CellPlanningName,
  CellText,
  CellNumberString,
} from '../../../../../containers/Table/Cell';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';
import { getSegmentTypeLabel } from '../../../../../services/Abstract.data';
import { COLUMNS, MAP_STATUS_LABEL, RE_MAP_ACCESSOR } from './config';

export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp[COLUMNS.planId],
      [RE_MAP_ACCESSOR[COLUMNS.status]]: MAP_STATUS_LABEL[tmp[COLUMNS.status]],
      [RE_MAP_ACCESSOR[COLUMNS.itemTypeId]]: getSegmentTypeLabel(
        tmp[COLUMNS.itemTypeId],
      ),
      btnExploreLoading: false,
    };

    data.list.push(tempt);
    data.map[tempt[COLUMNS.planId]] = tempt;
  });

  return data;
}

export function buildTableGroupColumns(columnsActive, columnPlanningName) {
  return [
    {
      Header: columnPlanningName.label,
      id: columnPlanningName.value,
      accessor: columnPlanningName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellPlanningName,
      getIsDisabledEditName: () => false,
      // getIsDisabledEditName: row =>
      //   !(row.accepted_actions || {})[MAA.EDIT_NAME],
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };

    if (property.value === COLUMNS.ctime || property.value === COLUMNS.utime) {
      column.Cell = CellDate;
    }

    if (property.value === COLUMNS.itemTypeId) {
      column.placement = 'left';
    }

    if (
      property.value === COLUMNS.groupTotal ||
      property.value === COLUMNS.planId
    ) {
      column.Cell = CellNumberString;
    }

    if (property.value === COLUMNS.status) {
      column.placement = 'left';
    }

    if (Object.keys(RE_MAP_ACCESSOR).includes(property.value)) {
      column.accessor = RE_MAP_ACCESSOR[property.value];
    }

    columns.push(column);
  });

  return columns;
}

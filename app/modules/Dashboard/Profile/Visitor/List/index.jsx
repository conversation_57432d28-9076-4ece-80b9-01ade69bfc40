/* eslint-disable consistent-return */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import { intersection, get } from 'lodash';

import ErrorBoundary from 'components/common/ErrorBoundary';
import NavBar from 'components/Organisms/NavBar';
import TableContainer from 'containers/Table';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import ModalProcessing from 'containers/modals/ModalProcessing';
import {
  UIIconButton as IconButton,
  TabPanel,
  UITabs,
} from '@xlab-team/ui-components';
import ModalRemoveData from 'containers/modals/ModalRemoveData';
import { DrawerInsightExplore } from '@antscorp/cdp-explore-package';
import { useMemo } from 'react';
import reducer from './reducer';
import saga from './saga';
import makeSelectVisitor, {
  makeSelectVisitorFilter,
  makeSelectVisitorColumn,
} from './selectors';
import { MODULE_CONFIG, BREADCRUMDATA } from './config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  init,
  getList,
  reset,
  updateValue,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { TableWrapper, TableRelative } from './styles';
import makeSelectVisitorCommon from '../selectors';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
// import TryVersion from '../../../../../containers/TryVersion';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import ControlTable from './ControlTable';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import useToggle from '../../../../../hooks/useToggle';
import Upload from '../../../../../containers/Upload';
import ModalExport from '../../../../../containers/modals/ModalExport';
import { OBJECT_NAME } from '../../utils';
import {
  getCurrentOwnerId,
  getCurrentUserId,
  getPortalId,
  getToken,
} from '../../../../../utils/web/cookie';
import { getDomainInsight } from '../../../MarketingHub/Journey/utils';
import ModalRemoveProfile from '../../../../../containers/modals/ModalRemoveProfile';

import DropdownViewAccess from '../../../../../components/common/DropdownViewAccess';
import { DATA_ACCESS_OBJECT } from '../../../../../utils/constants';
import { WrapperDataView } from '../../Customer/List/styles';
import { DrawerDetail, Icon } from '@antscorp/antsomi-ui';
import VisitorDetailModule from '../Detail/Loadable';
import { withRouter } from 'react-router-dom';
import { LabelModule } from '../../Customer/Detail/styles';
import { RequestAccessV2 } from '../../../../../components/Molecules/RequestAccessV2';
import { buildProfileURL } from '../../Customer/Main/utils';
import InsightIframe from '../../components/InsightIframe';
import { PROFILE_TEMPLATE_AVAILABLE_PORTAL_IDS } from '../../constants';

const MAP_TITLE = {
  navBar: {
    title: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_VISITOR, 'Visitors'),
    name: getTranslateMessage(TRANSLATE_KEY._FILTER_SEGMENT_VISITOR, 'Visitor'),
  },
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    // download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),

    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
    explore: getTranslateMessage(TRANSLATE_KEY._, 'EXPLORE'),
  },
};

const encryptConfig = { prefixes: [MODULE_CONFIG.key] };

export function ListVisitor(props) {
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });

  const { common, customer, filter, column } = props;
  const { main, table } = customer;
  const {
    viewData,
    viewInfoMapping,
    isDisplayRequestAccess,
    isEditPermission,
  } = main;
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalDelete, toggleModalDelete] = useToggle(false);

  const [activeTab, setActiveTab] = useState('activity');
  const [isOpenDetail, setIsOpenDetail] = useState(false);

  const searchParams = new URLSearchParams(window.location.search);
  const isShowOldVersion =
    searchParams.has('isOldMode') ||
    !PROFILE_TEMPLATE_AVAILABLE_PORTAL_IDS.includes(+getPortalId());

  useEffect(() => {
    props.init();
    return () => {
      props.reset();
    };
  }, []);

  useEffect(() => {
    if (!searchParams.has('id')) return;
    const tab = searchParams.get('tab');
    setActiveTab(tab);

    if (isShowOldVersion) {
      setIsOpenDetail(true);
    }

    return () => {
      setIsOpenDetail(false);
    };
  }, [window.location.search]);

  const callback = (type, data) => {
    if (type === 'FETCH_DATA') {
      props.fetchData();
    }
    if (type === 'ACTION_TABLE_DELETE') {
      toggleModalDelete();
    }
    if (type === 'APPLY_CONFIRM_DELETE') {
      const { isUpload } = data;
      if (isUpload) {
        toggleModalProcessing();
      } else {
        props.onRemove();
      }
    }
    if (type === 'DATA_ACCESS_ITEM') {
      const { item } = data;
      props.reset();
      props.onChangeDataView({
        viewData: item,
      });
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const columnsExport = React.useMemo(() => {
    const columnsAlias = get(column, 'columnObj.columns.columnsAlias', []);
    const attributeKeys = Object.keys(
      get(common, 'main.groupAttributes.map', {}),
    );

    return intersection(columnsAlias, attributeKeys);
  }, [column, common]);

  const toggleModalProcessing = () =>
    props.toggleModalProcessing({ isOpen: !main.isOpenModalProcessing });

  const breadcrumbs = useMemo(() => {
    const { viewId, viewDisplay } = viewData;
    const dataBreadcrumbs = BREADCRUMDATA.map(breadcrumb => {
      if (breadcrumb.type === 'main-visitor') {
        const newBreadcrumbData = {
          ...breadcrumb,
          isShowArrow: true,
        };
        if (!Object.keys(viewData).length) {
          newBreadcrumbData.display = 'Choose visitor view';
        } else if (viewId) {
          newBreadcrumbData.display = viewDisplay;
        } else if (viewData && viewData.itemTypeDisplay) {
          newBreadcrumbData.display = viewData.itemTypeDisplay;
        }
        return newBreadcrumbData;
      }
      return breadcrumb;
    });
    return dataBreadcrumbs;
  }, [viewData, viewData.viewId]);

  if (isDisplayRequestAccess) {
    const styleWrapper = {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      display: 'flex',
      backgroundColor: '#fff',
    };
    const serviceFn = ({ defaultService, message }) =>
      defaultService({
        body: {
          message,
          objectId: -1007,
          objectType: DATA_ACCESS_OBJECT.BO,
        },
      });

    return (
      <div style={styleWrapper}>
        <DropdownViewAccess
          value={viewData}
          viewAccessInfo={viewInfoMapping}
          callback={callback}
          stylePaper={{
            top: '45px !important',
            left: '245px !important',
          }}
          containerStyles={{
            position: 'absolute',
          }}
          existId="viewId"
        >
          <CustomHeader breadcrums={breadcrumbs} isFitContent />
        </DropdownViewAccess>
        <RequestAccessV2 requestService={serviceFn} />
      </div>
    );
  }

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Visitor/List/index.jsx">
      <UIHelmet title={MAP_TITLE.navBar.title} />
      <LayoutContent padding="0">
        <TableRelative>
          <TableWrapper isDisplayDataView={!!viewData.viewId}>
            <TableContainer
              columnActive={column.columnObj}
              table={table}
              encryptConfig={encryptConfig}
              isLoading={main.isLoading}
              moduleConfig={MODULE_CONFIG}
              columns={tableColumns}
              data={main.data}
              selectedIds={table.selectedIds}
              selectedRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              isSelectedAllPage={table.isSelectedAllPage}
              callback={callback}
              resizeColName="user_id"
              noDataText="No visitor found"
              ComponentControlTable={ControlTable}
              noCheckboxAction={viewData.viewId ? true : !isEditPermission}
              // widthFirstColumns={48}
              // initialWidthColumns={width > 1600 ? 291 : 218}
            >
              <>
                <Filters
                  encryptConfig={encryptConfig}
                  use="list"
                  rules={filter.rules}
                  moduleConfig={MODULE_CONFIG}
                  filterActive={filter.config.filterObj}
                  filterCustom={filter.config.library.filterCustom}
                  libraryFilters={filter.config.library.filters}
                  groups={common.main.groupAttributes.filterGroups}
                  isFilter={filter.config.design.isFilter}
                  isLoading={filter.config.isLoading}
                  content={
                    <DropdownViewAccess
                      value={viewData}
                      viewAccessInfo={viewInfoMapping}
                      callback={callback}
                      stylePaper={{
                        top: '110px !important',
                        left: '365px !important',
                      }}
                      containerStyles={{
                        position: '',
                      }}
                      existId="viewId"
                    >
                      {/* <CustomHeader breadcrums={breadcrumbs} isFitContent /> */}
                      <WrapperDataView>
                        <span>Data view: </span>
                        <span className="data-view">
                          {breadcrumbs[0].display}
                        </span>
                      </WrapperDataView>
                    </DropdownViewAccess>
                  }
                />
                <WrapActionTable
                  show={!filter.config.design.isFilter}
                  className="p-x-4"
                >
                  <div className="actionTable__inner">
                    <Search
                      moduleConfig={MODULE_CONFIG}
                      config={{
                        limit: 10,
                        page: 1,
                        sd: 'asc',
                        scope: 2,
                        itemTypeId: -1007,
                        itemTypeName: 'users',
                        propertyCode: 'user_id',
                        itemPropertyName: 'user_id',
                        isPk: 1,
                        systemDefined: 0,
                      }}
                      suggestionType="suggestion"
                      moduleLabel={MAP_TITLE.navBar.title}
                    />
                    <ModifyColumn
                      sort={table.sort}
                      moduleConfig={MODULE_CONFIG}
                      columnActive={column.columnObj}
                      columnCustom={column.library.columnCustom}
                      libraryColumns={column.library.columns}
                      defaults={MODULE_CONFIG.columnsDefault}
                      defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                      columns={column.columnObj.columns.columnsAlias}
                      groups={common.main.groupAttributes.groups}
                      isLoading={column.isLoading}

                      // handleConfirm={handleConfirmCustomizeColumn}
                    />
                    {!viewData?.viewId && (
                      <DrawerInsightExplore
                        use="all"
                        token={getToken()}
                        ownerId={getCurrentOwnerId()}
                        userId={getCurrentUserId()}
                        domain={getDomainInsight()}
                        styledWrapper={{
                          marginLeft: '5px',
                          ...(viewData.viewId || !Object.keys(viewData).length
                            ? { opacity: 0.5, pointerEvents: 'none' }
                            : {}),
                        }}
                        portalId={getPortalId()}
                        nameBO="Vistor"
                        itemTypeName="users"
                        itemTypeId={MODULE_CONFIG.objectId}
                        dataSourceId={-6666}
                        nameExplore="bo"
                        CustomButton={btnProps => (
                          <IconButton
                            {...btnProps}
                            iconName="explorer"
                            size="24px"
                            isVertical
                          >
                            {MAP_TITLE.action.explore.toUpperCase()}
                          </IconButton>
                        )}
                      />
                    )}
                    {!viewData.viewId
                      ? isEditPermission && (
                          <Upload itemTypeId={MODULE_CONFIG.objectId} />
                        )
                      : null}
                    <IconButton
                      iconName="file_download"
                      size="24px"
                      onClick={
                        table.paging.totalRecord === 0
                          ? () => {}
                          : toggleModalDownload
                      }
                      isVertical
                      disabled={table.paging.totalRecord === 0}
                    >
                      {MAP_TITLE.action.actExport.toUpperCase()}
                    </IconButton>
                  </div>
                </WrapActionTable>
              </>
            </TableContainer>
          </TableWrapper>
        </TableRelative>
      </LayoutContent>
      {/* <ModalDownload
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        filters={filter}
        itemTypeId={MODULE_CONFIG.objectId}
        itemTypeName="user"
        properties={column.columnObj.columns.columnsAlias}
      /> */}
      <InsightIframe
        src={buildProfileURL()}
        module="VISITOR"
        itemTypeId={-1007}
      />
      <ModalExport
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        objectName={OBJECT_NAME.VISITOR}
        object_type={MODULE_CONFIG.objectType}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        filters={filter}
        itemTypeId={+MODULE_CONFIG.itemTypeId || MODULE_CONFIG.objectId}
        viewId={viewData.viewId}
        itemTypeName="user"
        columns={columnsExport}
      />
      {/* <ModalRemoveData
        isOpen={isOpenModalRemove}
        toggle={toggleModalRemove}
        name={MAP_TITLE.navBar.name}
        objectType={MODULE_CONFIG.objectType}
        filter={filter}
        selectedRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        callback={callback}
        itemTypeId={MODULE_CONFIG.objectId}
      /> */}
      {/* <ModalProcessing
        isOpen={main.isOpenModalProcessing}
        toggle={toggleModalProcessing}
      /> */}
      {/* <ModalRemoveProfile
        isOpen={main.isOpenModalProcessing}
        toggle={toggleModalProcessing}
        callback={callback}
        isUpload
      /> */}
      <ModalRemoveProfile
        isOpen={isOpenModalDelete}
        callback={callback}
        toggle={toggleModalDelete}
        totalRecord={table.paging.totalRecord}
        selectedRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        onClose={() => toggleModalDelete(false)}
      />

      {/* Drawer detail visitor */}
      <DrawerDetail
        open={isOpenDetail}
        onClose={() => setIsOpenDetail(false)}
        menuProps={{
          items: [
            {
              icon: <Icon type="icon-ants-clock" />,
              label: 'Activity',
              key: 'activity',
            },
          ],
          selectedKeys: [activeTab],
          onClick: item => {
            setActiveTab(item.key);
            searchParams.set('tab', item.key);
            const newUrl = `${
              window.location.href.split('?')[0]
            }?${searchParams.toString()}`;
            window.history.pushState({ path: newUrl }, '', newUrl);
          },
        }}
        anchor="right"
        destroyOnClose
        headerProps={{
          children: <LabelModule className="label">Visitor 360</LabelModule>,
          height: 60,
          showBorderBottom: true,
          style: { padding: '0 15px' },
        }}
      >
        <VisitorDetailModule activeTab={activeTab} isDisplayContentDrawer />
      </DrawerDetail>
      {/* <TryVersion module="user-explorer/list" /> */}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  customer: makeSelectVisitor(),
  common: makeSelectVisitorCommon(),
  filter: makeSelectVisitorFilter(),
  column: makeSelectVisitorColumn(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },
    toggleModalProcessing: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL_PROCESSING`, params),
      );
    },
    onRemove: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@REMOVE_VISITOR`));
    },
    onChangeDataView: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_DATAVIEW`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  withRouter,
)(ListVisitor);

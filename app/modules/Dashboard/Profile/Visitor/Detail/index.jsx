/* eslint-disable react/prop-types */
import React, { useEffect, memo, useMemo, useState, useLayoutEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';

import Grid from '@material-ui/core/Grid';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { UILoading as Loading, TabPanel } from '@xlab-team/ui-components';

// import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';

import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import queryString from 'query-string';
import reducer from './reducer';
import saga from './saga';
import makeSelectVisitorDetail, {
  makeSelectEventsPeformance,
  makeSelectEventTracking,
  makeSelectVisitorName,
} from './selectors';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_LIST } from './widgets/VisitorList/config';

import { init, updateValue, reset } from '../../../../../redux/actions';

import {
  ContainerInfo,
  WrapperList,
  TabsExtend,
  Mt16,
  WrapperDashboardCustom,
  WrraperCustomerNotFound,
  HackStyleLayoutContent,
} from './styles';
import IdentityGraph from './widgets/IdentityGraph';
import WidgetProductRecommendation from '../../../../../containers/Widgets/ProductRecommendation';
import WidgetInfo from '../../../../../containers/Widgets/WidgetInfo';
import WorkCloud from '../../../../../containers/Widgets/WorkCloud';
import ActivityViewer from './widgets/ActivityViewer';
import VisitorList from './widgets/VisitorList';
import AudienceSegment from '../../../../../containers/Widgets/AudienceSegment';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
// import makeSelectVisitorCommon from '../selectors';
// import VisitorServices from '../../../../../services/CustomerV2';
import VisitorServices from '../../../../../services/Visitor';
import DataViewService from '../../../../../services/DataView';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { getBreadcrums } from './utils';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import { isUrlEmbedded } from '../../../../../utils/web/utils';
import {
  LabelModule,
  WrapperContentDrawer,
} from '../../Customer/Detail/styles';
import { getPortalId } from '../../../../../utils/web/cookie';
import APP from '../../../../../appConfig';

const MAP_TITLE = {
  visitor: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_VISITOR, 'Visitor'),
  overview: getTranslateMessage(
    TRANSLATE_KEY._TAB_CUSTOMER_OVERVIEW,
    'Overview',
  ),
  activity: getTranslateMessage(
    TRANSLATE_KEY._TAB_CUSTOMER_ACTIVITY,
    'Activity',
  ),
};

const widthStyle = {
  width: '100vh',
};

function StyleWrapperDashboard(props) {
  const { isCollapse } = props;
  if (props.isLoading) {
    return (
      <WrapperDashboardCustom>
        <div className="container-loading">
          <Loading isLoading={props.isLoading} iswhite />
        </div>
      </WrapperDashboardCustom>
    );
  }
  if (props.code !== 200) {
    return (
      <WrapperDashboardCustom>
        <WrraperCustomerNotFound>Visitor not found</WrraperCustomerNotFound>
      </WrapperDashboardCustom>
    );
  }
  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Visitor/Detail/index.jsx">
      <WrapperDashboardCustom isCollapse={isCollapse}>
        {props.children}
      </WrapperDashboardCustom>
    </ErrorBoundary>
  );
}

const encryptConfig = {
  prefixes: [
    `${MODULE_CONFIG.key}@@OBJECT_INFO@@`,
    // `${MODULE_CONFIG_LIST.key}`,
  ],
};

export function CustomerDetail(props) {
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga });

  const { customer, history } = props;
  const { main } = customer;
  const { isCollapse } = main;

  const queryParams = queryString.parse(history.location.search);

  const { userId } = props.match.params;

  const [dataViewInfo, setDataViewInfo] = useState({});

  useLayoutEffect(() => {
    // redirect to new layout detail visitor v2
    if (userId && main.activeTab) {
      const newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/profile/visitors/list?id=${userId}&tab=${
        main.activeTab
      }`;
      history.push(newUrl);
    }
  }, []);

  useEffect(() => {
    if (queryParams.view_id) {
      (async () => {
        const {
          code,
          data: dataViewData,
        } = await DataViewService.dataView.getDetailInfo({
          viewId: queryParams.view_id,
        });

        if (code === 200) {
          setDataViewInfo(dataViewData);
        }
      })();
    }
  }, [queryParams.view_id]);

  useEffect(() => {
    props.init({
      userId: userId || queryParams.id,
      mode: 'detail',
      queryParams,
    });

    return () => {
      if (props.isDisplayContentDrawer) {
        // redirect to origin url listing
        history.push(history.location.pathname);
      }
      props.reset();
    };
  }, [userId, queryParams.id]);

  useEffect(() => {
    if (props.activeTab) {
      onChangeTab(props.activeTab);
    }
  }, [props.activeTab]);

  const onChangeTab = tab => {
    if (tab !== main.activity) {
      props.onChangeTab({ tab, queryParams });
    }
  };

  const renderContent = activeTab => {
    let content = null;

    if (activeTab === 'activity') {
      content = (
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <WidgetInfo
              isCollapse={isCollapse}
              activeTab={main.activeTab}
              encryptConfig={encryptConfig}
              isLoading={main.widgets.userInfo.isLoading}
              info={main.info.properties}
              groupInformation={main.widgets.userInfo.groups.groupInformation}
              information={main.widgets.userInfo.groups.information}
              others={main.widgets.userInfo.groups.others}
              showOtherGroup
              groups={main.groupAttributes.groups}
              properties={main.widgets.userInfo.properties}
              isDisplayContentDrawer={props.isDisplayContentDrawer}
              // callback={callback}
            />
            <Mt16>
              <IdentityGraph
                activeTab={main.activity}
                componentId={customer.main.userAttributesComponentId}
                userId={main.userId}
                customerAttributes={customer.main.customerAttributes}
                isDisplayContentDrawer={props.isDisplayContentDrawer}
              />
            </Mt16>
            {/* <AudienceSegment
            limit={5}
            objectId={main.userId}
            ServiceFn={VisitorServices.cards.segments}
          /> */}
            {/* <Mt16>
              <WorkCloud
                objectId={main.userId}
                id
                dateRange={main.dateRange}
                ServiceFn={VisitorServices.cards.performance}
              />
            </Mt16> */}
          </Grid>
          <Grid item xs={8} style={widthStyle}>
            {!props.isDisplayContentDrawer && (
              <WidgetProductRecommendation
                use="user"
                objectId={main.userId}
                dateRange={main.dateRange}
              />
            )}
            <Mt16 style={{ marginTop: props.isDisplayContentDrawer ? 0 : '' }}>
              <ActivityViewer
                objectId={main.userId}
                isDisplayContentDrawer={props.isDisplayContentDrawer}
              />
            </Mt16>
          </Grid>
        </Grid>
      );
    }

    return content;
  };

  const showMoreActivity = tab => {
    const el = document.getElementById('main-content');

    el.scrollTo({
      top: 330,
      left: 0,
      behavior: 'smooth', // or can get `auto` variable
    });
    props.onChangeTab({ tab });
  };

  const onToggleCollapse = () => {
    props.onToggleCollapse();
  };
  const pageTitle = `${MAP_TITLE.visitor} - ${MAP_TITLE[main.activeTab]}`;

  const breadcrums = useMemo(() => {
    const name = props.customer.main.info.properties.user_id.value;

    return getBreadcrums(name, dataViewInfo.viewDisplay);
  }, [
    props.customer.main.info.properties.user_id.value,
    dataViewInfo.viewDisplay,
  ]);
  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Visitor/Detail/index.jsx">
      {!isUrlEmbedded() && !props.isDisplayContentDrawer && (
        <CustomHeader breadcrums={breadcrums} />
      )}
      <UIHelmet title={pageTitle} />
      <ContainerInfo className={`${isUrlEmbedded() ? 'm-top-0' : ''}`}>
        {!props.isDisplayContentDrawer ? (
          <>
            <WrapperList isCollapse={isCollapse}>
              <VisitorList
                isCollapse={isCollapse}
                onToggleCollapse={onToggleCollapse}
              />
            </WrapperList>
            <StyleWrapperDashboard
              isCollapse={isCollapse}
              isLoading={main.isLoading}
              code={main.code}
            >
              <HackStyleLayoutContent isCollapse={isCollapse} />
              <TabsExtend
                noBorderBottom
                activeTab={main.activeTab}
                onChange={onChangeTab}
                className="tab-nav-tab-cus-detail"
              >
                <TabPanel
                  marginTop
                  label={getTranslateMessage(
                    TRANSLATE_KEY._TAB_CUSTOMER_ACTIVITY,
                    'Activity',
                  )}
                  eventKey="activity"
                >
                  {renderContent(main.activeTab)}
                </TabPanel>
              </TabsExtend>
            </StyleWrapperDashboard>
          </>
        ) : (
          <WrapperContentDrawer>
            <div className="content">{renderContent(main.activeTab)}</div>
          </WrapperContentDrawer>
        )}
      </ContainerInfo>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  customer: makeSelectVisitorDetail(),
  // common: makeSelectVisitorCommon(),
  eventsPerfomance: makeSelectEventsPeformance(),
  eventTracking: makeSelectEventTracking(),
  customerName: makeSelectVisitorName(),
});

function mapDispatchToProps(dispatch) {
  return {
    reset: () => {
      dispatch(reset(MODULE_CONFIG.key));
    },
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@TAB`, params));
    },
    onToggleCollapse: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@COLLAPSE`));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withConnect,
)(memo(CustomerDetail));

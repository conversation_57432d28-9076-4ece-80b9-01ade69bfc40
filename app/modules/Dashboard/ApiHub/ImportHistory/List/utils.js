import _isEmpty from 'lodash/isEmpty';
import { getLabelImport } from '../../../../../utils/web/processStatus';
import {
  CellArray,
  CellDate,
  CellImportHistoryId,
  CellNumber,
  CellImportStatus,
  CellText,
  CellMainObject,
} from '../../../../../containers/Table/Cell';
import { initNumberId } from '../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
// import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../appConfig';
import {
  getImportMethodLabel,
  getLabelImportHistoryErrorInfo,
} from '../../../../../services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.import_id,
      import_status: getLabelImport(parseInt(tmp.import_status)),
      original_status: tmp.import_status,
      import_method: getImportMethodLabel(tmp.import_method),
      response_code: getLabelImportHistoryErrorInfo(tmp.response_code),
      c_user_id: tmp.c_user_name,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(columnID, columnName, columnsActive) {
  const columns = [
    {
      Header: columnID.label,
      id: columnID.value,
      accessor: columnID.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellImportHistoryId,
      getIsDisabledEditName: () => false,
    },
  ];
  if (columnName != null) {
    columns.push({
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellText,
      getIsDisabledEditName: () => false,
    });
  }
  columns.push({
    Header: 'Info Remaining',
    columns: buildTableColumns(columnsActive),
  });
  return columns;
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    // console.log("213",property.value)
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    // if (['c_user_id', 'u_user_id'].includes(property.value)) {
    //   column.placement = 'left';
    // }
    // if (['import_status'].includes(property.value)) {
    //   column.className = `${dataType} ${property.value} txt-status`;
    // }
    // ['import_id'].forEach(each => {
    //   if (each === property.propertyCode) {
    //     column.displayFormat = initNumberId();
    //   }
    // });
    // ['end_date'].forEach(each => {
    //   if (each === property.propertyCode) {
    //     column.alternativeLabel = MAP_TITLE.storyEndNever;
    //   }
    // });
    // ['story_id'].forEach(each => {
    //   if (each === property.propertyCode) {
    //     column.displayFormat = initNumberId();
    //   }
    // });
    if (property.value === 'import_status') {
      column.Cell = CellImportStatus;
      column.placement = 'import_status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
    }

    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {
  // campaign_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
  item_type_display: CellText,
  // import_method: CellText,
  // story_status: CellJourneyStatus,
};

export function getModuleConfig(moduleConfig, storyId, objectType, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = storyId;
  newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};
export const mapDataToConfigFilter = (
  channelActive,
  isMapConfigSuggestionForDetails,
  moduleConfig,
  isUseSelectorApi,
) => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: isUseSelectorApi ? null : 100,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '6-campaign_name',
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== moduleConfig.objectId // Khác object id để check với trường hợp channel all
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: `${
                isMapConfigSuggestionForDetails ? 'story_id' : 'story_type'
              }`,
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigFilterDetail = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
  };
  if (channelActive && channelActive.value !== 0) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_id',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    isGetOnlySuggestParams: 0,
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_type',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export function getBreadcrums(channelActive) {
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_ALL_IMPORT_HISTORY,
      'All History',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

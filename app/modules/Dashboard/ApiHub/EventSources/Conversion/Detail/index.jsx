/* eslint-disable react/prop-types */
import React, { useEffect, memo, useMemo, useCallback } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';

import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  UILoading as Loading,
  TabPanel,
  UIButton,
} from '@xlab-team/ui-components';
// import TableContainer from 'containers/Table';
import { useInjectReducer } from 'utils/injectReducer';
import HistoryIcon from '@material-ui/icons//History';
import { useInjectSaga } from 'utils/injectSaga';
import { useParams, useHistory, useLocation } from 'react-router-dom';

import reducer from './reducer';
import saga from './saga';
import LayoutContent from '../../../../../../components/Templates/LayoutContent';
import {
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { StyleTabs, WrapperDetail } from './styles';
import { makeSelectDetailDomainMain, makeSelectActiveRow } from './selectors';
import LayoutLoading from '../../../../../../components/Templates/LayoutContent/LayoutLoading';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import { updateUrl } from '../../../../../../utils/common';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';
import ConversionServices from '../../../../../../services/Conversion';

import SettingsPage from './Settings';
import AttributesPage from './Attributes';
// import AssignEventAttribute from '../Create/Design/Assign';
import PageNotFound from '../../../../../../components/Templates/LayoutContent/PageNotFound';
import { isEmpty } from 'lodash';
import {
  Button,
  DrawerDetail,
  EditableName,
  Empty,
  Icon,
} from '@antscorp/antsomi-ui';
import DetailModuleHistory from './DetailVersion/Loadable';
import {
  BoxHeaderLeft,
  BoxHeaderRight,
  StyledIcon,
  WrapperHeader,
} from '../../../../../../containers/Drawer/DrawerDetailDataObject/styled';
import { makeSelectVersionDetail } from './DetailVersion/selectors';
import makeSelectCreateSettings, {
  makeSelectCreateSettingsDataConfig,
} from '../Create/Design/Settings/selectors';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE } from '../Create/config';
import { useUpdateObjectName } from '../../../../../../hooks';
import { TableIcon } from '@antscorp/antsomi-ui/es/components/icons';
const styleOverFlowHidden = {
  // overflow: 'auto !important',
  // overflow: 'hidden scroll',
  justifyContent: 'flex-start',
};

const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._TAB_EVENT_EVENT, 'Event'),
  settings: getTranslateMessage(
    TRANSLATE_KEY._TAB_DESTINATION_SETTING,
    'Settings',
  ),
  attribute: getTranslateMessage(TRANSLATE_KEY._TITL_ATTRIBUTE, 'Attributes'),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
  restoreThisVersion: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Restore this version',
  ),
};
export function DetailPage(props) {
  const history = useHistory();

  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });
  // const [selectedRows, setSelectedRows] = useState([]);
  const { ...restParams } = useParams();
  const location = useLocation();
  const searchParams = new URLSearchParams(location?.search);
  const tab = searchParams.get('tab') || 'settings';
  const versionRestoreId = searchParams.get('versionRestore') || '';
  const versionId = searchParams.get('version') || null;
  const conversionId = props.conversionId || props.conversionIdURL;
  const {
    main = {},
    mainSetting,
    activeRow = {},
    isViewMode = false,
    settings = {},
    errors = [],
    eventPropertyService,
    getFullEventTrackingService,
    getFullEventTrackingMapService,
    isUsingJourneyTemplate,
    showAllSource,
    versionDetail = {},
    dataConfig,
    onSave,
    design,
  } = props;
  useEffect(() => {
    if (!isEmpty(settings)) {
      props.init({
        settings,
      });
    } else {
      if (!conversionId) return null;

      props.init({
        conversionId,
        tab,
        // eventAction,
        // eventCategory,
        // insightPropertyId,
        versionRestoreId,
      });
    }

    return () => {
      props.reset();
    };
  }, [conversionId, props.conversionIdURL, versionRestoreId]);
  const {
    isLoading: isLoadingConversionName,
    isError: isErrorConversionName,
    error: errorConversionName,
    updateName: updateConversionName,
    isSuccess: isSuccessConversionName,
  } = useUpdateObjectName({
    serviceFunc: ConversionServices.data.updateName,
    options: {},
  });
  const onChangeTab = value => {
    props.onChangeTab(value);
    const endParam = value;
    searchParams.delete('version');
    searchParams.delete('versionRestore');
    searchParams.set('tab', endParam);
    if (value === 'histories') {
      searchParams.set('version', activeRow.latestVersion || activeRow.version);
    }
    const newSearch = searchParams.toString();
    history.replace({ search: newSearch });
    // const newUrl = `${
    //   APP.PREFIX
    // }/${getPortalId()}/api-hub/event-sources/conversion/detail/${conversionId}/${endParam}`;

    // updateUrl(newUrl);
  };

  const onClickBackToList = useCallback(() => {
    history.push(
      `${APP.PREFIX}/${getPortalId()}/api-hub/event-sources/conversion`,
    );
  }, []);
  const handleCloseDrawer = useCallback(() => {
    if (isSuccessConversionName) {
      props.fetchData();
    }
    history.push(
      `${APP.PREFIX}/${getPortalId()}/api-hub/event-sources/conversion`,
    );
  }, [isSuccessConversionName]);
  const {
    dataAPIS: { versionHistoryListing },
  } = versionDetail;
  const DETAIL_TABS = useMemo(() => {
    const tabs = [
      {
        icon: <Icon type="icon-ants-pencil" />,
        label: 'Settings',
        key: 'settings',
      },
      {
        icon: <TableIcon />,
        label: 'Listing attribute',
        key: 'attributes',
      },
      {
        icon: <Icon type="icon-ants-version-history" />,
        label: 'Conversion History',
        key: 'histories',
      },
    ];

    return tabs;
  }, [tab]);
  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        // props.fetchData();
        break;
      }
      case 'EDIT_NAME_SUCCESS': {
        // props.updateName(data);
        break;
      }
      default:
        break;
    }
  };

  // if (main.isLoading && !isViewMode) {
  //   return <Loading isLoading isWhite />;
  // }
  const onRestore = () => {
    props.onChangeTab('settings');
    history.push(
      `${
        APP.PREFIX
      }/${getPortalId()}/api-hub/event-sources/conversion?detail=${conversionId}&tab=settings&versionRestore=${versionId}`,
    );
  };

  const renderContentStep = () => {
    switch (main.activeTab) {
      case 'attributes':
        return <AttributesPage />;
      case 'settings':
        return <SettingsPage design={design} activeRow={activeRow} />;
      case 'histories':
        return <DetailModuleHistory />;
      default:
        return <Empty />;
    }
  };

  const renderContent = () => {
    // console.log('props.activeRow', props.activeRow);
    if (Object.keys(activeRow).length === 0 && !isViewMode) {
      return (
        <PageNotFound
          labelButton={getTranslateMessage(TRANSLATE_KEY._, 'Back to list')}
          labelPageNotFound={getTranslateMessage(
            TRANSLATE_KEY._,
            'Conversion Not Found',
          )}
          onClick={onClickBackToList}
        />
      );
    }

    if (isViewMode) {
      return (
        <SettingsPage
          activeRow={activeRow}
          isViewMode={isViewMode}
          errors={errors}
          eventPropertyService={eventPropertyService}
          getFullEventTrackingService={getFullEventTrackingService}
          getFullEventTrackingMapService={getFullEventTrackingMapService}
          isUsingJourneyTemplate={isUsingJourneyTemplate}
          showAllSource={showAllSource}
          conversionId={conversionId}
          design="update"
        />
      );
    }

    return renderContentStep();
  };
  const renderContentSetting = () => {
    return (
      <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/Events/Detail/index.jsx">
        {!isViewMode && <UIHelmet title={main.valueObjectName.value.EN} />}
        <LayoutContent
          style={styleOverFlowHidden}
          padding={isViewMode && '0 15px'}
          // padding="3.25rem 0.75rem 0.0rem 0.75rem"
        >
          <WrapperDetail className="" isLoading={main.isLoading}>
            {/* <Loading isLoading={props.main.isLoading} isWhite /> */}
            <LayoutLoading isLoading={main.isLoading}>
              {renderContent()}
            </LayoutLoading>
          </WrapperDetail>
        </LayoutContent>
      </ErrorBoundary>
    );
  };

  const onBlurConversionName = () => {
    if (isEmpty(main.valueObjectName.value.EN)) return;
    updateConversionName({
      conversionId,
      data: {
        conversionName: main.valueObjectName.value.EN,
      },
    });
  };
  return isViewMode
    ? renderContentSetting()
    : !main.isLoading && (
        <DrawerDetail
          open={conversionId}
          menuProps={{
            items: DETAIL_TABS,
            selectedKeys: [main.activeTab],
            onClick: item => {
              if (item) {
                onChangeTab(item.key);
              }
            },
          }}
          headerProps={{
            showBorderBottom: true,
            height: '50px',
            style: {
              padding: '0 15px',
            },
            children: (
              <WrapperHeader>
                <BoxHeaderLeft>
                  <EditableName
                    onBlur={onBlurConversionName}
                    value={main.valueObjectName.value.EN}
                    required
                    onChange={name => {
                      props.updateName({
                        value: name,
                      });
                      // onChangeData('conversionName', {
                      //   DEFAULT_LANG: 'EN',
                      //   EN: name,
                      // })
                    }}
                    loading={isLoadingConversionName}
                    isError={isErrorConversionName}
                    error={errorConversionName}
                  />
                </BoxHeaderLeft>
                <BoxHeaderRight>
                  {versionId ? (
                    <Button
                      disabled={
                        Number(versionHistoryListing.data[0]?.version) ==
                        versionId
                      }
                      onClick={onRestore}
                      type="primary"
                    >
                      {MAP_TITLE.restoreThisVersion}
                    </Button>
                  ) : (
                    main.activeTab === 'settings' && (
                      <Button
                        type="primary"
                        onClick={() => onSave()}
                        disabled={
                          mainSetting?.isDoing ||
                          mainSetting?.isLoadingConfigFields ||
                          !dataConfig?.isValidate
                        }
                      >
                        {MAP_TITLE.labelSave}
                      </Button>
                    )
                  )}
                </BoxHeaderRight>
              </WrapperHeader>
            ),
          }}
          // fullScreen={isFullScreen}
          onClose={handleCloseDrawer}
        >
          {renderContentSetting()}
        </DrawerDetail>
      );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectDetailDomainMain(),
  activeRow: makeSelectActiveRow(),
  versionDetail: makeSelectVersionDetail(),
  dataConfig: makeSelectCreateSettingsDataConfig(),
  mainSetting: makeSelectCreateSettings(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB`, params));
    },
    updateName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_NAME`, params));
    },
    onSave: value => dispatch(update(`${MODULE_CONFIG_CREATE.key}`, value)),
    fetchData: params => {
      dispatch(getList('conversion-list', params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  memo,
)(DetailPage);

import styled from 'styled-components';
import { fonts } from 'utils/variables';
import colors from 'utils/colors';
import PerformanceChart from 'containers/Widgets/PerformanceChart';
import LayoutContent from '../../../../../../../components/Templates/LayoutContent';

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  padding: 3.25rem 0.75rem 0.2rem 0.75rem;
`;

// export const TableWrapper = styled.div`
//   position: absolute;
//   width: 100%;
//   height: 100%;
//   top: 0;
//   left: 0;
//     /* ${breakdownMd(
//       css`
//         height: calc(100vh - 108px);
//         top: 108px;
//         left: 0.75rem;
//         right: 0.75rem;
//       `,
//     )} */
// `;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  height: 100%;
  .table {
    .header {
      .tr {
        .type {
          justify-content: flex-start;
        }
      }
    }
    .body {
    }
    .footer {
      width: auto !important;
      box-shadow: none !important;
      .tr:last-of-type {
        display: none !important;
      }
      .tr {
        .td {
          background-color: ${colors.whitesmoke};
          border-right: 1px solid ${colors.platinum};
          justify-content: flex-end;
          font-family: ${fonts.roMedium};
          font-weight: 500;
          &:nth-child(3) {
            /* box-shadow: 2px 0 2px ${colors.pastelGray} !important; */
            /* box-shadow: -6px 0 6px 0 rgba(0, 0, 0, 0.09); */
            box-shadow: 2px 0px 2px #ccc;

            position: sticky !important;
            /* left: 124px; */
            left: 328px;
            z-index: 3;
            justify-content: flex-start;
          }
          &:nth-child(2) {
            position: sticky !important;
            /* left: 48px; */
            left: 48px;
            z-index: 3;
          }
        }
      }
    }
  }
`;

export const LayoutContentCampaign = styled(LayoutContent)`
  padding: 0 !important;
`;

export const PerformanceChartCampaign = styled(PerformanceChart)`
  margin-top: 0rem !important;
`;

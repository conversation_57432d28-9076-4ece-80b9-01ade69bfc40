import _isEmpty from 'lodash/isEmpty';
import {
  CellText,
  CellMainEventAttribute,
  CellDate,
  CellNumber,
  CellArray,
} from '../../../../../../../containers/Table/Cell';
import CellExpanded from '../../../../../../../containers/Table/Cell/Expanded';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import {
  DATA_INFO,
  MAP_DATA_TYPE,
} from '../../../../../../../services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.event_property_name,
    // label: item.event_property_display,
    label: item.translate_label,
    type_display:
      (DATA_INFO.storage_type.map[`${item.type}`] || {}).label || '',
    is_required_display:
      (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
    subRows: mapSubRows(item.items || []),
    data_type_display: (MAP_DATA_TYPE[`${item.data_type}`] || {}).label || '',
  }));

export const mapSubRows = subRows =>
  subRows.map(item => ({
    ...item,
    label: item.item_property_display,
    type_display:
      (DATA_INFO.storage_type.map[`${item.type}`] || {}).label || '',
    is_required_display:
      (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(columnName = {}, columnsActive = {}) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: 'label',
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      disableSortBy: true,
      // Cell: CellMainEventAttribute,
      // getIsDisabledEditName: () => false,
      // Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      className: 'main-expanded-style',
      Cell: CellExpanded,
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  is_required: 'is_required_display',
  data_type: 'data_type_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  type: CellText,
  is_required: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export function getModuleConfig(moduleConfig, storyId) {
  const newModuleConfig = { ...moduleConfig };
  // newModuleConfig.objectId = storyId;
  // case lỗi
  return newModuleConfig;
}

import _isEmpty from 'lodash/isEmpty';
import {
  CellText,
  CellMainEventConversion,
  CellDate,
  CellNumber,
  CellArray,
  CellCurrency,
  CellToggleWithStyle,
  CellStatusArchiveSegment,
  CellProcessStatus,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { initNumberId } from '../../../../../../utils/web/portalSetting';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { DATA_INFO } from '../../../../../../services/Abstract.data';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';
import {
  getLabelStatusArchive,
  getLabelStatusInActive,
} from '../../../../../../utils/web/processStatus';
import { generateKey } from '../../../../../../utils/common';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../../utils/web/cookie';
import APP from '../../../../../../appConfig';
import { makeUrlPermisison } from '../../../../../../utils/web/permission';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};
const MAP_AUDIENT_TYPE = {
  visitor: {
    label: getTranslateMessage(
      TRANSLATE_KEY._FILTER_SEGMENT_VISITOR,
      'Visitor',
    ),
  },
  customer: {
    label: getTranslateMessage(
      TRANSLATE_KEY._FILTER_SEGMENT_CUSTOMER,
      'Customer',
    ),
  },
};
export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.conversion_id,
    process_status: getLabelStatusInActive(Number(item.status)),
    type_display:
      (DATA_INFO.storage_type.map[`${item.storage_type}`] || {}).label || '',
    audience_type: MAP_AUDIENT_TYPE[item.audience_type]?.label,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnId,
  columnStatus,
  columnsActive,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggleWithStyle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      disableSortBy: true,
      // className: `${columnStatus.value}`,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainEventConversion,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: columnId.label,
      id: columnId.value,
      accessor: columnId.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      displayFormat: initNumberId(),
      placement: 'right',
      right: true,
      sticky: 'left',
      Cell: CellNumber,
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
    if (dataType === 'number') {
      if (property.value === 'conversion_id') {
        column.displayFormat = initNumberId();
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      column.right = true;
    }
    if (property.value === 'conversion_status') {
      column.Cell = CellProcessStatus;
      column.placement = 'status';
    }
  });
  return columns;
}

const MAP_ACCESSOR = {
  storage_type: 'type_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const {
    isSelectedAll,
    activeRows,
    totalRecord,
    rules,
    extendParams: {},
  } = oldData;
  const objectIds = [];
  if (!isSelectedAll)
    activeRows.forEach(each => {
      objectIds.push(each.event_tracking_name);
    });
  const params = {
    data: {
      totalSelected: 1,
      isCheckUsedStatus: 0,
      objectIds,
      total_selected: isSelectedAll ? totalRecord : activeRows.size,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [
              {
                AND: [],
              },
            ],
          },
    },
  };
  return params;
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.event_tracking_name,
      label: item.event_name_display,
    });
  });
  return data;
};
export const mapTreeData = list => {
  const dataOut = [];
  if (Object.keys(list).length > 0) {
    Object.keys(list).forEach(each => {
      dataOut.push({
        title: mapNameModule[each].label,
        id: mapNameModule[each].id,
        key: generateKey(),
        children: list[each].map(item => ({
          ...item,
          title:
            item.item_property_display ||
            item.story_name ||
            item.model_name ||
            item.segment_display ||
            item.object_name,
          id: item.id,
        })),
      });
    });
  }
  return dataOut;
};
const mapNameModule = {
  STORY: {
    label: 'Journey',
    id: 3,
  },
  SEGMENT: {
    label: 'Segment',
    id: 2,
  },
  AM: {
    label: 'Analytic Model',
    id: 4,
  },
  BO_ATTRIBUTE: {
    label: 'Attribute',
    id: 1,
  },
};
export const linkToObjectRelated = data => ({
  BO_ATTRIBUTE: `${
    APP.PREFIX
  }/${getPortalId()}/api-hub/objects/owner?ui=detail-BO-drawer&itemTypeId=${
    data.id
  }&tab=attributes&itemPropertyName=${data.object_property_name}`,
  SEGMENT: `${APP.PREFIX}/${getPortalId()}/profile/segments/${data.id ||
    {}}/detail`,
  STORY: makeUrlPermisison(
    `${
      APP.PREFIX
    }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
      data.channel_id
    }/detail/${data.id}/${'settings'}`,
  ),
  AM: `${APP.PREFIX}/${getPortalId()}/api-hub/analytics-models/detail/${
    data.id
  }/fields`,
});

/* eslint-disable indent */
/* eslint-disable camelcase */
import { subMinutes } from 'date-fns';
import _isEmpty from 'lodash/isEmpty';
import { isEqual, pick } from 'lodash';
import { toConditionAPI } from '../../../../../../../containers/Filters/utils';
import {
  CellText,
  CellMainEvent,
  CellDate,
  CellNumber,
  CellArray,
  CellToggle,
  CellTimeLive,
} from '../../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import {
  DATA_INFO,
  labelDisable,
  labelEnable,
} from '../../../../../../../services/Abstract.data';
import { PortalDate } from '../../../../../../../utils/date';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};
const DATA_STATUS = {
  0: { ...labelDisable },
  1: { ...labelEnable },
};

export const defaultMapping = item => ({
  ...item,
  id: item.event_tracking_name,
  type_display: (DATA_INFO.storage_type.map[`${item.type}`] || {}).label || '',
  priority: DATA_STATUS[item.is_priority || 0].label || '',
  streamJourney: DATA_STATUS[item.is_stream_journey || 0].label || '',
  useRecommend: DATA_STATUS[item.is_use_recommend || 0].label || '',
  activityProfile: DATA_STATUS[item.is_activity_profile || 0].label || '',
});

export const serializeData = (data, portalId, insightPropertyType) =>
  mapBluePrint(data, item => ({
    portalId,
    insightPropertyType,
    ...defaultMapping(item),
  }));

export const mapEvents = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.eventTrackingName,
    value: item.eventTrackingName,
    label: item.translateLabel || item.eventNameDisplay,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggle,
      placement: 'center',
      className: `${columnStatus.value}`,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainEvent,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    if (dataType === 'number') {
      if (property.value === 'ttl') {
        // column.displayFormat = initNumberId();
        column.Cell = CellTimeLive;
        column.className = `txt-left ${dataType} ${property.value}`;
      }
    }
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  is_priority: 'priority',
  is_activity_profile: 'activityProfile',
  is_stream_journey: 'streamJourney',
  is_use_recommend: 'useRecommend',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = oldData => {
  const {
    isSelectedAll,
    activeRows,
    totalRecord,
    rules,
    extendParams: { insightPropertyId },
  } = oldData;
  const events = [];
  if (!isSelectedAll)
    activeRows.forEach(each => {
      events.push({
        event_category_id: each.event_category_id,
        event_action_id: each.event_action_id,
      });
    });
  const params = {
    data: {
      insight_property_id: insightPropertyId,
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      events,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [
              {
                AND: [],
              },
            ],
          },
    },
  };
  return params;
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  // const {} = activeRows;
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.event_tracking_name,
      label: item.event_name_display,
    });
  });
  return data;
};

export const initHourValue = () => ({
  selection: 'last_6_hours',
  value: {
    fromDate: subMinutes(new PortalDate(), 6 * 60).getTime(),
    toDate: new PortalDate().getTime(),
  },
});

export const getRowDataByBaseEventInfo = (events, baseEventInfo) => {
  const compareKeys = [
    'event_action_id',
    'event_category_id',
    'event_tracking_name',
  ];

  const index = events.findIndex(event =>
    isEqual(pick(baseEventInfo, compareKeys), pick(event, compareKeys)),
  );

  return { index, data: index !== -1 ? events[index] : null };
};

export const toPortalTimezoneDuration = timeRange => {
  if (!timeRange || !timeRange.fromDate || !timeRange.toDate) {
    return timeRange;
  }

  return {
    fromDate: new PortalDate(timeRange.fromDate).getTime(),
    toDate: new PortalDate(timeRange.toDate).getTime(),
  };
};

/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { useEffect, memo, useMemo } from 'react';
import { TabPanel } from '@xlab-team/ui-components';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';

import ErrorBoundary from 'components/common/ErrorBoundary';
// import TableContainer from 'containers/Table';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';

import reducer from './reducer';
import saga from './saga';
import LayoutContent from '../../../../../../components/Templates/LayoutContent';
import { init, reset, updateValue } from '../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import {
  StyleTabs,
  WrapperDetail,
  StyleWrapper,
  WrapperNoData,
} from './styles';
import { makeSelectDetailDomainMain } from './selectors';
import LayoutLoading from '../../../../../../components/Templates/LayoutContent/LayoutLoading';
import { getBreadcrums } from './utils';
import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import { updateUrl } from '../../../../../../utils/common';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';

import SettingsPage from './Settings';
import EventsPage from './Events';
import AssignEventAttribute from './Assign/index';
import AssignEvents from './AssignEvents/index';
import { makeSelectSourceActive } from '../selectors';
import NoDataComponent from '../../../../../../components/common/NoDataFound';
import DrawerSetting from '../Create/Loadable';

const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._TITL_EVENT, 'Events'),
  settings: getTranslateMessage(
    TRANSLATE_KEY._TAB_DESTINATION_SETTING,
    'Settings',
  ),
  guideNoDestYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_ASSIGN_EVENT,
    "You haven't assigned any Event yet, first of all, you need to create a Source",
  ),
};
export function DetailPage(props) {
  const { tab, sourceId } = props;
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });
  const { main, activeRow = {} } = props;

  useEffect(() => {
    props.init({
      insightPropertyId: sourceId,
      tab,
    });

    return () => {
      props.reset();
    };
  }, [sourceId]);

  // const breadcrums = useMemo(() => getBreadcrums(activeRow.label), [
  //   activeRow.label,
  // ]);

  // const renderContent = () => {
  //   if (Object.keys(activeRow).length === 0) {
  //     return (
  //       <StyleTabs
  //         activeTab={main.activeTab}
  //         onChange={onChangeTab}
  //         className="tab-nav-tab-box-shadow"
  //         marginTop="12px"
  //       >
  //         <TabPanel label={MAP_TITLE.title} eventKey="events">
  //           <WrapperNoData>
  //             <NoDataComponent guideNoYet={MAP_TITLE.guideNoDestYet} />
  //           </WrapperNoData>
  //         </TabPanel>
  //         <TabPanel label={MAP_TITLE.settings} eventKey="settings">
  //           <WrapperNoData>
  //             <NoDataComponent guideNoYet={MAP_TITLE.guideNoDestYet} />
  //           </WrapperNoData>
  //         </TabPanel>
  //       </StyleTabs>
  //     );
  //   }
  //   if (props.use === 'assign') {
  //     return (
  //       <AssignEventAttribute
  //         activeRow={main.activeRow}
  //         design="update"
  //         insightPropertyId={insightPropertyId}
  //       />
  //     );
  //   }

  //   if (props.use === 'assign-events') {
  //     return (
  //       <AssignEvents
  //         activeRow={main.activeRow}
  //         design="update"
  //         insightPropertyId={insightPropertyId}
  //       />
  //     );
  //   }

  //   return (
  //     <StyleTabs
  //       activeTab={main.activeTab}
  //       onChange={onChangeTab}
  //       className="tab-nav-tab-box-shadow"
  //       marginTop="12px"
  //     >
  //       <TabPanel label={MAP_TITLE.title} eventKey="events">
  //         <EventsPage
  //           activeRow={activeRow}
  //           insightPropertyId={insightPropertyId}
  //         />
  //       </TabPanel>
  //       <TabPanel label={MAP_TITLE.settings} eventKey="settings">
  //         <SettingsPage
  //           activeRow={main.activeRow}
  //           insightPropertyId={insightPropertyId}
  //         />
  //       </TabPanel>
  //     </StyleTabs>
  //   );
  // };
  const renderContent = () => {
    if (
      ['Conversions', 'Predictive Events'].includes(
        main.activeRow.insightPropertyName,
      )
    ) {
      props.callback('CLOSE_DRAWER_SETTING');
    }
    return <DrawerSetting {...props} activeRow={main.activeRow} />;
  };
  return !main.isLoading && renderContent();
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectDetailDomainMain(),
  activeRow: makeSelectSourceActive(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB`, params));
    },
    updateName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ACTIVE_ROW_NAME`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  memo,
)(DetailPage);

/* eslint-disable camelcase */
/* eslint-disable indent */
import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from 'containers/Filters/utils';
import {
  CellText,
  // CellMainEventAttribute,
  CellDate,
  CellNumber,
  CellArray,
  CellToggle,
} from 'containers/Table/Cell';
import CellExpanded from 'containers/Table/Cell/Expanded';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { DATA_INFO } from 'services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    parentId: null,
    rowLevel: 1,
    id: item.event_property_name,
    label: item.event_property_display,
    property_name: item.event_property_name,
    type_display:
      (DATA_INFO.storage_type.map[`${item.type}`] || {}).label || '',
    is_required_display:
      (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
    subRows: mapSubRows(item.items || [], item),
  }));

export const mapSubRows = (subRows, parent) =>
  subRows.map(item => ({
    ...item,
    parentId: parent.event_property_name,
    rowLevel: 2,
    label: item.item_property_display,
    id: item.item_property_name,
    type_display:
      (DATA_INFO.object_attribute_type.map[`${item.type}`] || {}).label || '',
    is_required_display:
      (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
    property_name: item.item_property_name,
    event_property_name: parent.event_property_name,
    disabled_status: parseInt(item.is_required) === 1,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName = {},
  columnStatus,
  columnsActive = {},
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggle,
      placement: 'center',
      className: `${columnStatus.value} hiden-expanded-style`,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: 'label',
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      disableSortBy: true,
      // Cell: CellMainEventAttribute,
      // getIsDisabledEditName: () => false,
      // Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      className: 'main-expanded-style',
      Cell: CellExpanded,
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  is_required: 'is_required_display',
  event_property_name: 'property_name',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  type: CellText,
  is_required: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export function getModuleConfig(moduleConfig) {
  // const newModuleConfig = { ...moduleConfig };
  // newModuleConfig.objectId = storyId;
  // case lỗi
  return moduleConfig;
}

export const mapParamsDeleteFn = (oldData, newData) => {
  console.log('oldData, newData: ', oldData, newData);
  const {
    isSelectedAll,
    activeRows,
    totalRecord,
    rules,
    extendParams: { activeParentRow },
  } = oldData;
  const event_properties = [];
  const {
    number_event_action_id,
    number_event_category_id,
    insight_property_id,
  } = activeParentRow;
  if (!isSelectedAll)
    activeRows.forEach(each => {
      event_properties.push({
        event_property_name: each.event_property_name || null,
        item_type_id: each.item_type_id || null,
        item_property_name: each.item_property_name || null,
      });
    });
  const params = {
    data: {
      insight_property_id,
      event_category_id: number_event_category_id,
      event_action_id: number_event_action_id,
      total_selected: isSelectedAll ? totalRecord : activeRows.size,
      event_properties,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [
              {
                AND: [],
              },
            ],
          },
    },
  };
  return params;
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  console.log('activeRows: ', activeRows);
  // const {} = activeRows;
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.event_property_name,
      label: item.event_property_display,
    });
  });
  return data;
};

import { subMinutes } from 'date-fns';
import {
  CellArray,
  CellDate,
  CellNumber,
  CellRawEventLog,
  CellText,
} from '../../../../../../../../containers/Table/Cell';
import { COLUMNS_WIDTH } from '../../../../../../../../containers/Table/constants';

export const initHourValue = () => ({
  selection: 'last_6_hours',
  value: {
    fromDate: subMinutes(new Date(), 6 * 60).getTime(),
    toDate: new Date().getTime(),
  },
});

export function serializeData(data) {
  const list = [];
  const map = {};

  data.forEach(tmp => {
    const tempt = {
      id: tmp.propertyCode,
      ...tmp,
    };

    list.push(tempt);
    map[tempt.id] = tempt;
  });

  return {
    list,
    map,
  };
}

export function buildTableColumns(columnsActive) {
  const columns = [];

  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      Cell: CellText,
      accessor: property.value,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DF[property.displayFormat.type] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (['log_id', 'event_id', 'property_id'].includes(property.value)) {
      column.placement = 'left';
    }

    columns.push(column);
  });

  return columns;
}

const MAP_CELL_BY_VALUE = {
  log_id: CellText,
  raw_data: CellRawEventLog,
};

const MAP_CELL_BY_DF = {
  RAW_STRING: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

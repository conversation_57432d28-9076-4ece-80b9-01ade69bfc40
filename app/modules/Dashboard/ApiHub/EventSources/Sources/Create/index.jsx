/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect } from 'react';
import { connect, useDispatch } from 'react-redux';
import { useImmer } from 'use-immer';
import { useHistory, useParams } from 'react-router-dom';
import queryString from 'query-string';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { StepWrapper, StyleWrapper } from './styles';
import Design from './Design/index';
import { UILoading as Loading } from '@xlab-team/ui-components';
import {
  addNotification,
  getList,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';

import { MODULE_CONFIG as MODULE_CONFIG_SETTNGS } from './Design/Settings/config';
import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../config';
import { safeParseInt } from '../../../../../../utils/common';
import {
  Button,
  DrawerDetail,
  EditableName,
  Icon,
  Steps,
} from '@antscorp/antsomi-ui';
import { createStructuredSelector } from 'reselect';
import makeSelectCreateSettings, {
  makeSelectCreateSettingsDataConfig,
} from './Design/Settings/selectors';
import EventsPage from '../Detail/Events/Loadable';
import {
  convertDataToAPI,
  convertListEventToFeInitData,
  toAPI,
} from './Design/Assign/utils';
import EventSourcesServices from 'services/EventSources';
import { getErrorMessageV2Translate } from '../../../../../../utils/web/message';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import { makeSelectDetailDomainMain } from '../Detail/selectors';
import {
  BoxHeaderLeft,
  BoxHeaderRight,
  StyledIcon,
  WrapperHeader,
} from '../../../../../../containers/Drawer/DrawerDetailDataObject/styled';
import { PARAMS } from './Design/Assign/constants';
import { cloneDeep, isEmpty } from 'lodash';
import { toEntryAPI } from './Design/Settings/utils';
import { onChangeDataAtrsCurrentEvent } from './config';
import { useUpdateObjectName } from '../../../../../../hooks';
import { TableIcon } from '@antscorp/antsomi-ui/es/components/icons';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';

const MAP_TRANSLATE = {
  chooseCatelog: getTranslateMessage(
    TRANSLATE_KEY._BREADCRUMB_CREATE_SOURCE_STEP1,
    'Choose catalog',
  ),
  createSource: getTranslateMessage(TRANSLATE_KEY._, 'Fill source info'),
  assignEvent: getTranslateMessage(
    TRANSLATE_KEY._BREADCRUMB_CREATE_SOURCE_STEP3,
    'Assign Event',
  ),
};
const MENUTABS = [
  {
    icon: <Icon type="icon-ants-pencil" />,
    label: 'Settings',
    key: 'settings',
  },
  {
    icon: <TableIcon />,
    label: 'Listing events',
    key: 'events',
  },
];
const STEPS = [
  { index: 0, title: MAP_TRANSLATE.createSource },
  { index: 1, title: MAP_TRANSLATE.assignEvent },
];

const MAP_TITLE = {
  titleHelmet: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_EVENT_SOURCE,
    'Event Sources',
  ),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};

/** Main function Create Source */
export function CreateSource(props) {
  const {
    isOpen = false,
    main,
    dataConfig,
    sourceType,
    design = 'create',
    template,
    type,
    sourceId,
    searchParams,
    tab,
    activeRow,
    isLoadingDetail = false,
  } = props;
  const { step = '0' } = useParams();
  const history = useHistory();
  const dispatch = useDispatch();
  const [state, setState] = useImmer({
    activeStep: safeParseInt(step, 0),
    sourceType,
    sourceSettings: {},
    typeCreateCopy: '',
    stateAssign: null,
    isSaveCreate: false,
    refDataAssignAttrs: null,
    isLoading: false,
  });
  const { createCopy } = queryString.parse(window.location.search);
  const { isDoing, isLoadingConfigFields } = main;
  const {
    isLoading: isLoadingSourceName,
    isError: isErrorSourceName,
    error: errorSourceName,
    updateName: updateSourceName,
    isSuccess: isSuccessSourceName,
  } = useUpdateObjectName({
    serviceFunc: EventSourcesServices.data.updateName,
    options: {},
  });
  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };
  useEffect(() => {
    setStateCommon({
      activeStep: 0,
      sourceType,
      sourceSettings: {},
    });
  }, [sourceType]);
  const fetchInitData = async dataEvents => {
    setStateCommon({ isLoading: true });

    const dataToFe = {
      list: [],
      map: {},
      activeEvent: {},
      mapSavedEvent: {},
      listSearch: [],
    };

    try {
      const response = await EventSourcesServices.template.getListEvent(PARAMS);

      if (response.code === 200 && Array.isArray(response.data)) {
        const { list, map, mapSaved } = convertListEventToFeInitData(
          response.data,
          dataEvents,
        );
        dataToFe.listTmpForSearch = list;
        dataToFe.list = list;
        dataToFe.map = map;
        dataToFe.mapSavedEvent = mapSaved;
        let dataTmp;
        if (dataToFe.list.length > 0) {
          dataToFe.activeEvent = dataToFe.list[0];
          dataTmp = cloneDeep(list[0].dataAttrs);
        }
        setStateCommon({
          stateAssign: {
            dataEvent: dataToFe,
            activeEvent: dataToFe.activeEvent,
            isLoading: false,
            defaultInitData: dataTmp,
          },
        });
      }
    } catch (err) {
      console.log('err ===>', err);
      addMessageToQueue({
        path:
          'app/modules/Dashboard/ApiHub/EventSources/Sources/Create/Design/Assign/index.jsx',
        func: 'fetchInitData',
        data: err.stack,
      });
      setStateCommon({ isLoading: false });
    }
  };
  const fetchDataCreateCopy = async () => {
    try {
      const response = await EventSourcesServices.template.getListEventByInsightPropertyId(
        { insightPropertyId: createCopy },
      );
      if (response.code === 200 && Array.isArray(response.data)) {
        fetchInitData(response.data);
        // setStateCommon({ dataEvent: response.data, isLoading: false });
      }
    } catch (err) {
      console.log('err ===>', err);
      addMessageToQueue({
        path:
          'app/modules/Dashboard/ApiHub/EventSources/Sources/Detail/Assign/index.jsx',
        func: 'fetchData',
        data: err.stack,
      });
      // setStateCommon({ dataEvent: dataToFe, isLoading: false });
    }
  };
  useEffect(() => {
    if (createCopy) {
      fetchDataCreateCopy();
    }
  }, [createCopy]);
  const handleClose = () => {
    props.callback('CLOSE_DRAWER_SETTING', isSuccessSourceName);

    // reset state
    setStateCommon({
      activeStep: 0,
      sourceType,
      sourceSettings: {},
      stateAssign: null,
      isSaveCreate: false,
    });
    props.reset();
  };
  const onChangeStep = data => {
    const validate = isDoing || isLoadingConfigFields || !dataConfig.isValidate;
    if (validate) {
      return;
    }
    setStateCommon({
      activeStep: data,
    });
  };
  const callback = (type, data = {}) => {
    switch (type) {
      case 'CREATE_EVENT': {
        setStateCommon({
          activeStep: 1,
        });
        break;
      }
      case 'SELECT_SOURCE_TYPE': {
        setStateCommon({
          activeStep: 1,
          sourceType: data,
        });
        break;
      }
      // case 'ON_PREVIOUS_0': {
      //   const sourceSettings = state.sourceSettings;
      //   const valueType = createCopy
      //     ? data.insight_property_type
      //     : state.sourceType.value;
      //   sourceSettings[valueType] = data;
      //   if (createCopy) {
      //     setStateCommon({
      //       sourceType: LIST_SOURCE_TYPE[data.insight_property_type - 1],
      //       typeCreateCopy: data.insight_property_type,
      //     });
      //   }
      //   setStateCommon({
      //     activeStep: 0,
      //     sourceSettings,
      //   });
      //   break;
      // }
      case 'ON_PREVIOUS_0': {
        setStateCommon({
          activeStep: 0,
        });
        break;
      }
      case 'ON_NEXT_1': {
        const sourceSettings = state.sourceSettings;
        sourceSettings[state.sourceType.value] = data;
        setStateCommon({
          activeStep: 1,
          sourceSettings,
        });
        break;
      }
      case 'ON_CANCEL': {
        props.goToList();
        break;
      }
      case 'ON_CREATE_DONE': {
        props.createDone(data);
        break;
      }
      case 'CACHE_STATE_ASSIGN': {
        const { stateTmp, refDataAssignAttrs } = data;
        setStateCommon({ stateAssign: stateTmp, refDataAssignAttrs });
        break;
      }
      default:
        break;
    }
  };
  const onCreate = tempStateDataEvent => {
    // props.callback('ON_CREATE_DONE', 554926188);
    // return;
    try {
      setStateCommon({ isSaveCreate: true });
      const events = convertDataToAPI(tempStateDataEvent.list);
      // case create && createCopy
      const sourceSetting = toEntryAPI(dataConfig, main.sourceType, createCopy);

      const params = toAPI(sourceSetting, events);
      EventSourcesServices.data.create(params).then(res => {
        setStateCommon({ isSaveCreate: false });
        if (res.code === 200) {
          handleClose();
          const notification = {
            ...getErrorMessageV2Translate(
              res.codeMessage,
              'Create source success',
            ),
            timeout: 3000,
            type: 'success',
          };
          props.addNotification(notification);
          props.getList();
        } else if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
          const dataError = {
            name: 'sourceName',
            errors: [
              getTranslateMessage(res.codeMessage, 'This name already existed'),
            ],
          };
          dispatch(
            updateValue(`${MODULE_CONFIG_SETTNGS.key}@@ERROR_API`, dataError),
          );
        } else {
          const notification = {
            ...getErrorMessageV2Translate(
              res.codeMessage,
              'Create source error',
            ),
            timeout: 4000,
            type: 'danger',
          };
          props.addNotification(notification);
        }
      });
    } catch (err) {
      console.log(err);
      addMessageToQueue({
        path:
          'app/modules/Dashboard/ApiHub/EventSources/Sources/Create/Design/Assign/index.jsx',
        func: 'onCreate',
        data: err.stack,
      });
    }
  };
  const onUpdate = tempStateDataEvent => {
    try {
      setStateCommon({ isSaveCreate: true });
      const events = convertDataToAPI(tempStateDataEvent.list);
      const params = {
        insightPropertyId: sourceId,
        body: { events },
      };

      EventSourcesServices.data.assign(params).then(res => {
        // console.log('EventSourcesServices 2', res);
        setStateCommon({ isSaveCreate: false });

        // if (res.code === 200) {
        //   const notification = {
        //     ...getErrorMessageV2Translate(
        //       TRANSLATE_KEY._NOTIFICATION_SUCCESS,
        //       'Update source success',
        //     ),
        //     timeout: 1000,
        //     type: 'success',
        //   };
        //   // props.addNotification(notification);
        //   // props.callback('ON_CREATE_DONE', res.data);
        //   // setTimeout(() => {
        //   props.callback('ON_ASSIGN_DONE', res.data);
        //   // }, 1000);
        // } else {
        //   const notification = {
        //     ...getErrorMessageV2Translate(
        //       res.codeMessage,
        //       'Update source error',
        //     ),
        //     timeout: 4000,
        //     type: 'danger',
        //   };
        //   props.addNotification(notification);
        // }
      });
    } catch (err) {
      console.log(err);
      addMessageToQueue({
        path:
          'app/modules/Dashboard/ApiHub/EventSources/Sources/Create/Design/Assign/index.jsx',
        func: 'onUpdate',
        data: err.stack,
      });
    }
  };
  const onSave = () => {
    const tempStateDataEvent = onChangeDataAtrsCurrentEvent(
      state.stateAssign,
      state.refDataAssignAttrs,
    );
    if (design === 'create' || createCopy) {
      onCreate(tempStateDataEvent);
    } else {
      props.onUpdate({
        state,
        onUpdate,
        onFailure: () => {
          if (state.activeStep > 0) {
            setState(draft => {
              draft.activeStep = state.activeStep - 1;
            });
          }
        },
        onClose: handleClose,
        tempStateDataEvent,
      });
    }
  };
  const onChangeTab = value => {
    // props.onChangeTab(value);
    setStateCommon({
      activeStep: 0,
    });
    const endParam = value;
    searchParams.set('tab', endParam);
    const newSearch = searchParams.toString();
    history.replace({ search: newSearch });
  };
  const isDisabledSave =
    state.stateAssign?.dataEvent.list.length === 0 ||
    isDoing ||
    isLoadingConfigFields ||
    !dataConfig.isValidate ||
    (type === 'new' && state.activeStep == 0 && !state.stateAssign) ||
    state.isSaveCreate;

  const onBlurSourceName = () => {
    if (isEmpty(props.mainDetail.valueObjectName.value.EN)) return;
    updateSourceName({
      insightPropertyId: sourceId,
      data: {
        insightPropertyName: props.mainDetail.valueObjectName.value.EN,
      },
    });
  };
  const titleName =
    design === 'update'
      ? props.mainDetail.valueObjectName.value.EN
      : dataConfig?.sourceName.value.EN;
  return (
    <>
      <DrawerDetail
        open={
          isOpen ||
          (createCopy && type === 'clone') ||
          (sourceId && type === 'detail')
        }
        fullScreen={
          props.mainDetail?.drawerEventSource.isOpen ||
          props.mainDetail?.isOpenDrawerExplore
        }
        menuProps={{
          items:
            design === 'create'
              ? [
                  {
                    icon: <Icon type="icon-ants-pencil" />,
                    label: 'Create promotion pool',
                    key: 'settings',
                  },
                ]
              : MENUTABS,

          selectedKeys: tab,
          onClick: item => {
            if (item) {
              onChangeTab(item.key);
            }
          },
        }}
        onClose={handleClose}
        destroyOnClose
        headerProps={{
          showBorderBottom: true,
          height: '50px',
          style: {
            padding: '0 15px',
          },
          children: (
            <WrapperHeader>
              <BoxHeaderLeft>
                {design === 'create' ? (
                  <EditableName
                    value={dataConfig?.sourceName.value.EN}
                    required
                    onChange={name => {
                      props.onChangeDataConfig({
                        name: 'sourceName',
                        value: {
                          DEFAULT_LANG: 'EN',
                          EN: name,
                        },
                      });
                      // onChangeData('conversionName', {
                      //   DEFAULT_LANG: 'EN',
                      //   EN: name,
                      // })
                    }}
                    error={dataConfig.sourceName.errors[0]}
                  />
                ) : (
                  <EditableName
                    onBlur={onBlurSourceName}
                    value={props.mainDetail.valueObjectName.value.EN}
                    required
                    loading={isLoadingSourceName}
                    isError={isErrorSourceName}
                    error={errorSourceName}
                    onChange={name => {
                      props.onUpdateName(name);
                      // onChangeData('conversionName', {
                      //   DEFAULT_LANG: 'EN',
                      //   EN: name,
                      // })
                    }}
                  />
                )}
              </BoxHeaderLeft>
              <BoxHeaderRight>
                {tab === 'settings' ? (
                  <Button
                    type="primary"
                    variant="contained"
                    onClick={onSave}
                    disabled={isDisabledSave}
                  >
                    {MAP_TITLE.labelSave}
                  </Button>
                ) : (
                  <div style={{ width: '360px' }} />
                )}
              </BoxHeaderRight>
            </WrapperHeader>
          ),
        }}
      >
        <UIHelmet
          title={
            isEmpty(titleName) && design !== 'update'
              ? 'Create new event'
              : titleName
          }
        />
        {/* {isLoadingDetail && <Loading isLoading isWhite />} */}
        {tab === 'settings' || design === 'create' ? (
          <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/Events/Create/index.jsx">
            {/* <UIStep activeStep={state.activeStep} steps={STEPS} /> */}

            <StepWrapper>
              <Steps
                current={state.activeStep}
                items={STEPS}
                iconSize={24}
                onChange={onChangeStep}
                // style={{ width: '30% !important' }}
              />
            </StepWrapper>
            <StyleWrapper>
              <Design
                eventActionId={state.eventActionId}
                eventCategoryId={state.eventCategoryId}
                activeStep={state.activeStep}
                createCopy={createCopy}
                typeCreateCopy={state.typeCreateCopy}
                callback={callback}
                use={design}
                sourceType={state.sourceType}
                sourceSettings={state.sourceSettings}
                template={template}
                sourceId={sourceId}
                activeRow={activeRow}
                isSaveCreate={state.isSaveCreate}
                valueObjectName={props.mainDetail.valueObjectName}
                stateAssign={state.stateAssign}
                stateRefDataAssignAttrs={state.refDataAssignAttrs}
              />
            </StyleWrapper>
          </ErrorBoundary>
        ) : (
          <EventsPage activeRow={activeRow} insightPropertyId={sourceId} />
        )}
      </DrawerDetail>
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
  mainDetail: makeSelectDetailDomainMain(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    goToList: value =>
      dispatch(updateValue(`${MODULE_CONFIG_SETTNGS.key}@@GO_TO_LIST`, value)),
    createDone: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG_COMMON.key}@@CREATE_DONE@@`, value),
      ),
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG_SETTNGS.key}@@DATA_CONFIG@@`, value),
      ),
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    getList: () => {
      dispatch(getList('event-source-list'));
    },
    onUpdate: value => dispatch(update(MODULE_CONFIG_SETTNGS.key, value)),
    reset: value => dispatch(reset(`${MODULE_CONFIG_SETTNGS.key}`, value)),
    onUpdateName: value => {
      dispatch(updateValue(`mark-hub-es-source-detail@@UPDATE_NAME`, value));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CreateSource);

import { safeParse } from '../../../../../../../../utils/common';

export const convertListEventToFe = data => {
  const validateData = safeParse(data, []);
  const dataToFE = {
    list: [],
    map: {},
  };

  if (validateData.length === 0) {
    return dataToFE;
  }

  validateData.forEach(item => {
    const temp = {
      label: safeParse(item.translateLabel, item.eventNameDisplay),
      value: `${item.eventCategoryId}@@${item.eventActionId}`,
      // status: item.status,
      // type: item.type,
      eventActionId: item.eventActionId,
      eventCategoryId: item.eventCategoryId,
      // iconUrl: item.iconUrl,
      dataAttrs: {
        eventAttributes: safeParse(item.eventAttributes, []),
        mainObjects: safeParse(item.mainObjects, []),
        foreignObjects: safeParse(item.foreignObjects, []),
      },
    };

    dataToFE.list.push(temp);
    dataToFE.map[temp.value] = temp;
  });

  return dataToFE;
};

export const convertDataToAPI = data => {
  const validateData = safeParse(data, []);

  const dataToAPI = [];

  if (validateData.length === 0) {
    return dataToAPI;
  }

  validateData.forEach(event => {
    let { eventAttributes, mainObjects } = event.dataAttrs;

    if (event.isReassign == 1) {
      eventAttributes = eventAttributes.map(item => {
        return { ...item, isReassign: 1 };
      });

      mainObjects = mainObjects.map(item => {
        return { ...item, isReassign: 1 };
      });
    }

    const temp = {
      eventActionId: event.eventActionId,
      eventCategoryId: event.eventCategoryId,
      isReassign: event.isReassign || 0,
      eventAttributes,
      mainObjects,
      foreignObjects:
        event.dataAttrs.foreignObjects &&
        event.dataAttrs.foreignObjects.map(item => {
          const info = {
            itemTypeId: item.itemTypeId,
            isRequired: item.isRequired,
            isReassign: event.isReassign == 1 ? 1 : item.isReassign,
          };
          return info;
        }),
    };

    dataToAPI.push(temp);
  });

  return dataToAPI;
};

export function toAPI(createSourceSettings, events) {
  const data = { ...createSourceSettings, events };
  return data;
}

export const convertListEventToFeInitData = (data, initDateEvents) => {
  const validateData = safeParse(data, []);
  const dataToFE = {
    list: [],
    map: {},
    mapSaved: {},
  };

  if (validateData.length === 0) {
    return dataToFE;
  }
  validateData.forEach(item => {
    const temp = {
      label: safeParse(item.translateLabel, item.eventNameDisplay),
      value: `${item.eventCategoryId}@@${item.eventActionId}`,
      status: item.status,
      type: item.type,
      eventActionId: item.eventActionId,
      eventCategoryId: item.eventCategoryId,
      iconUrl: item.iconUrl,
    };

    // dataToFE.list.push(temp);
    dataToFE.map[temp.value] = temp;
  });
  if (Array.isArray(initDateEvents)) {
    // case initData when update 1 source or back previous step
    if (initDateEvents.length === 0) {
      dataToFE.list = initDateEvents;
    } else {
      initDateEvents.forEach(event => {
        const valueEvent = `${event.eventCategoryId}@@${event.eventActionId}`;
        const infoEvent = dataToFE.map[valueEvent];

        if (infoEvent) {
          infoEvent.iconUrl = event.iconUrl;

          infoEvent.dataAttrs = {
            eventAttributes: event.eventAttributes,
            mainObjects: event.mainObjects,
            foreignObjects: event.foreignObjects,
          };

          dataToFE.list.push(infoEvent);
          dataToFE.mapSaved[valueEvent] = infoEvent;
          dataToFE.map[valueEvent] = infoEvent;
        }
      });
    }
  }
  return dataToFE;
};

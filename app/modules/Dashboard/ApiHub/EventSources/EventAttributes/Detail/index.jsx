/* eslint-disable react/prop-types */
import React, { useEffect, memo, useCallback } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';

import ErrorBoundary from 'components/common/ErrorBoundary';
import { UILoading as Loading } from '@xlab-team/ui-components';
// import TableContainer from 'containers/Table';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { useHistory, useLocation } from 'react-router-dom';

import reducer from './reducer';
import saga from './saga';
import {
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as CREATE_MODULE_CONFIG } from '../Create/config';
import { MODULE_CONFIG as LIST_MODULE_CONFIG } from '../List/config';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import {
  makeSelectEventAttrDetailDomainMain,
  makeSelectActiveRow,
} from './selectors';
import SettingsPage from './Settings';
// import { getBreadcrums } from './utils';
// import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';
import PageNotFound from '../../../../../../components/Templates/LayoutContent/PageNotFound';

const MAP_TITLE = {
  titlAttribute: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_,
    'Event Attributes',
  ),
  settings: getTranslateMessage(TRANSLATE_KEY._MENU_SETTINGS, 'Settings'),
};
export function DetailPage(props) {
  const history = useHistory();

  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });
  // const [selectedRows, setSelectedRows] = useState([]);
  const { search } = useLocation();
  const querySearch = Object.fromEntries(new URLSearchParams(search));

  // const { main, mainCreate } = props;
  useEffect(() => {
    props.init({ attributeId: querySearch.attributeId, tab: 'settings' });
    return () => {
      props.reset();
    };
  }, [querySearch.attributeId]);

  const onClickBackToList = useCallback(() => {
    history.push(
      `${APP.PREFIX}/${getPortalId()}/api-hub/event-sources/event-attributes`,
    );
  }, []);

  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'EDIT_NAME_SUCCESS': {
        props.updateName(data);
        break;
      }
      case 'ON_SAVE': {
        props.onSave(data);
        break;
      }

      default:
        break;
    }
  };

  // const breadcrums = useMemo(() => {
  //   return getBreadcrums(props.activeRow.translate_label);
  // }, [props.activeRow.translate_label]);

  if (props.main.isLoading) {
    return <Loading isLoading isWhite />;
  }

  const renderContent = () => {
    if (Object.keys(props.activeRow).length === 0) {
      return (
        <PageNotFound
          labelButton={getTranslateMessage(TRANSLATE_KEY._, 'Back to list')}
          labelPageNotFound={getTranslateMessage(
            TRANSLATE_KEY._,
            'Event Atrribute Not Found',
          )}
          onClick={onClickBackToList}
        />
      );
    }

    return <SettingsPage callback={callback} />;
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/EventAttributes/Detail/index.jsx">
      <UIHelmet title={MAP_TITLE.titlAttribute} />
      {renderContent()}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectEventAttrDetailDomainMain(),
  activeRow: makeSelectActiveRow(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB`, params));
    },
    updateName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ACTIVE_ROW_NAME`, params));
    },
    onSave: value => {
      dispatch(update(CREATE_MODULE_CONFIG.key, value));
      dispatch(init(LIST_MODULE_CONFIG.key, {}));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  memo,
)(DetailPage);

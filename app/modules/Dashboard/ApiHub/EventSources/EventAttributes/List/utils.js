import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import {
  CellArray,
  CellDate,
  CellMainEventAttribute,
  CellNumber,
  CellText,
} from '../../../../../../containers/Table/Cell';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { DATA_INFO } from '../../../../../../services/Abstract.data';

// const MAP_TITLE = {
//   warnDeleteItem: (module, all, number_not_delete, number_delete) =>
//     getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
//       module,
//       all,
//       number_not_delete,
//       number_delete,
//     }),
//   warnDeleteAll: (module, number_delete) =>
//     getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
//       module,
//       number_delete,
//     }),
//   warnNotDelete: module =>
//     getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
//       module,
//     }),
// };

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.event_property_name,
    type_display:
      (DATA_INFO.storage_type.map[`${item.type}`] || {}).label || '',
    is_required_display:
      (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
    data_type_display:
      (DATA_INFO.event_attr_data_type.map[`${item.data_type}`] || {}).label ||
      '',
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(columnName = {}, columnsActive = {}) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainEventAttribute,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    if (['is_required', 'type'].includes(property.value)) {
      column.placement = 'left';
    }
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  data_type: 'data_type_display',
  is_required: 'is_required_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  type: CellText,
  is_required: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const { isSelectedAll, activeRows, totalRecord, rules } = oldData;
  const objectIds = [];
  const value = [];
  if (!isSelectedAll)
    activeRows.forEach(each => value.push(each.event_property_name));
  return {
    data: {
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      objectIds,
      isCheckUsedStatus: 0,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [
              {
                AND: [
                  {
                    column: 'event_property_name',
                    data_type: 'string',
                    operator: 'matches',
                    value,
                  },
                ],
              },
            ],
          },
    },
  };
};

// export const getTitleDeleteWarning = (
//   moduleName,
//   totalEntries,
//   totalAccepted,
//   totalDenied,
// ) => {
//   if (totalAccepted === 0) {
//     return MAP_TITLE.warnNotDelete(MAP_TITLE.itemNameDest);
//   }
//   if (totalDenied === 0) {
//     return MAP_TITLE.warnDeleteAll(MAP_TITLE.itemNameDest, totalEntries);
//   }
//   return MAP_TITLE.warnDeleteItem(
//     MAP_TITLE.itemNameDest,
//     totalEntries,
//     totalDenied,
//     totalAccepted,
//   );
// };

export const listTitleAcceptedFn = ({ activeRows, response }) => {
  const { acceptedCodes } = response;
  const data = [];
  acceptedCodes.forEach(each => {
    const item = activeRows.get(each) || {};
    data.push({
      value: item.event_property_name,
      label: item.event_property_display,
    });
  });
  return data;
};

export const getFilterFromUrl = () => {
  const query = new URLSearchParams(window.location.search);
  const eventPropertyName = query.get('event_property_name')?.split(',') || [];
  let queryRules;
  if (eventPropertyName.length > 0) {
    queryRules = {
      OR: [
        {
          AND: [
            {
              type: 1,
              column: 'event_property_name',
              data_type: 'string',
              operator: 'matches',
              extendValue: [],
              value: eventPropertyName,
            },
          ],
        },
      ],
    };
  }
  return queryRules;
};

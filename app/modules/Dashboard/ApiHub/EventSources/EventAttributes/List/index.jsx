/* eslint-disable camelcase */
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { useHistory, useLocation, withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
// import NavBar from 'components/Organisms/NavBar/index';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import ObjectServices from 'services/Object';
import reducer from './reducer';
import saga from './saga';
import EventAttributeServices from '../../../../../../services/EventAttribute';
import {
  makeSelectListEventAttributeTable,
  makeSelectListEventAttributeFilter,
  makeSelectListEventAttributeColumn,
  makeSelectListEventAttributeMain,
  makeSelectListEventAttributeData,
} from './selectors';
import { MODULE_CONFIG /* , BREADCRUMDATA */ } from './config';
import Filters from '../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import { init, getList, reset } from '../../../../../../redux/actions';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import Search from '../../../../../../containers/Search';
import ControlTable from './ControlTable';
import ModalDeleteMulti from '../../../../../../containers/modals/ModalDeleteMultiType2';
// import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import AddComponent from './AddComponent';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
// import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
// import { MAP_STORY_STATUS } from '../../../../../../utils/web/processStatus';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';
import NoDataComponent from '../../../../../../components/common/NoDataFound';
import { mapParamsDeleteFn, listTitleAcceptedFn } from './utils';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import { ModalListEventSource } from '../../../../../../containers/modals/ModalListEventSource';
import useToggle from '../../../../../../hooks/useToggle';
import { DrawerDetail, Icon, useLayoutStore } from '@antscorp/antsomi-ui';
import EventAttributesDetailModule from '../Detail/Loadable';
import EventAttributesCreateModule from '../Create/Loadable';

const MAP_TITLE = {
  title: getTranslateMessage(
    TRANSLATE_KEY._PERSONALIZE_EVENT,
    'Event Attribute',
  ),
  itemNameDest: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_,
    'event attribute',
  ),
  actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  noData: getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data'),
  // actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),

  guideNoDestYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_EVENT_ATTRIBUTE,
    "You haven't created any Event Attribute yet",
  ),
};

export function ListPage(props) {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalEvent, toggleModalEvent] = useToggle(false);
  const { main, table, filter, column } = props;
  const { isInitDone } = main;
  const history = useHistory();
  const { search } = useLocation();
  const searchQuery = Object.fromEntries(new URLSearchParams(search));

  const { setHeaderState, resetHeaderState } = useLayoutStore();
  // const [isOpenDrawerCreate, setIsOpenDrawerCreate] = useState(
  //   () => searchQuery.type === 'create',
  // );

  useEffect(() => {
    setHeaderState({
      pageTitle: 'Event Attributes',
    });
    props.init({});
    return () => {
      props.reset();
      resetHeaderState();
    };
  }, []);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'IS_OPEN_MODAL_LIST_EVENT': {
        toggleModalEvent();
        break;
      }
      case 'OPEN_TAB_CREATE_COPY': {
        toggleModalEvent();
        history.push({ search: `?type=create&copyId=${dataIn.id}` });

        break;
      }
      // case 'OPEN_CREATE_DRAWER': {
      //   history.push({ search: '?type=create' });
      //   setIsOpenDrawerCreate(true);
      //   break;
      // }
      case 'ACTION_TABLE_CLONE': {
        const id = dataIn.keys().next().value;
        history.push(
          `${
            APP.PREFIX
          }/${getPortalId()}/api-hub/event-sources/event-attributes/create?type=create&copyId=${id}`,
        );
        // console.log(dataIn);
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const menuItems = [
    { icon: <Icon type="icon-ants-pencil" />, label: 'Edit', key: 'edit' },
  ];

  return (
    <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/EventAttributes/List/index.jsx">
      <UIHelmet title={MAP_TITLE.title} />
      <LayoutContent height="100%">
        <LayoutContentLoading classNameLoading="m-y-2" isLoading={!isInitDone}>
          <TableContainer
            columnActive={column.columnObj}
            table={table}
            isLoading={main.isLoading}
            moduleConfig={MODULE_CONFIG}
            selectedIds={table.selectedIds}
            selectedRows={table.selectedRows}
            isSelectedAll={table.isSelectedAll}
            isSelectedAllPage={table.isSelectedAllPage}
            columns={tableColumns}
            data={props.data}
            callback={callback}
            noDataText={MAP_TITLE.noData}
            resizeColName="event_property_display"
            ComponentControlTable={ControlTable}
            global={{ isGotoDetail: true }}
            NoDataComponent={() => (
              <NoDataComponent
                AddComponent={() => <AddComponent callback={callback} />}
                guideNoYet={MAP_TITLE.guideNoDestYet}
              />
            )}
          >
            <Filters
              use="list"
              rules={filter.rules}
              moduleConfig={MODULE_CONFIG}
              filterActive={filter.config.filterObj}
              filterCustom={filter.config.library.filterCustom}
              libraryFilters={filter.config.library.filters}
              groups={main.groupAttributes.groupsFilter}
              isFilter={filter.config.design.isFilter}
              isLoading={filter.config.isLoading}
              AddComponent={() => <AddComponent callback={callback} />}
            />
            <WrapActionTable
              show={!filter.config.design.isFilter}
              className="p-x-4"
            >
              <Search
                moduleConfig={MODULE_CONFIG}
                config={{
                  objectType: MODULE_CONFIG.objectType,
                  limit: 20,
                  page: 1,
                  sort: 'asc',
                  filters: {},
                }}
                suggestionType="suggestionMultilang"
                moduleLabel={MAP_TITLE.title}
                isAddFilter={false}
              />
              <ModifyColumn
                sort={table.sort}
                moduleConfig={MODULE_CONFIG}
                columnActive={column.columnObj}
                columnCustom={column.library.columnCustom}
                libraryColumns={column.library.columns}
                requires={main.groupAttributes.requires}
                defaults={main.groupAttributes.defaults}
                defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                columns={column.columnObj.columns.columnsAlias}
                groups={main.groupAttributes.groups}
                isLoading={column.isLoading}
              />
            </WrapActionTable>
          </TableContainer>
        </LayoutContentLoading>
      </LayoutContent>
      {isOpenModalDelete && (
        <ModalDeleteMulti
          isUseAPICheck
          activeRows={table.selectedRows}
          isSelectedAll={table.isSelectedAll}
          totalRecord={table.paging.totalRecord}
          rules={filter.rules}
          label={getTranslateMessage(
            TRANSLATE_KEY._BOX_TITL_REMOVE_EVENT_ATTRIBUTE,
            'Remove Event Attribute',
          )}
          placeHolderName=""
          moduleName={MAP_TITLE.title}
          ObjectServicesFn={EventAttributeServices.data.delete}
          listTitleAcceptedFn={listTitleAcceptedFn}
          isOpenModal={isOpenModalDelete}
          setOpenModal={setIsOpenModalDelete}
          fetchData={props.fetchData}
          mapParamsFn={mapParamsDeleteFn}
          // getTitleWarning={getTitleDeleteWarning}
        />
      )}
      <ModalListEventSource
        isOpen={isOpenModalEvent}
        toggle={toggleModalEvent}
        callback={callback}
        dataParams={{ objectType: 'ES_ATTRIBUTE', filters: {}, sort: 'asc' }}
        label="Select event attribute"
        ObjectServicesFn={ObjectServices.suggestionMultilang.getList}
      />
      <DrawerDetail
        open={searchQuery.type === 'create' || searchQuery.type === 'detail'}
        // open={isOpenDrawerCreate}
        onClose={() => {
          history.push({ search: '' });
          if (searchQuery.isEdited) props.fetchData();
          // setIsOpenDrawerCreate(false);
        }}
        menuProps={{ items: menuItems, selectedKeys: ['edit'] }}
        destroyOnClose
      >
        {searchQuery.type === 'create' ? <EventAttributesCreateModule /> : null}
        {searchQuery.type === 'detail' ? <EventAttributesDetailModule /> : null}
      </DrawerDetail>
    </ErrorBoundary>
  );
}
const mapStateToProps = createStructuredSelector({
  main: makeSelectListEventAttributeMain(),
  table: makeSelectListEventAttributeTable(),
  filter: makeSelectListEventAttributeFilter(),
  column: makeSelectListEventAttributeColumn(),
  data: makeSelectListEventAttributeData(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(ListPage);

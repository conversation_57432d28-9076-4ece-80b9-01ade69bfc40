/* eslint-disable camelcase */
import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../containers/Filters/utils';
import {
  CellArray,
  CellDate,
  CellMainObject,
  CellNumber,
  CellObjectTypeBO,
  CellStatusArchiveBO,
  CellText,
  CellToggleWithStyle,
} from '../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { DATA_INFO } from '../../../../../services/Abstract.data';
import { initNumberWithoutDecimal } from '../../../../../utils/web/portalSetting';
import { getLabelStatusArchive } from '../../../../../utils/web/processStatus';
import { getCurrentOwnerIds } from '../../../../../utils/web/cookie';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  MENU_CODE,
  checkingRoleScope,
} from '../../../../../utils/web/permission';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export const serializeData = (data, activeTab) =>
  mapBluePrint(data, item => {
    const isHavePermission = isShowAction(
      activeTab,
      item,
      MENU_CODE.DATA_OBJECT,
    );

    return {
      ...item,
      id: item.item_type_id,
      item_type_status: getLabelStatusArchive(Number(item.status)),
      pre_defined_display:
        (DATA_INFO.storage_type.map[`${item.storage_type}`] || {}).label || '',
      disabledCheckbox: !isHavePermission,
    };
  });

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.item_type_id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
  activeTab,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggleWithStyle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      disableSortBy: true,
      // className: `${columnStatus.value}`,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainObject,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      activeTab,
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  // console.log('buildTableColumns', columnsActive);
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    // console.log('property.isSort', property.isSort , '-', property.value);

    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'num_attrs') {
      column.displayFormat = initNumberWithoutDecimal();
    } else if (property.value === 'item_type_status') {
      column.placement = 'status';
    }
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  storage_type: 'pre_defined_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  item_type_status: CellStatusArchiveBO,
  pre_defined: CellObjectTypeBO,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const { isSelectedAll, activeRows, totalRecord, rules } = oldData;
  const businessObject = [];
  if (!isSelectedAll)
    activeRows.forEach(each => {
      businessObject.push(each.item_type_id);
    });
  const params = {
    data: {
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      status: 3,
      itemTypeIds: businessObject,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [
              {
                AND: [],
              },
            ],
          },
    },
  };
  return params;
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.item_type_id,
      label: item.item_type_display,
    });
  });
  return data;
};
export const isShowAction = (typeBO, activeBO, menuCode) => {
  let isCheck = false;
  const userId = getCurrentOwnerIds();
  const isEditByUser = checkingRoleScope(
    menuCode,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.CREATED_BY_USER,
    true,
  );

  const isEditEverything = checkingRoleScope(
    menuCode,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.EVERYTHING,
    true,
  );

  const { share_access = {} } = activeBO;
  const { list_access = [] } = share_access;

  const tmp = list_access.find(item => Number(item.user_id) === Number(userId));

  if (typeBO === 'owner') {
    // / Created by
    if (isEditByUser && Number(userId) === Number(activeBO.object_owner_id)) {
      isCheck = isEditByUser;
    } else {
      isCheck = isEditEverything;
    }

    // share public role with edit
    if (
      ((Number(share_access.public_role) === 2 &&
        Number(share_access.is_public) === 1) ||
        Number(tmp?.role) === 2) &&
      isEditByUser
    ) {
      isCheck = true;
    }
  } else if (typeBO === 'share') {
    if (tmp) {
      isCheck =
        Number(tmp.allow_edit) === 1 && (isEditByUser || isEditEverything);
    }
    if (isEditEverything) {
      isCheck = isEditEverything;
    }

    if (
      (Number(share_access.public_role) === 2 || Number(tmp?.role) === 2) &&
      isEditByUser
    ) {
      isCheck = true;
    }
  }
  return isCheck;
};

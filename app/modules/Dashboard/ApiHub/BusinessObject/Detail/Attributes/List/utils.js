/* eslint-disable camelcase */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-danger */
/* eslint-disable indent */
import React from 'react';
import { subMinutes } from 'date-fns';
import { isEmpty } from 'lodash';
import { toConditionAPI } from '../../../../../../../containers/Filters/utils';
import {
  CellText,
  CellDate,
  CellNumber,
  CellArray,
  CellMainBOAttribute,
  CellToggleWithStyle,
  CellProcessStatus,
  CellStatusArchive,
} from '../../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import {
  DATA_INFO,
  MAP_DATA_TYPE,
} from '../../../../../../../services/Abstract.data';
import {
  getLabelProcessStatus,
  getLabelStatus,
  getLabelStatusWithType,
  getLabelStatusArchive,
} from '../../../../../../../utils/web/processStatus';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';
import { ItemTypeNameSegmentIds } from '../../../utils';
const MAP_TITLE = {
  labelPaused: getTranslateMessage(TRANSLATE_KEY._STATUS_PAUSED, 'Paused'),
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export const serializeData = data =>
  mapBluePrint(data, item => {
    let type_display = '--';

    if (item.type_labels?.length === 1) {
      type_display = getTranslateMessage(TRANSLATE_KEY[item.type_labels[0]]);
    }

    if (item.type_labels && item.type_labels.length > 1) {
      const start = getTranslateMessage(TRANSLATE_KEY[item.type_labels[0]]);
      const end = getTranslateMessage(TRANSLATE_KEY[item.type_labels[1]]);

      if (start && end) {
        type_display = `${start} - ${end}`;
      }
    }

    return {
      ...item,
      id: item.item_property_name,
      item_property_status: getLabelStatusArchive(Number(item.status)),
      type_display,
      storage_type_display:
        (DATA_INFO.storage_type.map[`${item.storage_type}`] || {}).label || '',
      is_required_display:
        (DATA_INFO.is_required.map[`${item.is_required}`] || {}).label || '',
      is_identity_display:
        (DATA_INFO.is_required.map[`${item.is_identity}`] || {}).label || '',
      data_type_display: (MAP_DATA_TYPE[`${item.data_type}`] || {}).label || '',
      process_status: getLabelStatusWithType(
        parseInt(item.type),
        parseInt(item.status),
        parseInt(item.process_status),
        item.compute_type,
      ),
      disabled_status: item.type === 1 || item.process_status === 1,
      group_id: item.group_display,
      original_process_status: item.process_status,
      // compute_schedule_end: item.compute_schedule_end === 'None' ? 'Never' : item.compute_schedule_end
    };
  });

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };

  dataIn.forEach(item => {
    // hide segment ids
    if (item.item_property_name === ItemTypeNameSegmentIds) {
      return;
    }
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
  isShowActionAttr,
  typeDataView,
) {
  if (isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggleWithStyle,
      placement: 'center',
      isAction: true,
      className: 'tbl-border-left padding-left-right-10',
      isShowActionAttr,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: parseInt(columnName.isSort) !== 1,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainBOAttribute,
      itemTypeId: columnName.item_type_id,
      isShowActionAttr,
      typeDataView,
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  //
  columnsActive.forEach(property => {
    const { dataType, displayFormat, value } = property;
    // console.log('property', property);
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.MAIN,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value} ${property.value ===
        ('process_status' || 'item_property_status') && 'txt-left status'}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
    if (['process_status', 'item_property_status'].includes(property.value)) {
      column.placement = 'status';
    }
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  storage_type: 'storage_type_display',
  is_required: 'is_required_display',
  data_type: 'data_type_display',
  is_identity: 'is_identity_display',
  // process_status: 'process_status_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  process_status: CellProcessStatus,
  item_property_status: CellStatusArchive,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  console.log('oldData, newData: ', oldData, newData);
  const {
    isSelectedAll,
    activeRows,
    totalRecord,
    rules,
    extendParams: { itemTypeId },
  } = oldData;
  const itemPropertyNames = [];
  if (!isSelectedAll)
    activeRows.forEach(each => {
      itemPropertyNames.push(each.item_property_name);
    });
  const params = {
    data: {
      itemTypeId: parseInt(itemTypeId),
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      itemPropertyNames,
      status: 3,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
          OR: [
            {
              AND: [],
            },
          ],
        },
    },
  };
  return params;
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const TYPE_LIMITATION_ATTR = {
  CUSTOM: 'custom',
  COMPUTED: 'computed',
  BOTH: 'both',
};

export const getRecoverWarningByLimitType = limitType => {
  if (isEmpty(limitType)) return '';

  const mesLimitCustomAttr = getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'The number of custom attribute has reached the limit.',
  );
  const mesLimitCoputedAttr = getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'The number of computed attribute has reached the limit.',
  );

  let mes = '';

  if (limitType === TYPE_LIMITATION_ATTR.CUSTOM) {
    mes = `${mesLimitCustomAttr}\n`;
  }

  if (limitType === TYPE_LIMITATION_ATTR.COMPUTED) {
    mes = `${mesLimitCoputedAttr}\n`;
  }

  if (limitType === TYPE_LIMITATION_ATTR.BOTH) {
    mes = mesLimitCustomAttr + mesLimitCoputedAttr;
  }

  mes += getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'If you want to recover attributes, please archived unused attributes.',
  );

  return (
    <span
      style={{ whiteSpace: 'pre-line' }}
      dangerouslySetInnerHTML={{ __html: mes }}
    />
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.item_property_name,
      label: item.item_property_display,
    });
  });
  return data;
};
export const typeAttribute = select => {
  let isType = false;
  select.forEach(item => {
    if (item.type !== 2 && item.compute_type !== 'custom_function') {
      isType = true;
    }
  });
  return isType;
};

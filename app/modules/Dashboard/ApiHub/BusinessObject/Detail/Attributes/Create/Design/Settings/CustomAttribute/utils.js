/* eslint-disable camelcase */
import React from 'react';
import { getTranslateMessage } from '../../../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../../../messages/constant';
import {
  initElementExplicit,
  MAP_INPUT_TEMPLATE,
  MAP_VALIDATE,
  Template3,
} from '../../../../../../../../../../utils/web/renderForm';
import {
  MAP_DATA_TYPE,
  DATA_ATTR_TYPE,
  OPTION_INPUT_VIA,
  MAP_OPTION_INPUT_VIA,
} from '../../../../../../../../../../services/Abstract.data';
import ObjectSettings from './ObjectSettings';
import { getDefaultVal } from '../../../../../../../../../../components/common/InputLanguage/utils';
import {
  toEntryAPI as toEntryAPIObjectSettings,
  toEntryFE as toEntryFEObjectSettings,
  validateAll as validateObjectSettings,
} from './ObjectSettings/utils';
import { getIsFilter, getIsSort } from '../utils';
import { safeParse } from '../../../../../../../../../../utils/common';
import { regexInputFomula } from '../../../../../../../EventSources/EventAttributes/Create/utils';
import { formularFunctionGroupsCustomAttribute } from '../../../../../../../../../../containers/UIDev/UIFormular/FunctionFieldsCustomAttribute/utils';
import { dynamicSort } from '../../../../../../../../../../utils/web/utils';

const MAP_TITLE = {
  dataTransformation: getTranslateMessage(
    TRANSLATE_KEY._,
    'Data Transformation',
  ),
  object: getTranslateMessage(TRANSLATE_KEY._TITL_OBJECT, 'Object'),
  groupAttribute: getTranslateMessage(
    TRANSLATE_KEY._TITL_GROUP_ATTRIBUTE,
    'Group attribute',
  ),
  code: getTranslateMessage(
    TRANSLATE_KEY._TITL_ATTRIBUTE_INTERNAL_CODE,
    'Attribute internal code',
  ),
  dataType: getTranslateMessage(TRANSLATE_KEY._TITL_DATA_TYPE, 'Data type'),
  isRequired: getTranslateMessage(
    TRANSLATE_KEY._TITL_IS_REQUIRED,
    'Is required',
  ),
  autoSuggestion: getTranslateMessage(
    TRANSLATE_KEY._TITL_AUTO_SUGGESTION,
    'Auto Suggestion',
  ),
  isEncryptable: getTranslateMessage(
    TRANSLATE_KEY._TITL_ENABLE_DATA_ENCRYPTION,
    'Enable Data Encryption',
  ),
  isId: getTranslateMessage(
    TRANSLATE_KEY._TITL_IS_IDENTITY_ATTR,
    'Is Identity Attribute',
  ),
  name: getTranslateMessage(
    TRANSLATE_KEY._TITL_ATTRIBUTE_NAME,
    'Attribute name',
  ),
  description: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  fieldFormat: getTranslateMessage(
    TRANSLATE_KEY._TITL_FIELD_FORMAT,
    'Field format',
  ),
  attributeType: getTranslateMessage(
    TRANSLATE_KEY._TITL_ATTRIBUTE_TYPE,
    'Attribute type',
  ),
  displayFormat: getTranslateMessage(
    TRANSLATE_KEY._RENDER_FORMAT,
    'Display Format',
  ),
  personalInformation: getTranslateMessage(
    TRANSLATE_KEY._,
    'Personal Identifiable Information',
  ),
  formula: getTranslateMessage(TRANSLATE_KEY._TITL_FUNCTION, 'Function'),
};

const DEFAULT_VALUE_VIA = {
  name: 'singleLineText',
  label: 'Single-Line Text',
  translateCode: 'Single-Line Text',
  value: 'singleLineText',
};

const MAP_INPUT_TYPE_EXTEND = {
  objectSettings: props => (
    <Template3 {...props}>
      <ObjectSettings
        {...props}
        onChange={props.onChange(props.name)}
        initData={props.initValue}
      />
    </Template3>
  ),
};
export const initDefaultDataConfig = () => ({
  isValidate: false,
  disabled: false,
  infoFields: [
    'object',
    'groupAttribute',
    'attributeType',
    // 'name',
    'code',
    'description',
  ],
  configFields: [
    'dataType',
    // 'objectSettings',
    // 'displayFormat',
    'isRequired',
    // 'autoSuggestion',
    'personalInformation',
    'isEncryptable',
    'isInputViaUI',
    'selectViaInputType',
  ],
  // name: {
  //   ...MAP_INPUT_TEMPLATE.multiLangInputNewInputRule,
  //   name: 'name',
  //   isRequired: true,
  //   label: MAP_TITLE.name,
  // },
  code: {
    ...MAP_INPUT_TEMPLATE.attributeCode,
    label: MAP_TITLE.code,
  },
  description: {
    ...MAP_INPUT_TEMPLATE.multiLangInput,
    name: 'description',
    label: MAP_TITLE.description,
    isValidate: true,
  },
  object: {
    ...MAP_INPUT_TEMPLATE.singleLineText,
    name: 'object',
    label: MAP_TITLE.object,
    isValidate: true,
  },
  groupAttribute: {
    ...MAP_INPUT_TEMPLATE.selectDropdown,
    name: 'groupAttribute',
    label: MAP_TITLE.groupAttribute,
    isValidate: true,
  },
  attributeType: {
    ...MAP_INPUT_TEMPLATE.singleLineText,
    name: 'attributeType',
    label: MAP_TITLE.attributeType,
    isValidate: true,
  },
  dataType: {
    ...MAP_INPUT_TEMPLATE.selectDropdown,
    name: 'dataType',
    label: MAP_TITLE.dataType,
    options: DATA_ATTR_TYPE,
    mapOptions: MAP_DATA_TYPE,
    onlyParent: false,
  },
  isRequired: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'isRequired',
    label: MAP_TITLE.isRequired,
    validate: MAP_VALIDATE.checkbox,
    isValidate: true,
  },
  autoSuggestion: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'autoSuggestion',
    label: MAP_TITLE.autoSuggestion,
    validate: MAP_VALIDATE.checkbox,
    isValidate: true,
  },
  personalInformation: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'personalInformation',
    label: MAP_TITLE.personalInformation,
    isValidate: true,
    validate: MAP_VALIDATE.checkbox,
  },
  isEncryptable: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'isEncryptable',
    label: MAP_TITLE.isEncryptable,
    validate: MAP_VALIDATE.checkbox,
    isValidate: true,
  },
  isId: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'isId',
    label: MAP_TITLE.isId,
    validate: MAP_VALIDATE.checkbox,
    isValidate: true,
  },
  displayFormat: {
    ...MAP_INPUT_TEMPLATE.displayFormat,
    label: MAP_TITLE.displayFormat,
    isValidate: true,
  },
  objectSettings: {
    ...initElementExplicit({
      name: 'objectSettings',
      value: [],
      isRequired: true,
      label: MAP_TITLE.objectSettings,
      validate: validateObjectSettings,
    }),
    componentEl: MAP_INPUT_TYPE_EXTEND.objectSettings,
  },
  isInputViaUI: {
    ...MAP_INPUT_TEMPLATE.checkboxCustomLabelTwoLevel,
    name: 'isInputViaUI',
    label: 'Enable',
    labelRoot: 'Input Via UI',
    isShow: false,
  },
  selectViaInputType: {
    ...MAP_INPUT_TEMPLATE.selectDropdown,
    name: 'selectViaInputType',
    label: 'Input type',
    options: OPTION_INPUT_VIA,
    mapOptions: MAP_OPTION_INPUT_VIA,
    isRequired: false,
    value: DEFAULT_VALUE_VIA,
    isShow: false,
    // onlyParent: false,
  },
  formula: {
    ...MAP_INPUT_TEMPLATE.formula,
    label: MAP_TITLE.formula,
    mapFields: {},
    girdLabel: 4,
    girdContent: 8,
    snippets: [
      ...getFuncSnippetsFormula(formularFunctionGroupsCustomAttribute),
    ],
    isNonExpression: true,
    isNoBracket: true,
  },
  dataTransformation: {
    ...MAP_INPUT_TEMPLATE.toggle,
    name: 'dataTransformation',
    label: MAP_TITLE.dataTransformation,
    isValidate: true,
    style: { marginTop: '10px' },
    // description: MAP_TITLE.dataTransformation,
    // xsTitle: 2,
  },
});

export const toEntryAPI = ({ dataConfig, dataInputVia }) => {
  const {
    name,
    code,
    description,
    dataType,
    groupAttribute,
    displayFormat,
    objectSettings,
    isRequired,
    autoSuggestion,
    isEncryptable,
    isId,
    activeObject = {},
    selectViaInputType = {},
    isInputViaUI,
    personalInformation,
    dataTransformation,
    formula,
  } = dataConfig;

  const data = {
    item_type_id: activeObject.itemTypeId,
    item_type_name: activeObject.itemTypeName,
    scope: 'custom_attribute',
    type: 2,
    group_id: (groupAttribute.value || {}).value,
    item_property_name: code.value,
    item_property_display: getDefaultVal(name.value),
    property_display_multilang: name.value,
    description: getDefaultVal(description.value),
    object_metadata: {
      option_metadata: toEntryAPIObjectSettings(objectSettings.value || []),
    },
    data_type: dataType.value.value,
    custom_regular: '',
    display_format: displayFormat.value,
    description_multilang: description.value,
    is_required: isRequired.value ? 1 : 0,
    is_sort: getIsSort({
      dataType: dataType.value.value,
    }),
    is_filter: getIsFilter({
      dataType: dataType.value.value,
    }),
    auto_suggestion: autoSuggestion.value ? 1 : 0,
    is_encrypt: isEncryptable.value ? 1 : 0,
    is_identify_info: personalInformation.value ? 1 : 0,
    is_identity: isId.value ? 1 : 0,
    is_input_via_ui: Number(isInputViaUI.value),
    input_via_ui_value: {
      input_type: isInputViaUI.value ? selectViaInputType.value.value : [],
      value:
        isInputViaUI.value &&
          ['radio', 'dropdown', 'datePicker'].includes(
            selectViaInputType.value.value,
          )
          ? mapDataInputViaUI(
            selectViaInputType.value.value,
            dataInputVia[selectViaInputType.value.value],
          )
          : {},
      alignment_type:
        isInputViaUI.value && ['radio'].includes(selectViaInputType.value.value)
          ? dataInputVia[selectViaInputType.value.value].alignmentType
          : '',
      ...(selectViaInputType.value.value === 'dropdown' &&
        dataInputVia[selectViaInputType.value.value] && {
        isAssociate: dataInputVia[selectViaInputType.value.value].isAssociate,
        isMultiSelect:
          dataInputVia[selectViaInputType.value.value].isMultiSelect,
        associateWith:
          dataInputVia[selectViaInputType.value.value].associateWith,
      }),
    },
  };
  if (dataType.value.value === 'string') {
    data.is_transform = dataTransformation.value ? 1 : 0;
    if (dataTransformation.value) {
      data.transform_function = regexInputFomula(formula.value);
      data.display_format.valueFormula = formula.value;
    }
  }
  return data;
};

const mapDataInputViaUI = (selectViaInputType, data) => {
  let dataOut = {};

  if (selectViaInputType === 'radio' || selectViaInputType === 'dropdown') {
    dataOut = data.value;
  }
  if (selectViaInputType === 'datePicker') {
    dataOut = {
      ...data.value,
      momentDateTime: data.value.momentDateTime,
      dateType: data.value.dateType,
    };
  }

  return dataOut;
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  const { groupAttribute } = dataConfig;
  const {
    group_id = 0,
    item_property_name = '',
    data_type,
    is_required = false,
    auto_suggestion = false,
    is_encrypt = false,
    // object_metadata = {},
    display_format = {},
    property_display_multilang = {},
    description_multilang = {},
    is_identity = 0,
    is_input_via_ui = 0,
    is_identify_info = 0,
    properties,
    is_transform = 0,
  } = activeRow;
  const { input_via_ui_value = {} } = properties || {};
  const { input_type = '' } = input_via_ui_value;
  const object_metadata = safeParse(activeRow.object_metadata, {});
  const data = {
    name: property_display_multilang,
    code: item_property_name,
    description: description_multilang,
    dataType: MAP_DATA_TYPE[data_type] || {},
    groupAttribute:
      groupAttribute.mapOptions[group_id] || groupAttribute.options[0],
    displayFormat: display_format,
    objectSettings: toEntryFEObjectSettings(
      object_metadata.option_metadata || [],
    ),
    isRequired: is_required === 1,
    autoSuggestion: auto_suggestion === 1,
    personalInformation: is_identify_info === 1,
    isEncryptable: is_encrypt === 1,
    isId: is_identity === 1,
    isInputViaUI: Boolean(is_input_via_ui),
    selectViaInputType: MAP_OPTION_INPUT_VIA[input_type],
  };
  if (data_type === 'string') {
    data.dataTransformation = Number(is_transform) === 1;
    if (Number(is_transform) === 1) {
      data.formula = display_format.valueFormula;
    }
  }
  return data;
};

export const mapValueViaToFE = ({ activeRow }) => {
  const { properties } = activeRow;
  const { input_via_ui_value = {} } = properties || {};
  const {
    value = [],
    input_type = '',
    associateWith,
    isAssociate,
    isMultiSelect,
  } = input_via_ui_value;
  const data = {
    [input_type]: value,
    inputType: input_type,
    ...(associateWith !== undefined && { associateWith }),
    ...(isAssociate !== undefined && { isAssociate }),
    ...(isMultiSelect !== undefined && { isMultiSelect }),
  };

  return data;
};

export const mapAcceptedActionToFE = ({ storageType = 1, use = 'default' }) => {
  if (use === 'create') {
    return MAP_ACCEPTED_ACTION_CREATE;
  }
  if (use === 'update') {
    return MAP_ACCEPTED_ACTION_UPDATE[storageType] || {};
  }
  return {};
};

const MAP_ACCEPTED_ACTION_CREATE = {
  object: false,
  groupAttribute: true,
  attributeType: false,
  name: true,
  code: true,
  description: true,
  dataType: true,
  displayFormat: true,
  isRequired: true,
  autoSuggestion: true,
  isEncryptable: true,
  isId: true,
  isInputViaUI: true,
  selectViaInputType: true,
  personalInformation: true,
  formula: true,
  dataTransformation: true,
};

const MAP_ACCEPTED_ACTION_UPDATE = {
  1: {
    edit: true,
    groupAttribute: true,
    formula: true,
    dataTransformation: true,
  },
  2: {
    edit: true,
    groupAttribute: true,
    name: true,
    description: true,
    displayFormat: true,
    autoSuggestion: true,
    isEncryptable: true,
    isId: true,
    isInputViaUI: true,
    selectViaInputType: true,
    personalInformation: true,
    formula: true,
    dataTransformation: true,
  },
  3: {
    groupAttribute: true,
    name: true,
    description: true,
    displayFormat: true,
    isRequired: true,
    autoSuggestion: true,
    isEncryptable: true,
    isId: true,
    isInputViaUI: true,
    selectViaInputType: true,
    personalInformation: true,
    formula: true,
    dataTransformation: true,
  },
};

export const MAP_DATA_KEY_INPUT_VIA = {
  SETTING_INPUT_TYPE_RADIO: 'radio',
  SETTING_INPUT_TYPE: 'dropdown',
  SETTING_INPUT_TYPE_DATE_PICKER: 'datePicker',
  SETTING_INPUT_ALIGNMENT_TYPE: 'radio',
};

// export const OPTION_INPUT_VIA_WITH_DATATYPE = {
//   stringText: [
//     {
//       name: 'singleLineText',
//       label: 'Single-Line Text',
//       translateCode: 'Single-Line Text',
//       value: 'singleLineText',
//     },
//     {
//       name: 'multiLineText',
//       label: 'Multi-Line Text',
//       translateCode: 'Multi-Line Text',
//       value: 'multiLineText',
//     },
//     {
//       name: 'singleCheckbox',
//       label: 'Single Checkbox',
//       translateCode: 'Single Checkbox',
//       value: 'singleCheckbox',
//     },
//     {
//       name: 'radio',
//       label: 'Radio',
//       translateCode: 'Radio',
//       value: 'radio',
//     },
//     {
//       name: 'dropdown',
//       label: 'Dropdown',
//       translateCode: 'Dropdown',
//       value: 'dropdown',
//     },
//   ],

//   number: [
//     {
//       name: 'number',
//       label: 'Number',
//       translateCode: 'Number',
//       value: 'number',
//     },
//   ],

//   datetime: [
//     {
//       name: 'datePicker',
//       label: 'Date picker',
//       translateCode: 'Date picker',
//       value: 'datePicker',
//     },
//   ],

//   boolean: [
//     {
//       name: 'singleCheckbox',
//       label: 'Single Checkbox',
//       translateCode: 'Single Checkbox',
//       value: 'singleCheckbox',
//     },
//   ],
// };

export const OPTION_INPUT_VIA_WITH_DATATYPE = {
  number: [
    {
      name: 'number',
      label: 'Number',
      translateCode: 'Number',
      value: 'number',
    },
  ],
  string: [
    {
      name: 'singleLineText',
      label: 'Single-Line Text',
      translateCode: 'Single-Line Text',
      value: 'singleLineText',
    },
    {
      name: 'multiLineText',
      label: 'Multi-Line Text',
      translateCode: 'Multi-Line Text',
      value: 'multiLineText',
    },
    {
      name: 'singleCheckbox',
      label: 'Single Checkbox',
      translateCode: 'Single Checkbox',
      value: 'singleCheckbox',
    },
    {
      name: 'radio',
      label: 'Radio',
      translateCode: 'Radio',
      value: 'radio',
    },
    {
      name: 'dropdown',
      label: 'Dropdown',
      translateCode: 'Dropdown',
      value: 'dropdown',
    },
  ],
  text: [
    {
      name: 'singleLineText',
      label: 'Single-Line Text',
      translateCode: 'Single-Line Text',
      value: 'singleLineText',
    },
    {
      name: 'multiLineText',
      label: 'Multi-Line Text',
      translateCode: 'Multi-Line Text',
      value: 'multiLineText',
    },
    {
      name: 'singleCheckbox',
      label: 'Single Checkbox',
      translateCode: 'Single Checkbox',
      value: 'singleCheckbox',
    },
    {
      name: 'radio',
      label: 'Radio',
      translateCode: 'Radio',
      value: 'radio',
    },
    {
      name: 'dropdown',
      label: 'Dropdown',
      translateCode: 'Dropdown',
      value: 'dropdown',
    },
  ],
  datetime: [
    {
      name: 'datePicker',
      label: 'Date picker',
      translateCode: 'Date picker',
      value: 'datePicker',
    },
  ],
  boolean: [
    {
      name: 'singleCheckbox',
      label: 'Single Checkbox',
      translateCode: 'Single Checkbox',
      value: 'singleCheckbox',
    },
  ],
  array_number: [],
  array_string: [],
  array_datetime: [],
  object: [],
};
export const fieldGroup = [
  'formula',
  'isRequired',
  'autoSuggestion',
  'personalInformation',
  'isEncryptable',
  'isId',
];
export const getFuncSnippetsFormula = data =>
  data
    .map(func => ({
      displayText: func.functionName,
      text: func.suggestionSyntax,
      className: 'icon-xlab-function-am-bo',
    }))
    .sort(dynamicSort('displayText'));

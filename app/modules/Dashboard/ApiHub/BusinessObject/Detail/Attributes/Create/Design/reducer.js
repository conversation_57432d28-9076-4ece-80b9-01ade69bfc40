/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import produce from 'immer';
import { combineReducers } from 'redux';
import ReduxTypes from '../../../../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import { ACTION_KEY, mapActiveObject, mapAttributeGroups } from './utils';
import { getAttrType } from '../../Detail/utils';
import {
  MAX_LCODE,
  serializeLabelToCodeAttr,
} from '../../../../../../../../utils/web/utils';
import {
  getUntitledCopyName,
  getUntitledName,
} from '../../../../../../../../utils/web/properties';
import { getCurrentAccessUserId } from '../../../../../../../../utils/web/cookie';
import { getListLanguage } from '../../../../../../../../utils/web/portalSetting';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import { isEmpty } from 'lodash';

export const initState = () => ({
  isInitDone: false,
  activeRow: {},
  design: 'create',
  isLoadingConfigFields: false,
  isLoading: true,
  isDoing: false,
  activeObject: {},

  itemTypeId: null,
  attributeGroups: {
    map: {},
    list: [],
  },
  dataToAPI: {},
  errorFromAPI: {},
  copyId: null,
  attrType: {},
  isShowConfirmForceRun: false,
  isOpenWarningLimit: false,
  drawerArchiveAttr: {
    isOpen: false,
    attrType: null,
  },
  modalComputationLimited: {
    isOpen: false,
    warningMes: '',
  },
  checkPermision: {},
  typeDataView: 1,
  attributeName: getUntitledName('Untitled Attribute'),
  errorName: '',
  isLoadingName: false,
});

export const mainReducerFor = _moduleConfig => {
  const PREFIX = MODULE_CONFIG.key;

  const mainReducer = (state = initState(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initState();
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          draft.isInitDone = false;
          const {
            activeRow,
            design,
            itemTypeId,
            copyId,
            typeDataView = 1,
          } = action.payload;
          draft.design = design;
          draft.itemTypeId = itemTypeId;
          draft.isShowConfirmForceRun = false;
          draft.typeDataView = typeDataView;
          draft.attributeName = getUntitledName('Untitled Attribute');
          if (design === 'create') {
            draft.activeRow = {};
            draft.copyId = copyId;
          } else if (design === 'preview' || design === 'update') {
            draft.activeRow = activeRow;
          }
          return draft;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.dataToAPI = action.payload;
          // console.log({ dataToAPI: action.payload });
          draft.isDoing = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.isDoing = false;
          return draft;
        }
        case `${PREFIX}@@ACTIVE_OBJECT${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.activeObject = mapActiveObject(action.payload);
          draft.isDoing = false;
          return draft;
        }
        case `${PREFIX}@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
          // const { name, errors } = action.payload;
          draft.errorFromAPI = action.payload;
          // draft.dataConfig[name].errors = errors;
          // draft.dataConfig[name].isValidate = false;
          // draft.dataConfig.isValidate = false;
          return draft;
        }
        case `${PREFIX}@@ATTRIBUTE_GROUPS${ReduxTypes.GET_LIST}`: {
          draft.isLoading = true;
          draft.attributeGroups = {
            map: {},
            list: [],
          };
          return draft;
        }
        case `${PREFIX}@@ATTRIBUTE_GROUPS${ReduxTypes.GET_LIST_DONE}`: {
          draft.isLoading = false;
          const map = mapAttributeGroups(action.payload || []);
          draft.attributeGroups = map;
          return draft;
        }
        case `${PREFIX}@@CLONE_ATTRIBUTE${ReduxTypes.INIT}`: {
          const { activeRow, name, createCopy } = action.payload;
          const attributeName = createCopy ? '' : getUntitledCopyName(name);
          const listLanguage = getListLanguage();
          const attributeCode = createCopy
            ? ''
            : serializeLabelToCodeAttr(attributeName);
          // console.log('action.payload', action.payload);
          activeRow.property_display_multilang[
            activeRow.property_display_multilang.DEFAULT_LANG
          ] = attributeName;
          activeRow.item_property_name = attributeCode.substring(0, MAX_LCODE);
          if (activeRow.alert_setting) {
            activeRow.alert_setting.alertAccountIds[0] = parseInt(
              getCurrentAccessUserId(),
            );
            activeRow.c_user_id = getCurrentAccessUserId();
          }

          if (createCopy) {
            listLanguage.forEach(each => {
              if (
                activeRow.property_display_multilang &&
                activeRow.property_display_multilang[each.value]
              ) {
                activeRow.property_display_multilang[each.value] = '';
              }
              if (
                activeRow.description_multilang &&
                activeRow.description_multilang[each.value]
              ) {
                activeRow.description_multilang[each.value] = '';
              }
            });
          }
          draft.activeRow = activeRow;
          draft.attrType = getAttrType(activeRow);
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}@@SHOW_CONFIRM_FORCE_RUN${ReduxTypes.UPDATE_VALUE}`: {
          draft.isShowConfirmForceRun = true;

          return draft;
        }
        case `${PREFIX}@@RESET_ATTRIBUTE_TYPE${ReduxTypes.UPDATE_VALUE}`: {
          draft.activeRow = {};
          draft.attrType = action.payload;

          return draft;
        }
        case `${PREFIX}@@CONFIRM_FORCE_RUN`: {
          draft.isShowConfirmForceRun = false;

          return draft;
        }
        case `${PREFIX}@@CHECK_PERMISSION${ReduxTypes.UPDATE_VALUE}`: {
          const data = action.payload;
          draft.checkPermision = data;
          return draft;
        }
        case `${PREFIX}${ACTION_KEY.TOGGLE_LIMITATION_WARNING}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isOpen } = action.payload;

          draft.isOpenWarningLimit = isOpen;

          return draft;
        }
        case `${PREFIX}${ACTION_KEY.TOGGLE_DRAWER_LIST_ATTR}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isOpen, attrType } = action.payload;

          draft.drawerArchiveAttr.isOpen = isOpen;
          draft.drawerArchiveAttr.attrType = attrType;

          return draft;
        }
        case `${PREFIX}${ACTION_KEY.TOGGLE_MODAL_LIMIT_COMPUTED}${
          ReduxTypes.UPDATE
        }`: {
          const { isOpen } = action.payload;

          draft.modalComputationLimited.isOpen = isOpen;
          draft.modalComputationLimited.warningMes = getTranslateMessage(
            TRANSLATE_KEY._WARN_ATTR_COMPUTATIONAL_LIMIT,
            'Currently, the computation limit has been reached, the system will compute this attribute in the next valid time.',
          );

          return draft;
        }
        case `${PREFIX}${ACTION_KEY.CHANGE_ATTRIBUTE_NAME_CREATE}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.attributeName = action.payload;

          return draft;
        }
        case `${PREFIX}${ACTION_KEY.UPDATE_ERROR_NAME}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.errorName = action.payload;

          return draft;
        }

        default:
          return state;
      }
    });

  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

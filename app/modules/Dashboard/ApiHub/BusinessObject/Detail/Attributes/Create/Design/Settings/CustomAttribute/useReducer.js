/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { useMemo, useReducer } from 'react';
import uniqid from 'uniqid';
import ReduxTypes from '../../../../../../../../../../redux/constants';
import {
  mapValueToFE,
  initDefaultDataConfig,
  mapAcceptedActionToFE,
  toEntryAPI,
  mapValueViaToFE,
  OPTION_INPUT_VIA_WITH_DATATYPE,
} from './utils';
import {
  init,
  initDone,
  reset,
  updateValue,
} from '../../../../../../../../../../redux/actions';
import {
  initDisplayFormat,
  isShowConfig,
  isShowDataTransformation,
} from '../../../../../../../../../../components/common/Modal/ModalConfigFormat/utils';
import {
  MAX_LCODE,
  serializeLabelToCodeAttr,
} from '../../../../../../../../../../utils/web/utils';
import { getDefaultVal } from '../../../../../../../../../../components/common/InputLanguage/utils';
import {
  DATA_ATTR_TYPE,
  MAP_DATA_TYPE,
} from '../../../../../../../../../../services/Abstract.data';
import {
  removeField,
  addFieldRightAfterKey,
  validateAll,
  isAudience,
} from '../../../../../../../../../../utils/web/renderForm.utils';
import { checkPermisionDecrypt, isShowAutoSuggestion } from '../utils';
import {
  checkTooManyBrackets,
  regexCodeFormula,
  regexInputFomula,
  validateCode,
} from '../../../../../../../EventSources/EventAttributes/Create/utils';
import { formularFunctionGroupsCustomAttribute } from '../../../../../../../../../../containers/UIDev/UIFormular/FunctionFieldsCustomAttribute/utils';
const getAllFields = (dataConfig = {}) =>
  [].concat(dataConfig.infoFields, dataConfig.configFields);

export const initState = () => ({
  isInitDone: false,
  dataConfig: initDefaultDataConfig(),
  activeRow: {},
  design: 'create',
  isLoading: true,
  isDoing: false,
  disabledSave: false,
  toEntryAPI,
  triggerSave: 0,
  dataInputVia: {
    type: 'dropdown',
    datePicker: {
      value: {
        momentDateTime: new Date(),
        dateType: 'DD_MM_YYYY_HH_MM_SS',
      },
      isValidate: true,
    },
    radio: {
      value: [
        {
          id: uniqid(),
          value: '',
          label: '',
          isPreSelect: true,
          isRemove: false,
        },
      ],
      isValidate: true,
      alignmentType: {
        horizontal: {
          label: 'Horizontal',
          value: 'horizontal',
          isChecked: true,
        },
        vertical: {
          label: 'Vertical',
          value: 'vertical',
          isChecked: false,
        },
      },
    },
    dropdown: {
      isAssociate: false,
      isMultiSelect: false,
      associateWith: [],
      // options
      value: [
        {
          id: uniqid(),
          value: '',
          label: '',
          isPreSelect: true,
          isRemove: false,
          associates: {},
        },
      ],
      isValidate: false,
    },
  },
  customInputs: [],
  isOpenEncry: false,
  fomularFunction: formularFunctionGroupsCustomAttribute,
});

const fromReducer = (state, action) => {
  return produce(state, draft => {
    switch (action.type) {
      case `${ReduxTypes.RESET}`: {
        return initState();
      }
      case `${ReduxTypes.INIT}`: {
        draft.isInitDone = false;
        draft.isLoading = true;
        const {
          globalData,
          globalData: {
            design = 'create',
            attributeGroups,
            activeObject,
            activeRow,
          },
        } = action.payload;
        draft.activeRow = activeRow || {};
        draft.globalData = globalData;
        draft.design = design;
        draft.dataConfig.activeObject = activeObject;
        draft.dataConfig.groupAttribute.options = attributeGroups.list;
        draft.dataConfig.groupAttribute.mapOptions = attributeGroups.map;
        return draft;
      }
      case `${ReduxTypes.INIT_DONE}`: {
        const {
          dataConfig,
          activeRow,
          activeRow: { storage_type },
          globalData: { design, activeObject, attrType, copyId, createCopy },
        } = state;
        const { itemTypeId } = activeObject;
        const itemTypeIdStories = [-1009, -1010, -1011];
        if (isAudience(itemTypeId)) {
          draft.dataConfig.configFields = addFieldRightAfterKey(
            draft.dataConfig.configFields,
            'isEncryptable',
            'isId',
          );
        }
        const mapValue = mapValueToFE({ activeRow, dataConfig });
        const mapAcceptedAction = mapAcceptedActionToFE({
          use: design,
          storageType: storage_type,
        });

        // show display format
        const dataTypeValue = (mapValue.dataType || {}).value;
        const isShow = isShowConfig(dataTypeValue);
        if (isShow) {
          draft.dataConfig.configFields = addFieldRightAfterKey(
            draft.dataConfig.configFields,
            'dataType',
            'displayFormat',
          );
          draft.dataConfig.displayFormat.dataType = dataTypeValue;
          if (design === 'create') {
            draft.dataConfig.displayFormat.value = initDisplayFormat(
              dataTypeValue,
            );
            draft.dataConfig.displayFormat.initValue = initDisplayFormat(
              dataTypeValue,
            );
          }
        }
        if (dataTypeValue === MAP_DATA_TYPE.object.value) {
          draft.dataConfig.configFields = addFieldRightAfterKey(
            draft.dataConfig.configFields,
            'dataType',
            'objectSettings',
          );
        }

        if (isShowAutoSuggestion(dataTypeValue, '', mapValue.code)) {
          draft.dataConfig.configFields = addFieldRightAfterKey(
            draft.dataConfig.configFields,
            'isRequired',
            'autoSuggestion',
          );
        }
        if (isShowDataTransformation(mapValue.dataType.value)) {
          const indexOfisRequired = draft.dataConfig.configFields.indexOf(
            'isRequired',
          );
          draft.dataConfig.configFields.splice(
            indexOfisRequired,
            0,
            'dataTransformation',
          );
          if (mapValue.dataTransformation) {
            draft.dataConfig.configFields.splice(
              indexOfisRequired + 1,
              0,
              'formula',
            );
          }
        }
        const allFields = getAllFields(draft.dataConfig);
        allFields.forEach(each => {
          if (mapValue[each]) {
            draft.dataConfig[each].value = mapValue[each];
            draft.dataConfig[each].initValue = mapValue[each];
          }

          if (each === 'isInputViaUI') {
            draft.dataConfig.selectViaInputType.isShow = mapValue[each];
          }

          draft.dataConfig[each].disabled = !mapAcceptedAction[each];
          if (design === 'update' || (design === 'create' && copyId)) {
            const { isValidate, errors } = draft.dataConfig[each].validate(
              draft.dataConfig[each],
            );

            if (each === 'dataType' && itemTypeIdStories.includes(itemTypeId)) {
              draft.dataConfig.isInputViaUI.isShow = true;

              draft.dataConfig.selectViaInputType.value =
                OPTION_INPUT_VIA_WITH_DATATYPE[mapValue[each].value][0];
              draft.dataConfig.selectViaInputType.options =
                OPTION_INPUT_VIA_WITH_DATATYPE[mapValue[each].value];
            }

            draft.dataConfig[each].errors = errors;
            if (each !== 'dataTransformation') {
              draft.dataConfig[each].isValidate = isValidate;
            }
          }
          if (each === 'dataType' && itemTypeIdStories.includes(itemTypeId)) {
            draft.dataConfig[each].options = DATA_ATTR_TYPE.filter(
              type => type.value !== 'array' && type.value !== 'object',
            );
          }
        });
        // draft.dataConfig.isValidate = validateAll(allFields, draft.dataConfig);

        // MAP DATA INPUT VIA TO FE
        const mapValueVia = mapValueViaToFE({ activeRow });
        const { inputType } = mapValueVia;
        draft.dataInputVia.type = inputType;
        if (
          inputType &&
          ['datePicker', 'radio', 'dropdown'].includes(inputType)
        ) {
          draft.dataInputVia[inputType].value = mapValueVia[inputType];
        }

        if (inputType === 'dropdown') {
          draft.dataInputVia[inputType].associateWith =
            mapValueVia.associateWith;
          draft.dataInputVia[inputType].isAssociate = mapValueVia.isAssociate;
          draft.dataInputVia[inputType].isMultiSelect =
            mapValueVia.isMultiSelect;
        }

        draft.dataConfig.object.value = activeObject.translateLabel;
        draft.dataConfig.attributeType.value = itemTypeIdStories.includes(
          itemTypeId,
        )
          ? 'Custom'
          : attrType.attributeType;
        // draft.dataConfig.isValidate = true;
        draft.dataConfig.personalInformation.disabled = false;
        draft.dataConfig.isEncryptable.disabled = false;

        if (mapValue.personalInformation) {
          draft.dataConfig.isEncryptable.value = true;
          draft.dataConfig.isEncryptable.disabled = true;
        }
        // map code in formula
        if (mapValue.code) {
          const dataTmp = structuredClone(
            formularFunctionGroupsCustomAttribute,
          );
          dataTmp.map(each => {
            each.suggestionSyntax = each.suggestionSyntax.replace(
              'expression',
              `$${mapValue.code}`,
            );
          });
          if (mapValue.formula) {
            const formulaValue = regexCodeFormula(
              mapValue.formula,
              mapValue.code,
            );
            draft.dataConfig.formula.value = formulaValue;
            draft.dataConfig.formula.initValue = formulaValue;
          }
          draft.fomularFunction = dataTmp;
        } else {
          const dataTmp = structuredClone(
            formularFunctionGroupsCustomAttribute,
          );
          draft.fomularFunction = dataTmp;
        }
        draft.dataConfig.disabled = !mapAcceptedAction.edit;
        draft.isInitDone = true;
        draft.isLoading = false;

        if (createCopy) {
          draft.dataConfig.isValidate = true;
        }

        return draft;
      }
      case `${ReduxTypes.UPDATE}`: {
        draft.isDoing = true;
        return draft;
      }
      case `${ReduxTypes.UPDATE_DONE}`: {
        draft.isDoing = false;
        return draft;
      }
      case `@@VALIDATE${ReduxTypes.UPDATE_VALUE}`: {
        const allFields = getAllFields(draft.dataConfig);
        allFields.forEach(each => {
          const { isValidate, errors } = draft.dataConfig[each].validate(
            draft.dataConfig[each],
          );

          draft.dataConfig[each].errors = errors;
          if (each !== 'dataTransformation') {
            draft.dataConfig[each].isValidate = isValidate;
          }
          if (each === 'formula') {
            const isValidateFormula = checkTooManyBrackets(
              draft.dataConfig[each].value,
            );
            if (isValidateFormula) {
              draft.dataConfig.formula.errors = 'Exceeded 3 nesting levels';
              draft.dataConfig.formula.isValidate = !isValidateFormula;
            }
            const dataWithFormat = regexInputFomula(
              draft.dataConfig[each].value,
            );
            if (!validateCode(dataWithFormat)) {
              draft.dataConfig.formula.errors = 'Syntax error';
              draft.dataConfig.formula.isValidate = validateCode(
                dataWithFormat,
              );
            }
          }
        });
        draft.dataConfig.isValidate = validateAll(allFields, draft.dataConfig);
        let validateInputVia = true;
        // draft.dataConfig.isInputViaUI.value === true
        //   ? draft.dataInputVia[state.dataInputVia.type].isValidate
        //   : true;
        if (
          draft.dataConfig.isInputViaUI.value &&
          ['radio', 'datePicker', 'dropdown'].includes(
            state.dataConfig.selectViaInputType.value.value,
          )
        ) {
          validateInputVia =
            draft.dataInputVia[state.dataInputVia.type].isValidate;
        }

        if (draft.dataConfig.isValidate && validateInputVia) {
          draft.triggerSave += 1;
        }

        return draft;
      }
      case `@@DATA_CONFIG${ReduxTypes.UPDATE_VALUE}`: {
        const { name, value, user } = action.payload;
        const {
          globalData: { design, activeObject },
        } = state;
        const isCheckPermision = checkPermisionDecrypt(
          user.decryptPermission,
          state.activeRow,
        );
        const { itemTypeId } = activeObject;
        const itemTypeIdStories = [-1009, -1010, -1011];

        draft.dataConfig[name].value = value;
        draft.dataConfig[name].errors = [];
        if (name === 'isInputViaUI') {
          draft.dataConfig.selectViaInputType.isShow = value;

          draft.dataConfig.selectViaInputType.value =
            OPTION_INPUT_VIA_WITH_DATATYPE[
            state.dataConfig.dataType.value.value
            ][0];
        }

        if (name === 'selectViaInputType') {
          draft.dataInputVia.type = value.value;
        }

        if (name === 'dataType') {
          const dataTypeValue = value.value;
          // console.log('dataTypeValue', dataTypeValue);

          if (itemTypeIdStories.includes(itemTypeId)) {
            draft.dataConfig.isInputViaUI.isShow = true;
            // draft.dataConfig.selectViaInputType.isShow = true;

            draft.dataConfig.selectViaInputType.options =
              OPTION_INPUT_VIA_WITH_DATATYPE[value.value];

            draft.dataConfig.selectViaInputType.value =
              OPTION_INPUT_VIA_WITH_DATATYPE[value.value][0];

            if (
              dataTypeValue === 'object' ||
              dataTypeValue === 'array_number' ||
              dataTypeValue === 'array_string' ||
              dataTypeValue === 'array_datetime'
            ) {
              draft.dataConfig.isInputViaUI.isShow = false;
              draft.dataConfig.selectViaInputType.isShow = false;
            }
          }

          const isShow = isShowConfig(dataTypeValue);

          if (isShow) {
            draft.dataConfig.configFields = addFieldRightAfterKey(
              draft.dataConfig.configFields,
              'dataType',
              'displayFormat',
            );
          } else {
            draft.dataConfig.configFields = removeField(
              draft.dataConfig.configFields,
              'displayFormat',
            );
          }

          if (dataTypeValue === MAP_DATA_TYPE.object.value) {
            draft.dataConfig.configFields = addFieldRightAfterKey(
              draft.dataConfig.configFields,
              'dataType',
              'objectSettings',
            );
          } else {
            draft.dataConfig.configFields = removeField(
              draft.dataConfig.configFields,
              'objectSettings',
            );
          }
          if (isShowAutoSuggestion(dataTypeValue)) {
            draft.dataConfig.configFields = addFieldRightAfterKey(
              draft.dataConfig.configFields,
              'isRequired',
              'autoSuggestion',
            );
            draft.dataConfig.autoSuggestion.value = true;
            draft.dataConfig.autoSuggestion.initValue = true;
          } else {
            draft.dataConfig.configFields = removeField(
              draft.dataConfig.configFields,
              'autoSuggestion',
            );
            draft.dataConfig.autoSuggestion.value = true;
            draft.dataConfig.autoSuggestion.initValue = true;
          }
          draft.dataConfig.displayFormat.value = initDisplayFormat(
            dataTypeValue,
          );
          draft.dataConfig.displayFormat.initValue = initDisplayFormat(
            dataTypeValue,
          );
          // if (value.value === 'string') {
          //   draft.dataConfig.configFields.push('dataTransformation');
          // }

          draft.dataConfig.displayFormat.dataType = dataTypeValue;
          const indexOfDataTransfomation = draft.dataConfig.configFields.indexOf(
            'dataTransformation',
          );
          const indexOfisRequired = draft.dataConfig.configFields.indexOf(
            'isRequired',
          );
          if (isShowDataTransformation(value.value)) {
            if (indexOfDataTransfomation < 0) {
              draft.dataConfig.configFields.splice(
                indexOfisRequired,
                0,
                'dataTransformation',
              );
            }
          } else if (indexOfDataTransfomation > -1) {
            draft.dataConfig.configFields.splice(indexOfDataTransfomation, 1);
            draft.dataConfig.dataTransformation.value = false;
            draft.dataConfig.formula.value = '';
            const indexOfFormula = draft.dataConfig.configFields.indexOf(
              'formula',
            );
            if (indexOfFormula > -1) {
              draft.dataConfig.configFields.splice(indexOfFormula, 1);
            }
          }
        } else if (name === 'name' && state.design === 'create') {
          const newDefaultVal = getDefaultVal(value);
          const oldDefaultVal = getDefaultVal(state.dataConfig[name].value);
          if (newDefaultVal !== oldDefaultVal) {
            const temptCode = serializeLabelToCodeAttr(newDefaultVal);
            draft.dataConfig.code.value = temptCode.substring(0, MAX_LCODE);
          }
          draft.dataConfig.code.errors = [];
          // const { isValidate, errors } = draft.dataConfig.code.validate(
          //   draft.dataConfig.code,
          // );
          // draft.dataConfig.code.errors = errors;
          // draft.dataConfig.code.isValidate = isValidate;
        }
        if (name === 'personalInformation' && value === true) {
          draft.dataConfig.isEncryptable.value = value;
          draft.dataConfig.isEncryptable.disabled = value;
        } else if (name === 'personalInformation' && value === false) {
          draft.dataConfig.isEncryptable.disabled = value;
        }
        if (
          name === 'isEncryptable' &&
          state.design === 'update' &&
          state.dataConfig.isEncryptable.value &&
          state.activeRow.is_encrypt === 1
        ) {
          draft.isOpenEncry = !state.isOpenEncry;
          draft.dataConfig.isEncryptable.value = true;
        }
        // if (
        //   name === 'isEncryptable' &&
        //   state.design === 'update' &&
        //   // !isCheckPermision &&
        //   state.dataConfig.isEncryptable.value
        // ) {
        //   draft.dataConfig.isEncryptable.value = true;
        // }
        // const { isValidate, errors } = draft.dataConfig[name].validate(
        //   draft.dataConfig[name],
        // );
        // draft.dataConfig[name].errors = errors;
        // draft.dataConfig[name].isValidate = isValidate;
        // const allFields = getAllFields(draft.dataConfig);
        // draft.dataConfig.isValidate = validateAll(allFields, draft.dataConfig);
        if (name === 'dataTransformation') {
          const indexOfisRequired = draft.dataConfig.configFields.indexOf(
            'isRequired',
          );
          if (value) {
            draft.dataConfig.configFields.splice(
              indexOfisRequired,
              0,
              'formula',
            );
          } else {
            const indexOfFormula = draft.dataConfig.configFields.indexOf(
              'formula',
            );
            draft.dataConfig.formula.value = '';
            draft.dataConfig.configFields.splice(indexOfFormula, 1);
          }
        }
        if (draft.dataConfig.code.value) {
          const dataTmp = structuredClone(
            formularFunctionGroupsCustomAttribute,
          );
          dataTmp.map(each => {
            each.suggestionSyntax = each.suggestionSyntax.replace(
              'expression',
              `$${draft.dataConfig.code.value}`,
            );
          });
          if (draft.dataConfig.formula.value) {
            const formulaValue = regexCodeFormula(
              draft.dataConfig.formula.value,
              draft.dataConfig.code.value,
            );
            draft.dataConfig.formula.value = formulaValue;
            draft.dataConfig.formula.initValue = formulaValue;
          }
          draft.fomularFunction = dataTmp;
        } else {
          const dataTmp = structuredClone(
            formularFunctionGroupsCustomAttribute,
          );
          draft.fomularFunction = dataTmp;
        }

        draft.dataConfig.isValidate = true;
        return draft;
      }
      // case `@@VALIDATE${ReduxTypes.UPDATE_VALUE}`: {
      //   return draft;
      // }
      case `@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
        const { name, errors } = action.payload;
        draft.dataConfig[name].errors = errors;
        draft.dataConfig[name].isValidate = false;
        draft.dataConfig.isValidate = false;
        return draft;
      }
      case `@@IS_LOADING${ReduxTypes.UPDATE_VALUE}`: {
        draft.isLoading = action.payload;
        return draft;
      }
      case `@@DATA_INPUT_VIA${ReduxTypes.UPDATE_VALUE}`: {
        const { type, data } = action.payload;
        draft.dataInputVia[type] = { ...data };
        draft.dataInputVia.type = type;
        draft.dataConfig.isValidate = true;
        return draft;
      }
      case `@@CUSTOM_INPUTS${ReduxTypes.UPDATE_VALUE}`: {
        if (action.payload && Array.isArray(action.payload)) {
          draft.customInputs = action.payload;
        }
        return draft;
      }
      case `@@TOGGLE${ReduxTypes.UPDATE_VALUE}`: {
        draft.isOpenEncry = false;

        return draft;
      }
      case `@@DATA_ENCRYP${ReduxTypes.UPDATE_VALUE}`: {
        draft.dataConfig.isEncryptable.value = false;
        return draft;
      }

      case `@@DATA_CONFIG_CODE${ReduxTypes.UPDATE_VALUE}`: {
        const { name } = action.payload;
        const temptCode = serializeLabelToCodeAttr(name);

        draft.dataConfig.code.value = temptCode.substring(0, MAX_LCODE);
        draft.dataConfig.code.errors = [];
        draft.dataConfig.isValidate = true;
        return draft;
      }

      default:
        return state;
    }
  });
};

export const useCustomReducer = () => {
  const [table, dispatch] = useReducer(fromReducer, initState());
  const dispatchAction = useMemo(() => mapDispatchToProps(dispatch), []);
  return [table, dispatchAction];
};

const mapDispatchToProps = dispatch => {
  const PREFIX = '';
  return {
    init: payload => {
      dispatch(init(PREFIX, payload));
    },
    initDone: payload => {
      dispatch(initDone(PREFIX, payload));
    },
    updateIsLoading: payload =>
      dispatch(updateValue(`${PREFIX}@@IS_LOADING`, payload)),
    reset: payload => {
      dispatch(reset(PREFIX, payload));
    },
    onChangeDataConfig: payload =>
      dispatch(updateValue(`@@DATA_CONFIG`, payload)),
    onChangeToggle: payload => dispatch(updateValue(`@@TOGGLE`, payload)),
    onChangeDataInputVia: payload =>
      dispatch(updateValue(`@@DATA_INPUT_VIA`, payload)),
    updateErrorAPI: payload => dispatch(updateValue(`@@ERROR_API`, payload)),
    put: dispatchFn => dispatch(dispatchFn),
    onChangeEncryp: payload => dispatch(updateValue(`@@DATA_ENCRYP`, payload)),
    onChangeCode: payload =>
      dispatch(updateValue(`@@DATA_CONFIG_CODE`, payload)),
  };
};

import { getLabelComputeStatusHistory } from '../../../../../../../utils/web/processStatus';
import {
  CellArray,
  CellCompHistoryId,
  CellComputeStatus,
  CellComputionTrigger,
  CellDate,
  CellNumber,
  CellProcessStatus,
  CellMainBOAttribute,
  CellText,
} from '../../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../../../appConfig';
import {
  getSegmentTypeLabel,
  getLabelComputation,
  getLabelComputationErrorInfoAM,
} from '../../../../../../../services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';
import { ItemTypeNameSegmentIds } from '../../../utils';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    if (tmp.item_property_name === ItemTypeNameSegmentIds) {
      return;
    }
    const tempt = {
      ...tmp,
      id: tmp.compute_id,
      compute_status: getLabelComputeStatusHistory(
        parseInt(tmp.compute_status),
      ),
      original_status: tmp.compute_status,
      segmentTypeDisplay: getSegmentTypeLabel(`${tmp.item_type_id}`),
      trigger_type: getLabelComputation(tmp.trigger_type),
      response_code:
        tmp.response_code === null &&
          (tmp.compute_status === 2 ||
            tmp.compute_status === 1 ||
            tmp.compute_status === 6)
          ? '--'
          : getLabelComputationErrorInfoAM(tmp.response_code),
      // segment_size: safeParse(tmp.segment_size, 'N/A'),
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(
  columnID,
  columnName,
  columnsActive,
  isEdit,
  typeDataView,
) {
  const columns = [
    {
      Header: columnID.label,
      id: columnID.value,
      accessor: columnID.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellCompHistoryId,
      getIsDisabledEditName: () => false,
    },
  ];
  if (columnName != null) {
    columns.push({
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      isShowActionAttr: isEdit,
      typeDataView,
      Cell: CellMainBOAttribute,
      getIsDisabledEditName: () => false,
      isDrawerAttributeHistory: true,
    });
  }
  columns.push({
    Header: 'Info Remaining',
    columns: buildTableColumns(columnsActive),
  });
  return columns;
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (property.value === 'compute_status') {
      column.Cell = CellComputeStatus;
      column.placement = 'compute_status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
    } else if (property.value === 'item_type_id') {
      column.accessor = `segmentTypeDisplay`;
    }
    //  else if (property.value === 'trigger_type') {
    //   column.Cell = CellComputionTrigger;
    // } else if (property.value === 'response_code') {
    //   column.Cell = CellComputionTrigger;
    // }
    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {};

export function getModuleConfig(moduleConfig, storyId, objectType, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = storyId;
  newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};
export const mapDataToConfigFilter = (
  channelActive,
  isMapConfigSuggestionForDetails,
  moduleConfig,
  isUseSelectorApi,
) => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: isUseSelectorApi ? null : 100,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '6-campaign_name',
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== moduleConfig.objectId // Khác object id để check với trường hợp channel all
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: `${isMapConfigSuggestionForDetails ? 'story_id' : 'story_type'
                }`,
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigFilterDetail = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
  };
  if (channelActive && channelActive.value !== 0) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_id',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    isGetOnlySuggestParams: 0,
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_type',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_CAMPAIGN,
      'Listing Campaigns',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

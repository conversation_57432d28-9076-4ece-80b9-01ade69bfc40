/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { createStructuredSelector } from 'reselect';

import Design from '../../Create/Design';
import { StyleWrapper } from './styles';
import { makeSelectDomainMain } from '../selectors';

function SettingPage(props) {
  const [design, setDesign] = useState('update');
  const { activeRow, attrType, itemTypeId } = props.main;

  const onChangeDesign = value => {
    setDesign(value);
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/Settings/BusinessObject/Detail/Attributes/Detail/Settings/index.jsx">
      <StyleWrapper>
        <Design
          design={design}
          activeRow={activeRow}
          attrType={attrType}
          onChangeDesign={onChangeDesign}
          activeStep={1}
          itemTypeId={props.itemTypeId ? props.itemTypeId : itemTypeId}
          callback={props.callback}
          isViewMode={props.isViewMode}
          errors={props.errors}
          newItemTypeId={props.newItemTypeId}
          isUIV2={props.isUIV2}
        />
      </StyleWrapper>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectDomainMain(),
});

const withConnect = connect(
  mapStateToProps,
  null,
);

export default compose(withConnect)(SettingPage);

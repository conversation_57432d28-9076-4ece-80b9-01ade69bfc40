/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, memo, useCallback } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import { UILoading as Loading } from '@xlab-team/ui-components';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { useParams, useHistory } from 'react-router-dom';
import { isEmpty } from 'lodash';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import LayoutContent from '../../../../../../../components/Templates/LayoutContent';
import SettingsPage from './Settings';
import PageNotFound from '../../../../../../../components/Templates/LayoutContent/PageNotFound';
import LayoutLoading from '../../../../../../../components/Templates/LayoutContent/LayoutLoading';

// Selectors
import reducer, { initialState } from './reducer';
import saga from './saga';
import {
  makeSelectDomainMain,
  makeSelectActiveRow,
  makeSelectActiveObject,
} from './selectors';

// Actions
import { init, reset } from '../../../../../../../redux/actions';

// Utils
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import { trackEvent } from '../../../../../../../utils/web/utils';
import { getPortalId } from '../../../../../../../utils/web/cookie';

// Contants
import { MODULE_CONFIG } from './config';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import APP from '../../../../../../../appConfig';

// Styles
import { WrapperDetail } from './styles';
import { ItemTypeNameSegmentIds, listitemTypeId } from '../../../utils';

const styleOverFlowHidden = {
  overflow: 'auto !important',
  justifyContent: 'flex-start',
  width: '100%',
};

export function DetailPage(props) {
  const history = useHistory();
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });
  const { tab = 'settings', ...restParams } = useParams();

  const searchParams = new URLSearchParams(window.location.search);

  const { main = initialState(), newItemTypeId } = props;

  const itemTypeIdSearchParam = searchParams.get('itemTypeId');
  const itemPropertyNameSearchParam = searchParams.get('itemPropertyName');

  const itemTypeId =
    itemTypeIdSearchParam || props.itemTypeId || restParams.itemTypeId;

  const itemPropertyName =
    itemPropertyNameSearchParam ||
    props.itemPropertyName ||
    restParams.itemPropertyName;

  useEffect(() => {
    if (!isEmpty(props.settings)) {
      props.init({ settings: props.settings });
    } else {
      props.init({ itemPropertyName, itemTypeId, tab });
    }

    return () => {
      props.reset();
    };
  }, [itemPropertyName, itemTypeId, props.settings]);

  useEffect(() => {
    if (!main.isLoading) {
      const object = {
        id: itemTypeId || props.itemTypeId,
        name: main.activeRow.item_property_display,
        type: 'attribute',
      };
      trackEvent('object', 'view', object);
    }
  }, [main.isLoading]);

  const onClickBackToList = useCallback(() => {
    history.push(
      `${
        APP.PREFIX
      }/${getPortalId()}/api-hub/objects/detail/${itemTypeId}/attributes`,
    );
  }, []);

  const callback = (type, _data) => {
    switch (type) {
      default:
        break;
    }
  };

  if (props.main.isLoading && !props.isViewMode) {
    return <Loading isLoading isWhite />;
  }

  const renderContent = () => {
    if (Object.keys(props.activeRow).length === 0) {
      return (
        !props.isViewMode && (
          <PageNotFound
            labelButton={getTranslateMessage(TRANSLATE_KEY._, 'Back to list')}
            labelPageNotFound={getTranslateMessage(
              TRANSLATE_KEY._,
              'Attribute Not Found',
            )}
            onClick={onClickBackToList}
          />
        )
      );
    }
    return (
      <SettingsPage
        callback={callback}
        isViewMode={props.isViewMode}
        errors={props.errors}
        newItemTypeId={newItemTypeId}
        isUIV2={props.isUIV2}
      />
    );
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/Settings/BusinessObject/Detail/Attributes/Detail/index.jsx">
      <LayoutContent
        style={styleOverFlowHidden}
        padding="0rem 0.75rem 0.0rem 0.75rem"
        height="100%"
        width="100%"
      >
        <WrapperDetail
          isLoading={props.main.isLoading}
          isViewMode={props.isViewMode}
        >
          {props.isViewMode ? (
            <LayoutLoading isLoading={props.main.isLoading}>
              {renderContent()}
            </LayoutLoading>
          ) : (
            renderContent()
          )}
        </WrapperDetail>
      </LayoutContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectDomainMain(),
  activeRow: makeSelectActiveRow(),
  activeObject: makeSelectActiveObject(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  memo,
)(DetailPage);

import APP from 'appConfig';
import { get } from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import { push } from 'react-router-redux';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';
import { getDetailDone, updateValue } from 'redux/actions';
import ReduxTypes from 'redux/constants';
import { makeSelectPortal } from 'modules/Dashboard/selector';
import SegmentServices from 'services/Segment';
import BusinessObject from 'services/BusinessObject';
import { addMessageToQueue } from 'utils/web/queue';
import { MODULE_CONFIG } from './config';
import {
  makeSelectActiveRow,
  makeSelectSegmentDetailDomainMain,
} from './selectors';
import { safeParse } from '../../../../../../../utils/common';
import { DETAIL_BO_DRAWER } from '../../../../../../../containers/Drawer/DrawerDetailDataObject/constants';

const PREFIX = MODULE_CONFIG.key;

export default function* workerSegmentDetailSaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(
    `${PREFIX}${ReduxTypes.GET_DETAIL}`,
    handleFetchObjectDetail,
  );
  yield takeLatest(
    `${PREFIX}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
  );
}

function* handleInit(action, args) {
  yield all([call(handleFetchObjectDetail)]);
}

function* handleFetchObjectDetail(action) {
  try {
    const { segmentId, versionId } = yield select(
      makeSelectSegmentDetailDomainMain(),
    );
    if (!_isEmpty(segmentId)) {
      // const res = yield call(SegmentServices.data.getDetail, {
      let res;
      let resTmp;
      if (versionId) {
        res = yield call(
          BusinessObject.BOCollection.data.getDetailwithVersion,
          {
            segmentId,
            versionId,
          },
        );
        resTmp = yield call(
          BusinessObject.BOCollection.data.getDetailLastVersion,
          {
            segmentId,
          },
        );
      } else {
        res = yield call(
          BusinessObject.BOCollection.data.getDetailLastVersion,
          {
            segmentId,
          },
        );
      }
      // console.log({ res });
      if (res.code === 200 && res.data) {
        const activeSegment = res.data[0];
        activeSegment.segmentId = activeSegment.segment_id;
        // const itemTypeId = parseInt(get(res, 'data.itemTypeId', ''));
        // if (itemTypeId !== -1003 && itemTypeId !== -1007) {
        //   activeSegment = {};
        // }
        yield put(getDetailDone(PREFIX, activeSegment));
        if (versionId) {
          if (resTmp.code === 200 && resTmp.data) {
            const activeSegmentTmp = resTmp.data[0];
            yield put(
              updateValue(
                `${MODULE_CONFIG.key}@@GET_CURRENT_VERSION`,
                activeSegmentTmp,
              ),
            );
          }
        }
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/Profile/Segment/Detail/saga.js',
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
  }
}

function* handleGoToList(action) {
  const portal = yield select(makeSelectPortal);
  const type = action.payload;
  const activeRow = yield select(makeSelectActiveRow());

  yield put(
    push(
      `${APP.PREFIX}/${
        portal.portalId
      }/api-hub/objects/owner?ui=${DETAIL_BO_DRAWER}&tab=collections&id=${
        activeRow.item_type_id
      }&subTab=settings`,
    ),
  );
}

/* eslint-disable camelcase */
import filtersConfigReducerFor from 'containers/Filters/reducer.config';
import filtersRulesReducerFor from 'containers/Filters/reducer.rules';
import columnConfigReducerFor from 'containers/ModifyColumn/reducer.config';
import tableReducerFor from 'containers/Table/reducer';
import produce from 'immer';
import { combineReducers } from 'redux';
import ReduxTypes from 'redux/constants';
import { MODULE_CONFIG } from './config';
import { mapSingleItemFE } from './utils';

// import { DEFAULT_ACTION } from './constants';
const PREFIX = MODULE_CONFIG.key;

export function initialState() {
  return {
    isLoading: true,
    activeTab: 'configure',
    name: '',
    status: 1,
    processStatus: 3,
    segmentId: null,
    itemTypeId: null,
    activeRow: {},
    versionId: null,
    activeRowCurrentVersion: {},
    itemTypeIdBo: null,
  };
}

/* eslint-disable default-case, no-param-reassign */
const mainReducerFor = () => {
  const mainReducer = (state = initialState(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT}`: {
          const { segmentId, tab, versionId, itemTypeId } = action.payload;
          // console.log({ payload: action.payload });
          draft.segmentId = segmentId;
          draft.isLoading = true;
          draft.activeTab = tab;
          draft.versionId = versionId;
          draft.itemTypeIdBo = itemTypeId;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          draft.isLoading = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.GET_LIST}`: {
          draft.isLoading = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.activeRow = mapSingleItemFE(action.payload);
          draft.itemTypeId = draft.activeRow.item_type_id;
          draft.name = draft.activeRow.segment_display;
          draft.status = parseInt(draft.activeRow.status);
          draft.processStatus = draft.activeRow.process_status;
          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${MODULE_CONFIG.key}@@CHANGE_TAB${ReduxTypes.UPDATE_VALUE}`: {
          draft.activeTab = action.payload;
          return draft;
        }
        case `${MODULE_CONFIG.key}@@GET_CURRENT_VERSION${ReduxTypes.UPDATE_VALUE
          }`: {
            // console.log(action);
            draft.activeRowCurrentVersion = mapSingleItemFE(action.payload);
            return draft;
          }
        case `${MODULE_CONFIG.key}@@ACTIVE_ROW_NAME${ReduxTypes.UPDATE_VALUE
          }`: {
            // console.log(action);
            const { name } = action.payload;
            draft.name = name;
            draft.activeRow.segmentDisplay = name;
            return draft;
          }
        case `${PREFIX}@@SEGMENT_NAME@@${ReduxTypes.UPDATE_VALUE}`: {

          // }
          draft.name = action.payload;
          return draft;
        }
        default:
          return state;
      }
    });
  return mainReducer;
};

const filter = combineReducers({
  rules: filtersRulesReducerFor(PREFIX),
  config: filtersConfigReducerFor(PREFIX),
});

// export default customerReducer;
export default combineReducers({
  main: mainReducerFor(),
  table: tableReducerFor(PREFIX),
  filter,
  column: columnConfigReducerFor(PREFIX),
});

/* eslint-disable react/prop-types */
// Libraries
import React, { memo, useEffect, useMemo, useState } from 'react';
import { UIButton, UILoading as Loading } from '@xlab-team/ui-components';
import HistoryIcon from '@material-ui/icons//History';
import { createStructuredSelector } from 'reselect';
import { useHistory, useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import CustomHeader from 'components/Organisms/CustomHeader';
import LayoutContent from 'components/Templates/LayoutContent';
import LayoutLoading from 'components/Templates/LayoutContent/LayoutLoading';
import PageNotFound from 'components/Templates/LayoutContent/PageNotFound';
import TabConfigure from './Configure';
import ListItems from './Items';
import DataTablePage from '../../DataTable';
import UIModalCommon from 'components/common/UIModalCommon';

// Selectors
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectActiveRow,
  makeSelectSegmentDetailDomainMain,
} from './selectors';

// Actions
import { init, reset, updateValue } from 'redux/actions';

// Utils
import { getTranslateMessage } from 'containers/Translate/util';
import { trackEvent } from '../../../../../../../utils/web/utils';
import { updateUrl } from 'utils/common';
import { getBreadcrums } from './utils';
import { getPortalId } from 'utils/web/cookie';
import { useInjectSaga } from 'utils/injectSaga';
import { useInjectReducer } from 'utils/injectReducer';

// Constants
import APP from 'appConfig';
import TRANSLATE_KEY from 'messages/constant';
import { MODULE_CONFIG } from './config';

// Styles
import { WrapperDetail } from './styles';
import { commonHandleSaveCustomizeColumnsTableSort } from '../../../../../../../containers/ModifyColumn/saga';

const styleOverFlowHidden = { overflow: 'auto !important' };

const MAP_TITLE = {
  navBar: {
    title: getTranslateMessage(
      TRANSLATE_KEY._TAB_OBJECT_COLLECTION,
      'Collections',
    ),
  },
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  },
  tab: {
    members: getTranslateMessage(
      TRANSLATE_KEY._TAB_SEGMENT_CONFIGURE,
      'Confiruge',
    ),
    overview: getTranslateMessage(
      TRANSLATE_KEY._TAB_SEGMENT_OVERVIEW,
      'Overview',
    ),
    configure: getTranslateMessage(
      TRANSLATE_KEY._TAB_SEGMENT_CONFIGURE,
      'Configure',
    ),
    computeHistory: getTranslateMessage(
      TRANSLATE_KEY._MENU_SUB_COMPUATATION_HISTORY,
      'Computation History',
    ),
  },
  itemNameSegment: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'segment(s)',
  ),
  modalWarning: getTranslateMessage(
    TRANSLATE_KEY._,
    'Collection is only changed when you save change',
  ),
  close: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Close'),
};
export function DetailPage(props) {
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });

  const params = useParams();

  const searchParams = new URLSearchParams(window.location.search);

  const segmentId = searchParams.get('id') || params.segmentId;

  const versionId = searchParams.get('versionId') || params.versionId;

  const tab = params.tab || 'configure';

  const itemTypeId = searchParams.get('itemTypeId') || params.itemTypeId;

  const typeDataView = searchParams.get('tab') === 'owner' ? 1 : 2;

  const [isOpenModalWarning, setIsOpenModalWarning] = useState(false);

  const { main, activeRow, subTab, collectionId } = props;
  const history = useHistory();

  // console.log(main);
  useEffect(() => {
    if (versionId) {
      setIsOpenModalWarning(true);
    }
    props.init({ itemTypeId, segmentId, tab, versionId, subTab });
    return () => {
      props.reset();
    };
  }, [itemTypeId, segmentId, tab, versionId]);
  useEffect(() => {
    if (!main.isLoading) {
      const object = {
        id: segmentId,
        name: main.name,
        type: 'collection',
      };
      trackEvent('object', 'view', object);
    }
  }, [main.isLoading]);
  const toggleModalWarning = () => {
    setIsOpenModalWarning(!isOpenModalWarning);
  };

  const callback = (type, data) => {
    // console.log('callback', type, data);
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'EDIT_NAME_SUCCESS': {
        props.updateName(data);
        break;
      }
      default:
        break;
    }
  };

  const onChangeTab = value => {
    props.onChangeTab(value);
    const newUrl = `${APP.PREFIX}/${getPortalId()}/api-hub/objects/detail/${
      activeRow.item_type_id
    }/${typeDataView}/collections/${activeRow.segment_id}/detail`;
    updateUrl(newUrl);
  };

  const onClickBackToList = () => {
    props.handleGoToList(typeDataView);
  };

  const iconName =
    props.main.itemTypeId === -1003 ? 'supervised_user_circle' : 'donut_small';
  const breadcrums = useMemo(() => {
    return getBreadcrums(
      activeRow.item_type_display,
      activeRow.item_type_id,
      activeRow.segment_display,
      typeDataView,
    );
  }, [activeRow.segment_display]);
  const onClickVersionHistory = () => {
    const link = `${APP.PREFIX}/${getPortalId()}/api-hub/objects/detail/${
      activeRow.item_type_id
    }/${typeDataView}/collections/${
      activeRow.segment_id
    }/detail/version-history/${activeRow.collection_version.version}`;
    history.push(link);
  };
  // console.log('breadcrums', breadcrums);
  const renderContent = () => {
    if (props.main.isLoading === true) {
      return <Loading isLoading isWhite />;
    }

    if (Object.keys(props.activeRow).length === 0 && !props.main.isLoading) {
      return (
        <PageNotFound
          labelButton={getTranslateMessage(
            TRANSLATE_KEY._004,
            'Back to Collections',
          )}
          labelPageNotFound={getTranslateMessage(
            TRANSLATE_KEY._004,
            'Collection Not Found',
          )}
          onClick={onClickBackToList}
        />
      );
    }
    if (subTab === 'items') {
      return (
        <>
          <ListItems
            activeRow={activeRow}
            callback={callback}
            itemTypeId={itemTypeId}
            collectionId={collectionId}
          />
        </>
      );
    }
    return (
      <>
        {!props.isUIV2 && (
          <CustomHeader breadcrums={breadcrums} callback={callback} />
        )}
        <LayoutContent
          style={styleOverFlowHidden}
          justifyContent="end"
          height="100%"
          padding="0"
        >
          <WrapperDetail isLoading={props.main.isLoading}>
            <LayoutLoading isLoading={props.main.isLoading}>
              <TabConfigure activeTab={main.activeTab} isUIV2={props.isUIV2} />

              {!props.isUIV2 && (
                <UIButton
                  variant="contained"
                  theme="outline"
                  onClick={onClickVersionHistory}
                  style={{
                    padding: '0 10px 0 5px',
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    border: 'none',
                  }}
                  // iconName="close"
                  reverse
                  iconSize="23"
                >
                  <HistoryIcon
                    style={{ marginRight: '5px' }}
                    color="action"
                    className="icon-history"
                  />
                  Version History
                </UIButton>
              )}
            </LayoutLoading>
          </WrapperDetail>
        </LayoutContent>
        <UIModalCommon
          isOpen={isOpenModalWarning}
          toggle={toggleModalWarning}
          header="Warning"
          cancelLabel={MAP_TITLE.close}
          footer={
            <UIButton
              style={{
                height: '29px',
                color: '#005fb8',
                fontSize: '12px',
                borderRadius: '3px',
                borderColor: '#edeef7',
              }}
              theme="outline"
              onClick={toggleModalWarning}
            >
              Close
            </UIButton>
          }
        >
          {MAP_TITLE.modalWarning}
        </UIModalCommon>
      </>
    );
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/Profile/Segment/Detail/index.jsx">
      {renderContent()}
    </ErrorBoundary>
  );
}

// Customer.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  main: makeSelectSegmentDetailDomainMain(),
  activeRow: makeSelectActiveRow(),
  // common: makeSelectCustomerCommon(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB`, params));
    },
    updateName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ACTIVE_ROW_NAME`, params));
    },
    handleGoToList: data => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_LIST`, data));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  memo,
)(DetailPage);

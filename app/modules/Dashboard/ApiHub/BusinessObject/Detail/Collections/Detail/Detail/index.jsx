/* eslint-disable react/prop-types */
/* eslint-disable import/order */
// Libraries
import React, { useEffect, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
// Constants
import { VERSION_HISTORY } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/constants';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { init, reset } from 'redux/actions';
import reducer from './reducer';
import saga from './saga';

// Components
import { Header } from './Header';
import { VersionList } from './VersionList';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Configs
import { PREFIX, PREFIX_DETAIL } from './config';

// Styles
import {
  StyledGrid,
  WrapperDetail,
  WrapperJourneyDetail,
  WrapperVersionList,
} from './styled';
import {
  makeSelectActiveRow,
  makeSelectSegmentDetailDomainMain,
} from '../selectors';
import { getBreadcrums } from '../utils';
import queryString from 'query-string';
import TabConfigure from '../Configure';
import { makeSelectVersionDetail } from './selectors';
import LayoutContent from '../../../../../../../../components/Templates/LayoutContent';
// Utils

const VersionHistoryDetail = props => {
  const params = useParams();

  const versionId = props.versionId || params.versionId;

  const collectionId = props.collectionId || params.segmentId;

  const typeDataView = params.tab === 'owner' ? 1 : 2;

  const itemTypeId = props.itemTypeId || params.itemTypeId;

  const styleOverFlowHidden = { overflow: 'auto !important' };
  const { versionDetail, channelActive } = props;
  // const prevStateRef = useRef(localStorage.getItem('journey-prev-state'));
  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });

  const {
    dataAPIS: { versionHistoryListing },
    checkPermision = {},
  } = versionDetail;
  useEffect(() => {
    props.initVersionDetail({ segmentId: collectionId, versionId, itemTypeId });
    return () => {
      props.reset();
    };
    // if (prevStateRef.current === 'journey-editing') {
    //   document.body.classList.add(prevStateRef.current);
    // }
  }, [versionId]);
  const breadcrums = useMemo(() => {
    return getBreadcrums(
      versionDetail.activeRow.item_type_display,
      versionDetail.activeRow.item_type_id,
      versionDetail.activeRow.segment_display,
    );
  }, [versionDetail.activeRow.segment_display]);

  return versionDetail.isLoading ? (
    <Loading isLoading isWhite />
  ) : (
    <>
      {!props.isUIV2 && <CustomHeader breadcrums={breadcrums} />}
      <LayoutContent
        style={styleOverFlowHidden}
        justifyContent="end"
        padding="0px"
        height="100%"
      >
        <WrapperDetail isLoading={versionDetail.isLoading}>
          {!props.isUIV2 && (
            <Header
              versionId={+versionId}
              main={versionDetail}
              versionHistoryListing={versionHistoryListing}
              typeDataView={typeDataView}
              checkPermision={checkPermision}
              // journeyInfo={journeyDetail}
              // prevState={prevStateRef.current}
            />
          )}
          <StyledGrid>
            <WrapperJourneyDetail>
              <TabConfigure
                activeRow={versionDetail.activeRow}
                activeTab={props.main.activeTab}
                isVersion
              />
            </WrapperJourneyDetail>
            <WrapperVersionList>
              <VersionList
                typeDataView={typeDataView}
                versionDetail={versionDetail}
                versionId={+versionId}
                versionHistoryListing={versionHistoryListing}
              />
            </WrapperVersionList>
          </StyledGrid>
        </WrapperDetail>
      </LayoutContent>
    </>
  );
};

VersionHistoryDetail.propTypes = {
  initVersionDetail: PropTypes.func,
  versionDetail: PropTypes.object,
  channelActive: PropTypes.object,
};

const mapStateToProps = createStructuredSelector({
  activeRow: makeSelectActiveRow(),
  versionDetail: makeSelectVersionDetail(),
  main: makeSelectSegmentDetailDomainMain(),
});

const mapDispatchToProps = dispatch => ({
  initVersionDetail: params => {
    dispatch(init(PREFIX, params));
  },
  reset: params => {
    dispatch(reset(PREFIX, params));
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(VersionHistoryDetail);

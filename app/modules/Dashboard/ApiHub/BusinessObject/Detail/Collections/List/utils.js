/* eslint-disable import/named */
import _isEmpty from 'lodash/isEmpty';
import { templateSettings } from 'lodash';
import {
  getLabelStatus,
  getLabelStatusWithType,
  getLabelStatusArchive,
} from '../../../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellMainBOCollection,
  CellProcessStatusSegment,
  CellDate,
  CellCurrency,
  CellNumber,
  CellOwner,
  CellArray,
  CellToggleAPI,
  CellToggleWithStyle,
  CellStatusArchiveCollection,
  CellArrayObject,
} from '../../../../../../../containers/Table/Cell';
import {
  getCollectionTypeLabel,
  getLabelSegmentErrorInfo,
  getstorageTypeLabel,
  getUpdateMethodLabel,
} from '../../../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../../../utils/web/permission';
import CollectionServices from '../../../../../../../services/BusinessObject';
import {
  initNumberId,
  initNumberWithoutDecimal,
} from '../../../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import { safeParse } from '../../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';

export const ACTION_LIMIT_TYPES = {
  TOTAL_LIMIT: 'TOTAL_LIMIT',
  COMPUTATIONS_LIMIT: 'COMPUTATIONS_LIMIT',
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.segment_id,
      portal_id: {
        value: portalId,
      },
      collection_status: getLabelStatusArchive(Number(tmp.status)),
      process_status: getLabelStatus(
        parseInt(tmp.status),
        parseInt(tmp.process_status),
      ),
      original_process_status: tmp.process_status,
      colletionTypeDisplay: getCollectionTypeLabel(`${tmp.segment_id}`),
      storage_type: getstorageTypeLabel(tmp.storage_type),
      c_user_id: tmp.c_user_name,
      u_user_id: tmp.u_user_name,
      update_method: getUpdateMethodLabel(tmp.update_method),
      disabled_status:
        (tmp.status === 1 && tmp.process_status === 1) ||
        tmp.process_status === 5,
      response_code: getLabelSegmentErrorInfo(tmp.response_code),
      // compute_schedule_end: tmp.compute_schedule_end === 'None' ? 'Never' : tmp.compute_schedule_end
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
    // console.log('tmp', tmp)
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
  isShowActionAttr,
  typeDataView,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggleWithStyle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      isShowActionAttr,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainBOCollection,
      isShowActionAttr,
      itemTypeId: columnName.item_type_id,
      typeDataView,
      getIsDisabledEditName: () => false,
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat, propertyCode } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'process_status') {
      column.Cell = CellProcessStatusSegment;
      column.placement = 'status';
      // column.className = `${dataType} ${property.value} ${'status'}`;
    } else if (property.value === 'label_ids') {
      column.Cell = CellArrayObject;
    } else if (property.value === 'collection_status') {
      column.placement = 'status';
      column.Cell = CellStatusArchiveCollection;
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'collection_status') {
      column.placement = 'status';
      column.Cell = CellStatusArchiveCollection;
    } else if (property.value === 'item_type_id') {
      column.accessor = `collectionTypeDisplay`;
      column.placement = 'left';
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      if (property.value === 'segment_size') {
        column.displayFormat = initNumberWithoutDecimal();
      } else if (property.value === 'segment_id') {
        column.displayFormat = initNumberId();
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    columns.push(column);
  });
  return columns;
}

export const getWarningLimitMessages = type => {
  let label = '';
  let dynamicCollections = '';
  let staticCollections = '';

  switch (type) {
    case 'static':
      label = staticCollections = 'static collections';
      break;
    case 'dynamic':
      label = dynamicCollections = 'dynamic collections';
      break;
    default:
      staticCollections = 'static collections';
      dynamicCollections = 'dynamic collections';
      label = 'dynamic collections, static collections';
  }

  function getLabel() {
    return getTranslateMessage(
      TRANSLATE_KEY._WARN_COLL_WARN_TOTAL_CONTENT_RECOVER,
      `The number of ${label} has reached the limit. If you want to recover collections, please archived unused collections.`,
      {
        dynamic_collections: dynamicCollections,
        static_collections: staticCollections,
      },
    );
  }

  return getLabel();
};

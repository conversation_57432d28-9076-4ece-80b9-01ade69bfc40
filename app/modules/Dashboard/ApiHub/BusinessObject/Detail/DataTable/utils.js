/* eslint-disable indent */
import React from 'react';
import { subMinutes } from 'date-fns';
import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import {
  CellText,
  CellDate,
  CellNumber,
  CellArray,
  CellMainDataTable,
  CellUrl,
  CellCodeStatus,
  CellPromotionId,
  CellAllocatedJourney,
  CellMainCustomerBoTable,
  CellMainVisitorBoTable,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import {
  DATA_INFO,
  getSegmentTypeLabel,
} from '../../../../../../services/Abstract.data';
import { initNumberWithoutDecimal } from '../../../../../../utils/web/portalSetting';
import { getLabelSatusPromotionCode } from '../../../../../../utils/web/processStatus';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(
      TRANSLATE_KEY._000,
      `Do you want to move ${all} ${module}(s) to group`,
      // {
      //   module,
      //   all,
      //   number_not_delete,
      //   number_delete,
      // }
    ),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export function getModuleConfig(moduleConfig, itemTypeId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = itemTypeId;
  return newModuleConfig;
}

export const serializeData = data => {
  return mapBluePrint(data, item => ({
    ...item,
    id: item.id || item.user_id || item.customer_id,
    id_display: item.id,
    code_status: getLabelSatusPromotionCode(parseInt(item.code_status)),
    segmentTypeDisplay: getSegmentTypeLabel(`${item.audience_type}`),

    // name: item.name.value,
    // date_created: item.date_created.value,
    // last_updated: item.last_updated.value,
  }));
};

// export const serializeDataGroup = data =>
//   mapBluePrint(data, item => ({
//     ...item,
//     id: item.groupId,
//     value: item.groupName,
//     label: item.groupName,
//   }));

// export const mapEvents = data =>
//   mapBluePrint(data, item => ({
//     ...item,
//     id: item.eventTrackingName,
//     value: item.eventTrackingName,
//     label: item.translateLabel || item.eventNameDisplay,
//   }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  if (_isEmpty(columnName)) return [];
  const disableSortBy = parseInt(columnName.isSort) !== 1;
  if (columnName.itemTypeId === -1007 || columnName.itemTypeId === -1003) {
    return [
      {
        Header: columnName.label,
        id: columnName.value,
        accessor: columnName.value,
        disableSortBy,
        width: COLUMNS_WIDTH.MAIN,
        minWidth: COLUMNS_WIDTH.MAIN,
        sticky: 'left',
        Cell:
          columnName.itemTypeId === -1003
            ? CellMainCustomerBoTable
            : CellMainVisitorBoTable,
        isEncrypt: columnName.isEncrypt,
        itemTypeId: columnName.itemTypeId,
        Footer: '',
        // Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      },
      {
        Header: 'Info Remaining',
        columns: buildTableColumns(columnsActive),
        Footer: '',
      },
    ];
  }
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainDataTable,
      isEncrypt: columnName.isEncrypt,
      itemTypeId: columnName.itemTypeId,
      Footer: '',
      // Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,

      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      isEncrypt: property.isEncrypt,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
      itemTypeId: property.itemTypeId,
    };

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    if (property.value === 'code_status') {
      column.Cell = CellCodeStatus;
      column.placement = 'code_status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
    } else if (property.value === 'audience_type') {
      column.accessor = `segmentTypeDisplay`;
      column.placement = 'left';
    } else if (property.value === 'pool_id') {
      column.Cell = CellPromotionId;
    } else if (property.value === 'story_id') {
      column.Cell = props =>
        CellAllocatedJourney({ ...props, isOpenNewTab: true });
    }
    if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }

    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  // type: 'type_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  page_url: CellUrl,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

/* eslint-disable camelcase */
/* eslint-disable indent */
import { subMinutes } from 'date-fns';
import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import {
  CellText,
  CellDate,
  CellNumber,
  CellArray,
  CellMainGroupAttribute,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { DATA_INFO } from '../../../../../../services/Abstract.data';
import { initNumberWithoutDecimal } from '../../../../../../utils/web/portalSetting';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(
      TRANSLATE_KEY._000,
      `Do you want to move ${all} ${module}(s) to group`,
      // {
      //   module,
      //   all,
      //   number_not_delete,
      //   number_delete,
      // }
    ),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.group_id,
    group_name_display: item.group_name,
    groupId: item.group_id,
    groupName: item.group_name,
    groupNameMultilang: item.group_name_multilang,
  }));

export const serializeDataGroup = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.groupId,
    value: item.groupName,
    label: item.groupName,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
  isShowActionData,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainGroupAttribute,
      isShowActionData,
      Footer: '',
      // Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'num_attrs') {
      column.displayFormat = initNumberWithoutDecimal();
    }
    if (['user_id'].includes(property.value)) {
      column.placement = 'left';
    }

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  // if (totalDenied === 0) {
  //   return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  // }
  // doi totalEntries thanh attributes.length
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

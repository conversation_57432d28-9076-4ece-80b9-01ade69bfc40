import { subMinutes } from 'date-fns';
import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../../../containers/Filters/utils';
import {
  CellText,
  CellDate,
  CellNumber,
  CellArray,
  CellMainAnalyticFields,
} from '../../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { DATA_INFO } from '../../../../../../../services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';

const MAP_TITLE = {
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};

export const serializeData = data =>
  mapBluePrint(data, item => {
    return {
      ...item,
      id: item.field_code,
      treat_as_display:
        getTranslateMessage(
          (DATA_INFO.treat_as_field_list_table.map[`${item.treat_as}`] || {})
            .translateCode,
          (DATA_INFO.treat_as_field_list_table.map[`${item.treat_as}`] || {})
            .label,
        ) || '',
      storage_type_display:
        getTranslateMessage(
          (DATA_INFO.storage_type_field.map[`${item.storage_type}`] || {})
            .translateCode,
          (DATA_INFO.storage_type_field.map[`${item.storage_type}`] || {})
            .label,
        ) || '',
      data_type_display: getTranslateMessage(
        (DATA_INFO.data_type_field.map[`${item.data_type}`] || {})
          .translateCode,
        (DATA_INFO.data_type_field.map[`${item.data_type}`] || {}).label || '',
      ),
    };
  });

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(columnName, columnsActive) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainAnalyticFields,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  type: 'type_display',
  storage_type: 'storage_type_display',
  treat_as: 'treat_as_display',
  data_type: 'data_type_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const { isSelectedAll, activeRows, totalRecord, rules, modelId } = oldData;
  const fieldCodes = [];
  if (!isSelectedAll)
    activeRows.forEach(each => fieldCodes.push(each.field_code));
  return {
    id: modelId,
    data: {
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      fieldCodes,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [],
          },
    },
  };
};
export const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({ value: item.field_name, label: item.field_name });
  });
  return data;
};

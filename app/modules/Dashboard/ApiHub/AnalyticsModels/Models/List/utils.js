import _isEmpty from 'lodash/isEmpty';
import { getLabelStatus } from '../../../../../../utils/web/processStatus';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import {
  CellText,
  CellMainAnalyticModel,
  CellDate,
  CellArray,
  CellToggle,
  CellProcessStatusDisplay,
  CellNumberString,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { DATA_INFO } from '../../../../../../services/Abstract.data';
import { initNumberId } from '../../../../../../utils/web/portalSetting';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.model_id,
    storage_type_display:
      (DATA_INFO.storage_type.map[`${item.storage_type}`] || {}).label || '',
    model_type_display:
      getTranslateMessage(
        (DATA_INFO.analytic_model_type.map[`${item.model_type}`] || {})
          .translateCode,
        (DATA_INFO.analytic_model_type.map[`${item.model_type}`] || {}).label,
      ) || '',
    process_status_display: getLabelStatus(
      parseInt(item.status),
      parseInt(item.process_status),
    ),
    disabled_status: true,
    // compute_schedule_end: item.compute_schedule_end === 'None' ? 'Never' : item.compute_schedule_end
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};
export const mapChannels = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.channelId,
    value: item.channelCode,
    label: item.translateLabel || item.channelName,
  }));

export function buildTableGroupColumns(
  columnName = {},
  columnStatus,
  columnsActive = {},
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggle,
      placement: 'center',
      className: 'tbl-border-left padding-left-right-10',
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainAnalyticModel,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;

    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value} ${property.value ===
        'process_status' && 'status'}`,
      placement: `${property.value === 'process_status' && 'left'}`,
    };

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    ['model_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });

    if (['process_status'].includes(property.value)) {
      column.placement = 'status';
    }
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  storage_type: 'storage_type_display',
  model_type: 'model_type_display',
  process_status: 'process_status_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  type: CellText,
  is_required: CellText,
  process_status: CellProcessStatusDisplay,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumberString,
  boolean: CellText,
  array: CellArray,
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const { isSelectedAll, activeRows, totalRecord, rules } = oldData;
  const objectIds = [];
  // const value = [];
  if (!isSelectedAll)
    activeRows.forEach(each => {
      // console.log('each', each);
      objectIds.push(each.model_id);
    });
  return {
    data: {
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      objectIds,
      isCheckUsedStatus: 0,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [],
          },
    },
  };
};

// export const listTitleAcceptedFn = ({ activeRows, response }) => {
//   console.log('activeRows', activeRows);
//   let data = [];
//   // activeRows.forEach(each => {
//   // const item = activeRows.get(each) || {};
//   // const item = { value: each.model_id, label: each.model_name } || {};
//   // data = [...data, item];
//   // });
//
//   return data;
// };

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({ value: item.model_name, label: item.model_name });
  });
  return data;
};

/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { useParams, withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import get from 'lodash/get';
import isEqual from 'lodash/isEqual';
import { useImmer } from 'use-immer';
import moment from 'moment';
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  UILoading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
// import injectReducer from 'utils/injectReducer';
// import injectSaga from 'utils/injectSaga';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import {
  FORMAT_DATE_CALENDAR,
  buildDisableHours,
  getTimeRangeCurrentDate,
  getDateFromComputeSchedule,
} from 'components/Organisms/CalendarTable/utils';
import {
  defaultComputationSchedule,
  FORMAT_DATE_HOUR as FORMAT_FULL_DATE_SCHEDULE,
  FORMAT_DATE as FORMAT_DATE_SCHEDULE,
  FORMAT_HOUR as FORMAT_HOUR_SCHEDULE,
} from 'components/Organisms/ComputationSchedule/constant';
import ModalCalendarTable from 'containers/modals/ModalCalendarTable';

import queryString from 'query-string';

import reducer from './reducer';
import saga from './saga';

import {
  updateValue,
  init,
  update,
  reset,
} from '../../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';

import GeneralSettings from './GeneralSettings';
import {
  useCustomReducer as useReducerGeneralSettings,
  GeneralContext,
} from './GeneralSettings/useReducer';
import { toEntryReducer } from './GeneralSettings/utils';
import InitialezeModelFields from './InitialezeModelFields';
import {
  makeSelectMainInsightPropertyIds,
  makeSelectMainSelectedFields,
  makeSelectCommonMain,
  makeSelectMainOpenModals,
  makeSelectMainItemTypeIds,
  makeSelectMainLimitationPortal,
  makeSelectMainLimitationHour,
} from './selectors';

import { getBreadcrums } from '../utils';
import ModalConfirmFieldSettingError from './_modals/ModalConfirmFieldSettingError';
import ModalConfirmNotApplyFieldSetting from './_modals/ModalConfirmNotApplyFieldSetting';
import PageNotFound from '../../../../../../../components/Templates/LayoutContent/PageNotFound';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import ComputationScheduleAM from './ComputationSchedule';
import NotificationSetup from './NotificationSetup';
import {
  getCurrentAccessUserId,
  getCurrentUserId,
} from '../../../../../../../utils/web/cookie';
import Footer from './Footer';
import ModalConfirm from '../../../../../../../containers/modals/ModalConfirm';
import DrawerArchive from '../../../../../../../components/common/DrawerArchive';
import useToggle from '../../../../../../../hooks/useToggle';
import AnalyticsModalServices from '../../../../../../../services/AnalyticsModel';
import InsightServices from '../../../../../../../services/Insights';
import { safeParse } from '../../../../../../../utils/common';
import {
  CellMainAnalyticModel,
  CellStatusText,
  CellText,
} from '../../../../../../../containers/Table/Cell';
import { useCancellablePromise } from '../../../../../../../utils/web/useHooks';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';
import { PARAMS_KEY } from '../../Main/config';
import HeaderDrawer from '../../../../../../../components/common/HeaderDrawer';
import { Button, EditableName, Flex, Scrollbars } from '@antscorp/antsomi-ui';
import {
  BodyWrapper,
  StepStyled,
  StepWrapper,
  WrapperDisableStyled,
} from './styled';
import { random } from 'lodash';
import { typeAccess } from '../../../../../../../components/common/ShareAccess/constants';
import UIHelmet from '../../../../../../../components/Templates/LayoutContent/Helmet';
import { getDefaultVal } from '../../../../../../../components/common/InputLanguage/utils';

const columnsUsed = [
  {
    Header: getTranslateMessage(TRANSLATE_KEY._, 'Analytic Model Name'),
    id: 'model_name',
    accessor: 'model_name',
    Cell: props =>
      CellMainAnalyticModel({
        ...props,
        isOpenNewTab: true,
        isDisplayModelId: true,
      }),
    sticky: 'left',
    width: COLUMNS_WIDTH.MAIN,
    minWidth: COLUMNS_WIDTH.MAIN,
    // disableResizing: true,
    disableSortBy: true,
  },
  {
    Header: 'Info Remaining',
    columns: [
      {
        Header: getTranslateMessage(TRANSLATE_KEY._, 'Status'),
        // Header: 'Type',
        id: 'status',
        accessor: 'status',
        disableSortBy: true,
        Cell: CellStatusText,
        minWidth: COLUMNS_WIDTH.SWITCH,
      },
      {
        Header: getTranslateMessage(TRANSLATE_KEY._, 'Created by'),
        id: 'c_user_id',
        accessor: 'c_user_id',
        Cell: CellText,
        placement: 'left',
        disableSortBy: true,
        minWidth: COLUMNS_WIDTH.DEFAULT,
      },
    ],
  },
];

const MAP_TITLE = {
  titlePage: getTranslateMessage(TRANSLATE_KEY._, 'Create new analytic model'),
  title_warning: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_AM_WARN_TOTAL,
    'Warning Total Analytic Model Limit',
  ),
  content: getTranslateMessage(
    TRANSLATE_KEY._WARN_AM_WARN_TOTAL_CONTENT_CREATE,
    `The number of analytic model has reached the limit.
    If you want to create new analytic model, please remove unused analytic models.
    `,
  ),
  btn_go_to_list: getTranslateMessage(
    TRANSLATE_KEY._ACT_WARN_AM_GO_TO_LIST,
    'Go To Analytic Model List',
  ),
  btn_cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  title_search: getTranslateMessage(TRANSLATE_KEY._TITLE_SEARCH, 'Search'),
  analytic_model_name: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_AM_NAME,
    'Analytic Model Name',
  ),
  status: getTranslateMessage(TRANSLATE_KEY._WARN_TITLE_AM_STATUS, 'Status'),
  create_by: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_AM_CREATED_BY,
    'Created by',
  ),
  close: getTranslateMessage(TRANSLATE_KEY._ACT_CLOSE, 'Close'),
  title_warning_computation: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_AM_COMPUTATIONAL_LIMIT,
    'Warning Analytic Model Computational Limit',
  ),
  content_computation: getTranslateMessage(
    TRANSLATE_KEY._WARN_AM_COMPUTATIONAL_LIMIT,
    'Currently, the computation limit has been reached, the system will compute this analytic model in the next valid time.',
  ),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};

function Content(props) {
  useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });

  const { design, main } = props;
  const {
    isInitDone,
    activeModel,
    generalData,
    isComputing,
    modelType,
    notificationSetup,
    computationSchedule = {},
    modalCalendarTable = {},
    errors,
  } = main;

  const { modelId: modelIdParams } = useParams();

  const ownerId = activeModel.cUserId || getCurrentUserId();
  const [isHasPathIframe, setIsHasPathIframe] = useState(false);

  const { history, location, modelId = modelIdParams } = props;
  const searchParams = new URLSearchParams(location.search);
  const modelTypeUrl = searchParams.get(PARAMS_KEY.CREATE_MODEL_TYPE);
  const fieldCode = props?.fieldCode || searchParams.get('fieldCode') || '';

  const [isOpenModalLimit, toggleModalLimit] = useToggle(false);
  const [isOpenDrawerRemove, setIsOpenDrawerRemove] = useState(false);
  const [dataTable, setDataTable] = useState([]);
  const [valueSearch, setValueSearch] = useState('');
  const [pageSetting, setPageSetting] = useImmer({
    limit: 25,
    page: 1,
  });
  const [isLoadingAM, setIsLoadingAM] = useState(false);
  const [limitPortal, setLimitPortal] = useState(0);
  // type visitor,customer
  const isHideStep = [3, 4].includes(parseInt(modelType));
  // state
  const [freshnessAndShare, setFreshnessAndShare] = useState({
    accessInfo: {},
    selectedFreshnessData: -1,
  });
  const [titlePage, setTitlePage] = useState('');

  const contentLimit = useMemo(() => {
    const newContent = {
      title: MAP_TITLE.title_warning,
      content: MAP_TITLE.content,
    };
    if (props.isLimitationHour) {
      newContent.title = MAP_TITLE.title_warning_computation;
      newContent.content = MAP_TITLE.content_computation;
    }
    return newContent;
  }, [props.isLimitationHour]);

  useEffect(() => {
    props.init({ design, modelId, fieldCode, modelTypeUrl });

    // updateUrl(location.pathname);
    return () => {
      props.reset();
    };
  }, []);

  useEffect(() => {
    // const handleGetMessage = event => {
    //   console.log(event.data);
    //   if (
    //     event.data &&
    //     event.data.data &&
    //     event.data.type === 'POST_ACCESS_INFO'
    //   ) {
    //     const { isPublic, listAccess } = event.data.data;
    //     const generalInfo = isPublic === 0 ? typeAccess[0] : typeAccess[1];
    //     const accessInfo = {
    //       general: generalInfo,
    //       listAccess,
    //       isPublic,
    //     };

    //     setFreshnessAndShare(prev => ({
    //       ...prev,
    //       accessInfo,
    //     }));
    //   }
    // };

    if (isHasPathIframe) {
      const listStepAddition = [...main.steps];
      listStepAddition[0].title = getTranslateMessage(
        TRANSLATE_KEY._,
        'Design Model',
      );
      listStepAddition[1].title = getTranslateMessage(
        TRANSLATE_KEY._,
        'Schedule',
      );

      props.updateStep(listStepAddition);

      if (!Object.keys(freshnessAndShare.accessInfo).length) {
        const userId = getCurrentAccessUserId();
        InsightServices.account
          .accountInfo({
            type: 'user-info',
            userId,
          })
          .then(res => {
            if (res.data && res.code === 200) {
              const userInfo = res.data;

              const { full_name, email, user_id, avatar } = userInfo || {};
              const newAccess = {
                id: random(6),
                name: full_name,
                email,
                userId: user_id,
                avatar,
                accessType: { label: 'Owner', value: 'owner' },
                role: 1,
              };

              const accessInfoTmp = {
                general: typeAccess[0],
                listAccess: [newAccess],
                isPublic: 0,
              };

              setFreshnessAndShare(prev => ({
                ...prev,
                accessInfo: accessInfoTmp,
              }));
            }
          });
      }
    }
    // return () => {
    //   window.removeEventListener('message', handleGetMessage);
    // };
  }, [isHasPathIframe]);

  useEffect(() => {
    setIsHasPathIframe(
      window.location.pathname.split('/').includes('create-iframe') ||
        window.location.pathname.split('/').includes('update-iframe'),
    );
  }, [window.location.pathname]);

  const [generalReducer, generalDispatchAction] = useReducerGeneralSettings();

  const generalValueContext = useMemo(
    () => ({
      customReducer: generalReducer,
      dispatchAction: generalDispatchAction,
    }),
    [generalReducer, generalDispatchAction],
  );
  const globalData = useMemo(
    () => ({
      isParentInitDone: isInitDone,
      design: main.design,
      isComputing,
      modelType,
    }),
    [isInitDone, main.design, isComputing],
  );
  useEffect(() => {
    if (isInitDone) {
      generalDispatchAction.put(
        updateValue('@@PARENTS_UPDATE_DATACONFIG', {
          data: generalData.arrayItemErrors,
        }),
      );
      // console.log('generalData.arrayItemErrors', generalData.arrayItemErrors);
    }
  }, [generalData.arrayItemErrors]);
  const breadcrums = useMemo(
    () => getBreadcrums(activeModel.translateLabel, activeModel.modelId),
    [activeModel.translateLabel],
  );

  useEffect(() => {
    if (!isOpenDrawerRemove && !isOpenModalLimit) {
      if (props.isLimitationPortal) {
        props.onUpdateIsLimitationPortal({ isLimitation: false });
      }
      if (props.isLimitationHour) {
        props.onUpdateIsLimitationHour({
          isLimitation: false,
          design,
        });
      }
      setPageSetting(draft => {
        (draft.page = 1), (draft.limit = 25);
      });
    }
  }, [isOpenDrawerRemove, isOpenModalLimit]);

  useEffect(() => {
    if (props.isLimitationPortal || props.isLimitationHour) {
      toggleModalLimit();
    }
  }, [props.isLimitationPortal, props.isLimitationHour]);
  const callAPIGetListingAM = async (page = pageSetting, value = '') => {
    setIsLoadingAM(true);
    const params = {
      data: {
        page: page.page,
        limit: page.limit,
        search: '',
        sort: safeParse('', 'last_updated_date'),
        sd: safeParse('', 'desc'),
        columns: ['model_id', 'model_name', 'status', 'c_user_id'],
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'model_name',
                  data_type: 'string',
                  operator: 'contains',
                  value,
                },
              ],
            },
          ],
        },
      },
    };
    const res = await AnalyticsModalServices.data.getList(params);
    if (res && res.meta && res.meta.limitNumberObject) {
      setLimitPortal(res.meta.limitNumberObject);
    }
    setIsLoadingAM(false);
    if (res && res.data && res.data.length) {
      const data = res.data.map((each, key) => {
        // Serialize Dâta
        const row = each;
        if (each.model_id) {
          row.id = each.model_id;
        }
        return row;
      });
      return data;
    }
    return [];
  };

  useEffect(() => {
    callAPIGetListingAM(pageSetting, valueSearch).then(res => {
      setDataTable(res);
    });

    return () => {};
  }, [valueSearch, pageSetting]);

  useEffect(() => {
    if (!titlePage) {
      setTitlePage(
        generalValueContext.customReducer.dataConfig?.name?.value?.[
          generalValueContext.customReducer.dataConfig?.name?.value
            ?.DEFAULT_LANG
        ],
      );
    }
  }, [generalValueContext?.customReducer?.dataConfig?.name]);

  const handleOpenDrawer = async () => {
    toggleModalLimit(false);
    setIsOpenDrawerRemove(true);
    const listAM = await callAPIGetListingAM();
    setDataTable(listAM);
  };

  const handleAfterSave = () => {
    if (typeof props.onAfterSave === 'function') {
      props.onAfterSave();
    }
  };

  const handleAfterSaveName = newName => {
    if (typeof props.onAfterSaveName === 'function') {
      props.onAfterSaveName(newName);
    }
  };

  const callback = async (type, data) => {
    switch (type) {
      case 'CHANGE_DATA_SOURCE': {
        props.onChangeInsightPropertyIds(data);
        break;
      }
      case 'CHANGE_ITEM_TYPE_IDS': {
        props.onChangeItemTypeIds(data);
        break;
      }
      case 'CHANGE_IS_LONG_PERIOD': {
        props.onChangeIsLongPeriod(data);
        break;
      }
      case 'GET_VALUE_SEARCH':
        setValueSearch(data);
        break;
      case 'REMOVE_SUCCESS': {
        const listAM = await callAPIGetListingAM(pageSetting, data);
        setDataTable(listAM);
        break;
      }
      case 'ON_PAGING_TABLE':
        setPageSetting(draft => {
          Object.keys(data).forEach(key => {
            draft[key] = data[key];
          });
        });
        break;
      case 'ON_SAVE': {
        const payload = {
          isHasPathIframe,
          callback: () => handleAfterSave(),
        };
        callback('APPLY_GENERAL');
        if (isHasPathIframe) {
          payload.freshnessAndShare = freshnessAndShare;
        }
        props.update(payload);
        break;
      }

      case 'APPLY_GENERAL': {
        generalDispatchAction.put(updateValue('@@VALIDATE'));
        const newGeneralData = toEntryReducer({
          dataConfig: generalReducer.dataConfig,
        });
        props.onApplyGeneral({ data: newGeneralData });
        break;
      }

      case 'ON_CONFIRM_FIELD_ERROR': {
        // console.log('ON_CONFIRM_FIELD_ERROR', data);
        props.onConfirmModal();
        break;
      }
      case 'ON_CONFIRM_NOT_APPLY_FIELD': {
        props.onConfirmModal();
        // console.log('ON_CONFIRM_NOT_APPLY_FIELD', data);
        break;
      }
      case 'ON_TOGGLE_MODAL': {
        props.toggleModal(data);
        break;
      }
      case 'ON_GENERATE_FIELDS': {
        props.onGenerateFieldsItemTypeIds();
        break;
      }
      case 'CHANGE_COMPUTATION_SCHEDULE': {
        const { repeatStartTime = {} } = data;
        const { day, hour } = repeatStartTime;

        props.onChangeComputationSchedule({ value: data });

        // delete errors
        props.onChangeErrors({ name: 'limit_schedule', message: '' });

        // set time calendar
        const selectedTime = moment(
          `${day} ${hour}`,
          FORMAT_FULL_DATE_SCHEDULE,
        ).format(FORMAT_DATE_CALENDAR);
        props.onChangeModalCalendarTable({
          selectedTime,
        });
        break;
      }
      case 'RESET_SUGGESTION_TIME': {
        if (typeof props.onResetSuggestionTime === 'function') {
          props.onResetSuggestionTime();
        }

        break;
      }
      case 'ON_CANCEL': {
        // Temporary code BK
        if (window.parent && isHasPathIframe) {
          // xu ly postMessage khi dang o path 'iframe'
          const data = {
            type: 'ANALYTICS_MODEL_CANCEL',
            data: {},
          };
          window.parent.postMessage(data, '*');
          break;
        }
        if (typeof props.onCancel === 'function') {
          props.onCancel();
        }
        // props.onGoToList();
        break;
      }
      case 'ON_CHANGE_NOTIFICATION_SETUP': {
        props.onChangeNotificationSetup(data);
        break;
      }
      case 'TOGGLE_MODAL_CALENDAR_TABLE': {
        toggleCalendarTable();
        break;
      }
      case 'ON_CLICK_CELL': {
        props.onChangeModalCalendarTable({
          selectedTime: data,
        });
        break;
      }
      case 'CURRENT_DATE': {
        const { currentDate } = data;
        props.onChangeModalCalendarTable({
          currentDate,
        });
        const timeRange = getTimeRangeCurrentDate(currentDate);
        const currentTimeRange = getTimeRangeCurrentDate(
          modalCalendarTable.currentDate,
        );

        if (!isEqual(currentTimeRange, timeRange)) {
          props.onGetPlans({
            timeRange,
          });
        }
        props.onGetPlans({ timeRange });
        break;
      }
      case 'ON_BACK': {
        callback('APPLY_GENERAL');
        props.onChangeStep(main.currentStep - 1);
        break;
      }
      case 'ON_NEXT': {
        callback('APPLY_GENERAL');
        props.onChangeStep(main.currentStep + 1);
        break;
      }
      case 'ON_CHANGE_NAME': {
        generalDispatchAction.onChangeDataConfig({
          name: 'name',
          value: { DEFAULT_LANG: 'EN', EN: data },
        });
        // props.onChangeModelName(newValue);
        break;
      }
      case 'ON_BLUR_NAME': {
        handleTitlePage();
        if (design === 'update') {
          props.onChangeName({
            newName: generalValueContext.customReducer.dataConfig?.name?.value,
            callback: () =>
              handleAfterSaveName(
                getDefaultVal(
                  generalValueContext.customReducer.dataConfig?.name?.value,
                ),
              ),
          });
        }
        break;
      }

      // case 'UPDATE_FRESHNESS_DATA':
      //   setFreshnessAndShare(prev => ({
      //     ...prev,
      //     freshnessAndShare: data,
      //   }));
      //   break;

      // case 'UPDATE_SHARE_ACCESS':
      //   if (data) {
      //     setFreshnessAndShare(prev => ({
      //       ...prev,
      //       accessInfo: data,
      //     }));
      //   }
      //   break;
      default:
        if (typeof props.callback === 'function') {
          props.callback(type, data);
        }
        break;
    }
  };

  const toggleCalendarTable = () => {
    props.onChangeModalCalendarTable({
      isOpen: !modalCalendarTable.isOpen,
    });

    // call khi open modal
    if (!modalCalendarTable.isOpen) {
      const { selectedTime, currentDate } = getDateFromComputeSchedule(
        computationSchedule,
      );

      // rebuild disableTimes for current computeSchedule
      const disableTimes = buildDisableHours({
        computeSchedule: computationSchedule,
        dataEventsGroupByDate: modalCalendarTable.dataEventsGroupByDate,
        limitHour: modalCalendarTable.limitHour,
        timeRange: modalCalendarTable.timeRange,
      });

      props.onChangeModalCalendarTable({
        disableTimes,
        currentDate,
        selectedTime,
      });

      const timeRange = getTimeRangeCurrentDate(currentDate);
      props.onGetPlans({ timeRange });
    }
  };

  const onConfirmCalendarTable = () => {
    const selectedTime = moment(
      modalCalendarTable.selectedTime,
      FORMAT_DATE_CALENDAR,
    );

    const startDay = moment(selectedTime).format(FORMAT_DATE_SCHEDULE);
    const startHour = moment(selectedTime).format(FORMAT_HOUR_SCHEDULE);

    props.onChangeComputationSchedule({
      value: {
        ...computationSchedule,
        repeatStartTime: { day: startDay, hour: startHour },
      },
    });

    // delete errors
    props.onChangeErrors({ name: 'limit_schedule', message: '' });

    toggleCalendarTable();
  };

  const handleChangeStep = step => {
    callback('APPLY_GENERAL');
    props.onChangeStep(step);
  };

  const handleTitlePage = () => {
    const newName = getDefaultVal(
      generalValueContext.customReducer.dataConfig?.name?.value,
    );
    const isError = !!generalValueContext?.customReducer?.dataConfig?.name
      ?.errors?.[0];

    if (design === 'create' && !newName) {
      setTitlePage(MAP_TITLE.titlePage);
    }

    if (newName && !isError) {
      setTitlePage(newName);
    }
  };

  const render = () => {
    if (!isInitDone) {
      return <UILoading isLoading />;
    }
    if (
      design !== 'create' &&
      Object.keys(props.main.activeModel).length === 0
    ) {
      return (
        <PageNotFound
          labelButton={getTranslateMessage(
            TRANSLATE_KEY._ACT_BACK_TO_LIST,
            'Back to list',
          )}
          labelPageNotFound={getTranslateMessage(
            TRANSLATE_KEY._USER_GUIDE_ANALYTIC_MODEL_NOT_FOUND,
            'Analytics Models not found',
          )}
          onClick={props.onGoToList}
        />
      );
    }

    if (design === 'preview') {
      return (
        <WrapperDisable>
          <HeaderDrawer
            extraContent={
              <Flex align="center" gap={10}>
                {fieldCode && (
                  <Button onClick={() => callback('ON_CANCEL')}>
                    {MAP_TITLE.btn_cancel}
                  </Button>
                )}
                <Button
                  loading={props.main.isDoing}
                  disabled={
                    !!get(main, 'computationSchedule.errors.repeatOnValue') ||
                    modalCalendarTable.isLoading ||
                    !(
                      props.insightPropertyIds?.length > 0 ||
                      props.itemTypeIds?.length > 0 ||
                      props.selectedFields?.list?.length > 0
                    ) ||
                    main.currentStep + 1 < main.steps.length ||
                    isComputing ||
                    props.main.isDoing
                  }
                  type="primary"
                  onClick={() => callback('ON_SAVE')}
                >
                  {MAP_TITLE.labelSave}
                </Button>
              </Flex>
            }
          >
            <EditableName
              isLoading={props.main.isDoing || props.main.isLoading}
              defaultValue={
                main?.generalData?.data?.name?.[
                  main?.generalData?.data?.name?.DEFAULT_LANG
                ]
              }
              onChange={newValue => callback('ON_CHANGE_NAME', newValue)}
              error={
                generalValueContext?.customReducer?.dataConfig?.name
                  ?.errors?.[0]
              }
              required
              readonly
            />
          </HeaderDrawer>
          <StepWrapper>
            <StepStyled
              current={main.currentStep}
              items={main.steps}
              saveHighestStep={false}
              onChange={handleChangeStep}
            />
          </StepWrapper>
          <BodyWrapper className="m-x-0">
            <div className="">
              <GeneralContext.Provider value={generalValueContext}>
                <GeneralSettings
                  callback={callback}
                  initData={main.generalData.data}
                  infoData={main.generalData.infoData}
                  globalData={globalData}
                  isChangeInputType={
                    props.openModals.CONFIRM_MODAL_CHANGE_INPUT_TYPE
                  }
                  onConfirm={props.onConfirmModal}
                  currentStep={main.currentStep}
                />
              </GeneralContext.Provider>
            </div>
          </BodyWrapper>
          <Footer
            callback={callback}
            isDoing={props.main.isDoing}
            disabled={
              !!get(main, 'computationSchedule.errors.repeatOnValue') ||
              modalCalendarTable.isLoading
            }
            showBack={main.currentStep > 0}
            showNext={main.currentStep + 1 < main.steps.length}
          />
        </WrapperDisable>
      );
    }
    // console.log({ generalReducer });
    return (
      <>
        {/* {!isHasPathIframe && (
          <CustomHeader
            showCancel
            breadcrums={breadcrums}
            callback={callback}
          />
        )} */}
        <Flex vertical style={{ height: '100%' }}>
          <HeaderDrawer
            extraContent={
              <Flex align="center" gap={10}>
                {fieldCode && (
                  <Button onClick={() => callback('ON_CANCEL')}>
                    {MAP_TITLE.btn_cancel}
                  </Button>
                )}
                <Button
                  loading={props.main.isDoing}
                  disabled={
                    !!get(main, 'computationSchedule.errors.repeatOnValue') ||
                    modalCalendarTable.isLoading ||
                    !(
                      props.insightPropertyIds?.length > 0 ||
                      props.itemTypeIds?.length > 0 ||
                      props.selectedFields?.list?.length > 0
                    ) ||
                    (main.currentStep + 1 < main.steps.length && !isHideStep) ||
                    isComputing ||
                    props.main.isDoing
                  }
                  type="primary"
                  onClick={() => callback('ON_SAVE')}
                >
                  {MAP_TITLE.labelSave}
                </Button>
              </Flex>
            }
          >
            <EditableName
              isLoading={
                props.main.isDoing ||
                props.main.isLoading ||
                props.main.isLoadingChangeName
              }
              defaultValue={
                main?.generalData?.data?.name?.[
                  main?.generalData?.data?.name?.DEFAULT_LANG
                ]
              }
              onChange={newValue => callback('ON_CHANGE_NAME', newValue)}
              error={
                generalValueContext?.customReducer?.dataConfig?.name
                  ?.errors?.[0]
              }
              // error={main?.generalData?.arrayItemErrors?.[0]?.value[0]}
              onBlur={() => callback('ON_BLUR_NAME')}
              required
            />
          </HeaderDrawer>
          {!isHideStep && (
            <StepWrapper>
              <StepStyled
                current={main.currentStep}
                items={main.steps}
                onChange={handleChangeStep}
                isHasPathIframe={isHasPathIframe}
              />
            </StepWrapper>
          )}

          {/* <Scrollbars style={{ width: '100%' }}> */}
          <Scrollbars>
            <WrapperDisableStyled disabled={isComputing || props.main.isDoing}>
              <BodyWrapper>
                <div className="">
                  <GeneralContext.Provider value={generalValueContext}>
                    <GeneralSettings
                      callback={callback}
                      initData={main.generalData.data}
                      infoData={main.generalData.infoData}
                      globalData={globalData}
                      isChangeInputType={
                        props.openModals.CONFIRM_MODAL_CHANGE_INPUT_TYPE
                      }
                      onConfirm={props.onConfirmModal}
                      currentStep={main.currentStep}
                      // isHasPathIframe={isHasPathIframe}
                      // accessInfo={freshnessAndShare.accessInfo}
                      // selectedFreshnessData={
                      //   freshnessAndShare.selectedFreshnessData
                      // }
                    />
                  </GeneralContext.Provider>
                </div>
                {(props.insightPropertyIds.length > 0 ||
                  props.itemTypeIds.length > 0 ||
                  props.selectedFields.list.length > 0) && (
                  <>
                    {main.currentStep === 0 && (
                      <InitialezeModelFields
                        callback={callback}
                        isDoing={props.main.isDoing}
                        inputType={
                          modelType == '2' &&
                          generalReducer.dataConfig.inputType &&
                          generalReducer.dataConfig.inputType.value
                        }
                        isHasPathIframe={isHasPathIframe}
                      />
                    )}
                    {main.currentStep === 1 && (
                      <Flex vertical gap={20}>
                        <ComputationScheduleAM
                          isLoading={computationSchedule.isLoading}
                          design={design}
                          computeSchedule={computationSchedule}
                          callback={callback}
                          errors={errors}
                        />
                        <NotificationSetup
                          value={notificationSetup}
                          callback={callback}
                          ownerId={ownerId}
                          disabled={isComputing || props.main.isDoing}
                        />
                      </Flex>
                    )}
                  </>
                )}
              </BodyWrapper>
            </WrapperDisableStyled>
          </Scrollbars>
          {!isHideStep && (
            <Footer
              callback={callback}
              isDoing={props.main.isDoing}
              disabled={
                !!get(main, 'computationSchedule.errors.repeatOnValue') ||
                modalCalendarTable.isLoading
              }
              showBack={main.currentStep > 0}
              showNext={main.currentStep + 1 < main.steps.length}
            />
          )}
        </Flex>
        {/* </Scrollbars> */}
        <ModalCalendarTable
          isOpen={modalCalendarTable.isOpen}
          toggle={toggleCalendarTable}
          onConfirm={onConfirmCalendarTable}
          isLoading={modalCalendarTable.isLoading}
          selectedTime={modalCalendarTable.selectedTime}
          dataEvents={modalCalendarTable.dataEvents}
          disableTimes={modalCalendarTable.disableTimes}
          callback={callback}
          currentDate={modalCalendarTable.currentDate}
          timeRange={modalCalendarTable.timeRange}
        />
        <ModalConfirmFieldSettingError
          isOpen={props.openModals.CONFIRM_MODAL_FIELD_SETTING_ERROR}
          name="CONFIRM_MODAL_FIELD_SETTING_ERROR"
          toggle={props.toggleModal}
          onConfirm={props.onConfirmModal}
          callback={callback}
        />
        <ModalConfirmNotApplyFieldSetting
          isOpen={props.openModals.CONFIRM_MODAL_FIELD_NOT_APPLY_SAVE_SETTING}
          name="CONFIRM_MODAL_FIELD_NOT_APPLY_SAVE_SETTING"
          toggle={props.toggleModal}
          onConfirm={props.onConfirmModal}
          callback={callback}
        />
        <ModalConfirm
          title={contentLimit.title}
          isBorderBottom
          isOpen={isOpenModalLimit}
          toggle={toggleModalLimit}
          labelConfirmBtn={MAP_TITLE.btn_go_to_list}
          onConfirm={handleOpenDrawer}
          isFooterV2={props.isLimitationHour}
        >
          {contentLimit.content}
        </ModalConfirm>
        <DrawerArchive
          title={MAP_TITLE.title_warning}
          label={getTranslateMessage(
            TRANSLATE_KEY._BOX_TITL_REMOVE_MODEL,
            'Remove Model',
          )}
          isOpen={isOpenDrawerRemove}
          togglePopup={() => setIsOpenDrawerRemove(false)}
          labelButtonAction="Remove"
          labelNotiLimit={MAP_TITLE.content}
          typePopup="Analytic Model"
          column={columnsUsed}
          data={dataTable}
          callback={callback}
          objectType="ANALYTIC_MODEL"
          moduleName="Models"
          isLoading={isLoadingAM}
          limit={limitPortal}
        />
      </>
    );
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/Sources/Create/Design/index.jsx">
      <UIHelmet title={titlePage} />
      {render()}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  // dataConfig: makeSelectCreateDesDataConfig(),
  main: makeSelectCommonMain(),
  itemTypeIds: makeSelectMainItemTypeIds(),
  insightPropertyIds: makeSelectMainInsightPropertyIds(),
  selectedFields: makeSelectMainSelectedFields(),
  openModals: makeSelectMainOpenModals(),
  isLimitationPortal: makeSelectMainLimitationPortal(),
  isLimitationHour: makeSelectMainLimitationHour(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    init: value => dispatch(init(MODULE_CONFIG.key, value)),
    reset: value => dispatch(reset(MODULE_CONFIG.key, value)),
    update: value => dispatch(update(MODULE_CONFIG.key, value)),
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    onChangeInsightPropertyIds: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@INSIGHT_PROPERTY_IDS@@`, value),
      ),
    onChangeItemTypeIds: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ITEM_TYPE_IDS@@`, value)),
    onChangeIsLongPeriod: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_LONG_PERIOD@@`, value)),
    onApplyGeneral: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@APPLY_GENERAL@@`, value)),
    onValidateAllFields: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@VALIDATE_ALL_FIELD`, value)),
    onValidateGeneral: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@VALIDATE_GENERAL`, value)),
    toggleModal: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_MODAL@@`, value)),
    onConfirmModal: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CONFIRM_MODAL@@`, value)),
    onGoToList: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_LIST@@`, value)),
    onGenerateFieldsItemTypeIds: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GENERATE_FIELDS@@`, value)),
    onChangeComputationSchedule: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@COMPUTATION_SCHEDULE@@`, value),
      ),
    onChangeModalCalendarTable: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@MODAL_CALENDAR_TABLE@@`, value),
      ),
    onChangeNotificationSetup: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@NOTIFICATION_SETUP@@`, value),
      ),
    onUpdateIsLimitationPortal: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@LIMITATION_PORTAL`, value)),
    onUpdateIsLimitationHour: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@LIMITATION_HOUR`, value)),
    onGetPlans: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GET_PLANS@@`, value)),
    onChangeErrors: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ERRORS@@`, value)),
    onResetSuggestionTime: () => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@RESET_SUGGESTION_TIME@@`));
    },
    onChangeModelName: name => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_MODEL_NAME`, name));
    },
    onChangeStep: step => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_STEP`, step));
    },
    updateStep: step => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@UPDATE_STEP`, step));
    },
    onChangeName: value => {
      dispatch(update(`${MODULE_CONFIG.key}@@ON_CHANGE_NAME`, value));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withConnect,
)(Content);

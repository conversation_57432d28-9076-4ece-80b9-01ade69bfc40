/* eslint-disable no-lonely-if */
/* eslint-disable no-empty */
/* eslint-disable prettier/prettier */
/* eslint-disable no-param-reassign */
import { safeParse } from 'utils/common';

export const serializeData = data => {
  let validateData = safeParse(data, []);

  if (validateData.length === 0) {
    return validateData;
  }

  validateData = validateData.map(item => {
    const temp = {
      ...item,
      hidden: false,
    };

    if (temp.items) {
      temp.open = validateData.length === 1 || false;
      temp.items = temp.items.map(element => {
        return { ...element, checked: false, hidden: false };
      });
    } else {
      temp.checked = false;
    }

    return temp;
  });

  return validateData;
};

export const handleOpenElement = (data, indexOpen, open) => {
  return data.map((option, indexItem) => {
    if (indexItem === indexOpen) {
      const temp = {
        ...option,
        open: !open,
      };

      return temp;
    }
    return option;
  });
};

export const handleOnCheck = (data, indexChild = -1, indexParent, checked) => {
  if (indexChild === -1) {
    return data.map((itemParent, index) => {
      if (index === indexParent) {
        return { ...itemParent, checked };
      }
      return itemParent;
    });
  }

  return data.map((itemParent, index) => {
    if (index === indexParent && itemParent.items) {
      itemParent.items = itemParent.items.map((itemChild, indexItemChild) => {
        if (indexItemChild === indexChild) {
          return { ...itemChild, checked };
        }

        return itemChild;
      });
    }
    return itemParent;
  });
};

const validateByValueSearch = (label, valueSearch) => {
  return label.toLowerCase().includes(
    valueSearch
      .toString()
      .toLowerCase()
      .trim(),
  );
};

export const handleSearch = (data, valueSearch) => {
  const arr = [];
  data.forEach(item => {
    let isShow = false;
    let isSearchParent = false;
    if (item.items) {
      item.items.forEach(itemChild => {
        if (!validateByValueSearch(itemChild.label, valueSearch)) {
          if (validateByValueSearch(item.label, valueSearch)) {
            isSearchParent = true;
          } else {
            itemChild.hidden = true;

          }
        } else {
          isShow = true;
          itemChild.hidden = false;
        }
      });
    } else {
      if (validateByValueSearch(item.label, valueSearch)) {
        isShow = true;
      }
    }

    if (isShow) {
      item.open = true;
      item.hidden = false;
    } else if (isSearchParent) {
      item.hidden = false;
      item.open = false;

    }
    else {
      item.hidden = true;
    }

    arr.push(item);
  });

  return arr;
};

/* eslint-disable no-prototype-builtins */
import parse from 'html-react-parser';
import TRANSLATE_KEY from 'messages/constant';
import { getErrorMessageV2Translate } from '../../../../../../../utils/web/message';

export const NOTI = {
  fail: res => ({
    id: 'edit-name-error',
    ...getErrorMessageV2Translate(res.codeMessage),
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
  success: () => ({
    id: 'edit-name-success',
    message: parse(
      `Please wait for 10 minutes to system process, <br /> and then refresh the page to see the data`,
    ),
    translateCode: TRANSLATE_KEY._USER_GUIDE_UPLOAD_WAITING,
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'success',
  }),
  serviceNotFound: () => ({
    id: 'object-server-error',
    message: 'Service not found',
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
};
export const mappingAttributes = data => {
  const dataOut = [];
  data.map(each => {
    if (
      each.itemAttributeCode === 'item_type_name' ||
      each.itemAttributeCode === 'item_property_name'
    ) {
      dataOut.push({
        headerCode: each.headerCode,
        headerIndex: each.headerIndex,
        itemColumn: each.itemAttributeCode,
        itemAttributeName: each.itemAttributeCode,
        allowNull: each.allowNull,
      });
    } else {
      dataOut.push({
        headerCode: each.headerCode,
        headerIndex: each.headerIndex,
        itemColumn: each.itemAttributeCode,
        allowNull: each.allowNull,
        itemAttributeName: each.itemAttributeCode,
        languageCode: each.languaValue.value,
      });
    }
  });
  return dataOut;
};
export const mapFailedSucces = data => {
  const failed = [];
  const success = [];
  data.map(each => {
    if (each.errorMessage) {
      failed.push(each);
    } else {
      success.push(each);
    }
  });
  return { failed, success };
};
export const mapNameDescription = data => {
  const dataOut = [
    {
      row_count: '',
      attribute_id: 'Name',
      language: '',
      current_value: '',
      update_value: '',
      error_message: ' ',
      subRows: [],
    },
    {
      row_count: '',
      attribute_id: 'Descripstion',
      language: '',
      current_value: '',
      update_value: '',
      error_message: ' ',
      subRows: [],
    },
  ];
  data.map(each => {
    if (
      each.hasOwnProperty('item_type_display') ||
      each.hasOwnProperty('item_property_display')
    ) {
      dataOut[0].subRows.push({
        row_count: '47',
        attribute_id:
          each.item_property_name ||
          each.item_type_id ||
          each.item_type_name ||
          '--',
        language: optionLang[each.displayLanguage].label || '--',
        current_value:
          each.currentItemTypeDisplay ||
          each.currentItemPropertyDisplay ||
          '--',
        update_value:
          each.updatedItemTypeDisplay ||
          each.updatedItemPropertyDisplay ||
          '--',
        error_message: each.errorMessage,
      });
    }
    // eslint-disable-next-line no-prototype-builtins
    if (each.hasOwnProperty('description')) {
      dataOut[1].subRows.push({
        row_count: '47',
        attribute_id:
          each.item_property_name ||
          each.item_type_id ||
          each.item_type_name ||
          '--',
        language: optionLang[each.descriptionLanguage].label || '--',
        current_value: each.currentDescription || '--',
        update_value: each.updatedDescription || '--',
        error_message: each.descriptionError || '--',
      });
    }
  });
  return dataOut;
};
const optionLang = {
  VI: { name: 'vi', value: 'VI', label: 'Việt Nam', disabled: false },
  EN: { name: 'en', value: 'EN', label: 'English', disabled: false },
  JA: { name: 'ja', value: 'JA', label: '日本', disabled: false },
};

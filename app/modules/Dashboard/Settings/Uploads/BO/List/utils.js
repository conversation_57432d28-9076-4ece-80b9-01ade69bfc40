import _isEmpty from 'lodash/isEmpty';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import {
  CellText,
  CellDate,
  CellMainUserAccount,
  CellNumberString,
} from '../../../../../../containers/Table/Cell';
import CellArray from '../../../../../../containers/Table/Cell/CellArray';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export const serializeData = data =>
  mapBluePrint(data, item => {
    const arrayAssignedRole = [];
    item.assigned_role.map(each => arrayAssignedRole.push(each.role_name));
    return {
      ...item,
      id: item.user_id,
      assigned_role_display: arrayAssignedRole,
    };
  });

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(columnName, columnsActive) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainUserAccount,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      className: 'border-left',
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumberString,
  boolean: CellText,
  array: CellArray,
};

const MAP_CELL_BY_VALUE = {
  // c_user_id: CellText,
  // u_user_id: CellText,
  assigned_role: CellArray,
};

const MAP_ACCESSOR = {
  assigned_role: 'assigned_role_display',
};

export const mapParamsDeleteFn = (oldData, newData) => {
  const { isSelectedAll, activeRows, totalRecord, rules } = oldData;
  const userIds = [];
  if (!isSelectedAll) {
    activeRows.forEach(each => {
      return userIds.push(each.id);
    });
  }
  return {
    data: {
      totalSelected: isSelectedAll ? totalRecord : activeRows.size,
      userIds,
      filters: isSelectedAll
        ? toConditionAPI(rules)
        : {
            OR: [],
          },
    },
  };
};

export const listTitleAcceptedFn = ({ activeRows }) => {
  const data = [];
  activeRows.forEach(item => {
    data.push({
      value: item.user_id,
      label: item.full_name,
    });
  });
  return data;
};

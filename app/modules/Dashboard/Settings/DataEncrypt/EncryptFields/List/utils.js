import _isEmpty from 'lodash/isEmpty';
import {
  CellText,
  CellMainEncryptField,
  CellDate,
  CellNumber,
  CellArray,
  CellAddItem,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { mapBluePrint } from '../../../../../../utils/web/renderForm';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';
export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: `${item.item_property_name}@@${item.item_type_id}`,
    user_id_display: item.user_id.map(user => user.userId),
  }));

export const mapUser = user =>
  mapBluePrint(user, item => ({
    ...item,
    id: item.userId,
    label: item.userName,
    value: item.userId,
  }));

export function buildTableGroupColumns(columnName = {}, columnsActive = {}) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainEncryptField,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      className: '',
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      accessorData: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value}`,
    };

    if (property.value === 'user_id') {
      column.idName = 'parent_user_id';
    }

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  user_id: 'user_id_display',
};

const MAP_CELL_BY_VALUE = {
  user_id: CellAddItem,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

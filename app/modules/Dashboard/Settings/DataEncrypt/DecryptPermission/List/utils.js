import _isEmpty from 'lodash/isEmpty';
import {
  CellText,
  CellMainDecryptPermission,
  CellDate,
  CellNumber,
  CellArray,
  CellAddItem,
} from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { mapBluePrint } from '../../../../../../utils/web/renderForm';
import { DATA_INFO } from '../../../../../../services/Abstract.data';
import { safeParse } from '../../../../../../utils/common';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.user_id,
    label: item.full_name,
    subLabel: item.email,
    validate_decrypt_display:
      (DATA_INFO.validate_decrypt.map[`${item.validate_decrypt}`] || {})
        .label || '',
    item_property_name_display: item.item_property_name.map(
      each => `${each.itemPropertyName}@@${safeParse(each.itemTypeId, 0)}`,
    ),
  }));

export const mapEncryptFields = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: `${item.itemPropertyName}@@${safeParse(item.itemTypeId, 0)}`,
    value: `${item.itemPropertyName}@@${safeParse(item.itemTypeId, 0)}`,
    label: safeParse(item.itemPropertyDisplay, item.itemPropertyName),
  }));

export function buildTableGroupColumns(columnName = {}, columnsActive = {}) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: true,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainDecryptPermission,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
      className: '',
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;

    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      accessorData: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '', // 2 === isMetrics typre
      className: `${dataType} ${property.value}`,
    };

    // console.log('property.value', property.value);
    if (property.value === 'validate_decrypt') {
      column.minWidth = 150;
      column.width = 150;
      column.placement = 'right';
    }

    if (property.value === 'item_property_name') {
      column.idName = 'parent_item_property_name';
      column.minWidth = 700;
      column.width = 700;
    }
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  validate_decrypt: 'validate_decrypt_display',
  item_property_name: 'item_property_name_display',
};

const MAP_CELL_BY_VALUE = {
  item_property_name: CellAddItem,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

import React from 'react';

import APP from 'appConfig';

import NotFoundPage from './Notfound/Loadable';
import AdminLoyalty from './AdminLoyalty/Loadable';
import ProfileModule from './Profile/Loadable';
// import Profile2Module from './Profile2/Loadable';
import ApiHubModule from './ApiHub/Loadable';
import MarketingHubModule from './MarketingHub/Loadable';
import RedirectModule from './RedirectModule/Loadable';
import RedirectModule2 from './RedirectModule2/Loadable';
import RedirectModule3 from './RedirectModule3/Loadable';
import RedirectHomePage from './RedirectHomePage/Loadable';
import SettingsHubModule from './Settings/Loadable';
import DashboardModule from './Dashboard/Loadable';
// import Automation from './Marketing/Automation/Loadable';
// import InboxPage2 from './Conversations/Inbox2/Loadable';

const routes = [
  // new router
  {
    path: `${APP.PREFIX}/:portalId/dashboard`,
    private: true,
    exact: false,
    main: () => <DashboardModule />,
  },
  {
    path: `${APP.PREFIX}/:portalId/profile`,
    private: true,
    exact: false,
    main: () => <ProfileModule />,
  },
  {
    path: `${APP.PREFIX}/:portalId/api-hub`,
    private: true,
    exact: false,
    main: () => <ApiHubModule />,
  },
  {
    path: `${APP.PREFIX}/:portalId/marketing-hub`,
    private: true,
    exact: false,
    main: () => <RedirectModule3 moduleName="marketing-hub" />,
  },
  {
    path: `${APP.PREFIX}/:portalId/settings`,
    private: true,
    exact: false,
    main: () => <SettingsHubModule />,
  },
  {
    path: `${APP.PREFIX}/:portalId/admin-loyalty`,
    private: true,
    exact: false,
    main: () => <AdminLoyalty />,
  },

  /* -------------------------------- V2 route -------------------------------- */

  {
    path: `${APP.PREFIX}/:portalId/:userId/marketing-hub`,
    private: true,
    exact: false,
    main: () => <MarketingHubModule />,
  },

  /* ----------------------------- End of v2 route ---------------------------- */

  // for old version
  {
    path: `${APP.PREFIX}/profile`,
    private: true,
    exact: false,
    // main: () => <ProfileModule />,
    main: () => <RedirectModule2 moduleName="profile" />,
  },
  {
    path: `${APP.PREFIX}/api-hub`,
    private: true,
    exact: false,
    main: () => <RedirectModule2 moduleName="api-hub" />,
  },
  {
    path: `${APP.PREFIX}/marketing-hub`,
    private: true,
    exact: false,
    main: () => <RedirectModule2 moduleName="marketing-hub" />,
  },
  {
    path: `${APP.PREFIX}/settings`,
    private: true,
    exact: false,
    main: () => <RedirectModule2 moduleName="settings" />,
  },
  {
    path: `${APP.PREFIX}/:portalId`,
    private: true,
    exact: true,
    main: () => <RedirectModule2 moduleName="recommendation-page" />,
  },
  {
    path: `${APP.PREFIX}`,
    private: true,
    exact: true,
    main: () => <RedirectModule2 moduleName="recommendation-page" />,
  },
  // {
  //   path: `${APP.PREFIX}/:portalId/`,
  //   exact: false,
  //   main: () => <RedirectModule />,
  // },
  // {
  //   path: `/ui-dev`,
  //   exact: false,
  //   main: () => <UIDev />,
  // },
  {
    path: '',
    private: true,
    exact: false,
    // main: () => <NotFoundPage />,
    main: () => <DashboardModule />,
  },
];

export default routes;

/* eslint-disable no-undef */
import { format } from 'date-fns-tz';
import { safeParse } from '../../utils/common';
import {
  getLocalStorage,
  setAppCookieSessionSubDomain,
  getPortalId,
  getCurrentAccessUserId,
} from '../../utils/web/cookie';
import APP from '../../appConfig';
import { validateImageURLPrefix, isLocalhost } from '../../utils/web/utils';
import { MENU_CODE } from './menu.config';
import TRANSLATE_KEY from '../../messages/constant';
import { getPortalTimeZone } from '../../utils/web/portalSetting';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  SUB_MENU_CODE,
  checkingRoleScope,
} from '../../utils/web/permission';

const FORMAT_TIME_ZONE = 'xxxxx';

export function mapDataPortal(items, portalId) {
  const data = {
    list: [],
    map: {},
  };
  const configPortalId = parseInt(safeParse(PORTAL_CONFIG.PORTAL_ID, 0));
  // console.log('configPortalId', configPortalId, PORTAL_CONFIG);
  if (configPortalId > 0) {
    items.forEach(item => {
      // if (item.portalId !== configPortalId) {
      //   data.map[item.portalId] = item;
      // }
      data.map[item.portalId] = item;
      data.list.push(item);
    });
  } else {
    items.forEach(item => {
      data.map[item.portalId] = item;
      if (item.portalId !== portalId) {
        data.list.push(item);
      }
    });
  }
  // console.log('mapDataPortal', data);

  return data;
}

export const serializeDataAccount = dataAPI => {
  const data = {
    list: [],
    map: {},
    option: [],
  };

  if (Array.isArray(dataAPI)) {
    if (safeParse(dataAPI, []).length === 0) {
      return data;
    }

    dataAPI.forEach(item => {
      const temp = {
        user_id: item.userId,
        full_name: item.userName,
        email: item.email,
      };
      data.list.push(temp);
      data.option.push(temp);
      data.map[temp.user_id] = temp;
    });
  }

  return data;
};

export function validateMaintainPage() {
  if (
    PORTAL_CONFIG.IS_MAINTAIN &&
    PORTAL_CONFIG.MAINTAIN_TIME - new Date().getTime() > 0
  ) {
    return true;
  }
  return false;
}

export function serializeDecryptPermission(decryptPermission = {}) {
  const { encryptFields = {} } = decryptPermission;
  const { itemAttributes = [] } = encryptFields;

  const result = itemAttributes.reduce((map, obj) => {
    // eslint-disable-next-line no-param-reassign
    map[obj.itemTypeId] = obj.fields;
    return map;
  }, {});
  // console.log('serializeDecryptPermission', result);
  APP_CACHE_PARAMS.decryptPermission = {
    encryptFields: {
      itemAttributes: result,
    },
  };
}

/**
 * Build when refresh page
 * Remove decrypt field with duration = 'refresh page'
 * @param {*} decryptPermission
 */
export function buildDecryptPermissionFromLocalStorage() {
  try {
    const stringDecryptPermission =
      getLocalStorage('cdp_decrypt_permission') || null;
    const decryptPermission = safeParse(
      JSON.parse(stringDecryptPermission),
      {},
    );
    // console.log('decryptPermission', decryptPermission);
    const itemAttributes = safeParse(decryptPermission.itemAttributes, {});
    Object.keys(itemAttributes).forEach(itemTypeId => {
      const tmp = safeParse(itemAttributes[itemTypeId], {});
      Object.keys(tmp).forEach(attributeCode => {
        if (tmp[attributeCode].duration === 'page_refresh') {
          itemAttributes[itemTypeId][attributeCode].duration = 'lock';
        }
      });
    });

    // console.log('itemAttributes', itemAttributes);
    APP_CACHE_PARAMS.decryptData = { itemAttributes };
    // decryptData
  } catch (err) {
    console.log(err);
  }
}

export function serializeNetworkInfo(networkInfo = {}) {
  const { networkName, subLogo, homePage } = networkInfo || {};
  const tmp = {
    logo: PORTAL_CONFIG.LOGO_HEADER,
    // favicon: '//e.antsomi.com/cdp/default/favicon.ico',
    favicon: '',
    networkName,
    subLogo,
    homePage,
  };

  if (validateImageURLPrefix(networkInfo.logo)) {
    tmp.logo = networkInfo.logo;
  }
  if (validateImageURLPrefix(networkInfo.favicon)) {
    tmp.favicon = networkInfo.favicon;
    changeFavicon(networkInfo.favicon);
  }

  return tmp;
}

const changeFavicon = link => {
  let $favicon = document.querySelector('link[rel="icon"]');
  // If a <link rel="icon"> element already exists,
  // change its href to the given link.
  if ($favicon !== null) {
    $favicon.href = link;
    // Otherwise, create a new element and append it to <head>.
  } else {
    $favicon = document.createElement('link');
    $favicon.rel = 'icon';
    $favicon.href = link;
    document.head.appendChild($favicon);
  }
};

export function setActiveMenu(menuCode) {
  // for left menu cdp
  window.postMessage({
    source: 'CDP_ACTIVE-MENU-TOP-LEFT',
    payload: { menuCode },
  });
  APP_CACHE_PARAMS.menuCodeActive = menuCode;

  // for integration left menu 1rd, need 1s for load and bind static
  // update 3s -> 1s. 3s something is make active menu is slow
  setTimeout(() => {
    // console.log('setActiveMenu', menuCode);
    window.postMessage({
      eventType: 'ACTIVE-MENU-TOP-LEFT',
      header: menuCode,
    });
  }, 1000);
}

export function setCrossAppAuthentication(userInfo = {}) {
  try {
    const ogsToken = {
      user_id: `${userInfo.personal.user_id}`,
      account_id: `${userInfo.personal.user_id}`,
      role: 1,
      token: userInfo.token,
      language: userInfo.personal.language,
      // full_name: userInfo.personal.full_name,
      // email: userInfo.personal.email,
      // avatar: userInfo.personal.avatar,
      // seller_role: 1,
      // conversion_id: 0,
    };

    const COOKIE_EXPIRE = 2592000; // 60 * 60 * 24 * 30 // 30 days
    setAppCookieSessionSubDomain(
      `${PORTAL_CONFIG.INSIGHT_U_OGS}_${userInfo.portalId}`,
      JSON.stringify(ogsToken),
      COOKIE_EXPIRE,
      false,
    );
  } catch (error) {
    console.log(error);
  }
}

export function serializeAvaliableMenu(menus = []) {
  const availableSet = new Set([]);
  const mapMenuPermision = new Map();
  const mapAppByMenuCode = new Map();
  const info = {};
  const appInfo = {};

  if (Array.isArray(menus)) {
    menus.forEach(app => {
      info[app.app_code] = mapMenuAPItoFE(app.childs);
      appInfo[app.app_code] = app.app_name;

      if (app.childs) {
        app.childs.forEach(menu => {
          // set.add(`${app.app_code}@@${menu.menu_code}`);
          availableSet.add(menu.menu_code);
          mapMenuPermision.set(menu.menu_code, {
            selected_view: menu.selected_view,
            selected_edit: menu.selected_edit,
          });
          mapAppByMenuCode.set(menu.menu_code, app.app_code);
          if (menu.childs) {
            menu.childs.forEach(item => {
              availableSet.add(item.menu_code);
              mapMenuPermision.set(item.menu_code, {
                selected_view: item.selected_view,
                selected_edit: item.selected_edit,
              });
              mapAppByMenuCode.set(item.menu_code, app.app_code);
            });
          }
          // mapMenuPermision.set('')
        });
      }
    });
  }
  // console.log('serializeAvaliableMenu', set);
  return { availableSet, info, mapMenuPermision, mapAppByMenuCode, appInfo };
}

function createUrlLink(url) {
  return url.replaceAll(':user_id', getCurrentAccessUserId());
}

export function getUrlLink(item) {
  const res = {
    reload: true,
    href: '',
    isParent: false,
    items: [],
    menuCodesActive: new Set([item.menu_code]),
  };
  if (isLocalhost()) {
    res.href = createUrlLink(`${item.app_path}${item.menu_path}`);
    res.reload = false;
  } else if (window.location.origin === item.app_domain) {
    res.href = createUrlLink(`${item.app_path}${item.menu_path}`);
    res.reload = false;
  } else {
    res.href = createUrlLink(
      `${item.app_domain}${item.app_path}${item.menu_path}`,
    );
    res.reload = true;
  }

  if (item.menu_code === MENU_CODE.EVENT_SOURCES) {
    const PARENT_PREFIX_API_HUB = `${APP.PREFIX}/${getPortalId()}/api-hub`;
    res.isParent = true;
    res.isAccordion = true;
    res.menuCodesActive = new Set([
      item.menu_code,
      ...Object.values(SUB_MENU_CODE.API_HUB),
    ]);
    res.items = [
      {
        key: 'event-attributes',
        menuCodeKey: 'ES_EVENT_ATTRIBUTE',
        menuCodesActive: new Set(['ES_EVENT_ATTRIBUTE']),
        iconName: 'list-alt',
        reload: false,
        href: createUrlLink(
          `${PARENT_PREFIX_API_HUB}/event-sources/event-attributes`,
        ),
        label: 'Event Attributes',
        translateCode: TRANSLATE_KEY._TITL_EVENT_ATTRIBUTE,
      },
      {
        key: 'event',
        menuCodeKey: 'ES_EVENT',
        menuCodesActive: new Set(['ES_EVENT']),
        iconName: 'event-note',
        reload: false,
        href: createUrlLink(`${PARENT_PREFIX_API_HUB}/event-sources/events`),
        label: 'Events',
        translateCode: TRANSLATE_KEY._TITL_EVENT,
      },
      {
        key: 'event-source',
        menuCodeKey: 'ES_SOURCE',
        menuCodesActive: new Set(['ES_SOURCE']),
        iconName: 'dns',
        reload: false,
        href: createUrlLink(`${PARENT_PREFIX_API_HUB}/event-sources/sources`),
        label: 'Sources',
        translateCode: TRANSLATE_KEY._MENU_SUB_SOURCE,
      },
      {
        key: 'conversion-event',
        menuCodeKey: 'ES_CONVERSION_EVENT',
        menuCodesActive: new Set(['ES_CONVERSION_EVENT']),
        iconName: 'conversion-event',
        reload: false,
        href: createUrlLink(
          `${PARENT_PREFIX_API_HUB}/event-sources/conversion`,
        ),
        label: 'Conversion Events',
        translateCode: TRANSLATE_KEY._004,
      },
    ];
  } else if (item.menu_code === MENU_CODE.BUSINESS_OBJECT) {
    const PARENT_PREFIX_API_HUB = `${APP.PREFIX}/${getPortalId()}/api-hub`;
    res.isParent = true;
    res.isAccordion = true;
    res.menuCodesActive = new Set([
      item.menu_code,
      ...Object.values(SUB_MENU_CODE.BO),
    ]);

    if (item.childs && item.childs.length > 0) {
      item.childs.forEach(each => {
        if (each.menu_code === 'DATA_OBJECT') {
          res.items.push({
            key: 'bo-list',
            menuCodeKey: SUB_MENU_CODE.BO.LIST,
            menuCodesActive: new Set([SUB_MENU_CODE.BO.LIST]),
            iconName: 'list-alt',
            reload: false,
            href: createUrlLink(`${PARENT_PREFIX_API_HUB}/objects`),
            label: 'Data Objects',
            translateCode: TRANSLATE_KEY._MENU_SUB_DATA_OBJECT,
          });
        } else if (each.menu_code === 'DATA_VIEW') {
          res.items.push({
            key: 'bo-view',
            menuCodeKey: SUB_MENU_CODE.BO.VIEW,
            menuCodesActive: new Set([SUB_MENU_CODE.BO.VIEW]),
            iconName: 'list-alt',
            reload: false,
            href: createUrlLink(`${PARENT_PREFIX_API_HUB}/objects/data-view`),
            label: 'Data Views',
            translateCode: TRANSLATE_KEY._MENU_SUB_DATA_VIEW,
          });
        }
      });
    }
    res.items = [
      ...res.items,
      {
        key: 'bo-import-history',
        menuCodeKey: SUB_MENU_CODE.BO.IMPORT,
        menuCodesActive: new Set([SUB_MENU_CODE.BO.IMPORT]),
        iconName: 'file_upload',
        reload: false,
        href: createUrlLink(`${PARENT_PREFIX_API_HUB}/import-history`),
        label: 'Upload Histories',
        translateCode: TRANSLATE_KEY._MENU_SUB_UPLOAD_HISTORY,
      },
      {
        key: 'bo-export-history',
        menuCodeKey: SUB_MENU_CODE.BO.EXPORT,
        menuCodesActive: new Set([SUB_MENU_CODE.BO.EXPORT]),
        iconName: 'file_download',
        reload: false,
        href: createUrlLink(`${PARENT_PREFIX_API_HUB}/export-history`),
        label: 'Export Histories',
        translateCode: TRANSLATE_KEY._MENU_SUB_EXPORT_HISTORY,
      },
    ];
  }

  return res;
}

function mapMenuAPItoFE(menus) {
  const arr = [];
  menus.forEach(item => {
    if (+item.show_hide === 1) {
      const res = getUrlLink(item);
      const tmp = {
        key: item.menu_id,
        menuCodeKey: item.menu_code,
        menuCodesActive: res.menuCodesActive,
        items: res.items,
        isParent: res.isParent,
        isAccordion: res.isAccordion,
        iconName: '',
        iconUrl: item.icon,
        href: res.href,
        reload: res.reload,
        label: item.menu_name,
      };
      arr.push(tmp);
    }
  });
  return arr;
}

export const getLabelPortalTimeZone = () =>
  `(UTC ${format(new Date(), FORMAT_TIME_ZONE, {
    timeZone: getPortalTimeZone(),
  })} ${getPortalTimeZone()})`;

export const checkPermissionEdit = (
  getListType,
  menuCodeActive,
  listAccess = [],
  cUserID,
) => {
  const userId = getCurrentAccessUserId();
  let result = false;
  // const isViewNone = checkingRoleScope(
  //   menuCodeActive,
  //   APP_ACTION.VIEW,
  //   APP_ROLE_SCOPE.NONE,
  //   true,
  // );

  const isViewEverything = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.VIEW,
    APP_ROLE_SCOPE.EVERYTHING,
    true,
  );

  const isViewOwned = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.VIEW,
    APP_ROLE_SCOPE.CREATED_BY_USER,
    true,
  );

  const isEditNone = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.UPDATE,
    APP_ROLE_SCOPE.NONE,
    true,
  );

  const isEditEverything = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.UPDATE,
    APP_ROLE_SCOPE.EVERYTHING,
    true,
  );

  const isEditOwned = checkingRoleScope(
    menuCodeActive,
    APP_ACTION.UPDATE,
    APP_ROLE_SCOPE.CREATED_BY_USER,
    true,
  );

  if (getListType === 1) {
    if (isViewEverything && isEditOwned) {
      result = +cUserID === +userId;
    } else if (
      (isViewOwned && isEditOwned) ||
      (isViewEverything && isEditEverything)
    ) {
      result = true;
    } else if (
      (isViewOwned && isEditNone) ||
      (isViewEverything && isEditNone)
    ) {
      result = false;
    }
  } else if (getListType === 2) {
    if (isViewEverything && isEditEverything) {
      result = true;
    } else {
      const userHasAssign = listAccess.find(
        access => +access.user_id === +userId,
      );
      if (userHasAssign) {
        result = +userHasAssign.role !== 3;
      }
    }
  }

  return result;
};

import {
  all,
  call,
  delay,
  put,
  race,
  select,
  take,
  takeLatest,
} from 'redux-saga/effects';

import { replace } from 'react-router-redux';
// import { delay } from 'redux-saga';
// import delay from '@redux-saga/delay-p';
import { OrderedMap } from 'immutable';
import JourneyServices from 'services/Journey';
import { safeParse, updateUrl } from 'utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import {
  makeSelectListStoryAttributes,
  makeSelectListStoryColumn,
  makeSelectListStoryDateRange,
  makeSelectListStoryDefaultMetrics,
  makeSelectListStoryDomainMain,
  makeSelectListStoryFilter,
  makeSelectListStoryModuleConfig,
  makeSelectListStoryTable,
  makeSelectListStoryModuleConfigColumn,
  makeSelectListStoryViewType,
  makeSelectListStoryApiGroupAttributes,
} from './selectors';
import { buildTableGroupColumns } from './utils';
import {
  addNotification,
  getDetailDone,
  getListDone,
  initDone,
  onTablePaging,
  onTableResetSelectRow,
  onTableSelectAll,
  onUpdateTableSelectRow,
  updateDone,
  updateValue,
} from '../../../../../redux/actions';
import {
  toConditionAPI,
  toConditionUI,
  toConditionAPIWithDefaultRule,
} from '../../../../../containers/Filters/utils';
import {
  getCurrentAccessUserId,
  getCurrentOwnerIds,
  getLocalStorage,
  getPortalId,
  setLocalStorage,
} from '../../../../../utils/web/cookie';
import {
  commonHandleFilterSearchAddItemCallback,
  commonHandleGetLastFilter,
} from '../../../../../containers/Filters/saga';
import { initRules } from '../../../../../containers/Filters/action';
import {
  OPERATORS,
  OPERATORS_OPTION,
} from '../../../../../containers/Filters/_UI/operators';
import { commonHandleGetColumns } from '../../../../../containers/ModifyColumn/saga';
import { makeSelectJourneyChannelActive } from '../selectors';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { initDateValue } from '../../../../../components/Templates/CalendarSelection/utils';
import { getColumnTablePerformance } from '../../../../../utils/web/attribute';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  makeUrlPermisison,
  MENU_CODE,
} from '../../../../../utils/web/permission';
import { getViewType, JOURNEY_CHART_METRIC } from '../utils';
import { getDefaultMetricChart } from '../../../../../utils/web/properties';
import { UI_DETAIL_DRAWER } from '../constant';
import { KEY_PARAMS } from '../Main/constants';
import MetaDataServices from 'services/MetaData';
import { normalizeArray } from '../../../../../containers/Drawer/DrawerIntegration/utils';
import APP from '../../../../../appConfig';
import { isNumber } from 'lodash';

const PREFIX = MODULE_CONFIG.key;
const DEFAULT_METRICS = ['impression', 'click'];
const getReducer = state => state.get(PREFIX);

export default function* workerJourneySaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}${ReduxTypes.GET_LIST}`, handleGetListData);

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_TABLE_COLUMN${ReduxTypes.UPDATE_VALUE}`,
    handleBuildTableColumn,
  );

  /** FILTER HANDLE */
  yield takeLatest(
    `${PREFIX}@@COMMON_RESET_PAGING${ReduxTypes.UPDATE_VALUE}`,
    handleResetPaging,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_FILTER_SEARCH_GOTO${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchGoto,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_SEARCH_ADD_ITEM${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchAddItem,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES${ReduxTypes.UPDATE_VALUE}`,
    buildFilterRules,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_CHANGE_RULES${ReduxTypes.UPDATE_VALUE}`,
    handelChangeRuleFilter,
  );
  /** END FILTER HANDLE */

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES_WITH_LOOKUP${
      ReduxTypes.UPDATE_VALUE
    }`,
    buildFilterRulesWithLookupInfo,
  );
  /** END FILTER HANDLE */

  yield takeLatest(
    `${PREFIX}@@CELL_STATUS${ReduxTypes.UPDATE}`,
    handleUpdateStatus,
  );
  yield takeLatest(
    `${PREFIX}@@DATE_RANGE${ReduxTypes.UPDATE}`,
    handleSaveDateRange,
  );
  yield takeLatest(
    `${PREFIX}@@DEFAULT_METRICS${ReduxTypes.UPDATE}`,
    handleSaveDefaultMetrics,
  );
  yield takeLatest(
    `${PREFIX}@@CREATE_COPY${ReduxTypes.CREATE}`,
    handleCreateCopy,
  );
  yield takeLatest(
    `${PREFIX}@@VIEW_TYPE${ReduxTypes.UPDATE_VALUE}`,
    handleChangeListAttributes,
  );

  yield takeLatest(
    `${PREFIX}@@GET_LIST_CHANNELS${ReduxTypes.GET_LIST}`,
    getListChannels,
  );
}

export function* handleInit(action, args) {
  // gọi data date range dùng chung cho 2 component
  const channelActive = yield select(makeSelectJourneyChannelActive());
  yield all([
    put(
      updateValue(`${PREFIX}@@CHANNEL_ACTIVE`, {
        channelActive,
      }),
    ),
    call(handleGetDateRangeValue),
    call(handleGetDefaultMetrics),
  ]);
  yield delay(0); // Important
  // get data init cho table
  yield call(handleInitTable);

  yield put(initDone(PREFIX));

  // yield race([
  //   call(intervalHandleGetData),
  //   take(`${PREFIX}${ReduxTypes.RESET}`),
  // ]);
}

export function* getListChannels() {
  let result = {
    list: [],
    map: {},
  };

  try {
    const response = yield call(MetaDataServices.channel.getList, {
      objectType: 'GEN2_DESTINATION',
    });

    if (response && response.code === 200 && response.data) {
      result = normalizeArray(response.data, 'channelId');
    }

    yield put(getListDone(`${PREFIX}@@GET_LIST_CHANNELS`, result));
    return result;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleFetchListChannel',
      data: {
        err: error.stack,
      },
    });
  }

  return result;
}

export function* handleInitTable(action, args) {
  const moduleConfig = yield select(makeSelectListStoryModuleConfig());
  const reducer = yield select(makeSelectListStoryDomainMain());
  // call get list attribute before get modify column
  yield call(handleGetListAttributes);

  const moduleConfigColumn = yield select(
    makeSelectListStoryModuleConfigColumn(),
  );
  if (reducer.storyId) {
    updateUrl(
      `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
        reducer.moduleConfig.channelId
      }/list`,
    );
  }
  const filterOveride = {
    OR: [
      {
        AND: [
          {
            column: 'story_id',
            data_type: 'number',
            operator: 'matches',
            type: 1,
            value: [reducer.storyId],
          },
        ],
      },
    ],
  };
  yield all([
    call(commonHandleGetColumns, PREFIX, moduleConfigColumn),
    call(
      commonHandleGetLastFilter,
      PREFIX,
      moduleConfig,
      buildFilterRulesWithLookupInfo,
      reducer.storyId && { filterOveride },
    ),
  ]);

  yield put(initDone(`${PREFIX}@@FIRST_TIME`));
  yield call(handleGetListData, action);
  // yield race([
  //   call(intervalHandleGetData),
  //   take(`${PREFIX}${ReduxTypes.RESET}`),
  // ]);
}

export function* intervalHandleGetData() {
  try {
    const reducer = yield select(makeSelectListStoryDomainMain());
    const { isMounted } = reducer;
    while (true) {
      const viewType = yield select(makeSelectListStoryViewType());
      // console.log({ viewType });

      yield delay(10000);
      if (!isMounted) {
        // dừng handleGetDataInterval khi component bị unmount
        break;
      }
      if (viewType !== 'calendar') {
        yield call(handleGetDataInterval);
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'intervalHandleGetData',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetDataInterval() {
  try {
    const { main } = yield select(getReducer);
    const { data } = main;
    const reducer = yield select(makeSelectListStoryDomainMain());
    const reducerColumm = yield select(makeSelectListStoryColumn());
    const reducerFilter = yield select(makeSelectListStoryFilter());
    const { rules } = reducerFilter;
    const apiRules = toConditionAPI(rules);

    const {
      dateRange,
      groupAttributes: { map },
    } = reducer;
    const { columnObj } = reducerColumm;
    // const columns = [...columnObj.columns.columnsAlias];
    const columnsAlias = [...columnObj.columns.columnsAlias];
    const columns = [];
    const perfColumns = [];
    // columnsAlias.forEach(each => {
    //   if (map[each] && map[each].type === 2) {
    //     perfColumns.push(each);
    //   } else {
    //     columns.push(each);
    //   }
    // });
    columnsAlias.forEach(each => {
      if (safeParse(map[each], {}).type == '2') {
        perfColumns.push(each);
      } else if (safeParse(map[each], {}).type == '1') {
        columns.push(each);
      }
    });

    ['story_name', 'story_id', 'status', 'trigger_type'].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });
    columns.forEach((each, index) => {
      if (each === 'channel_id') {
        columns.splice(index, 1);
      }
    });
    if (data.length > 0) {
      const storyIds = [];
      data.forEach(item => {
        storyIds.push(item.story_id);
      });
      const params = {
        data: {
          story_ids: storyIds,
          columns,
          // perf_columns: perfColumns,
          durations: dateRange.value,
          filters: apiRules,
        },
      };
      const res = yield call(JourneyServices.data.getByIds, params);
      // const responseTotal = yield call(JourneyServices.data.getListTotal, params);
      yield put(
        updateValue(`${PREFIX}@@UPDATE_DATA_INTERVAL`, {
          data: res.data,
          // totalPerfomanceMetrics: responseTotal.data[0] || {},
        }),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleGetDataInterval',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetListData(action) {
  try {
    // xử lý get listing journey theo channel từ params
    const channelActiveFromPayload = action && action.payload;

    let channelActive = yield select(makeSelectJourneyChannelActive());

    if (channelActiveFromPayload && isNumber(channelActiveFromPayload.value)) {
      channelActive = channelActiveFromPayload;
    }

    const reducer = yield select(makeSelectListStoryDomainMain());
    const reducerFilter = yield select(makeSelectListStoryFilter());
    const reducerColumm = yield select(makeSelectListStoryColumn());
    const reducerTable = yield select(makeSelectListStoryTable());
    const { columnObj } = reducerColumm;
    const { rules } = reducerFilter;
    const { paging, sort } = reducerTable;
    const { key, by } = sort;
    const {
      dateRange,
      calendarView,
      groupAttributes: { map },
    } = reducer;

    const keyViewType = `${MODULE_CONFIG.objectType}-${channelActive.value}`;
    const viewType = getViewType(keyViewType);

    yield put(updateValue(`${PREFIX}@@VIEW_TYPE`, viewType));

    const calendarViewDateRange = { ...calendarView.dateRange };

    // console.log('columnObj.columns.columnsAlias', columnObj);
    const columnObjAlias = safeParse(columnObj.columns.columnsAlias, [
      'story_name',
      'status',
    ]);
    const columnsAlias = [...columnObjAlias];
    const columnPerformance = getColumnTablePerformance(columnsAlias, map);
    const { perfColumns } = columnPerformance;
    const { columns } = columnPerformance;
    // columnsAlias.forEach(each => {
    //   if (
    //     safeParse(map[each], {}).type == '2' ||
    //     safeParse(map[each], {}).type == '3'
    //   ) {
    //     perfColumns.push(each);
    //   } else if (safeParse(map[each], {}).type == '1') {
    //     columns.push(each);
    //   }
    // });

    [
      'story_name',
      'story_id',
      'status',
      'trigger_type',
      'channel_id',
      'is_blast_campaign',
      'workflow_setting',
    ].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });

    // console.log('rules', rules);
    const apiRules = toConditionAPI(rules);
    const apiRulesCalendar = toConditionAPIWithDefaultRule(apiRules, [
      {
        type: 1,
        column: 'end_date',
        data_type: 'datetime',
        operator: 'after_date',
        value: calendarViewDateRange.startDate,
        time_unit: 'DAY',
      },
      {
        type: 1,
        column: 'start_date',
        data_type: 'datetime',
        operator: 'before_date',
        value: calendarViewDateRange.endDate,
        time_unit: 'DAY',
      },
    ]);
    // console.log({ apiRules, rules, apiRulesCalendar });

    // console.log('dateRange', dateRange);
    // const durations = {
    //   fromDate: dateRange.value.fromDate,
    //   toDate: dateRange.value.toDate,
    // };

    const calendarParams = {
      data: {
        story_type: +channelActive.value === 0 ? null : channelActive.value,
        object_type: channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES',
        page: 1,
        limit: 1000,
        search: '',
        sort: 'start_date',
        sd: 'asc',
        columns: [
          'story_name',
          'status',
          'trigger_type',
          'start_date',
          'end_date',
          'story_id',
          'channel_id',
          'trigger_metadata',
        ],
        perf_columns: [],
        filters: apiRulesCalendar,
        durations: dateRange.value,
      },
      object_type: channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES',
    };
    // console.log({ calendarParams });

    const params = {
      data: {
        story_type: +channelActive.value === 0 ? null : channelActive.value,
        object_type: channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES',
        page: paging.page,
        limit: paging.limit,
        search: '',
        sort: safeParse(key, 'utime'),
        sd: safeParse(by, 'desc'),
        columns,
        perf_columns: perfColumns,
        filters: apiRules,
        durations: dateRange.value,
      },
      object_type: channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES',
    };
    // const response = yield call(JourneyServices.data.getList, params);
    // const responseTotal = yield call(JourneyServices.data.getListTotal, params);
    const response = yield call(
      JourneyServices.data.getListV2_1,
      viewType === 'calendar' ? calendarParams : params,
    );
    // const response = {
    //   meta: {
    //     total: 3992,
    //     totalPerf: {
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //     },
    //   },
    //   data: [
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-06 18:38:41',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-06T10:41:04.327Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-06T10:38:00.000Z',
    //       end_date: null,
    //       story_id: 5854732,
    //       utime: '2024-05-06T10:41:06.071Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-06 18:38:41',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Verify data object deleted or no permission',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-06T09:36:14.154Z',
    //       owner_id: 1600085374,
    //       c_user_id: 'Ngô Văn Lin',
    //       label_ids: [],
    //       start_date: '2024-05-06T09:35:00.000Z',
    //       end_date: null,
    //       story_id: 5854602,
    //       utime: '2024-05-06T10:05:01.608Z',
    //       u_user_id: 'Ngô Văn Lin',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Verify data object deleted or no permission',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name:
    //         '[T] Test conversion copy 2024-05-06 16:10:12 copy 2024-05-06 16:12:37',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-06T08:12:43.328Z',
    //       owner_id: 1600084823,
    //       c_user_id: 'Huỳnh Ngọc Thắng',
    //       label_ids: [],
    //       start_date: '2024-03-19T08:24:00.000Z',
    //       end_date: null,
    //       story_id: 5854438,
    //       utime: '2024-05-06T09:33:08.410Z',
    //       u_user_id: 'Huỳnh Ngọc Thắng',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label:
    //         '[T] Test conversion copy 2024-05-06 16:10:12 copy 2024-05-06 16:12:37',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: '[T] Test conversion copy 2024-05-06 16:10:12',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-06T08:10:29.234Z',
    //       owner_id: 1600084823,
    //       c_user_id: 'Huỳnh Ngọc Thắng',
    //       label_ids: [],
    //       start_date: '2024-03-19T08:24:00.000Z',
    //       end_date: null,
    //       story_id: 5854435,
    //       utime: '2024-05-06T08:10:29.601Z',
    //       u_user_id: 'Huỳnh Ngọc Thắng',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: '[T] Test conversion copy 2024-05-06 16:10:12',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-06 15:11:45',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-06T07:12:07.903Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-06T07:11:00.000Z',
    //       end_date: null,
    //       story_id: 5854305,
    //       utime: '2024-05-06T07:12:08.264Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-06 15:11:45',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name:
    //         'Ẩn header campaign/variant/setting/history khi ở edit mode',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-06T04:45:24.551Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-06T04:44:00.000Z',
    //       end_date: null,
    //       story_id: 5853976,
    //       utime: '2024-05-06T06:43:12.286Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: 'Viet Nam 2',
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label:
    //         'Ẩn header campaign/variant/setting/history khi ở edit mode',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-03 17:30:06',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T09:31:26.043Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2023-07-17T07:24:00.000Z',
    //       end_date: null,
    //       story_id: 5847425,
    //       utime: '2024-05-03T09:31:26.473Z',
    //       u_user_id: 'Thanh QC',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-03 17:30:06',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name:
    //         '[K] JT - Camapaigns with same name copy 2024-05-03 17:22:55',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-03T09:25:25.749Z',
    //       owner_id: 1600085762,
    //       c_user_id: 'KhangLNH',
    //       label_ids: [],
    //       start_date: '2024-05-03T01:36:35.000Z',
    //       end_date: null,
    //       story_id: 5847411,
    //       utime: '2024-05-03T09:25:26.704Z',
    //       u_user_id: 'KhangLNH',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label:
    //         '[K] JT - Camapaigns with same name copy 2024-05-03 17:22:55',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: '[Delivery] Case 01 - 01 copy 2024-05-03 16:28:57',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T08:29:33.029Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2023-07-17T07:24:00.000Z',
    //       end_date: null,
    //       story_id: 5847267,
    //       utime: '2024-05-03T08:29:34.153Z',
    //       u_user_id: 'KhangLNH',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: '[Delivery] Case 01 - 01 copy 2024-05-03 16:28:57',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-03 14:39:54',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T06:40:44.728Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-03T06:40:00.000Z',
    //       end_date: null,
    //       story_id: 5847060,
    //       utime: '2024-05-03T06:40:45.366Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-03 14:39:54',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'My destiny',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T04:20:08.666Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-03T04:00:00.000Z',
    //       end_date: null,
    //       story_id: 5846772,
    //       utime: '2024-05-03T04:20:09.991Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'My destiny',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-03 11:52:06',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T03:56:57.114Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-03T03:52:00.000Z',
    //       end_date: null,
    //       story_id: 5846661,
    //       utime: '2024-05-03T03:56:57.876Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-03 11:52:06',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-03 11:48:17',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T03:51:00.329Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-03T03:48:00.000Z',
    //       end_date: null,
    //       story_id: 5846656,
    //       utime: '2024-05-03T03:51:01.236Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-03 11:48:17',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: '[K] JT - Camapaigns with same name',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-03T02:37:48.433Z',
    //       owner_id: 1600085762,
    //       c_user_id: 'KhangLNH',
    //       label_ids: [],
    //       start_date: '2024-05-03T01:36:35.000Z',
    //       end_date: null,
    //       story_id: 5846512,
    //       utime: '2024-05-03T09:20:58.110Z',
    //       u_user_id: 'KhangLNH',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: '[K] JT - Camapaigns with same name',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name: 'Untitled Journey#2024-05-03 10:08:46',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-03T02:09:45.616Z',
    //       owner_id: 1600085702,
    //       c_user_id: '<EMAIL>',
    //       label_ids: [],
    //       start_date: '2024-05-03T02:18:58.601Z',
    //       end_date: '2024-05-03T02:18:58.601Z',
    //       story_id: 5846473,
    //       utime: '2024-05-03T02:18:58.601Z',
    //       u_user_id: '<EMAIL>',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-03 10:08:46',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-02 17:38:03',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-02T09:41:15.215Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2023-07-17T07:24:00.000Z',
    //       end_date: null,
    //       story_id: 5844780,
    //       utime: '2024-05-02T09:41:16.275Z',
    //       u_user_id: 'Thanh QC',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-02 17:38:03',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name:
    //         'Untitled Journey#2024-05-02 15:59:46 copy 2024-05-02 17:24:37',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-02T09:24:47.288Z',
    //       owner_id: 1600080360,
    //       c_user_id: 'ANTS | QC | Duy',
    //       label_ids: [],
    //       start_date: '2024-05-02T07:59:00.000Z',
    //       end_date: null,
    //       story_id: 5844753,
    //       utime: '2024-05-02T09:24:47.702Z',
    //       u_user_id: 'Huỳnh Ngọc Thắng',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label:
    //         'Untitled Journey#2024-05-02 15:59:46 copy 2024-05-02 17:24:37',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name: 'Untitled Journey#2024-05-02 17:21:55',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-02T09:23:25.104Z',
    //       owner_id: 1600085702,
    //       c_user_id: '<EMAIL>',
    //       label_ids: [],
    //       start_date: '2024-05-02T09:26:36.313Z',
    //       end_date: '2024-05-02T09:26:36.313Z',
    //       story_id: 5844749,
    //       utime: '2024-05-02T09:26:46.875Z',
    //       u_user_id: '<EMAIL>',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: 'g',
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-02 17:21:55',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-02 16:03:16',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-02T08:05:13.005Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-02T08:03:00.000Z',
    //       end_date: null,
    //       story_id: 5844593,
    //       utime: '2024-05-02T08:05:13.326Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-02 16:03:16',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-02 16:04:00',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-02T08:04:13.706Z',
    //       owner_id: 1600080515,
    //       c_user_id: 'Đạt Lê',
    //       label_ids: [],
    //       start_date: '2024-05-02T08:04:00.000Z',
    //       end_date: null,
    //       story_id: 5844589,
    //       utime: '2024-05-02T08:04:14.571Z',
    //       u_user_id: 'Nhi QC( View + Edit = everything)',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-02 16:04:00',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-02 15:59:46',
    //       trigger_type: 'EVENT_BASED',
    //       ctime: '2024-05-02T08:00:26.741Z',
    //       owner_id: 1600080360,
    //       c_user_id: 'ANTS | QC | Duy',
    //       label_ids: [],
    //       start_date: '2024-05-02T07:59:00.000Z',
    //       end_date: null,
    //       story_id: 5844503,
    //       utime: '2024-05-02T08:00:27.754Z',
    //       u_user_id: 'ANTS | QC | Duy',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-02 15:59:46',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name: 'thanh test znsdasd',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-02T04:47:22.291Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2024-05-02T06:37:29.702Z',
    //       end_date: '2024-05-02T06:37:29.702Z',
    //       story_id: 5844169,
    //       utime: '2024-05-04T05:01:33.249Z',
    //       u_user_id: 'Huỳnh Ngọc Thắng',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'thanh test znsdasd',
    //       translate_description: null,
    //     },
    //     {
    //       status: 9,
    //       story_name: 'Untitled Journey#2024-05-01 16:16:13',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-05-01T08:17:07.637Z',
    //       owner_id: 1600083229,
    //       c_user_id: 'Antsomi Product',
    //       label_ids: [],
    //       start_date: '2024-05-01T08:16:00.000Z',
    //       end_date: null,
    //       story_id: 5842168,
    //       utime: '2024-05-01T08:17:09.742Z',
    //       u_user_id: 'Antsomi Product',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: true,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: true,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [1, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-05-01 16:16:13',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name:
    //         'Untitled Journey#2024-04-29 15:32:52 copy 2024-04-29 15:34:36',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-04-29T07:35:33.022Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2024-04-29T17:00:00.000Z',
    //       end_date: null,
    //       story_id: 5837400,
    //       utime: '2024-05-02T04:41:56.714Z',
    //       u_user_id: 'Thanh QC',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label:
    //         'Untitled Journey#2024-04-29 15:32:52 copy 2024-04-29 15:34:36',
    //       translate_description: null,
    //     },
    //     {
    //       status: 7,
    //       story_name: 'Untitled Journey#2024-04-29 15:32:52',
    //       trigger_type: 'SCHEDULED',
    //       ctime: '2024-04-29T07:34:23.557Z',
    //       owner_id: 1600083946,
    //       c_user_id: 'Thanh QC',
    //       label_ids: [],
    //       start_date: '2024-04-29T16:01:00.000Z',
    //       end_date: null,
    //       story_id: 5837395,
    //       utime: '2024-05-02T04:47:02.293Z',
    //       u_user_id: 'Thanh QC',
    //       t26_dropdown_2: null,
    //       t26_dropdown_3: null,
    //       t26_dropdown_1: null,
    //       channel_id: 8,
    //       is_blast_campaign: '0',
    //       portal_id: 33167,
    //       row_count: 3992,
    //       events_entered_control_group: 0,
    //       calc_ctr: 0,
    //       calc_total_delivery_error_rate: 0,
    //       events_hard_bounce: 0,
    //       events_soft_bounce: 0,
    //       events_delivered: 0,
    //       events_finish: 0,
    //       calc_open_rate: 0,
    //       events_sent: 0,
    //       calc_delivered_rate: 0,
    //       events_enter: 0,
    //       calc_click_rate: 0,
    //       events_open: 0,
    //       events_exit: 0,
    //       events_invalid_request: 0,
    //       events_delivery_system_error: 0,
    //       events_process_error: 0,
    //       calc_total_delivery_error: 0,
    //       events_click: 0,
    //       calc_5770548: 0,
    //       calc_5625216: 0,
    //       calc_5625215: 0,
    //       calc_1792083: 0,
    //       calc_1792082: 0,
    //       calc_1792087: 0,
    //       calc_1792085: 0,
    //       calc_1376300: 0,
    //       calc_1374678: 0,
    //       accepted_actions: {
    //         ADD_NODE: true,
    //         EDIT: true,
    //         EDIT_STORY_STATUS: true,
    //         EDIT_STORY_NAME: true,
    //         EDIT_DESCRIPTION: true,
    //         EDIT_NODE_NAME: true,
    //         EDIT_TRIGGER_NODE_EVENT: true,
    //         EDIT_TRIGGER_EVENT_CONDITIONS: true,
    //         EDIT_TRIGGER_SCHEDULE_TIME: true,
    //         EDIT_TRIGGER_TARGET: false,
    //         EDIT_START_DATE: true,
    //         EDIT_END_DATE: true,
    //         EDIT_FREQUENCY_CAPPING: true,
    //         EDIT_ACTION_NODE: true,
    //         EDIT_NODE_DESTINATION_TYPE: false,
    //         EDIT_NODE_DESTINATION_CONTENT: true,
    //         ADD_VARIANT: true,
    //         REMOVE_VARIANT: true,
    //         REMOVE_NODE: true,
    //         REMOVE_STORY: true,
    //         CLONE_STORY: true,
    //       },
    //       available_status: [8, 10, 3],
    //       channel_name: 'Journey Orchestration',
    //       translate_label: 'Untitled Journey#2024-04-29 15:32:52',
    //       translate_description: null,
    //     },
    //   ],
    //   totalRecord: 3992,
    // };

    if (response !== null) {
      const data = {
        totalRecord: response.totalRecord,
      };

      // const portal = yield select(state => makeSelectPortal(state));
      yield put(
        getListDone(PREFIX, {
          data: response.data,
          totalPerfomanceMetrics: response.meta.totalPerf || {},
        }),
      );
      yield put(updateValue(`${PREFIX}@@IS_INIT_DONE`, true));
      yield put(onTablePaging(PREFIX, { data }));

      /* ------------------------- KEEP CHECK SELECTED ROW ------------------------ */
      if (action && action.payload && typeof action.payload === 'object') {
        // isKeepSelectedRow => Giữ lại những row cho check khi get list
        // isSelectedAll => Có select All  cái item khôngDang
        const { isKeepSelectedRow, isSelectedAll } = action.payload;

        if (isKeepSelectedRow && !isSelectedAll) {
          yield put(
            onUpdateTableSelectRow(PREFIX, {
              selectedRows: action.payload.selectedRows,
            }),
          );
        } else if (isKeepSelectedRow && isSelectedAll) {
          yield put(
            onTableSelectAll(PREFIX, {
              data: { isSelectedAll: true },
            }),
          );
        }
      }
    }

    // yield put(setTotalList(response));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleGetListData',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetListAttributes() {
  // console.log('handleGetListAttributes');
  try {
    // const reducerAttribute = yield select(makeSelectListStoryAttributes());
    // if (reducerAttribute.groups.length === 0) {
    const channelActive = yield select(makeSelectJourneyChannelActive());
    const viewType = yield select(makeSelectListStoryViewType());

    const params = {
      objectType: channelActive.value === 0 ? 'All_JOURNEY' : 'STORIES',
      storyType: '',
    };
    if (channelActive.value !== 0) {
      params.storyType = `?storyType=${channelActive.value}`;
    }
    // console.log('params journey', params);
    const response = yield call(
      JourneyServices.columns.getListGroupAttrsForJourneyListing,
      params,
    );
    const data =
      viewType === 'calendar'
        ? safeParse(response.data, []).filter(item => item.type === 1)
        : safeParse(response.data, []);

    yield put(getListDone(`${PREFIX}@@GROUP_ATTRS`, data));
    yield put(getListDone(`${PREFIX}@@API_GROUP_ATTRS`, data));
    // }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleGetListAttributes',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleChangeListAttributes() {
  try {
    // const channelActive = yield select(makeSelectJourneyChannelActive());
    const viewType = yield select(makeSelectListStoryViewType());
    const apiGroupAttributes = yield select(
      makeSelectListStoryApiGroupAttributes(),
    );

    const data =
      viewType === 'calendar'
        ? safeParse(apiGroupAttributes, []).filter(item => item.type === 1)
        : safeParse(apiGroupAttributes, []);

    yield put(getListDone(`${PREFIX}@@GROUP_ATTRS`, data));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleChangeListAttributes',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleBuildTableColumn(action) {
  try {
    const { columns = [], isFetch } = action.payload;
    // console.log('action.payload', action.payload);
    ['story_name'].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });

    const reducerMain = yield select(makeSelectListStoryDomainMain());
    const { groupAttributes } = reducerMain;
    const list = [];
    let columnName = {};
    let columnStatus = {};
    columns.forEach(col => {
      if (groupAttributes.map[col] !== undefined) {
        if (col === 'story_name') {
          columnName = groupAttributes.map[col];
        } else if (col === 'status') {
          columnStatus = groupAttributes.map[col];
        } else {
          list.push(groupAttributes.map[col]);
        }
      }
    });

    if (Object.keys(columnName).length === 0) {
      columnStatus = groupAttributes.map.story_name;
    }

    // ###
    if (Object.keys(columnStatus).length === 0) {
      columnStatus = groupAttributes.map.status;
    }

    const feColumns = buildTableGroupColumns(columnName, columnStatus, list);
    yield put(updateValue(`${PREFIX}@@COMMON_COLUMN_TABLE`, feColumns));
    if (isFetch) {
      yield call(handleGetListData);
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleBuildTableColumn',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleResetPaging() {
  yield put(onTablePaging(PREFIX, { data: { page: 1 } }));
}

export function* handleFilterSearchGoto(action) {
  const data = action.payload;
  // console.log('data', data);
  const { channelId, id } = data;
  try {
    // const channelActive = yield select(makeSelectJourneyChannelActive());
    // yield put(
    //   replace(
    //     makeUrlPermisison(
    //       `/gen2/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}/detail/${id}/campaigns`,
    //     ),
    //   ),
    // );
    yield put(
      replace(
        makeUrlPermisison(
          `/gen2/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}/list?${
            KEY_PARAMS.UI
          }=${UI_DETAIL_DRAWER}&${
            KEY_PARAMS.JOURNEY_ID
          }=${id}&channelId=${channelId}&tab=settings&design=preview`,
        ),
      ),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleBuildTableColumn',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* buildFilterRules(action) {
  const { rules } = action.payload;
  const reducer = yield select(makeSelectListStoryDomainMain());
  const moduleConfig = yield select(makeSelectListStoryModuleConfig());

  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
}

export function* buildFilterRulesWithLookupInfo(action) {
  const { rules, isFetch } = action.payload;
  const reducer = yield select(makeSelectListStoryDomainMain());
  const moduleConfig = yield select(makeSelectListStoryModuleConfig());

  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
  if (isFetch) {
    yield call(handleGetListData);
  }
}

export function* handelChangeRuleFilter(action) {
  try {
    const reducerFilter = yield select(makeSelectListStoryFilter());
    const moduleConfig = yield select(makeSelectListStoryModuleConfig());
    const customFilterId = reducerFilter.config.library.filterCustom.filterId;
    yield put(
      updateValue(`${PREFIX}@@COMMON_FILTER_OBJ`, {
        filterId: customFilterId,
      }),
    );

    const data = {
      type: 'apply',
      filterId: customFilterId,
      rules: toConditionAPI(reducerFilter.rules),
    };
    yield put(
      updateValue(`${PREFIX}@@TO_COMMON_FILTER`, {
        data,
        callbackPrefix: PREFIX,
        moduleConfig,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handelChangeRuleFilter',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleFilterSearchAddItem(action) {
  try {
    const { data } = action.payload;
    const moduleConfig = yield select(makeSelectListStoryModuleConfig());

    const reducerAttribute = yield select(makeSelectListStoryAttributes());
    const property = reducerAttribute.map.story_name;
    if (property) {
      const itemFilter = OrderedMap({
        value: data,
        property,
        operator: OPERATORS_OPTION.CONTAINS,
        operators: OPERATORS.string,
        dataType: property.dataType,
      });
      yield call(
        commonHandleFilterSearchAddItemCallback,
        PREFIX,
        itemFilter,
        makeSelectListStoryFilter,
        moduleConfig,
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleFilterSearchAddItem',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleUpdateStatus(action) {
  const API_RES_CODE_SUCCESS = 200;
  try {
    // console.log('handleUpdateStatus', action);
    const { status, selectedRows } = action.payload;

    // const reducer = yield select(makeSelectListStoryDomainMain());

    // // check quyền VIEW EVERYTHING và truyền param _owner_id tương ứng
    const hasRoleEditEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.UPDATE,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const params = {
      data: {
        story_ids: [...selectedRows.keys()],
        status,
        owner_id: parseInt(getCurrentOwnerIds()),
      },
      _owner_id: hasRoleEditEverything ? null : getCurrentAccessUserId(),
    };

    const res = yield call(JourneyServices.data.updateStatusOwner, params);
    const isSuccessed = res.code;
    if (isSuccessed === API_RES_CODE_SUCCESS) {
      let activeType;
      if (status === 1 || status === 10) {
        activeType = 'active';
      } else if (status === 2) {
        activeType = 'pause';
      } else {
        activeType = '';
      }
      if (selectedRows.size > 1 && activeType.length > 0) {
        yield put(
          updateValue(`${PREFIX}@@CELL_NOTI`, {
            selectedRows,
            res,
            activeType,
          }),
        );
      }

      // console.log('activeType', activeType);
      yield put(
        updateDone(`${PREFIX}@@CELL_STATUS`, {
          selectedRows,
          isSuccessed,
          res,
        }),
      );

      // call API get interval for refresh action
      // yield call(handleGetDataInterval);

      // yield put(handleResetSelect);
      yield put(onTableResetSelectRow(PREFIX, new Map()));
    } else {
      yield put(
        updateDone(`${PREFIX}@@CELL_STATUS`, {
          selectedRows,
          isSuccessed: null,
          res,
        }),
      );

      yield put(onTableResetSelectRow(PREFIX, new Map()));

      const notification = NOTI.updateStatus.fail(res);
      yield put(addNotification(notification));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleUpdateStatus',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetDateRangeValue() {
  try {
    const dateRange = (() => {
      try {
        const item = getLocalStorage('story-list-calendar');
        return item ? JSON.parse(item) : initDateValue();
      } catch (error) {
        addMessageToQueue({
          path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
          func: 'handleGetDateRangeValue',
          data: error.stack,
        });
        console.log(error);
        const item = initDateValue();
        setLocalStorage('story-list-calendar', JSON.stringify(item));
        return item;
      }
    })();
    // console.log('dateRange saga get--story', dateRange);
    yield put(getListDone(`${PREFIX}@@DATE_RANGE`, dateRange));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleGetDateRangeValueParent',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleSaveDateRange(action) {
  try {
    // console.log('handleSaveDateRange', action);
    const dateRange = yield select(makeSelectListStoryDateRange()) ||
      initDateValue();
    // console.log('dateRange saga set--story', dateRange);

    setLocalStorage('story-list-calendar', JSON.stringify(dateRange));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleSaveDateRange',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetDefaultMetrics() {
  try {
    const channelActive = yield select(makeSelectJourneyChannelActive());
    const { key } = channelActive;
    const dataSetLocalStorage = {};
    const defalultMetrix = {};
    defalultMetrix[key] = DEFAULT_METRICS;
    const initMetric = getDefaultMetricChart(
      key,
      dataSetLocalStorage,
      defalultMetrix,
      JOURNEY_CHART_METRIC,
      'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
    );
    // const dateRange = (() => {
    //   try {
    //     /* ------------ Check localStorage ở những User cũ có dạng Array ------------ */
    //     const item = getLocalStorage('story-list-default-metrics');
    //     if (Array.isArray(JSON.parse(item))) {
    //       localStorage.removeItem('story-list-default-metrics');
    //       dataSetLocalStorage[key] = JSON.parse(item);
    //       setLocalStorage(
    //         'story-list-default-metrics',
    //         JSON.stringify(dataSetLocalStorage),
    //       );
    //       return dataSetLocalStorage;
    //     }
    //     return item ? JSON.parse(item) : defalultMetrix;
    //   } catch (error) {
    //     addMessageToQueue({
    //       path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
    //       func: 'handleGetDefaultMetrics',
    //       data: error.stack,
    //     });
    //     console.log(error);
    //     const item = defalultMetrix;
    //     setLocalStorage('story-list-default-metrics', JSON.stringify(item));
    //     return item;
    //   }
    // })();
    yield put(getDetailDone(`${PREFIX}@@DEFAULT_METRICS`, initMetric));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleGetDefaultMetricsParent',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleSaveDefaultMetrics(action) {
  try {
    const channelActive = yield select(makeSelectJourneyChannelActive());
    const { key } = channelActive;
    const dateRange = yield select(makeSelectListStoryDefaultMetrics()) ||
      DEFAULT_METRICS;
    const item = getLocalStorage(JOURNEY_CHART_METRIC);
    const dataGetLocalStorage = JSON.parse(item);
    const dataSetLocalStorage = {
      ...dataGetLocalStorage,
    };
    dataSetLocalStorage[key] = dateRange;
    setLocalStorage(JOURNEY_CHART_METRIC, JSON.stringify(dataSetLocalStorage));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleSaveDefaultMetrics',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleCreateCopy(action) {
  try {
    // console.log('action', action);
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/List/saga.js',
      func: 'handleSaveDateRange',
      data: err.stack,
    });
    console.error(err);
  }
}

/*
 +-+-+-+-+
 |N|O|T|I|
 +-+-+-+-+
*/

const NOTI = {
  updateStatus: {
    fail: res => ({
      id: 'update-status-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: {},
  },
};

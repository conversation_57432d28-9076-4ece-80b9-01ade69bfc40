/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import moment from 'moment';
import { useHistory, withRouter } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import PerformanceChart from 'containers/Widgets/PerformanceChart';
import ErrorBoundary from 'components/common/ErrorBoundary';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import TableContainer from 'containers/Table';
import Calendar from '../Calendar';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
  UIDrawer,
} from '@xlab-team/ui-components';
import UIModalNotice from 'containers/modals/ModalNotice';
import CalendarSelection from 'components/Templates/CalendarSelection';
import JourneyServices from 'services/Journey';
import AddComponent from 'containers/AddComponent';
import { Divider } from 'components/Atoms/Divider';

import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListChannelInfo,
  makeSelectListStoryCalendarView,
  makeSelectListStoryColumn,
  makeSelectListStoryDateRange,
  makeSelectListStoryDefaultMetrics,
  makeSelectListStoryDomainMain,
  makeSelectListStoryDomainMainData,
  makeSelectListStoryFilter,
  makeSelectListStoryMapDataFooter,
  makeSelectListStoryModuleConfig,
  makeSelectListStoryModuleConfigColumn,
  makeSelectListStoryTable,
} from './selectors';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_MAIN } from '../config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  addNotification,
  create,
  dashboardSetOwnerIdFromData,
  getDataJourneyFromPackage,
  getList,
  getListTemplateJourneyInsightRes,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { StyleWrapper, TableRelative, TableWrapper } from './styles';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
import ControlTable from './ControlTable';
import ModalDeleteMulti from '../../../../../containers/modals/ModalDeleteMulti';
import { toConditionAPI } from '../../../../../containers/Filters/utils';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import { MAP_STORY_STATUS } from '../../../../../utils/web/processStatus';
import { useLocalStorage } from '../../../../../utils/web/useHooks';
import {
  makeSelectDataSourceIdJourney,
  makeSelectDataSourceInfo,
  makeSelectIsOpenModalJourneyInfo,
  makeSelectJourneyChannelActive,
  makeSelectLastBuildTimeJourney,
  makeSelectListTemplateJourneyInsightRes,
} from '../selectors';
import { getBreadcrums } from './utils';
import queryString from 'query-string';
import { mapDataJourneyToDataEventsCalendar } from '../Calendar/utils';
import APP from '../../../../../appConfig';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getPortalId,
  getToken,
  setCurrentCreateOwnerId,
} from '../../../../../utils/web/cookie';
import NoDataComponent from '../../../../../components/common/NoDataFound';
import useToggle from '../../../../../hooks/useToggle';
import useNotificationBar from 'hooks/useNotificationBar';
import { makeSelectStoryDetailDomainActiveTab } from '../Main/selectors';
import {
  configToAPISuggestion,
  getBuildTimeWithModelId,
  getChannelCodeById,
  getDomainInsight,
  getObjectTypeByChannelId,
  getViewType,
  setViewType as setViewTypeLocalStorage,
} from '../utils';
import ModalExport from '../../../../../containers/modals/ModalExport';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  makeUrlPermisison,
} from '../../../../../utils/web/permission';
import { trackEvent } from '../../../../../utils/web/utils';

import { DrawerInsightExplore } from '@antscorp/cdp-explore-package'; // package
import { buildTimeDrawer } from '../../../../../containers/Drawer/DrawerInsightExplore/utils';
import { getObjectPropSafely } from '../../../../../components/common/UIEditorPersonalization/utils.3rd';
import AddButton from 'components/Atoms/AddButton';
import ModalJourneyInfo from 'containers/modals/ModalJourneyInfo';
import { getTemplateType } from '../../../../../containers/Drawer/DrawerCreatingJourney/utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { CHANNEL, UI_DETAIL_DRAWER } from '../constant';
import { KEY_PARAMS } from '../Main/constants';

const MAP_TITLE = {
  titlStories: getTranslateMessage(TRANSLATE_KEY._TAB_JOURNEY, 'Journeys'),
  // actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  actExplore: getTranslateMessage(TRANSLATE_KEY._, 'EXPLORE'),
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_STORY, 'Warnings', {
      module,
      all,
      number_not_delete,
      x: number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
  guideNoJourYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_JOURNEY_YET,
    "You haven't created any Journeys yet",
  ),
  titlCalendar: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Calendar'),
};

const getTitleDeleteWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

const PATH = 'app/modules/Dashboard/MarketingHub/Journey/List/index.jsx';

export function StoryPage(props) {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const [configFilter, setConfigFilter] = useState({});
  const [isLoadingSelectionTime, setIsLoadingSelectionTime] = useState(true);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);

  const history = useHistory();
  const searchParams = new URLSearchParams(window.location.search);

  const [showChart, setShowChart] = useLocalStorage(
    'show-chart-list-story',
    true,
  );

  const {
    main,
    table,
    filter,
    column,
    dateRange,
    channelActive,
    defaultMetrics,
    activeTab,
    channelId,
    moduleConfig,
    moduleConfigColumn,
    calendarView = {},
    rightTabWidth,
    isOpenModalJourneyInfo,
    journeyCommonMain,
  } = props;

  const { isInitDone, isFistLoadTable, isInitFirstTimeDone } = main;
  const { isShow } = useNotificationBar();

  const keyViewType = `${MODULE_CONFIG.objectType}-${channelId}`;
  const [viewType, setViewType] = useState(getViewType(keyViewType));

  useEffect(() => {
    props.getListChannel();
  }, []);

  const listChannels = useMemo(() => {
    const tmpListChannels = props.listChannels.list.map(item => {
      return {
        key: item.channelId,
        value: item.channelId,
        code: item.channelCode,
        label: item.channelName,
        logoUrl: item.logoUrl,
      };
    });
    tmpListChannels.filter(item => item.key !== 11 && item.key !== 6);

    // add orchestration
    tmpListChannels.push({
      key: CHANNEL.JOURNEY_ORCHESTRATION.id,
      value: CHANNEL.JOURNEY_ORCHESTRATION.id,
      code: CHANNEL.JOURNEY_ORCHESTRATION.code,
      iconType: 'icon-ants-orchestration',
      label: CHANNEL.JOURNEY_ORCHESTRATION.label,
    });

    return tmpListChannels;
  }, [channelActive, props.listChannels.list]);
  /** Set page title layout 2.0 */

  useEffect(() => {
    // handle open creat journey when use template from media/email template module
    // redirect url from media/email to cdp
    // .../journeys/2/list?type=create-by-template&id=26608&public=0
    try {
      if (!searchParams.has('type')) return;
      (async () => {
        const type = searchParams.get('type');
        const id = searchParams.get('id');
        const publicId = searchParams.get('public');
        if (type === 'create-by-template' && id) {
          const objectType = getObjectTypeByChannelId(channelId);
          const res = await JourneyServices.mediaTemplate.getObjectTemplate({
            id,
            objectType,
            publicId,
          });

          if (res.code === 200) {
            const templateType = getTemplateType(channelId);
            const channelCode = getChannelCodeById(channelId);
            props.updateCreatingJourneyInfo({
              channelCode,
              templateType,
              data: {},
              selectedTemplateTemp: res.data,
            });
            // re-update url after open drawer
            history.push(
              makeUrlPermisison(
                `${
                  APP.PREFIX
                }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
                  channelActive.value
                }/list`,
              ),
            );
            props.toggleLayoutJourney(true);
          }
        }
      })();
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'callback',
        data: error.stack,
      });
      console.log(error);
    }
  }, []);

  useEffect(() => {
    // if (!searchParams.has('channelId') && +channelId !== +channelActive?.value)
    //   return;

    const { storyId } = queryString.parse(window.location.search);
    const object = {
      type: 'story',
      name: 'story',
      isOwner: true,
    };
    trackEvent('object', 'listing', object);
    if (activeTab === 'list') {
      // gọi init ở đây vì đã bắn chéo reducer từ ngoài saga tổng vô
      props.init({ channelId, storyId });
      const filtersBody = {
        objectType: channelActive.value !== 0 ? 'STORIES' : 'ALL_JOURNEY',
        filtersBody: {
          column: 'channel_id',
          data_type: 'number',
          operator: 'equals',
          value: channelActive.value,
        },
        isGetOnlySuggestParams: true,
        isFilters: channelActive.value !== 0 && true,
      };
      setConfigFilter(configToAPISuggestion(filtersBody));
      const newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
        channelActive.value
      }/list`;
      // updateUrl(makeUrlPermisison(newUrl));
    }
    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      props.resetIsLoadingModify();
    };
  }, [channelId, activeTab]);

  let timer;

  useEffect(() => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      // console.log({ isInitDone, isLoading: main.isLoading });
      // khong fetch lan dau (do da dung props.init())
      if (!main.isLoading) {
        props.fetchData();
      }
    }, 1000);
  }, [viewType, calendarView.dateRange]);

  useEffect(() => () => setIsLoadingSelectionTime(false), []);

  const handleSetViewType = type => {
    props.updateIsInitDone(false);
    setTimeout(() => {
      setViewType(type);
      setViewTypeLocalStorage(keyViewType, type);
    }, 200);
  };

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(dataIn);
        break;
      }
      case 'ACTION_TABLE_CHANGE_STATUS': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'ON_CHANGE_DATERANGE': {
        props.onChangeDateRange(dataIn);
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dataIn);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics(dataIn);
        props.onSaveDefaultMetrics(dataIn);
        break;
      }
      case 'ACTION_TABLE_CREATE_COPY': {
        const { ownerData = {}, input } = dataIn;
        if (dataIn.type === 'MODAL_ACCOUNT') {
          props.dashboardSetOwnerIdFromData(ownerData.user_id);
        }
        const id = input.keys().next().value;
        const activeRow = input.get(id);
        const isBlastCampaign = getObjectPropSafely(
          () => Number(activeRow.is_blast_campaign),
          false,
        );

        let search = `?copyId=${id}&channelId=${activeRow.channel_id}`;

        if (isBlastCampaign) {
          search = `?isBlastCampaign=true&copyId=${id}&channelId=${
            activeRow.channel_id
          }`;
        }

        const channelInfo = listChannels.find(
          channel => +channel.value === +activeRow.channel_id,
        );

        // Check oid in url
        const oid = searchParams.get('oid');

        if (oid) {
          search += `&oid=${oid}`;
        }

        history.push({
          search,
        });

        props.updateChannelActiveBySearchParams(channelInfo);
        props.onToggleModalJourneyInfo(false);
        props.toggleLayoutJourney(true);
        break;
      }
      case 'CREATE_WITH_PERMISSION': {
        if (dataIn.type === 'MODAL_ACCOUNT') {
          const { ownerData = {} } = dataIn;
          props.dashboardSetOwnerIdFromData(ownerData.user_id);
        }

        props.onToggleModalJourneyInfo(true);

        break;
      }
      case 'SET_VIEW_TABLE': {
        handleSetViewType('table');
        break;
      }
      case 'SET_LOADING_SELECTION_CHART': {
        setIsLoadingSelectionTime(dataIn);
        break;
      }
      case 'ON_CHANGE_CALENDAR_DATERANGE': {
        props.onChangeCalendarDateRange(dataIn);
        break;
      }
      case 'ON_CLICK_EVENT': {
        const itemChannelId = dataIn.event.channelId;
        history.push(
          `${
            APP.PREFIX
          }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/list?ui=${UI_DETAIL_DRAWER}&${
            KEY_PARAMS.JOURNEY_ID
          }=${
            dataIn.event.id
          }&channelId=${itemChannelId}&tab=statistic&subTab=campaigns`,
        );
        break;
      }

      case 'CLOSE_MODAL_JOURNEY_INFO': {
        // setIsOpenModalCreate(false);
        props.onToggleModalJourneyInfo(false);
        break;
      }

      case 'ON_NEXT_STEP_CREATE_JOURNEY': {
        const { ownerId, channel } = dataIn;
        props.onToggleModalJourneyInfo(false);

        const drawerData = {
          isOpenCreateJourney: true,
          tempOwnerId: getCurrentOwnerId(),
        };

        // Khi ở All channel, create 1 journey sẽ build thêm 1 thông tin channelActive cho drawer tạo
        if (channelId === 0) {
          drawerData.channelActive = channel;
          // build searchParams
          searchParams.set('channelId', channel.value);
          history.replace({ search: searchParams.toString() });
        }
        props.updateDrawerJourneyInfo(drawerData);
        // props.onChangeChannel(channel);
        props.dashboardSetOwnerIdFromData(ownerId);
        setCurrentCreateOwnerId(ownerId);
        break;
      }
      default:
        break;
    }
  };

  const callbackGetListTemplateJourneyInsight = async (data, type) => {
    switch (type) {
      case 'GET_LIST_TEMPLATE':
        props.getListTemplateJourneyInsightRes(data);
        break;

      case 'GET_DATA_JOURNEY_FROM_PACKAGE':
        if (data.dataSourceId) {
          props.getDataJourneyFromPackage({
            dataSourceIdJourney: data.dataSourceId,
            dataSourceInfo: data.sourceInfo,
          });
        }
        break;
      case 'GET_BUILD_TIME':
        if (data.modelId && !props.lastBuildTimeJourney) {
          const buildTimeTZ = await getBuildTimeWithModelId(data.modelId);
          if (buildTimeTZ) {
            props.getDataJourneyFromPackage({
              lastBuildTimeJourney: buildTimeTZ,
            });
          }
        }
        break;

      default:
    }
  };

  const dataJourney = useMemo(() => {
    let result = props.dataSourceIdJourney;

    if (Array.isArray(result)) {
      // trường hợp dataSource là 1 array (journey performance + target segment) thì cần truyền dataSourceInfo qua package để xử lý
      // do tại CDP không có đủ thông tin để chọn dataSourceId truyền vào package
      result = props.dataSourceInfo;
    }
    return result;
  }, [props.dataSourceIdJourney, props.dataSourceInfo]);

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const hasRoleEditEverything = checkingRoleScope(
    MODULE_CONFIG.menuCode,
    APP_ACTION.DELETE,
    APP_ROLE_SCOPE.EVERYTHING,
  );
  const mapParamsDeleteFn = useCallback(
    (oldData, newData) => ({
      data: {
        totalSelected: oldData.isSelectedAll
          ? oldData.totalRecord
          : newData.accepted.length,
        objectIds: newData.accepted.map(each => each.story_id),
        isCheckUsedStatus: 0,
        filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
      },
      _owner_id: hasRoleEditEverything ? null : newData.accepted[0].owner_id,
    }),
    [],
  );
  const initDateRangeData = useMemo(() => dateRange, [isInitDone]);

  // check cái isInitDone = false => tiến hành render

  const breadcrums = useMemo(() => getBreadcrums(channelActive), [
    channelActive.label,
  ]);

  const renderListingTable = () => (
    <>
      <NavBarRightContent
        right="14px"
        className="p-right-4"
        style={{ width: rightTabWidth || 'auto' }}
      >
        <CalendarSelection
          isLoading={isLoadingSelectionTime}
          initData={dateRange}
          callback={callback}
          // maxWidth={channelActive.value === 2 && '135px'}
          maxTotalWidth={rightTabWidth ? rightTabWidth - 16 : 0}
          hiddenLabel={props.isShowOverviewTab}
        />
      </NavBarRightContent>
      <StyleWrapper>
        {/* <CustomHeader breadcrums={breadcrums} /> */}
        <LayoutContent
          padding="0"
          height={isShow ? `calc(100vh - 155px - 50px)` : 'calc(100vh - 155px)'}
          zIndex="1"
        >
          {/* <LayoutContentLoading isLoading={!isInitDone}> */}
          <PerformanceChart
            isParentInitDone={isInitFirstTimeDone}
            defaultMetric={defaultMetrics}
            dateRange={dateRange.value}
            showChart={showChart}
            listSelection={main.groupAttributes.metricsPerformanceChart}
            stylesChart={{ width: '100%', height: 200 }}
            ServiceFn={JourneyServices.chart.getListV2_1}
            // objectId="--"
            // channelId={channelActive.value === 0 ? null : channelActive.value}
            storyType={channelActive.value === 0 ? null : channelActive.value}
            storyId={null}
            objectType={channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES'}
            callback={callback}
            rules={filter.rules}
          />
          <TableRelative>
            <TableWrapper>
              <TableContainer
                global={{ activeTab: props.activeTab }}
                columnActive={column.columnObj}
                table={table}
                isLoading={main.isLoading || !isInitDone}
                moduleConfig={moduleConfig}
                selectedIds={table.selectedIds}
                selectedRows={table.selectedRows}
                isSelectedAll={table.isSelectedAll}
                isSelectedAllPage={table.isSelectedAllPage}
                columns={tableColumns}
                data={props.data}
                callback={callback}
                noDataText="No data"
                resizeColName="status"
                // widthFirstColumns={298} // 150 + 48
                // initialWidthColumns={578} // 150 + 280 + 48
                ComponentControlTable={ControlTable}
                menuCode={MODULE_CONFIG.menuCode}
                isShowFooter
                mapDataFooter={props.mapDataFooter}
                NoDataComponent={() => (
                  <NoDataComponent
                    AddComponent={() => (
                      <AddComponent
                        menuCode={MODULE_CONFIG.menuCode}
                        callback={callback}
                        className="m-left-0"
                        isJourneyV2
                        customLabel={{
                          confirm: 'Continue',
                          title: 'Who is the owner of this journey?',
                        }}
                      />
                    )}
                    guideNoYet={MAP_TITLE.guideNoJourYet}
                    menuCode={MODULE_CONFIG.menuCode}
                  />
                )}
              >
                <>
                  <Filters
                    use="list"
                    menuCode={MODULE_CONFIG.menuCode}
                    rules={filter.rules}
                    moduleConfig={moduleConfig}
                    filterActive={filter.config.filterObj}
                    filterCustom={filter.config.library.filterCustom}
                    libraryFilters={filter.config.library.filters}
                    groups={main.groupAttributes.groupsFilter}
                    isFilter={filter.config.design.isFilter}
                    isLoading={filter.config.isLoading}
                    isLoadingGroupAttributes={main.groupAttributes.isLoading}
                    AddComponent={
                      () => (
                        // channelId !== 0 && (
                        // <AddComponent
                        //   menuCode={MODULE_CONFIG.menuCode}
                        //   callback={callback}
                        // />
                        <AddButton
                          data-test="add-journey-btn"
                          onClick={() => props.onToggleModalJourneyInfo(true)}
                        />
                      )
                      // )
                    }
                  />
                  <WrapActionTable
                    show={!filter.config.design.isFilter}
                    className="p-x-4"
                  >
                    <div className="actionTable__inner">
                      <Search
                        suggestionType="suggestionMultilang"
                        moduleConfig={moduleConfig}
                        config={configFilter}
                        moduleLabel={MAP_TITLE.titlStories}
                      />
                      <ModifyColumn
                        sort={table.sort}
                        moduleConfig={moduleConfigColumn}
                        columnActive={column.columnObj}
                        columnCustom={column.library.columnCustom}
                        libraryColumns={column.library.columns}
                        defaults={moduleConfigColumn.columnsDefault}
                        defaultSortColumns={
                          moduleConfigColumn.defaultSortColumns
                        }
                        columns={column.columnObj.columns.columnsAlias}
                        groups={main.groupAttributes.groups}
                        isLoading={column.isLoading}
                        isLoadingInfoProperties={
                          // Using for loading modify column info-properties
                          main.isLoadingModifyColumn
                        }
                      />
                      <DrawerInsightExplore
                        use="all"
                        token={getToken()}
                        userId={getCurrentAccessUserId()}
                        ownerId={getCurrentOwnerId()}
                        styledWrapper={{
                          marginTop: '2px',
                          marginLeft: '8px',
                        }}
                        domain={getDomainInsight()}
                        portalId={getPortalId()}
                        listTemplateRes={props.listTemplateJourneyInsightRes}
                        callback={callbackGetListTemplateJourneyInsight}
                        itemTypeId={-1009}
                        dataSourceId={dataJourney}
                        lastBuildTime={buildTimeDrawer(
                          props.lastBuildTimeJourney,
                        )}
                        templateCreate="Journeys"
                        nameExplore="journey"
                        CustomButton={btnProps => (
                          <IconButton
                            {...btnProps}
                            iconName="explorer"
                            size="24px"
                            isVertical
                            data-test="exploration"
                          >
                            {MAP_TITLE.actExplore.toUpperCase()}
                          </IconButton>
                        )}
                      />
                      <IconButton
                        iconName="file_download"
                        size="24px"
                        onClick={
                          table.paging.totalRecord === 0
                            ? () => {}
                            : toggleModalDownload
                        }
                        disabled={table.paging.totalRecord === 0 && !isInitDone}
                        isVertical
                        data-test="export-file"
                      >
                        {MAP_TITLE.actExport.toUpperCase()}
                      </IconButton>
                      <IconButton
                        iconName="event"
                        size="24px"
                        onClick={() => handleSetViewType('calendar')}
                        isVertical
                        data-test="calendar"
                      >
                        {MAP_TITLE.titlCalendar.toUpperCase()}
                      </IconButton>
                    </div>
                    <Divider className="m-x-1" />
                    <IconButton
                      size="24px"
                      iconName={showChart ? 'arrow-up' : 'arrow-down'}
                      onClick={() => setShowChart(prev => !prev)}
                    />
                  </WrapActionTable>
                </>
              </TableContainer>
            </TableWrapper>
          </TableRelative>
          {/* </LayoutContentLoading> */}
        </LayoutContent>
        {main.isShowNoti && (
          <UIModalNotice
            isOpen={main.isShowNoti}
            type={main.typeNotiStatus}
            selectedRows={table.selectedRows}
            result={main.resultNoti}
            setIsOpen={() => props.onResetData()}
          />
        )}
        {isOpenModalDelete && (
          <ModalDeleteMulti
            activeRows={table.selectedRows}
            isSelectedAll={table.isSelectedAll}
            totalRecord={table.paging.totalRecord}
            rules={filter.rules}
            label={getTranslateMessage(
              TRANSLATE_KEY._BO_CNFRM_POP_TITLE,
              'Confirm your action',
            )}
            placeHolderName=""
            moduleName={MAP_TITLE.titlStories}
            ObjectServicesFn={JourneyServices.data.deleteWithCondition}
            criteriaFn={each =>
              each.original_status === MAP_STORY_STATUS.DESIGNED
            }
            isOpenModal={isOpenModalDelete}
            setOpenModal={setIsOpenModalDelete}
            fetchData={props.fetchData}
            mapParamsFn={mapParamsDeleteFn}
            getTitleWarning={getTitleDeleteWarning}
          />
        )}
        {isOpenModalJourneyInfo && (
          <ModalJourneyInfo
            isOpen={isOpenModalJourneyInfo}
            callback={callback}
            channelActive={channelActive}
            listChannels={listChannels}
            isLoading={props.listChannels.isLoading}
          />
        )}
        <ModalExport
          isOpen={isOpenModalDownload}
          toggle={toggleModalDownload}
          paging={table.paging}
          sort={table.sort}
          filters={filter}
          objectName="Journey"
          channelId={channelActive.value === 0 ? null : channelActive.value}
          itemTypeId={MODULE_CONFIG.objectId}
          itemTypeName="journeys"
          perf_columns={{ column, main }}
          durations={dateRange.value}
          // columns={column.columnObj.columns.columnsAlias}
          // properties={column.columnObj.columns.columnsAlias}
          customName={`data_all_journey_${getObjectPropSafely(() =>
            channelActive.label.toLowerCase(),
          )}_${new Date().toLocaleString()}`}
          feService={JourneyServices.data.download}
          object_type={channelActive.value === 0 ? 'ALL_JOURNEY' : 'STORIES'}
          columns={{ column, main, type: 'story_id' }}
        />
      </StyleWrapper>
    </>
  );

  const renderListingCalendar = () => {
    const calendarConfig = {
      type: 1,
      name: 'MONTH',
      // currentDate = endDate - 1 month because startDate is prev month
      currentDate: moment(calendarView.dateRange.currentDate).format(
        '01/MM/YYYY',
      ),
    };
    return (
      <StyleWrapper>
        {/* <CustomHeader breadcrums={breadcrums} /> */}
        <Calendar
          isLoading={main.isLoading}
          callback={callback}
          data={mapDataJourneyToDataEventsCalendar(props.data)}
          calendarConfig={calendarConfig}
          filter={filter}
          moduleConfig={moduleConfig}
          main={main}
          AddComponent={() =>
            channelId !== 0 && (
              <AddComponent
                menuCode={MODULE_CONFIG.menuCode}
                callback={callback}
              />
            )
          }
        />
      </StyleWrapper>
    );
  };

  // chờ đông bộ channel mới cho render Listing này
  // use case render list channel cũ sau đó mới render ra list journey theo channel mới
  if (!searchParams.has('channelId') && +channelId !== +channelActive?.value)
    return null;

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/List/index.jsx">
      {viewType === 'table'
        ? renderListingTable()
        : viewType === 'calendar'
        ? renderListingCalendar()
        : null}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListStoryDomainMain(),
  table: makeSelectListStoryTable(),
  filter: makeSelectListStoryFilter(),
  column: makeSelectListStoryColumn(),
  data: makeSelectListStoryDomainMainData(),
  mapDataFooter: makeSelectListStoryMapDataFooter(),
  dateRange: makeSelectListStoryDateRange(),
  channelActive: makeSelectJourneyChannelActive(),
  defaultMetrics: makeSelectListStoryDefaultMetrics(),
  activeTab: makeSelectStoryDetailDomainActiveTab(),
  moduleConfig: makeSelectListStoryModuleConfig(),
  moduleConfigColumn: makeSelectListStoryModuleConfigColumn(),
  calendarView: makeSelectListStoryCalendarView(),
  listTemplateJourneyInsightRes: makeSelectListTemplateJourneyInsightRes(),
  dataSourceIdJourney: makeSelectDataSourceIdJourney(),
  dataSourceInfo: makeSelectDataSourceInfo(),
  lastBuildTimeJourney: makeSelectLastBuildTimeJourney(),
  isOpenModalJourneyInfo: makeSelectIsOpenModalJourneyInfo(),
  listChannels: makeSelectListChannelInfo(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    getListChannel: params => {
      dispatch(getList(`${MODULE_CONFIG.key}@@GET_LIST_CHANNELS`, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onResetData: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@RESET_NOTI`, params));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeCalendarDateRange: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CALENDAR_DATE_RANGE`, params),
      );
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeDefaultMetrics: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    onSaveDefaultMetrics: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    onCreateCopy: params => {
      dispatch(create(`${MODULE_CONFIG.key}@@CREATE_COPY`, params));
    },
    resetIsLoadingModify: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    },
    dashboardSetOwnerIdFromData: params => {
      dispatch(dashboardSetOwnerIdFromData(params));
    },
    updateIsInitDone: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_INIT_DONE`, params));
    },
    getListTemplateJourneyInsightRes: params => {
      dispatch(
        getListTemplateJourneyInsightRes(MODULE_CONFIG_MAIN.key, params),
      );
    },
    getDataJourneyFromPackage: params => {
      dispatch(getDataJourneyFromPackage(MODULE_CONFIG_MAIN.key, params));
    },
    onChangeChannel: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG_MAIN.key}@@CHANNEL_ACTIVE@@`, params),
      );
    },
    updateDrawerJourneyInfo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG_MAIN.key}@@DRAWER_JOURNEY_INFO`, params),
      );
    },
    onToggleModalJourneyInfo: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_MAIN.key}@@TOGGLE_MODAL_JOURNEY_INFO@@`,
          params,
        ),
      );
    },
    toggleLayoutJourney: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG_MAIN.key}@@TOGGLE_LAYOUT_JOURNEY`, params),
      );
    },
    updateCreatingJourneyInfo: value => {
      dispatch(
        updateValue(`${MODULE_CONFIG_MAIN.key}@@CREATING_JOURNEY_INFO`, value),
      );
    },
    updateChannelActiveBySearchParams: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_MAIN.key}@@UPDATE_CHANNEL_ACTIVE_BY_SEARCH_PARAMS@@`,
          params,
        ),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(StoryPage);

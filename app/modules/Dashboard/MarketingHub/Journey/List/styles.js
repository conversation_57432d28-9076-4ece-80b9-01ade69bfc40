import { Popover } from '@material-ui/core';
import styled from 'styled-components';
import colors from 'utils/colors';

export const StyleWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: ${props =>
    props.height || (props.isShowDrawer && 'calc(100vh - 54px)')};
  /* margin-top: -12px; */
  /* padding-right: 12px; */
  /* padding-bottom: 12px; */
`;

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  //margin-top: 0.5rem;
  /* padding: 3.25rem 0.75rem 0.2rem 0.75rem; */
`;

// export const WraperCalendar = styled.div`
//   /* width: 100%; */
//   height: 0rem;
//   position: absolute;
//   top: -15rem;
//   left: 357px;
//   ${breakdownMd(
//     css`
//       /* height: 2.5rem; */
//     `,
//   )}
// `;
// export const TableWrapper = styled.div`
//   position: absolute;
//   width: 100%;
//   height: 100%;
//   top: 0;
//   left: 0;
//     /* ${breakdownMd(
//       css`
//         height: calc(100vh - 108px);
//         top: 108px;
//         left: 0.75rem;
//         right: 0.75rem;
//       `,
//     )} */
// `;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  height: 100%;

  .table {
    .header {
      .tr {
        .th:first-child {
          border-right: 1px solid ${colors.platinum};
        }
      }
    }

    .body {
      .tr {
        .td:first-child {
          border-right: 1px solid ${colors.platinum};
        }
      }
    }
  }
`;

export const WrapperAddButton = styled.div`
  margin-left: 1rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

export const StylePopover = styled(Popover)`
  .MuiPopover-paper {
    border-radius: 10px;
  }
`;

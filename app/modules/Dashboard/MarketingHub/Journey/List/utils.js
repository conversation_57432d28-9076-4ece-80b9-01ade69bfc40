import { getLabelStoryStatus } from '../../../../../utils/web/processStatus';
import {
  CellArray,
  CellDate,
  CellMainJourney,
  CellNumber,
  CellStoryStatus,
  CellText,
  CellArrayObject,
} from '../../../../../containers/Table/Cell';
import { DATA_INFO } from '../../../../../services/Abstract.data';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { initNumberId } from '../../../../../utils/web/portalSetting';

// import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../appConfig';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_JOURNEY,
      'Listing Journeys',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach((tmp, index) => {
    // console.log('tmp', tmp);
    const tempt = {
      ...tmp,
      id: tmp.story_id,
      status: getLabelStoryStatus(parseInt(tmp.status)),
      original_status: tmp.status,
      trigger_type_display: tmp.trigger_type
        ? DATA_INFO.trigger_type.map[tmp.trigger_type].label
        : '',
      channel_id_display: tmp.channel_name,
    };
    // // hard ownerId for test data permission
    // if (index < 300) {
    //   tempt.owner_id = 561374215;
    // }
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  const disableSortBy = parseInt(columnStatus.isSort) !== 1;

  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellMainJourney,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      disableSortBy,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      // maxWidth: 150,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellStoryStatus,
      placement: 'status',
      className: `${columnStatus.value}`,
      Footer: '',
      // left:
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    // console.log('columnsActive', property);
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '0' : '',
      className: `${dataType} ${property.value}`,
    };

    if (['c_user_id', 'u_user_id'].includes(property.value)) {
      column.placement = 'left';
    }

    // if (property.value === 'label_ids') {
    //   column.Cell = CellArrayObject;
    // }
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    ['story_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['end_date'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  trigger_type: 'trigger_type_display',
  channel_id: 'channel_id_display',
};

const MAP_CELL_BY_VALUE = {
  // for customize column
  // story_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};

export const checkRuleForCloneJourney = (action, selectedRows) => {
  let IS_DISABLE = false;
  const actionTmp = [];

  selectedRows.forEach(each => {
    // console.log('each', each);
    const { name } = each.status;
    if (name === 'error' || name === 'removed') {
      IS_DISABLE = true;
    }
  });

  if (selectedRows.size > 1) {
    IS_DISABLE = true;
  }
  action.forEach(each => {
    if (each.value === 'copy') {
      each.disabled = IS_DISABLE;
    }
    actionTmp.push(each);
  });

  return actionTmp;
};

export const mapDataToConfigFilter = (
  channelActive,
  moduleConfig,
  isUseApiSelector,
  isMapConfigSuggestionForDetails,
) => {
  // console.log('channelActive, moduleConfig', channelActive, moduleConfig);
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType, // Sử dụng objectype của stories để đi call API
    limit: isUseApiSelector ? null : 100,
    page: 1,
    sd: 'asc',
    propertyCode: 'story_id',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '5-story_name',
    itemTypeId: null,
    // offset: null,
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== moduleConfig.objectId // Khác object id để check với trường hợp channel all
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: isMapConfigSuggestionForDetails
                ? 'story_id'
                : 'channel_id',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 100,
    page: 1,
    sd: 'asc',
    propertyCode: 'story_id',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    isGetOnlySuggestParams: 0,
  };
  if (channelActive && channelActive.value !== 0) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'channel_id',
              data_type: 'number',
              operator: 'equals',
              value: channelActive.value,
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

import { subDays } from 'date-fns';
import { getLabelStoryStatus } from '../../../../../utils/web/processStatus';
import {
  CellText,
  // CellMainJourney,
  // CellStoryStatus,
  CellDate,
  CellNumber,
  CellArray,
  CellMainZone,
  // CellToggle,
  CellArrayObject,
} from '../../../../../containers/Table/Cell';
import { DATA_INFO } from '../../../../../services/Abstract.data';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { initNumberId } from '../../../../../utils/web/portalSetting';

// import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BREADCRUMDATA } from './config';
import APP from '../../../../../appConfig';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_ZONE,
      'Listing zones',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

// Data table
export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach((tmp, index) => {
    // Map columm trùng với cột
    const tempt = {
      ...tmp,
      id: tmp.zone_id,
      // Gọi tới zone_type trong abstract
      zone_type_display: tmp.type
        ? DATA_INFO.zone_type.map[tmp.type].label
        : '',
    };
    // hard ownerId for test data permission
    // if (index < 3) {
    //   tempt.owner_id = 561374215;
    // }
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellMainZone,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    // {
    //   Header: columnStatus.label,
    //   id: columnStatus.value,
    //   name: columnStatus.value,
    //   accessor: columnStatus.value,
    //   width: 150,
    //   minWidth: 150,
    //   // maxWidth: 150,
    //   // disableResizing: true,
    //   sticky: 'left',
    //   Cell: CellStoryStatus,
    //   placement: 'center',
    //   className: `${columnStatus.value}`,
    //   Footer: '',
    // },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    // console.log('columnsActive', property);
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      // Nếu ko có accessor thì bằng chính value còn có thì chuyển hướng gọi data[type]
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    ['zone_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['end_date'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}

// Tạo ra một trường mới map với data để khi gọi tới data[type]
const MAP_ACCESSOR = {
  type: 'zone_type_display',
};

const MAP_CELL_BY_VALUE = {
  // for customize column
  // zone_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
};

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};

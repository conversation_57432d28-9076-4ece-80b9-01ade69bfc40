import { createSelector } from 'reselect';

import { MODULE_CONFIG } from './config';
import { initialState } from './reducer';
import { isEmpty, get } from 'lodash';

/**
 * Direct selector to the Story state domain
 */

const selectDomain = state =>
  state.get(MODULE_CONFIG.key) || {
    main: initialState,
  };

/**
 * Other specific selectors
 */

/**
 * Default selector used by Story
 */

const makeSelectJourneyCommon = () =>
  createSelector(
    selectDomain,
    substate => substate,
  );

const makeSelectJourneyCommonMain = () =>
  createSelector(
    selectDomain,
    substate => substate.main,
  );

const makeSelectJourneyChannel = () =>
  createSelector(
    selectDomain,
    substate => substate.main.channels,
  );

const makeSelectJourneyChannelActive = () =>
  createSelector(
    selectDomain,
    substate => {
      let result = substate.main.channelActive;

      if (!isEmpty(substate.main.drawerJourneyInfo.channelActive)) {
        result = substate.main.drawerJourneyInfo.channelActive;
      }

      return result;
    },
  );

const makeSelectCreatingJourneyInfo = () =>
  createSelector(
    selectDomain,
    substate => substate.main.creatingJourneyInfo,
  );

const makeSelectThirdPartyCampaignInfo = () =>
  createSelector(
    selectDomain,
    substate => substate.main.thirdPartyCampaigns,
  );

const makeSelectCache3rdPartyCampaign = () =>
  createSelector(
    selectDomain,
    substate => substate.main.cacheCreateCopy3rdParty,
  );

const makeSelectCacheZaloZNSTemplate = () =>
  createSelector(
    selectDomain,
    substate => substate.main.cacheZaloZNSTemplate,
  );

const makeSelectJourneyCommonLoading = () =>
  createSelector(
    selectDomain,
    substate => substate.main.isLoading,
  );

const makeSelectListTemplateJourneyInsightRes = () =>
  createSelector(
    selectDomain,
    substate => substate.main.listTemplateJourneyInsightRes,
  );

const makeSelectDataSourceIdJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.dataSourceIdJourney,
  );

const makeSelectDataSourceInfo = () =>
  createSelector(
    selectDomain,
    substate => substate.main.dataSourceInfo,
  );

const makeSelectLastBuildTimeJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.lastBuildTimeJourney,
  );

const makeSelectClosableDrawerCreateJr = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.closable,
  );

const makeSelectIsOpenSubDrawer = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.isOpenSubDrawer,
  );

const makeSelectIsOpenCreateJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.isOpenCreateJourney,
  );

const makeSelectIsOpenLayoutJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.isOpenLayoutJourney,
  );

const makeSelectIsSaveDoneJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.isSaveDone,
  );

const makeSelectLoadingOnStepTwo = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.loadingOnStepTwo,
  );

const makeSelectIsOpenDrawerCreateCopyJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.isOpenDrawerCreateCopyJourney,
  );

const makeSelectIsOpenModalJourneyInfo = () =>
  createSelector(
    selectDomain,
    substate => substate.main.isOpenModalJourneyInfo,
  );

const makeSelectIsOpenModalConfirmCreateCopyJourney = () =>
  createSelector(
    selectDomain,
    substate => substate.main.drawerJourneyInfo.isOpenModalConfirmCreateCopy,
  );

const makeSelectStatusRename = () =>
  createSelector(
    selectDomain,
    substate => substate.main.isRenameSuccess,
  );

const makeSelectIsOpenFullScreen = () =>
  createSelector(
    selectDomain,
    substate => substate.main.isFullScreen,
  );

const selectCacheListWhatsappTemplate = createSelector(
  selectDomain,
  substate => {
    const { status, data = {} } = get(
      substate,
      'main.cacheWhatsappTemplate',
      initialState.cacheWhatsappTemplate,
    );

    return { status, data };
  },
);

const makeSelectCacheListWhatsappTemplateByKey = cacheKey => {
  return createSelector(
    selectCacheListWhatsappTemplate,
    ({ status, data: whatsappTemplate }) => {
      return { status, data: get(whatsappTemplate, cacheKey) };
    },
  );
};

const selectCacheListBitrixSource = createSelector(
  selectDomain,
  substate => {
    const { status, data = {} } = get(
      substate,
      'main.cacheExtraBitrixSource',
      initialState.cacheExtraBitrixSource,
    );

    return { status, data };
  },
);

const makeSelectCacheListBitrixSourceByKey = cacheKey => {
  return createSelector(
    selectCacheListBitrixSource,
    ({ status, data: bitrixSource }) => {
      return { status, data: get(bitrixSource, cacheKey) };
    },
  );
};

export default makeSelectJourneyCommon;

export {
  makeSelectDataSourceIdJourney,
  makeSelectDataSourceInfo,
  makeSelectJourneyChannel,
  makeSelectJourneyChannelActive,
  makeSelectJourneyCommonLoading,
  makeSelectJourneyCommonMain,
  makeSelectLastBuildTimeJourney,
  makeSelectListTemplateJourneyInsightRes,
  makeSelectCreatingJourneyInfo,
  makeSelectThirdPartyCampaignInfo,
  makeSelectIsOpenSubDrawer,
  makeSelectIsOpenCreateJourney,
  makeSelectIsOpenDrawerCreateCopyJourney,
  makeSelectIsOpenModalJourneyInfo,
  makeSelectIsOpenLayoutJourney,
  makeSelectIsOpenModalConfirmCreateCopyJourney,
  makeSelectLoadingOnStepTwo,
  makeSelectStatusRename,
  makeSelectIsOpenFullScreen,
  makeSelectIsSaveDoneJourney,
  makeSelectCache3rdPartyCampaign,
  makeSelectCacheZaloZNSTemplate,
  makeSelectCacheListWhatsappTemplateByKey,
  selectCacheListWhatsappTemplate,
  makeSelectCacheListBitrixSourceByKey,
  makeSelectClosableDrawerCreateJr,
};

// import { getLabelStoryStatus } from '../../../../../../utils/web/processStatus';
import {
  getLabelCampaignStatus,
  getLabelStoryStatus,
} from '../../../../../utils/web/processStatus';
import {
  CellArray,
  CellDate,
  CellJourneyStatus,
  CellMainCampaign,
  CellNumber,
  CellStoryStatus,
  CellText,
  CellToggle,
  CellArrayObject,
} from '../../../../../containers/Table/Cell';
// import { getSegmentTypeLabel } from '../../../../../../services/Abstract.data';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../../utils/web/permission';
import { initNumberId } from '../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
// import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../appConfig';
import { getCurrentAccessUserId } from '../../../../../utils/web/cookie';
import { MENU_CODE } from '../../../../../utils/web/permission';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId, isDetailJourney = false) {
  const data = { list: [], map: {} };
  list.forEach((tmp, index) => {
    const tempt = {
      ...tmp,
      id: tmp.campaign_id,
      // status: getLabelCampaignStatus(tmp.status),
      // story_status: getLabelCampaignStatus(tmp.story_status),
      original_status: tmp.status,
      story_type: tmp.story_type_name,
      // channel_name: tmp.story_type_name,
      story_status: getLabelStoryStatus(tmp.story_status),
      // channel_id: use original_channel_id if need
      channel_id: tmp.channel_name,
      original_channel_id: tmp.channel_id,
      original_story_status: tmp.story_status,
      original_story_type: tmp.story_type,
      isDetailJourney,
    };

    data.list.push(tempt);
    data.map[tempt.id] = tempt;

    // console.log('tmp', tmp);
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  const disableSortBy = parseInt(columnStatus.isSort) !== 1;

  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      Footer: '',
      use: 'PERMISSION',
      menuCode: MENU_CODE.JOURNEY,
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellMainCampaign,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (['c_user_id', 'u_user_id', 'channel_id'].includes(property.value)) {
      column.placement = 'left';
    }
    if (['story_status'].includes(property.value)) {
      column.className = `${dataType} ${property.value} txt-status`;
    }
    ['campaign_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['end_date'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    ['story_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {
  // campaign_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
  channel_id: CellText,
  story_status: CellJourneyStatus,
};

export function getModuleConfig(moduleConfig, storyId, objectType, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = storyId;
  newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};
export const mapDataToConfigFilter = (
  channelActive,
  isMapConfigSuggestionForDetails,
  moduleConfig,
  isUseSelectorApi,
) => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: isUseSelectorApi ? null : 100,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '6-campaign_name',
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== moduleConfig.objectId // Khác object id để check với trường hợp channel all
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: `${
                isMapConfigSuggestionForDetails ? 'story_id' : 'story_type'
              }`,
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigFilterDetail = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
  };
  if (channelActive && channelActive.value !== 0) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_id',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    isGetOnlySuggestParams: 0,
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_type',
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${getCurrentAccessUserId()}/${
        channelActive.value
      }`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_CAMPAIGN,
      'Listing Campaigns',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

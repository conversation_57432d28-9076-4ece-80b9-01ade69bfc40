/* eslint-disable indent */
/* eslint-disable func-names */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import CalendarSelection from 'components/Templates/CalendarSelection';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import { Divider } from 'components/Atoms/Divider';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import JourneyServices from 'services/Journey';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListCampaignCalendarView,
  makeSelectListCampaignDateRange,
  makeSelectListCampaignDefaultMetrics,
  makeSelectListStoryCampaignColumn,
  makeSelectListStoryCampaignDomainMain,
  makeSelectListStoryCampaignDomainMainData,
  makeSelectListStoryCampaignFilter,
  makeSelectListStoryCampaignTable,
  makeSelectListStoryMapDataFooter,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_MAIN } from '../config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
import {
  addNotification,
  getDataJourneyFromPackage,
  getList,
  getListTemplateJourneyInsightRes,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import {
  PerformanceChartCampaign,
  TableRelative,
  TableWrapper,
} from './styles';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
import ControlTable from './ControlTable';
import ModalDeleteMulti from '../../../../../containers/modals/ModalDeleteMulti';
import { toConditionAPI } from '../../../../../containers/Filters/utils';
import AddComponent from './AddComponent';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../components/Templates/LayoutContent';
// import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { MAP_STORY_STATUS } from '../../../../../utils/web/processStatus';
import { useLocalStorage } from '../../../../../utils/web/useHooks';
// import ModalDownload from '../../../../../containers/modals/ModalDownload';
import useNotificationBar from 'hooks/useNotificationBar';
import useToggle from '../../../../../hooks/useToggle';
import { makeSelectActiveRow } from '../Detail/selectors';
import { mapDataToConfigSusggestion } from './utils';
import {
  makeSelectDataSourceIdJourney,
  makeSelectDataSourceInfo,
  makeSelectJourneyChannelActive,
  makeSelectLastBuildTimeJourney,
  makeSelectListTemplateJourneyInsightRes,
} from '../selectors';
import { StyleWrapper } from '../List/styles';
import { makeSelectStoryDetailDomainActiveTab } from '../Main/selectors';
import {
  configToAPISuggestion,
  getBuildTimeWithModelId,
  // getColumnsExport,
  getDomainInsight,
  getViewType,
  setViewType as setViewTypeLocalStorage,
} from '../utils';
// import { isProduction, safeParse } from '../../../../../utils/common';
import ModalExport from '../../../../../containers/modals/ModalExport';
// import {
//   APP_ACTION,
//   // makeUrlPermisison,
//   MENU_CODE,
//   validateAction,
// } from '../../../../../utils/web/permission';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getPortalId,
  getToken,
} from '../../../../../utils/web/cookie';
import { mapDataCampaignToDataEventsCalendar } from '../Calendar/utils';
import Calendar from '../Calendar';
// import APP from '../../../../../appConfig';
import moment from 'moment';
import { trackEvent } from '../../../../../utils/web/utils';
import { DrawerInsightExplore } from '@antscorp/cdp-explore-package';
import { buildTimeDrawer } from '../../../../../containers/Drawer/DrawerInsightExplore/utils';

const MAP_TITLE = {
  titlStories: getTranslateMessage(TRANSLATE_KEY._TAB_CAMPAIGN, 'Campaign'),
  // actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  actExplore: getTranslateMessage(TRANSLATE_KEY._, 'EXPLORE'),
  itemNameStories: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'campaign(ies)',
  ),
  titlCalendar: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Calendar'),
};
const layoutStyle = {
  overflow: 'hidden',
  // height: 'calc(100vh - 131px)',
};
// const navbarStyle = {
//   width: '0',
//   height: '0',
//   position: 'absolute',
//   top: '25px',
//   right: '357px',
// };

// const defaultMetric = ['impression', 'click'];

export function Campaign(props) {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isLoadingSelectionTime, setIsLoadingSelectionTime] = useState(true);
  const [configFilter, setConfigFilter] = useState(
    mapDataToConfigSusggestion(),
  );

  // const hasCreateRole = validateAction(
  //   MENU_CODE.JOURNEY,
  //   APP_ACTION.CREATE,
  //   getCurrentAccessUserId(),
  // );

  // console.log('configFilter', configFilter);

  // const [showChart, setShowChart] = useState(false);
  const [showChart, setShowChart] = useLocalStorage(
    'show-chart-list-story-campaign',
    true,
  );

  // const [width, setWidth] = useState(window.innerWidth);
  const { isShow } = useNotificationBar();

  const {
    main,
    table,
    filter,
    column,
    data,
    moduleConfig,
    moduleConfigColumn,
    dateRange,
    defaultMetrics,
    // activeRow,
    channelId,
    storyId,
    channelActive,
    activeTab,
    calendarView = {},
  } = props;
  const { isInitDone, isFistLoadTable } = main;

  const keyViewType = `${MODULE_CONFIG.objectType}-${channelId}`;
  const [viewType, setViewType] = useState(getViewType(keyViewType));

  // console.log('channelActive', channelActive);
  useEffect(() => {
    if (activeTab === 'campaigns') {
      const object = {
        type: 'campaign',
        name: 'campaign',
        isOwner: true,
      };
      trackEvent('object', 'listing', object);
      props.init({ channelId, storyId });
      const filtersBody = {
        objectType: channelActive.value !== 0 ? 'CAMPAIGNS' : 'ALL_CAMPAIGN',
        filtersBody: {
          column: 'story_type',
          data_type: 'number',
          operator: 'equals',
          value: channelActive.value,
        },
        isGetOnlySuggestParams: true,
        isFilters: channelActive.value !== 0 && true,
      };
      setConfigFilter(configToAPISuggestion(filtersBody));
    }
    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      props.resetIsLoadingModify();
      // setDateValue(state.value);
    };
  }, [channelId, activeTab]);

  useEffect(() => {
    // khong fetch lan dau (do da dung props.init())
    if (!main.isLoading) {
      props.fetchData();
    }
  }, [viewType, calendarView.dateRange]);

  useEffect(() => () => setIsLoadingSelectionTime(false), []);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(dataIn);
        break;
      }
      // case 'ACTION_TABLE_CHANGE_STATUS': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      case 'ON_CHANGE_DATERANGE': {
        props.onChangeDateRange(dataIn);
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dateRange);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics(dataIn);
        props.onSaveDefaultMetrics(dataIn);
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'SET_VIEW_TABLE': {
        handleSetViewType('table');
        break;
      }
      case 'SET_LOADING_SELECTION_CHART': {
        setIsLoadingSelectionTime(dataIn);
        break;
      }
      case 'ON_CHANGE_CALENDAR_DATERANGE': {
        props.onChangeCalendarDateRange(dataIn);
        break;
      }
      case 'ON_CLICK_EVENT': {
        // console.log({ dataIn });
        break;
      }
      default:
        break;
    }
  };

  const callbackGetListTemplateJourneyInsight = async (data, type) => {
    switch (type) {
      case 'GET_LIST_TEMPLATE':
        props.getListTemplateJourneyInsightRes(data);
        break;

      case 'GET_DATA_JOURNEY_FROM_PACKAGE':
        if (data.dataSourceId) {
          props.getDataJourneyFromPackage({
            dataSourceIdJourney: data.dataSourceId,
            dataSourceInfo: data.sourceInfo,
          });
        }
        break;
      case 'GET_BUILD_TIME':
        if (data.modelId && !props.lastBuildTimeJourney) {
          const buildTimeTZ = await getBuildTimeWithModelId(data.modelId);
          if (buildTimeTZ) {
            props.getDataJourneyFromPackage({
              lastBuildTimeJourney: buildTimeTZ,
            });
          }
        }
        break;

      default:
    }
  };

  const dataJourney = useMemo(() => {
    let result = props.dataSourceIdJourney;

    if (Array.isArray(result)) {
      // trường hợp dataSource là 1 array (journey performance + target segment) thì cần truyền dataSourceInfo qua package để xử lý
      // do tại CDP không có đủ thông tin để chọn dataSourceId truyền vào package
      result = props.dataSourceInfo;
    }
    return result;
  }, [props.dataSourceIdJourney, props.dataSourceInfo]);

  const handleSetViewType = type => {
    props.updateIsInitDone(false);
    setTimeout(() => {
      setViewType(type);
      setViewTypeLocalStorage(keyViewType, type);
    }, 200);
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const mapParamsDeleteFn = useCallback(
    (oldData, newData) => ({
      data: {
        totalSelected: oldData.isSelectedAll
          ? oldData.totalRecord
          : newData.accepted.length,
        objectIds: newData.accepted.map(each => each.story_id),
        isCheckUsedStatus: 0,
        filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
      },
    }),
    [],
  );

  // const initDateRangeData = useMemo(() => dateRange, [isInitDone]);

  // const breadcrums = useMemo(() => getBreadcrums(channelActive), [
  //   channelActive.label,
  // ]);
  // console.log(props);

  // console.log('render Campaigns', channelId, moduleConfigColumn);
  const renderListingTable = () => (
    <>
      <NavBarRightContent
        right="14px"
        className="p-right-4"
        style={{ width: props.rightTabWidth || 'auto' }}
      >
        <CalendarSelection
          initData={dateRange}
          callback={callback}
          isLoading={isLoadingSelectionTime}
          // maxWidth={channelActive.value === 2 && '135px'}
          hiddenLabel={props.isShowOverviewTab}
          maxTotalWidth={props.rightTabWidth ? props.rightTabWidth - 16 : 0}
        />
      </NavBarRightContent>
      <StyleWrapper>
        {/* <CustomHeader breadcrums={breadcrums} /> */}
        <LayoutContent
          padding="0"
          margin="0"
          style={layoutStyle}
          zIndex="1"
          height={isShow ? `calc(100vh - 155px - 50px)` : 'calc(100vh - 155px)'}
        >
          <LayoutContentLoading isLoading={!isInitDone}>
            <PerformanceChartCampaign
              defaultMetric={defaultMetrics}
              dateRange={dateRange.value}
              showChart={showChart}
              listSelection={main.groupAttributes.metricsPerformanceChart}
              stylesChart={{ width: '100%', height: 200 }}
              ServiceFn={JourneyServices.campaigns.chart.getListV2_1}
              // objectId={channelId}
              // style={{ marginTop: `${showChart ? '0.5rem' : '0'}` }}
              callback={callback}
              rules={filter.rules}
              storyId={null} // Tương ứng story id
              storyType={channelId === 0 ? null : channelId} // Tương ứng story type
              objectType={
                channelActive.value === 0 ? 'ALL_CAMPAIGN' : 'CAMPAIGNS'
              }
            />

            <TableRelative>
              <TableWrapper>
                <TableContainer
                  global={{ activeTab: props.activeTab }}
                  columnActive={column.columnObj}
                  table={table}
                  isLoading={main.isLoading}
                  moduleConfig={moduleConfig}
                  selectedIds={table.selectedIds}
                  selectedRows={table.selectedRows}
                  isSelectedAll={table.isSelectedAll}
                  isSelectedAllPage={table.isSelectedAllPage}
                  columns={tableColumns}
                  data={data}
                  callback={callback}
                  noDataText="No data"
                  resizeColName="campaign_name"
                  ComponentControlTable={ControlTable}
                  menuCode={MODULE_CONFIG.menuCode}
                  widthFirstColumns={198} // 150 + 48
                  initialWidthColumns={478} // 150 + 280 + 48
                  // widthFirstColumns={124}
                  // initialWidthColumns={width > 1600 ? 291 : 218}
                  // initialWidthColumns={274}
                  isShowFooter
                  mapDataFooter={props.mapDataFooter}
                >
                  <>
                    <Filters
                      use="list"
                      rules={filter.rules}
                      moduleConfig={moduleConfig}
                      filterActive={filter.config.filterObj}
                      filterCustom={filter.config.library.filterCustom}
                      libraryFilters={filter.config.library.filters}
                      groups={main.groupAttributes.groupsFilter}
                      isFilter={filter.config.design.isFilter}
                      isLoading={filter.config.isLoading}
                      // AddComponent={AddComponent}
                    />
                    <WrapActionTable
                      show={!filter.config.design.isFilter}
                      className="p-x-4"
                    >
                      <div className="actionTable__inner">
                        <Search
                          moduleConfig={moduleConfig}
                          config={configFilter}
                          suggestionType="suggestionMultilang"
                          moduleLabel={MAP_TITLE.titlStories}
                          isAddFilter
                          isGoTo={false}
                        />
                        <ModifyColumn
                          sort={table.sort}
                          moduleConfig={moduleConfigColumn}
                          columnActive={column.columnObj}
                          columnCustom={column.library.columnCustom}
                          libraryColumns={column.library.columns}
                          defaults={moduleConfigColumn.columnsDefault}
                          // defaults={props.main.groupAttributes.requires}
                          defaultSortColumns={
                            moduleConfigColumn.defaultSortColumns
                          }
                          columns={column.columnObj.columns.columnsAlias}
                          groups={main.groupAttributes.groups}
                          isLoading={column.isLoading}
                          isLoadingInfoProperties={
                            // Using for loading modify column info-properties
                            main.isLoadingModifyColumn
                          }
                        />
                        <DrawerInsightExplore
                          use="all"
                          token={getToken()}
                          userId={getCurrentAccessUserId()}
                          ownerId={getCurrentOwnerId()}
                          styledWrapper={{
                            marginTop: '2px',
                            marginLeft: '8px',
                          }}
                          domain={getDomainInsight()}
                          portalId={getPortalId()}
                          listTemplateRes={props.listTemplateJourneyInsightRes}
                          callback={callbackGetListTemplateJourneyInsight}
                          itemTypeId={-1009}
                          dataSourceId={dataJourney}
                          lastBuildTime={buildTimeDrawer(
                            props.lastBuildTimeJourney,
                          )}
                          templateCreate="Campaigns"
                          nameExplore="journey"
                          CustomButton={btnProps => (
                            <IconButton
                              {...btnProps}
                              iconName="explorer"
                              size="24px"
                              isVertical
                            >
                              {MAP_TITLE.actExplore.toUpperCase()}
                            </IconButton>
                          )}
                        />
                        <IconButton
                          iconName="file_download"
                          size="24px"
                          onClick={
                            table.paging.totalRecord === 0
                              ? () => {}
                              : toggleModalDownload
                          }
                          disabled={table.paging.totalRecord === 0}
                          isVertical
                        >
                          {MAP_TITLE.actExport.toUpperCase()}
                        </IconButton>
                        <IconButton
                          iconName="event"
                          size="24px"
                          onClick={() => handleSetViewType('calendar')}
                          isVertical
                          // disabled={false}
                        >
                          {MAP_TITLE.titlCalendar.toUpperCase()}
                        </IconButton>
                      </div>
                      <Divider className="m-x-1" />
                      <IconButton
                        iconName={showChart ? 'arrow-up' : 'arrow-down'}
                        size="24px"
                        onClick={() => setShowChart(prev => !prev)}
                        // disabled
                      />
                    </WrapActionTable>
                  </>
                </TableContainer>
              </TableWrapper>
            </TableRelative>
          </LayoutContentLoading>
        </LayoutContent>
        {isOpenModalDelete && (
          <ModalDeleteMulti
            activeRows={table.selectedRows}
            isSelectedAll={table.isSelectedAll}
            totalRecord={table.paging.totalRecord}
            rules={filter.rules}
            label={getTranslateMessage(
              TRANSLATE_KEY._BOX_TITL_DELETE_STORY,
              'Delete stories',
            )}
            placeHolderName=""
            moduleName={MAP_TITLE.titlStories}
            ObjectServicesFn={JourneyServices.data.deleteWithCondition}
            criteriaFn={each =>
              each.original_status === MAP_STORY_STATUS.DESIGNED
            }
            isOpenModal={isOpenModalDelete}
            setOpenModal={setIsOpenModalDelete}
            fetchData={props.fetchData}
            mapParamsFn={mapParamsDeleteFn}
          />
        )}
        <ModalExport
          isOpen={isOpenModalDownload}
          toggle={toggleModalDownload}
          paging={table.paging}
          sort={table.sort}
          filters={filter}
          sortDefault="utime"
          itemTypeId={MODULE_CONFIG.objectId}
          itemTypeName="campaigns"
          perf_columns={{ column, main }}
          channelId={channelActive.value === 0 ? null : channelActive.value}
          durations={dateRange.value}
          // columns={column.columnObj.columns.columnsAlias}
          // properties={column.columnObj.columns.columnsAlias}
          customName={`data_all_campaigns_${channelActive.label.toLowerCase()}_${new Date().toLocaleString()}`}
          feService={JourneyServices.data.download}
          objectName="Campaign"
          object_type={channelActive.value === 0 ? 'ALL_CAMPAIGN' : 'CAMPAIGNS'}
          columns={{ column, main, type: 'campaign_id' }}
        />
      </StyleWrapper>
    </>
  );

  const renderListingCalendar = () => {
    const calendarConfig = {
      type: 1,
      name: 'MONTH',
      // currentDate = endDate - 1 month because startDate is prev month
      currentDate: moment(calendarView.dateRange.currentDate).format(
        '01/MM/YYYY',
      ),
    };
    return (
      <StyleWrapper>
        {/* <CustomHeader breadcrums={breadcrums} /> */}
        <Calendar
          isLoading={main.isLoading}
          callback={callback}
          data={mapDataCampaignToDataEventsCalendar(props.data)}
          calendarConfig={calendarConfig}
          filter={filter}
          moduleConfig={moduleConfig}
          main={main}
          AddComponent={() =>
            channelId !== 0 && (
              <AddComponent
                menuCode={MODULE_CONFIG.menuCode}
                callback={callback}
              />
            )
          }
        />
      </StyleWrapper>
    );
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/Campaigns/index.jsx">
      {isInitDone ? (
        viewType === 'table' ? (
          renderListingTable()
        ) : viewType === 'calendar' ? (
          renderListingCalendar()
        ) : null
      ) : (
        <div style={{ position: 'relative', height: 'calc(100vh - 120px)' }}>
          <Loading isLoading={!isInitDone} />
        </div>
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListStoryCampaignDomainMain(),
  table: makeSelectListStoryCampaignTable(),
  filter: makeSelectListStoryCampaignFilter(),
  column: makeSelectListStoryCampaignColumn(),
  data: makeSelectListStoryCampaignDomainMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  mapDataFooter: makeSelectListStoryMapDataFooter(),
  dateRange: makeSelectListCampaignDateRange(),
  defaultMetrics: makeSelectListCampaignDefaultMetrics(),
  activeRow: makeSelectActiveRow(),
  channelActive: makeSelectJourneyChannelActive(),
  activeTab: makeSelectStoryDetailDomainActiveTab(),
  calendarView: makeSelectListCampaignCalendarView(),
  listTemplateJourneyInsightRes: makeSelectListTemplateJourneyInsightRes(),
  dataSourceIdJourney: makeSelectDataSourceIdJourney(),
  dataSourceInfo: makeSelectDataSourceInfo(),
  lastBuildTimeJourney: makeSelectLastBuildTimeJourney(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeCalendarDateRange: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CALENDAR_DATE_RANGE`, params),
      );
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeDefaultMetrics: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    onSaveDefaultMetrics: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    resetIsLoadingModify: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    },
    updateIsInitDone: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_INIT_DONE`, params));
    },
    getListTemplateJourneyInsightRes: params => {
      dispatch(
        getListTemplateJourneyInsightRes(MODULE_CONFIG_MAIN.key, params),
      );
    },
    getDataJourneyFromPackage: params => {
      dispatch(getDataJourneyFromPackage(MODULE_CONFIG_MAIN.key, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(Campaign);

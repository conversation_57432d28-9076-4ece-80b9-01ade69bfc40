/* eslint-disable indent */
/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useRef } from 'react';
import { connect } from 'react-redux';
import { useParams, withRouter } from 'react-router-dom';
// import { Prompt } from 'react-router';

import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import styled from 'styled-components';
import queryString from 'query-string';

import ErrorBoundary from 'components/common/ErrorBoundary';

import reducer from './reducer';
import saga from './saga';
// import { makeSelectListSegmentDomainMain } from './selectors';
import { init, updateValue, update, reset } from '../../../../../redux/actions';
import Content from './Content';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';

import {
  makeSelectConfigureCreateWorkflow,
  makeSelectMainCreateWorkflow,
} from './selectors';
import ModalConfirmExit from '../../../../../containers/modals/ModalConfirmExit';
import './style.scss';
import { getStoryId, renderContentPopupConfirm, getBreadcrums } from './utils';
import { makeSelectJourneyChannelActive } from '../selectors';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  makeUrlPermisison,
  MENU_CODE,
  validateAction,
} from '../../../../../utils/web/permission';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../utils/web/cookie';
import APP from '../../../../../appConfig';
import { updateUrl } from '../../../../../utils/common';
import { STORY_SETUP_ACTION } from './utils.story.rules';
import ModalConfirmActive from './_UI/modals/ModalConfirmActive';
import ModalConfirmSaveChanged from './_UI/modals/ModalConfirmSaveChanged';
import { makeSelectDashboard } from '../../../selector';
import { useDeepCompareEffect } from '../../../../../hooks';

const WrapperModalExit = styled.div`
  .modal {
    top: 30% !important;
  }
`;

export function CreateStory(props) {
  const {
    moduleConfig,
    design,
    main,
    isViewMode,
    isDisplayDiagramBlastCampaign = false,
    isShowDrawer = false,
  } = props;
  useInjectReducer({
    key: moduleConfig.key,
    reducer: reducer(moduleConfig),
  });
  useInjectSaga({
    key: moduleConfig.key,
    saga,
    args: { moduleConfig },
  });
  const params = useParams();

  const activeId = props.activeId || params.activeId;

  // const hasCreateRole = false;
  const hasEditRole = validateAction(
    MENU_CODE.JOURNEY,
    APP_ACTION.UPDATE,
    props.activeRow ? props.activeRow.c_user_id : getCurrentAccessUserId(),
  );
  const cacheCopyId = useRef(null);

  const { copyId } = queryString.parse(window.location.search);

  useDeepCompareEffect(() => {
    let itemTypeId = 0;

    if (
      moduleConfig.key === 'mark-hub-story-detail-create' &&
      design !== 'preview'
    ) {
      document.body.classList.add('journey-editing');
    }

    if (design === 'create') {
      cacheCopyId.current = copyId;

      const splitUrl = window.location.pathname.split('/');
      itemTypeId = splitUrl[splitUrl.length - 1];
      props.init({
        itemTypeId,
        param: '',
        design,
        channelActive: props.channelActive,
        copyId,
        isShowDrawer,
      });
    } else if (design === 'update' || design === 'preview') {
      const { activeRow } = props;
      // const splitUrl = window.location.href.split('?design=');
      const queryDesign = queryString.parse(window.location.search).design;
      let paramDesignUrl = 'preview';
      if (queryDesign) {
        paramDesignUrl = queryDesign;
      }
      // update không có design create
      if (paramDesignUrl === 'create' || props.versionId) {
        paramDesignUrl = 'update';
      }

      if (!hasEditRole) {
        paramDesignUrl = 'preview';

        const newUrl = `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
          activeRow.channel_id
        }/list?ui=detail-drawer&journeyId=${activeId}&channelId=${
          activeRow.channel_id
        }&tab=settings&design=preview`;

        updateUrl(makeUrlPermisison(newUrl));
      }

      props.init({
        itemTypeId: activeRow.itemTypeId,
        activeRow,
        design,
        paramDesignUrl,
        channelActive: props.channelActive,
        rootJourneyDetail: props.rootJourneyDetail,
        isDisplayDiagramBlastCampaign,
      });
    }

    return () => {
      //   document.body.classList.remove('xlab-desgin-full-layout');
      //   document.body.classList.remove('journey-editing');
      props.reset();
    };
  }, []);

  const callback = (type, data) => {
    // console.log('callback', type, data);
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'SAVE_SEGMENT': {
        // console.log('data ====>', data);
        props.onSave(data);
        break;
      }
      case 'CHANGE_SEGMENT_NAME': {
        props.onChangeName(data);
        props.onValidate();
        break;
      }
      case 'UPDATE_CONDITIONS': {
        props.onValidate();
        break;
      }
      case 'ON_CANCEL': {
        if (props.main.disabled === false) {
          props.toggleModal(data);
        } else {
          props.goToList();
        }
        break;
      }
      default:
        break;
    }
  };

  const roleActions = new Set([STORY_SETUP_ACTION.EDIT_STORY_NAME]);

  const handleChangeName = value => {
    if (props.design === 'create') {
      props.onChangeStoryName(value);
    } else {
      props.onChangeNameDesignUpdate(value);
      props.onChangeStoryName(value);
    }
  };

  const onConfirm = () => {
    props.toggleModal(false);
    props.goToList();
  };

  const onToggleModal = () => {
    props.toggleModal(false);
  };

  // const data = tableDataMultiColumns;
  // console.log('configure ===>', configure);
  const isLoading = main.isLoading;
  // const isLoading = true;
  // const shouldBlockNavigation = true;

  const breadcrums = useMemo(() => {
    if (design !== 'create') return [];

    let listBreadcrumb = getBreadcrums(props.channelActive);

    const { ownerId, accounts, menuCodeActive } = props.dashboard;
    const isHasEveythingJourney = checkingRoleScope(
      menuCodeActive,
      APP_ACTION.CREATE,
      APP_ROLE_SCOPE.EVERYTHING,
    );

    const accountOwner = accounts.map[ownerId.value];
    const ownerName = accountOwner && accountOwner.full_name;

    if (isHasEveythingJourney) {
      listBreadcrumb[0].display = ownerName;
      listBreadcrumb[0].urlPath = {
        first: '',
        last: '',
      };
    } else {
      listBreadcrumb.splice(0, 1);
    }

    if (props.isJourneyV2 || props.isShowDrawer) {
      // chỉ giữ lại journey name
      listBreadcrumb = [...listBreadcrumb].filter(bread => !bread.urlPath);
    }

    return listBreadcrumb;
  }, [props.channelActive.label, props.dashboard.ownerId, design]);

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Desgin.jsx">
      <Content
        use={props.use}
        hasEditRole={hasEditRole}
        isLoading={isLoading}
        callback={callback}
        moduleConfig={moduleConfig}
        versionId={props.versionId}
        page={props.page}
        typeResume={props.typeResume}
        isViewMode={isViewMode}
        cacheCopyId={cacheCopyId}
        isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
        isJourneyV2={props.isJourneyV2}
        activeId={activeId}
        isShowDrawer={isShowDrawer}
        keyResume={props.keyResume}
        // design={design}
        // onChangeDesign={props.onChangeDesign}
      />

      <WrapperModalExit className="wrapper-modal-exit">
        <ModalConfirmExit
          isOpen={props.configure.main.isOpenModal}
          toggle={onToggleModal}
          design={props.design}
          showDiscard={getStoryId(props.main, props.design) > 0}
          title={renderContentPopupConfirm(props.main, props.design).title}
          onConfirm={onConfirm}
        >
          {renderContentPopupConfirm(props.main, props.design).content}
        </ModalConfirmExit>
      </WrapperModalExit>

      <ModalConfirmActive moduleConfig={props.moduleConfig} />

      <ModalConfirmSaveChanged moduleConfig={props.moduleConfig} />
    </ErrorBoundary>
  );
}

// CreateStory.propTypes = {
//   dispatch: PropTypes.func.isRequired,
// };

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigureCreateWorkflow(),
  main: makeSelectMainCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
  dashboard: makeSelectDashboard(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    init: params => {
      dispatch(init(prefix, params));
    },
    reset: params => {
      dispatch(reset(prefix, params));
    },
    onSave: data => dispatch(update(prefix, data)),
    onChangeName: value =>
      dispatch(updateValue(`${prefix}@@SEGMENT_NAME@@`, value)),
    onValidate: data => dispatch(updateValue(`${prefix}@@VALIDATE`, data)),
    toggleModal: data => dispatch(updateValue(`${prefix}@@TOGGLE_MODAL`, data)),
    goToList: () => dispatch(updateValue(`${prefix}@@GO_TO_LIST`)),
    onChangeStoryName: value =>
      dispatch(updateValue(`${prefix}@@STORY_NAME@@`, value)),
    onChangeNameDesignUpdate: value =>
      dispatch(updateValue(`${prefix}@@STORY_NAME_DESIGN_UPDATE@@`, value)),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  // withReducer,
  withRouter,
  withConnect,
  // withSaga,
)(CreateStory);

/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/prop-types */
import React, { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import { UILoading as Loading, UITippy } from '@xlab-team/ui-components';
import TextField from '@material-ui/core/TextField';

import { getInnerTextWidth, validateName } from './utils';
import { WrapperInput, WrapperLoading, IconEdit, useStyles } from './styled';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { safeParse } from '../../../../../../../utils/common';
import { MAP_VALIDATE } from '../../../../../../../utils/web/validateForm';
import useDebounce from '../../../../../../../hooks/useDebounce';

const KEY_ENTER = 13;
const DEFAULT_ERROR_MESSAGE = getTranslateMessage(
  TRANSLATE_KEY._INVALID_NAME_SYSTEM_ERROR,
  'Error happened, please try again!',
);

const InputAutoResize = props => {
  const classes = useStyles();
  const { isHidden = false } = props;
  const [state, setState] = useImmer({
    isLoading: false,
    textMessage: 'edit',
    name: props.value,
    errors: {},
    originName: props.value,
  });

  const titleInput = useRef(null);
  // const [inputControlTitleWidth, setWidthInputControlTitle] = useState({
  //   width: `${Math.floor(getInnerTextWidth(props.value)) + 13}px`,
  // });
  const [inputControlTitleWidth, setWidthInputControlTitle] = useState({
    width: '',
  });

  const preName = useRef(null);

  const setStateCommon = object => {
    setState(draft => {
      Object.keys(object).forEach(key => {
        draft[key] = object[key];
      });
    });
  };

  // useEffect(() => {
  //   setWidthInputControlTitle({
  //     width: `${Math.floor(getInnerTextWidth(props.value)) + 13}px`,
  //   });
  // }, []);

  useEffect(() => {
    setStateCommon({ errors: props.errors });
  }, [props.errors]);

  useEffect(() => {
    setStateCommon({ name: props.value });
    setWidthInputControlTitle({
      width: `${Math.floor(getInnerTextWidth(props.value)) + 13}px`,
    });
  }, [props.value]);

  useEffect(() => {
    preName.current = state.name;
  }, [state.name]);

  const handleChangeName = e => {
    const { value } = e.target;

    const slicedValue = value.slice(0, 255);
    setStateCommon({ name: slicedValue });

    setWidthInputControlTitle({
      width: `${Math.floor(getInnerTextWidth(slicedValue)) + 13}px`,
    });

    // const dataError = validateName(value);
    const { errors, isValidate } = MAP_VALIDATE.textNewRule({
      slicedValue,
    });
    setStateCommon({ errors: { story_name: errors } });
    if (isHidden) {
      props.onChange(slicedValue);
    }
  };

  const updateName = value => {
    if (props.design !== 'create') {
      if (
        preName.current !== props.value &&
        state.errors[props.nameKey].length === 0
      ) {
        setStateCommon({ isLoading: true, textMessage: 'done' });

        const params = {
          ...props.paramsUpdate,
          data: {
            [props.nameKey]: value,
          },
        };
        props.serviceUpdateName(params).then(res => {
          if (res.code === 200) {
            setTimeout(() => {
              setStateCommon({ isLoading: false });
              props.onChange(value);
            }, 2000);

            setTimeout(() => {
              setStateCommon({ textMessage: 'edit' });
            }, 5000);
          } else {
            let messageError = getTranslateMessage(
              TRANSLATE_KEY._INVALID_NAME_SYSTEM_ERROR,
              'Error happened, please try again!',
            );

            if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
              messageError = getTranslateMessage(
                TRANSLATE_KEY._NOTIFICATION_NAMESAKE,
                'This name already existed',
              );
            }
            setStateCommon({
              isLoading: false,
              textMessage: 'edit',
              errors: { [props.nameKey]: [messageError] },
            });
            props.onChange(value);
          }
        });
      }
    } else {
      // case design === create
      props.onChange(value);
    }
  };

  const onClick = () => {
    // titleInput.current.selectionStart = titleInput.current.value.length;
    // titleInput.current.selectionEnd = titleInput.current.value.length;
    titleInput.current.setSelectionRange(
      titleInput.current.value.length,
      titleInput.current.value.length,
    );
    titleInput.current.focus();
  };

  const onBlur = e => {
    const { value } = e.target;
    if (value && value.trim()) {
      updateName(value);
      setStateCommon({ originName: value });
    } else {
      handleChangeName({
        target: {
          value: state.originName,
        },
      });
    }
  };

  const onKeyPress = e => {
    const { value } = e.target;

    if (e.charCode === KEY_ENTER && value && value.trim()) {
      updateName(value);
    }
  };

  let errorMessage = DEFAULT_ERROR_MESSAGE;
  let isError = false;

  if (
    typeof state.errors === 'object' &&
    Object.keys(safeParse(state.errors, {})).length > 0
  ) {
    // if (state.errors[props.nameKey] && isArray(state.errors[props.nameKey])) {
    //   errorMessage = state.errors[props.nameKey][0] || DEFAULT_ERROR_MESSAGE;
    //   isError = state.errors[props.nameKey].length > 0;
    // }
    const { errors, isValidate } = MAP_VALIDATE.textNewRule({
      value: state.name,
    });
    console.log('errors, isValidate', errors, isValidate);
    isError = !isValidate;
    errorMessage = errors[0] || DEFAULT_ERROR_MESSAGE;
  }

  return (
    <WrapperInput isError={isError} isShowDrawer={props.isShowDrawer}>
      <UITippy content={state.name} arrow distance={10}>
        <TextField
          type="text"
          inputProps={props.inputProps}
          // autoFocus

          inputRef={titleInput}
          value={state.name}
          style={inputControlTitleWidth}
          onChange={handleChangeName}
          // className={`${props.className} ${classes.fontInput} input-auto-resize`}
          className={classes.fontInput}
          onBlur={onBlur}
          onKeyPress={onKeyPress}
          disabled={state.isLoading}
          // label={
          //   isError ? (
          //     <div className="" style={{ fontSize: '0.9rem' }}>
          //       {errorMessage}
          //     </div>
          //   ) : (
          //     ''
          //   )
          // }
          error={isError}
          fullWidth
        />
      </UITippy>
      <WrapperLoading>
        <Loading isLoading={state.isLoading} size={20} />
        {!state.isLoading && !isHidden && (
          <IconEdit type="icon-ants-pencil" onClick={onClick} />
          // <Icon onClick={onClick}>{state.textMessage}</Icon>
        )}
      </WrapperLoading>
    </WrapperInput>
  );
};

InputAutoResize.defaultProps = {
  serviceUpdateName: () => {},
  design: 'create',
  paramsUpdate: {},
};

export default InputAutoResize;

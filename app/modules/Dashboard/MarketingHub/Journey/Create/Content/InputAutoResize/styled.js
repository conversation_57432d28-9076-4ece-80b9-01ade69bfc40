import styled from 'styled-components';
import { makeStyles } from '@material-ui/core/styles';
import { Icon } from '@antscorp/antsomi-ui';

export const useStyles = makeStyles(() => ({
  fontInput: {
    fontSize: 16,
    color: 'rgb(0, 94, 184)',
    fontWeight: 'bold',
  },
}));

export const WrapperInput = styled.div`
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 32px;
  max-width: ${props => (props.isShowDrawer ? `100%` : '500px')};
  .MuiFormControl-root {
    ${({ isError }) => (isError ? `min-width: 340px;` : `min-width: 20px;`)}

  }

  label + .MuiInput-formControl {
    margin-top: 12px;
    /* ${({ isError }) => !isError && `margin-top: 12px;`} */
  }

  .MuiInputBase-input {
    padding-bottom: 2px;
    /* min-width: 100px; */
  }

  .MuiInput-underline:before {
    border-bottom: none;
  }

  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: none;
  }

  .MuiInput-underline:after {
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    position: absolute;
    transform: scaleX(0);
    transition: transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
    border-bottom: 2px solid #000;
    pointer-events: none;
  }

  .MuiInput-underline:focus-within:not(.Mui-disabled):before {
    border-bottom: 2px solid rgba(0, 0, 0, 0.87);
  }

  .MuiFormControl-root {
    min-width: unset;
  }

  span {
    font-size: 16px;
    color: #005eb8;
    cursor: pointer;
  }

  .MuiInputBase-input {
    font-size: 1rem;
    color: #005eb8;
    font-weight: bold;
    padding: 2px 0;
  }

  .MuiInputBase-root {
    &.MuiInput-underline {
      padding: 0;
      &:not(.Mui-disabled):before {
        border-bottom: none;
      }
    }
  }
`;

export const Input = styled.input`
  background-color: transparent;
  box-sizing: content-box;
  height: 32px;

  color: inherit;
  cursor: text;

  border-width: 0px;
  border-style: initial;
  border-color: initial;
  border-image: initial;

  outline: 0px;

  max-width: 100%;

  &:focus {
    border-bottom: 1px solid #005eb8;
  }
`;

export const WrapperLoading = styled.div`
  right: 0px;
  position: absolute;
  min-width: 30px;
  max-width: 35px;
  height: 30px;
  /* position: relative; */
  display: flex;
  align-items: center;
  margin-left: 5px;
  visibility: hidden;
`;

export const IconEdit = styled(Icon)`
  color: #005fb8;
  font-size: 24px;
`;

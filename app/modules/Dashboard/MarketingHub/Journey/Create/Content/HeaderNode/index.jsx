/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect, useCallback, useState, useMemo } from 'react';
import UIStatusTag from 'components/cdp/UIStatusTag';
import { connect } from 'react-redux';
import { get, isEmpty } from 'lodash';
import { createStructuredSelector } from 'reselect';
import {
  UIButton,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import UIIconXlab from 'components/common/UIIconXlab';
import UIModalCommon from 'components/common/UIModalCommon';
import { useHistory, useParams } from 'react-router-dom';
import { WrapperHeaderNode } from '../styles';
import {
  makeSelectMainCreateWorkflow,
  makeSelectConfigureCreateWorkflow,
} from '../../selectors';
import {
  makeSelectIsOpenModalConfirmCreateCopyJourney,
  makeSelectJourneyChannelActive,
} from '../../../selectors';
import {
  addNotification,
  init,
  update,
  updateValue,
} from '../../../../../../../redux/actions';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { getLabelStoryStatus } from '../../../../../../../utils/web/processStatus';
import ControlAction from './ControlAction';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getPortalId,
  setCurrentCreateOwnerId,
} from '../../../../../../../utils/web/cookie';
import APP from '../../../../../../../appConfig';
import useToggle from '../../../../../../../hooks/useToggle';
import UILinearProgress from '../../../../../../../components/common/UILinearProgress';
import { makeUrlPermisison } from '../../../../../../../utils/web/permission';
import MoreInfo from '../../../../../../../components/common/MoreInfo';
import { VersionCard } from '../../../Detail/VersionHistory2/Detail/VersionList/VersionCard';
import { getPrefixDetail } from '../../../Detail/VersionHistory2/Detail/utils';
import { makeSelectActiveRow } from '../../../Detail/selectors';
import { JOURNEY_STATUS } from '../../../../../../../services/Abstract.data';
import ModalConfirmExit from '../../../../../../../containers/modals/ModalConfirmExit';
import ButtonSaveAs from './ButtonSaveAs';
import { isShowSaveAs } from './utils';
import classnames from 'classnames';
import { createPortal } from 'react-dom';
import { MODULE_CONFIG } from '../../../config';
import ModalConfirmCreateCopy from '../../../../../../../containers/modals/ModalConfirmCreateCopy';
import { KEY_PARAMS, TAB_DRAWER_JOURNEY } from '../../../Main/constants';
import { Button, Icon } from '@antscorp/antsomi-ui';
import { NOTI } from '../../../../../../../components/common/DrawerArchive/RelatedObject/utils';
import { safeParse } from '../../../../../../../utils/common';
import { isDisableActive } from '../../BlastCampaign/utils';

const MAP_TITLE = {
  warning: getTranslateMessage(TRANSLATE_KEY._TITL_WARNING, 'Warning'),
  titleModal: getTranslateMessage(
    TRANSLATE_KEY._WARN_START_DATE_IN_PAST,
    'The start date of journey is in the past. Please confirm if you still want to keep this setting.',
  ),
  confirm: getTranslateMessage(TRANSLATE_KEY._ACT_CONFIRM, 'Confirm'),
  back: getTranslateMessage(TRANSLATE_KEY._ACT_BACK, 'Back'),
  cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  close: getTranslateMessage(TRANSLATE_KEY._ACT_CLOSE, 'Close'),
  versionHistory: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Version History'),
  cancelDescription: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    "Change you created won't be saved if you leave this site",
  ),
  saveChange: getTranslateMessage(
    TRANSLATE_KEY._ACT_SAVE_CHANGE,
    'Save change',
  ),
  activate: getTranslateMessage(TRANSLATE_KEY._ACT_STORY_ACTIVATE, 'Activate'),
  createCopy: getTranslateMessage(
    TRANSLATE_KEY._ACTION_CREATE_COPY,
    'Create copy',
  ),
};

const stickyStyle = {
  position: 'sticky',
  top: '57px',
  zIndex: 998,
  backgroundColor: '#ffffff',
  marginTop: '1px',
};
const notification = {
  message:
    'Some errors were found. Please check the highlighted fields and try again.',
  translateCode: TRANSLATE_KEY._NOTI_UNSUCCESS_UPDATE_SUBTITLE,
  timeout: 10000,
  type: 'danger',
  title: getTranslateMessage(
    TRANSLATE_KEY._NOTI_UNSUCCESS_UPDATE,
    'Update unsuccessfully',
  ),
};
const HeaderNode = props => {
  const {
    activeRow,
    isViewMode,
    activeRowDetail,
    isShowDrawer,
    configure,
  } = props;
  const { story_version = {} } = activeRowDetail;
  const history = useHistory();
  const searchParams = new URLSearchParams(window.location.search);

  const params = useParams();

  const channelId = activeRow.channel_id || params.channelId || props.channelId;
  const oid = searchParams.get('oid');

  const [isOpenModalCancel, toggleModalCancel] = useToggle(false);
  const [isOpenModalConfirmExit, setIsOpenModalConfirmExit] = useState(false);
  const isShowButtonSaveAs = isShowSaveAs(props.isBlastCampaign, channelId);

  const setting3rdParty = useMemo(() => {
    const is3rdParty = get(
      activeRow,
      'workflow_setting.metadata.isThirdParty',
      false,
    );
    const scheduledThirdParty = get(
      activeRow,
      'workflow_setting.metadata.scheduledThirdParty',
      {},
    );

    return {
      is3rdParty,
      scheduledThirdParty,
    };
  }, [activeRow]);

  useEffect(() => {
    if (props.versionId) {
      props.onDisabledSaveChange(false);
    }
  }, [props.versionId]);

  const onCreateCopy = useCallback(() => {
    setCurrentCreateOwnerId(getCurrentOwnerId());

    if (isShowDrawer) {
      searchParams.delete(KEY_PARAMS.UI);
      searchParams.delete(KEY_PARAMS.TAB);
      searchParams.delete('design');
      searchParams.delete(KEY_PARAMS.JOURNEY_ID);

      if (props.isBlastCampaign) {
        searchParams.set(KEY_PARAMS.IS_BLAST_CAMPAIGN, 'true');
      }

      searchParams.set(KEY_PARAMS.COPY_ID, activeRowDetail.activeId);
      searchParams.set(KEY_PARAMS.CHANNEL_ID, activeRowDetail.channel_id);

      let url = `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
        params.channelId
      }/list?${searchParams.toString()}`;

      if (oid) {
        url += `&oid=${oid}`;
      }
      history.push(url);

      props.toggleLayoutJourney(true);
    } else if (props.isBlastCampaign) {
      history.push(
        `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
          activeRow.channel_id
        }/blast-campaign/create?copyId=${activeRow.activeId}`,
      );
    } else {
      history.push(
        `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
          activeRow.channel_id
        }/create?copyId=${activeRow.activeId}`,
      );
    }
  }, []);

  useEffect(() => {
    if (props.isBlastCampaign) {
      props.callback('UPDATE_DESIGN_BLAST', props.design);
    }
  }, [props.design]);

  const onConfirmCreateCopy = useCallback(() => {
    if (setting3rdParty?.is3rdParty) {
      props.onUpdateCache3rdPartyCampaign(setting3rdParty?.scheduledThirdParty);
    }

    props.toggleModalConfirmCreateCopyJourney(true);
  }, [setting3rdParty]);

  const callback = ({ type }) => {
    switch (type) {
      case 'CONFIRM_CREATE_COPY': {
        onCreateCopy();
        break;
      }
      default: {
        break;
      }
    }
  };

  const onActiveStory = () => {
    props.onUpdateStatus({ value: 1, nextStatus: JOURNEY_STATUS.ACTIVE });
  };
  const onChangeStoryStatus = data => {
    if (
      ((props.isBlastCampaign &&
        isDisableActive(configure.main?.mainNodesBlast)) ||
        Object.keys(configure.main.errors).length > 0) &&
      (data.type === 'ACTION_TABLE_CHANGE_STATUS' && data.use === 'active')
    ) {
      props.addNotification(notification);
      return;
    }
    props.onUpdateStatus(data);
  };

  const isRestore = searchParams.has(KEY_PARAMS.RESTORE);
  const isCreateCopy = searchParams.has(KEY_PARAMS.COPY_ID);
  const onClickVersionHistory = () => {
    if (props.isShowDrawer) {
      searchParams.set(KEY_PARAMS.TAB, TAB_DRAWER_JOURNEY.VERSION_HISTORY.key);
      searchParams.set(KEY_PARAMS.VERSION_ID, story_version.version);
      searchParams.delete('design');
      searchParams.delete(KEY_PARAMS.RESTORE);

      history.push({ search: searchParams.toString() });
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            activeRow.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            activeRow.channel_id
          }/detail/${activeRow.story_id}/version-history/${
            story_version.version
          }`,
        ),
      );
    }
  };

  const onCancel = () => {
    if (props.versionId) {
      toggleModalCancel();
    } else {
      onConfirmCancel();
    }
  };

  const onBack = () => {
    if (props.isHasChange && props.design === 'create') {
      setIsOpenModalConfirmExit(true);
    } else {
      handleGoToCreateJourney();
    }
  };

  const onBackV2 = () => {
    const isCompare = searchParams.has('compare');
    searchParams.delete('restore');

    if (isCompare) {
      searchParams.delete('versionId');
    }

    history.push({
      search: searchParams.toString(),
    });
  };

  const handleGoToCreateJourney = () => {
    history.push(
      makeUrlPermisison(
        `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}/create/template`,
      ),
    );
  };

  const onConfirmExit = () => {
    setIsOpenModalConfirmExit(false);
    handleGoToCreateJourney();
  };
  const rightEle = document.getElementById('header-journey-right');
  const leftEle = document.getElementById('header-journey-left');
  const onConfirmCancel = () => {
    if (props.design === 'update') {
      props.init({
        itemTypeId: activeRowDetail.itemTypeId,
        activeRow: activeRowDetail,
        design: props.design,
        // paramDesignUrl,
        channelActive: props.channelActive,
        // rootJourneyDetail: props.rootJourneyDetail,
        ...(props.isBlastCampaign
          ? { blastCampaign: props.blastCampaign }
          : {}),
      });

      props.onChangeLoading(true);

      props.onChangeDesign('preview');

      if (props.isShowDrawer) {
        searchParams.delete('restore');
        searchParams.delete('versionId');

        history.push({ search: searchParams.toString() });
      } else {
        history.push(
          makeUrlPermisison(
            `${APP.PREFIX}/${
              activeRow.portal_id
            }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
              activeRow.channel_id
            }/detail/${activeRow.story_id}/settings?design=preview`,
          ),
        );
      }
    } else if (typeof props.onCancel === 'function') {
      props.onCancel(true);
    }
  };

  const renderVersionInfo = () => (
    <div className="d-flex align-items-center">
      <b className="m-right-5">Version {story_version.version}</b>
      <MoreInfo>
        <VersionCard
          versionId={story_version.version}
          userInfo={story_version.user_info}
          utime={story_version.utime}
          versionInfo={story_version.version_info}
          isActive={false}
          isCurrentVersion={get(story_version, 'version_info.current_version')}
          isMoreInfo
        />
      </MoreInfo>
    </div>
  );

  const objStatus = getLabelStoryStatus(activeRow.status);
  const { modalSaveAsTemplate = {} } = props.main;
  const { show = false } = modalSaveAsTemplate;
  return (
    <section
      style={{
        ...(props.isBlastCampaign ? stickyStyle : {}),
        ...(props.isJourneyV2 ? { top: 0 } : {}),
      }}
      className={classnames({
        'height-0 d-none': isShowDrawer,
      })}
    >
      <WrapperHeaderNode
        className={classnames('d-flex align-items-center', {
          'height-0': isShowDrawer,
        })}
        isJourneyV2={props.isJourneyV2}
      >
        {props.isViewMode && !isShowDrawer ? renderVersionInfo() : null}
        {((props.design === 'create' &&
          props.isBlastCampaign &&
          !props.isJourneyV2 &&
          !isCreateCopy) ||
          isRestore) && (
          <>
            {isShowDrawer && leftEle ? (
              createPortal(
                <Button onClick={onBackV2} style={{ textWrap: 'nowrap' }}>
                  <Icon
                    type="icon-ants-angle-left"
                    size={12}
                    style={{ marginRight: '5px', marginLeft: '5px' }}
                  />
                  {MAP_TITLE.back}
                </Button>,
                leftEle,
              )
            ) : (
              <UIButton
                variant="contained"
                theme="outline"
                style={{
                  width: 47,
                  height: 28,
                  padding: 0,
                  borderColor: '#B8CFE6',
                }}
                onClick={onBack}
                disabled={!props.isAbleToCheckChange}
              >
                {MAP_TITLE.back}
              </UIButton>
            )}
          </>
        )}
        {isShowDrawer ? (
          <>
            {rightEle &&
              createPortal(
                <div className="right-content" id="right-content">
                  <div className="story-ui-status">
                    <UIStatusTag
                      pulse={objStatus.pulse}
                      label={objStatus.description}
                      use={objStatus.use}
                      // translateCode={objStatus.label}
                    />
                  </div>

                  {props.design === 'preview' && props.use === 'setting' && (
                    <Button
                      onClick={() => props.handleChangeDesign()}
                      icon={<Icon type="icon-ants-pencil" />}
                      disabled={!props.hasEditRole}
                      // disabled={props.main.disabled || props.main.isDoing}
                    >
                      {getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit')}
                    </Button>
                  )}

                  {props.design === 'create' ||
                  +activeRow.status === JOURNEY_STATUS.IN_DESIGN ? (
                    // {activeRow.status == 9 ? (
                    <Button
                      onClick={onActiveStory}
                      disabled={
                        props.main.actionDisabled ||
                        props.main.isDoing ||
                        (props.isBlastCampaign &&
                          props.main.firstActionDisabled)
                      }
                    >
                      {MAP_TITLE.activate}
                    </Button>
                  ) : (
                    <ControlAction
                      activeRow={activeRow}
                      onChange={onChangeStoryStatus}
                    />
                  )}
                  {props.design === 'update' && !props.isShowDrawer && (
                    <UIIconXlab
                      name="line-vertical"
                      className="icon-line-vertical no-space"
                    />
                  )}

                  {/* chi show CreateCopy khi o tab setting + view mode */}
                  {isViewMode && props.use === 'setting' ? (
                    <Button
                      onClick={onConfirmCreateCopy}
                      disabled={props.main.isDoing}
                    >
                      {props.isBlastCampaign && (
                        <Icon type="icon-ants-clipboard" />
                      )}
                      {MAP_TITLE.createCopy}
                    </Button>
                  ) : null}

                  {!isViewMode ? (
                    <>
                      {!props.isJourneyV2 && !props.isShowDrawer && (
                        <UIIconXlab
                          name="line-vertical"
                          className="icon-line-vertical no-space"
                        />
                      )}
                      {isShowButtonSaveAs ? (
                        <ButtonSaveAs
                          onClick={props.onSave}
                          disabled={props.main.disabled || props.main.isDoing}
                          isLoading={props.main.isDoing}
                          moduleConfig={props.moduleConfig}
                          channelId={channelId}
                        />
                      ) : (
                        <Button
                          type="primary"
                          onClick={props.onSave}
                          disabled={props.main.disabled || props.main.isDoing}
                          isLoading={props.main.isDoing}
                        >
                          {getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save')}
                        </Button>
                      )}

                      {!props.isJourneyV2 && !props.isShowDrawer && (
                        <>
                          <UIIconXlab
                            name="line-vertical"
                            className="icon-line-vertical no-space"
                          />

                          <Button
                            onClick={onCancel}
                            style={{ padding: '0 10px 0 5px' }}
                            iconName="close"
                            reverse
                            iconSize="23"
                          >
                            {MAP_TITLE.close}
                          </Button>
                        </>
                      )}
                    </>
                  ) : null}
                </div>,
                rightEle,
              )}
          </>
        ) : (
          <div className="right-content" id="right-content">
            <div className="story-ui-status">
              <UIStatusTag
                pulse={objStatus.pulse}
                label={objStatus.description}
                use={objStatus.use}
                // translateCode={objStatus.label}
              />
            </div>

            {props.design === 'preview' && props.use === 'setting' && (
              <WrapperDisable noPermission={!props.hasEditRole}>
                <UIButton
                  variant="contained"
                  theme="outline"
                  onClick={() => props.handleChangeDesign()}
                  iconName="edit"
                  reverse
                  // disabled={props.main.disabled || props.main.isDoing}
                >
                  {getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit')}
                </UIButton>
              </WrapperDisable>
            )}

            {props.design === 'create' ||
            +activeRow.status === JOURNEY_STATUS.IN_DESIGN ? (
              // {activeRow.status == 9 ? (
              <Button
                onClick={onActiveStory}
                disabled={props.main.actionDisabled || props.main.isDoing}
                // disabled
              >
                {MAP_TITLE.activate}
              </Button>
            ) : (
              <ControlAction
                activeRow={activeRow}
                onChange={onChangeStoryStatus}
              />
            )}
            {props.design === 'update' && (
              <UIIconXlab
                name="line-vertical"
                className="icon-line-vertical no-space"
              />
            )}

            {/* chi show CreateCopy khi o tab setting + view mode */}
            {isViewMode && props.use === 'setting' ? (
              <UIButton
                variant="contained"
                theme="outline"
                onClick={onCreateCopy}
                disabled={props.main.isDoing}
              >
                {/*! props.main.disabled || */}
                {MAP_TITLE.createCopy}
              </UIButton>
            ) : null}

            {!isViewMode ? (
              <>
                {props.versionId ||
                (props.design === 'update' && !props.isViewMode) ? (
                  <UIButton
                    variant="contained"
                    theme="outline"
                    onClick={onClickVersionHistory}
                  >
                    {MAP_TITLE.versionHistory}
                  </UIButton>
                ) : null}

                {/* <UIIconXlab
                  name="line-vertical"
                  className="icon-line-vertical no-space"
                /> */}
                {isShowButtonSaveAs ? (
                  <ButtonSaveAs
                    onClick={props.onSave}
                    disabled={props.main.disabled || props.main.isDoing}
                    isLoading={props.main.isDoing}
                    moduleConfig={props.moduleConfig}
                    channelId={channelId}
                    isBlastCampaign={props.isBlastCampaign}
                  />
                ) : (
                  <UIButton
                    variant="contained"
                    theme="primary"
                    onClick={props.onSave}
                    disabled={props.main.disabled || props.main.isDoing}
                    isLoading={props.main.isDoing}
                    data-cy="button-save-journey"
                  >
                    {getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save')}
                  </UIButton>
                )}

                {!props.isJourneyV2 && (
                  <>
                    <UIIconXlab
                      name="line-vertical"
                      className="icon-line-vertical no-space"
                    />

                    <UIButton
                      variant="contained"
                      theme="outline"
                      onClick={onCancel}
                      style={{ padding: '0 10px 0 5px' }}
                      iconName="close"
                      reverse
                      iconSize="23"
                    >
                      {MAP_TITLE.close}
                    </UIButton>
                  </>
                )}
              </>
            ) : null}
          </div>
        )}
      </WrapperHeaderNode>
      <UILinearProgress isShow={props.main.isDoing && !show} />

      <UIModalCommon
        isOpen={isOpenModalCancel}
        toggle={toggleModalCancel}
        header="The change will be lost"
        onConfirm={onConfirmCancel}
      >
        {MAP_TITLE.cancelDescription}
      </UIModalCommon>
      <ModalConfirmExit
        isOpen={isOpenModalConfirmExit}
        toggle={() => setIsOpenModalConfirmExit(false)}
        design={props.design}
        title={getTranslateMessage(
          TRANSLATE_KEY._,
          'Cancel creating of this journey',
        )}
        onConfirm={onConfirmExit}
      >
        {getTranslateMessage(
          TRANSLATE_KEY._,
          'Once you confirm cancel, this journey will NOT be saved',
        )}
      </ModalConfirmExit>
      <ModalConfirmCreateCopy callback={callback} />
    </section>
  );
};

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigureCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
  main: makeSelectMainCreateWorkflow(),
  activeRowDetail: (state, props) =>
    makeSelectActiveRow()(state, {
      moduleConfig: {
        key: getPrefixDetail(
          props.moduleConfig.key,
          props.isBlastCampaign ? -13 : -7,
        ),
      },
    }),
  isOpenModalConfirmCreateCopy: makeSelectIsOpenModalConfirmCreateCopyJourney(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  const prefixDetail = getPrefixDetail(props.moduleConfig.key);
  return {
    init: params => {
      dispatch(init(prefix, params));
    },
    onSave: () => {
      dispatch(
        update(prefix, {
          copyId: props.cacheCopyId && props.cacheCopyId.current,
          isShowDrawer: props.isShowDrawer,
          isJourneyV2: props.isJourneyV2,
        }),
      );
    },
    onUpdateStatus: value =>
      dispatch(update(`${prefix}@@NODE_STATUS@@`, value)),
    onChangeName: value =>
      dispatch(updateValue(`${prefix}@@STORY_NAME@@`, value)),
    onChangeNameDesignUpdate: value =>
      dispatch(
        updateValue(`${prefixDetail}@@STORY_NAME_DESIGN_UPDATE@@`, value),
      ),
    onChangeDesign: value =>
      dispatch(updateValue(`${prefix}@@DESIGN@@`, value)),
    onDisabledSaveChange: value =>
      dispatch(updateValue(`${prefix}@@DISABLED_SAVE_CHANGE@@`, value)),
    onChangeLoading: value =>
      dispatch(updateValue(`${prefix}@@IS_LOADING@@`, value)),
    toggleLayoutJourney: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_LAYOUT_JOURNEY`, params),
      );
    },
    onUpdateCache3rdPartyCampaign: newSTT => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@CACHE_COPY_3RD_PARTY_CAMPAIGN`,
          newSTT,
        ),
      );
    },
    toggleModalConfirmCreateCopyJourney: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_MODAL_CREATE_COPY_JOURNEY@@`,
          params,
        ),
      );
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(HeaderNode);

/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable prefer-destructuring */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { useImmer } from 'use-immer';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { UILoading, UIWrapperDisable } from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';

// Services
import DestinationServices from 'services/Destination';

import DestinationCustom from './DestinationCustom';
import DestinationExisting from './DestinationExisting';
import {
  DATA_DESIGN_NODE_DESTINATION,
  DATA_DESIGN_NODE_DESTINATION_EMAIL,
  DATA_DESIGN_NODE_DESTINATION_LINE,
  DATA_DESIGN_NODE_DESTINATION_WEB_EMBEDDED,
  MAP_TEMPLATE_CATALOG,
} from './utils';
import {
  WrapperDesignNodeDestination,
  WrapperUISquare,
  WrapperTitle,
  WrapperContent,
  CalloutText,
} from './styles';
import PopoverGroup from './_UI/DropdownTemplate';
import {
  makeSelectConfigureMainCreateWorkflowErrors,
  makeSelectMainCreateWorkflow,
} from '../../../selectors';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { DESTINATION_TYPE, JOURNEY_TYPE } from './constants';
import { getObjectPropSafely } from '../../../../../../../../utils/common';
import { CHANNEL_CODE } from '../constant';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { CATALOG_CODES } from '../../../../../Destination/CreateV2/Design/Templates/constants';
import AppPushSquare from '../../../../CreateTemplate/_UI/TeamplateConfigs/components/BlastAppPush/components/CustomSquare';

const MAP_TITLE = {
  title: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_DESIGN_OPTION,
    'How would you like to start designing your campaign?',
  ),
  labelRequireField: getTranslateMessage(
    TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
    'This field is required',
  ),
  messageTemplate: getTranslateMessage(TRANSLATE_KEY._, 'Message Templates'),
};

const initData = dataParent => ({
  itemSelected: {},
  dataTemplate: { ...dataParent },
  valueSearch: '',
  isOpenPopover: false,
  isUseTemplateCampaign: false,
  validate: { errors: false, label: '' },
});

function LayoutDestination(props) {
  const { main, errors, activeNode, moduleConfig } = props;
  const { nodeId } = activeNode;
  const { activeRow = {} } = main;
  const { story_id } = activeRow;

  const [state, setState] = useImmer(initData(props));
  const [activeBox, setActiveBox] = useState(DESTINATION_TYPE.CUSTOM);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateList, setTemplateList] = useState([]);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  // console.log('state', state.validate.errors);

  // const [{ campaign }, { setCampaign }] = useFetchData({
  //   catalogId: props.activeNode.value,
  //   storyId: story_id,
  //   isOpenPopover: state.isOpenPopover,
  // });

  const isChannelLine = getObjectPropSafely(
    () =>
      (props.activeNode && props.activeNode.channelCode) === CHANNEL_CODE.LINE,
  );
  const isZaloOA = getObjectPropSafely(
    () =>
      (props.activeNode && props.activeNode.catalogCode) ===
      CATALOG_CODES.ZALO_OA,
  );
  const isAntsomiAppPush = getObjectPropSafely(
    () =>
      (props.activeNode && props.activeNode.catalogCode) ===
      CATALOG_CODES.ANTSOMI_APP_PUSH,
  );
  const isNewLayout = isChannelLine || isZaloOA || isAntsomiAppPush;

  // console.log('catalogId', props.activeNode.value );

  useEffect(() => {
    let tmpItemSelected = {};
    if (props.initData && props.initData.destinationId) {
      tmpItemSelected = DATA_DESIGN_NODE_DESTINATION[0];
    }
    setState(draft => {
      draft.itemSelected = tmpItemSelected;
    });
    return () => {
      setState(draft => {
        draft = initData(props);
      });
    };
  }, [props.initData, activeNode]);

  useEffect(() => {
    let errorsTmp = false;
    if (errors[nodeId] && _.isEmpty(state.itemSelected)) {
      errorsTmp = true;
    }

    setState(draft => {
      draft.validate.errors = errorsTmp;
    });
  }, [errors[nodeId]]);

  useEffect(() => {
    if (isNewLayout) {
      (async () => {
        try {
          setIsLoadingTemplate(true);
          const { channelId = '', catalogCode = '' } =
            getObjectPropSafely(() => props.activeNode) || {};

          const res = await DestinationServices.info.getListTemplate({
            channelId,
            catalogCode,
          });

          if (res && res.code === 200 && Array.isArray(res.data)) {
            const { templates = [] } = getObjectPropSafely(
              () => res.data[0] || {},
              {},
            );
            const templateCatalog = getObjectPropSafely(
              () => MAP_TEMPLATE_CATALOG[catalogCode],
            );

            const tmp = templates.map(each => {
              const mapping = templateCatalog[each.id];
              const label = _.get(mapping, 'label', each.label);

              const obj = {
                value: DESTINATION_TYPE.CUSTOM,
                disabled: false,
                hasImage: true,
                description: '',
              };

              if (mapping) {
                obj.icon = mapping.icon;
                obj.image = mapping.image;
              }

              obj.label = label;
              obj.template = {
                label,
                value: each.id,
              };

              return obj;
            });

            setTemplateList(tmp);
          }
        } catch (error) {
          addMessageToQueue({
            path:
              'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/LayoutDestination.jsx',
            func: 'useEffect',
            data: error.stack,
          });
          // eslint-disable-next-line no-console
          console.log(error);
        } finally {
          setIsLoadingTemplate(false);
        }
      })();
    }
  }, [isChannelLine, isZaloOA]);

  const onChange = item => {
    setState(draft => {
      draft.itemSelected = item;
      draft.validate.errors = false;
    });
  };

  const handleActiveBox = item => {
    setActiveBox(item && item.value);
  };

  useEffect(
    () => () => {
      if (isNewLayout) {
        setActiveBox(DESTINATION_TYPE.CUSTOM);
        setSelectedTemplate(null);
        setTemplateList([]);
        setIsLoadingTemplate(false);
      }
    },
    [],
  );
  // console.log('itemSelected', state.itemSelected);
  // onChange('destination', { itemSquareSelected: state.itemSelected });

  const renderDesign = {
    1: (
      <DestinationCustom
        {...props}
        itemSquareSelected={state.itemSelected}
        selectedTemplate={isNewLayout ? selectedTemplate : ''}
      />
    ),
    2: (
      <DestinationExisting
        {...state.dataTemplate}
        isEnableTesting={props.isEnableTesting}
        validateKey={props.validateKey}
        isUseTemplateCampaign={state.isUseTemplateCampaign}
        itemSquareSelected={state.itemSelected}
        moduleConfig={moduleConfig}
      />
    ),
    3: <DestinationCustom {...props} itemSquareSelected={state.itemSelected} />,
    4: <DestinationCustom {...props} itemSquareSelected={state.itemSelected} />,
    5: <DestinationCustom {...props} itemSquareSelected={state.itemSelected} />,
  };

  const callback = (type, value) => {
    switch (type) {
      case 'ON_SELECT_VALUE_TEMPLATE':
        setState(draft => {
          const {
            channel_id,
            destination_id,
            catalog_id,
            campaign_id,
            variant_ids,
            zone_id,
            priority,
            is_fit_content,
            content_placement,
          } = value.itemSelected;
          const dataTmp = {
            role: 'RESET',
            isFetchInfoData: false,
            channelId: channel_id,
            destinationId: destination_id,
            catalogId: catalog_id,
            campaignId: campaign_id,
            lookup: { campaignId: campaign_id, variantIds: variant_ids },
          };

          // check vs channel id web và catalog code = web_embbed. Nhưng ko lấy dc catalog Code nên dùng tạm rule channel id = 2 = web
          if (channel_id == '2') {
            dataTmp.zoneId = zone_id;
            dataTmp.priority = priority;
            dataTmp.isFitContent = is_fit_content;
            dataTmp.contentPlacement = content_placement;
          }

          draft.itemSelected = value.itemSquare;
          draft.dataTemplate = { ...props, initData: dataTmp };
          draft.isUseTemplateCampaign = true;
          draft.validate.errors = false;
        });
        break;

      case 'CHANGE_OPEN_POPOVER':
        setState(draft => {
          draft.isOpenPopover = value;
        });

        if (isNewLayout && value) {
          setActiveBox(DESTINATION_TYPE.COPY);
        }
        break;

      default:
        break;
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/LayoutDestination.jsx">
      <WrapperDesignNodeDestination
        style={
          isNewLayout ? { marginLeft: 14, width: 'calc(100% - 14px)' } : {}
        }
      >
        {_.isEmpty(state.itemSelected) && (
          <WrapperTitle style={{ margin: '15px 0px' }}>
            {MAP_TITLE.title}
          </WrapperTitle>
        )}
        <WrapperContent
          isNewLayout={isNewLayout}
          style={isNewLayout ? { justifyContent: 'flex-start' } : {}}
        >
          {_.isEmpty(state.itemSelected) &&
            (props.activeNode.catalogCode === 'web_embedded'
              ? DATA_DESIGN_NODE_DESTINATION_WEB_EMBEDDED
              : +props.activeNode.channelId === JOURNEY_TYPE.EMAIL
              ? DATA_DESIGN_NODE_DESTINATION_EMAIL
              : isNewLayout
              ? DATA_DESIGN_NODE_DESTINATION_LINE
              : DATA_DESIGN_NODE_DESTINATION
            ).map(each => {
              if (each.value === 2) {
                return (
                  <PopoverGroup
                    key={each.value}
                    icon={each.icon}
                    label={each.label}
                    isActive={isNewLayout && activeBox === each.value}
                    value={each.value}
                    item={each}
                    disabled={each.disabled}
                    size="300px"
                    description={each.description}
                    useTippy
                    callback={callback}
                    valueSearch={state.valueSearch}
                    iconBackgroundColor={each.iconBackgroundColor}
                    tag={each.value === 4 ? 'BETA' : ''}
                    storyId={story_id}
                    catalogId={props.activeNode.value}
                    isOpenPopover={state.isOpenPopover}
                  />
                );
              }

              return (
                <WrapperUISquare
                  key={each.value}
                  icon={each.icon}
                  isActive={isNewLayout && activeBox === each.value}
                  label={each.label}
                  value={each.value}
                  item={each}
                  disabled={each.disabled}
                  size="300px"
                  description={each.description}
                  useTippy
                  onClick={isNewLayout ? handleActiveBox : onChange}
                  iconBackgroundColor={each.iconBackgroundColor}
                  // tag={each.value === 4 ? 'BETA' : ''}
                />
              );
            })}
        </WrapperContent>
        {/* <WrapperErrors style={{ paddingLeft: isNewLayout ? '0px' : '189px' }}>
          {state.validate.errors && <P>{MAP_TITLE.labelRequireField}</P>}
        </WrapperErrors> */}
        {isNewLayout &&
          _.isEmpty(state.itemSelected) &&
          activeBox === DESTINATION_TYPE.CUSTOM && (
            <UIWrapperDisable disabled={isLoadingTemplate}>
              <WrapperTitle style={{ margin: '20px 0px', fontWeight: 400 }}>
                {MAP_TITLE.messageTemplate}
              </WrapperTitle>
              <WrapperContent
                style={{
                  margin: '15px',
                  flexWrap: 'wrap',
                  justifyContent: 'flex-start',
                }}
              >
                <UILoading isLoading={isLoadingTemplate} />
                {templateList.length === 0 && !isLoadingTemplate ? (
                  <CalloutText>
                    {getTranslateMessage(
                      TRANSLATE_KEY._BLAST_CAMPAIGN_NO_TEMP,
                      'There are no available templates. Please check your destinations.',
                    )}
                  </CalloutText>
                ) : (
                  templateList.map(each => {
                    const {
                      hasImage = false,
                      image = '',
                      template = {},
                      ...restItem
                    } = each;

                    return (
                      <WrapperUISquare
                        key={each.value}
                        icon={each.icon}
                        isNewLayout
                        backgroundColorCard="#FCFDFF"
                        btnLabel={getTranslateMessage(
                          TRANSLATE_KEY._,
                          'Use Template',
                        )}
                        styles={{
                          wrapperCard: { padding: 0 },
                          head: { left: 0, marginTop: 10 },
                          wrapperIconLabel: { height: '100%' },
                          wrapperImage: { width: 310, height: 210 },
                        }}
                        label={each.label}
                        value={each.value}
                        hasImage={hasImage}
                        imageUrl={image}
                        item={restItem}
                        disabled={each.disabled}
                        size="300px"
                        description={each.description}
                        useTippy
                        onClick={item => {
                          onChange(item);
                          setActiveBox(each.value);
                          setSelectedTemplate(template);
                        }}
                        iconBackgroundColor={each.iconBackgroundColor}
                        {...isAntsomiAppPush && {
                          CustomSquare: AppPushSquare,
                          isHasTemplate: true,
                        }}
                      />
                    );
                  })
                )}
              </WrapperContent>
            </UIWrapperDisable>
          )}
      </WrapperDesignNodeDestination>
      {renderDesign[state.itemSelected.value]}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectMainCreateWorkflow(),
  errors: makeSelectConfigureMainCreateWorkflowErrors(),
});
export default connect(
  mapStateToProps,
  null,
)(LayoutDestination);

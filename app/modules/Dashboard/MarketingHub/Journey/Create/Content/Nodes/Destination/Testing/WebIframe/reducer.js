import produce from 'immer';
import { combineReducers } from 'redux';

import { safeParse } from 'utils/common';

import tableReducerFor from 'containers/Table/reducer';

import { MODULE_CONFIG } from './config';
import { getModuleConfig } from './utils';

import ReduxTypes from '../../../../../../../../../../redux/constants';
// import { DEFAULT_ACTION } from './constants';
const PREFIX = MODULE_CONFIG.key;

export const initialState = {
  isLoading: true,
  isInitDone: false,
  data: [],
  // userAttributes -> for customer detail -> using it
  userAttributes: {
    map: {},
    list: [],
    groups: [],
  },
  mapInfo: {
    itemAttribute: {},
  },
  sourceInfo: null,
  zoneInfo: null,
  itemTypeId: '',
  itemType: null,
  moduleConfig: { ...MODULE_CONFIG },
};

/* eslint-disable default-case, no-param-reassign */
const mainReducerFor = () => {
  const mainReducer = (state = initialState, action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT}`: {
          const { itemTypeId, storyId } = action.payload;
          draft.itemTypeId = itemTypeId;
          draft.moduleConfig = getModuleConfig(itemTypeId, storyId);

          draft.isLoading = true;
          draft.isInitDone = false;
          return;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          draft.isLoading = false;
          draft.isInitDone = true;
          return;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          draft.isLoading = true;
          draft.sourceInfo = null;
          return;
        }
        case `${PREFIX}${ReduxTypes.GET_LIST}`: {
          draft.isLoading = true;
          return;
        }
        // case `${PREFIX}${ReduxTypes.GET_LIST_DONE}`: {
        //   draft.isLoading = false;
        //   const { data, portalId } = action.payload;
        //   draft.data = serializeData(
        //     safeParse(data, []),
        //     portalId,
        //     draft.itemTypeId,
        //   );
        //   return;
        // }
        case `${PREFIX}@@UPDATE_LOADING${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = action.payload;
          return;
        }
        case `${PREFIX}@@GET_SOURCE_DONE${ReduxTypes.UPDATE_VALUE}`: {
          draft.sourceInfo = action.payload;
          return;
        }
        case `${PREFIX}@@GET_ZONE_DONE${ReduxTypes.UPDATE_VALUE}`: {
          draft.zoneInfo = action.payload;
          return;
        }
        // edit
        case `${PREFIX}DATA_LOOKUP${ReduxTypes.UPDATE_VALUE}`: {
          const { type, data } = action.payload;
          if (type === 'itemAttribute') {
            draft.mapInfo.itemAttribute = data.map;
          }
          return;
        }
        default:
          // eslint-disable-next-line consistent-return
          return state;
      }
    });
  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

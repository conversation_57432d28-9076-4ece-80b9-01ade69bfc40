/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable import/order */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import {
  makeSelectMainCreateWorkflow,
  makeSelectActiveIdNodeData,
} from '../../../../selectors';

import ColumnPreview from './Setting/ColumnPreview';
import {
  ContainerCustomize,
  WrapperContentCustomize,
  WrapperFilterContent,
  WrapperSuggestion,
  SuggestionColumn,
  SuggestionTitle,
  WrapperButton,
  SpanSelect,
  ButtonRemoveAll,
  WrapperHeader,
  Title,
} from './styles';
import ResultsTesting from './ResultsTesting';
import List from './Setting/List/index';
import CustomerService from '../../../../../../../../../services/Object';
import QuickTestServices from '../../../../../../../../../services/Journey';
import { getTranslateMessage } from '../../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../../messages/constant';
import { toEntryCustomer, toEntryVisistor, toEntryExpermenID } from './utils';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Button,
  DRAWER_DETAIL_DIMENSION,
  DrawerDetail,
  POST_MESSAGE_TYPES,
} from '@antscorp/antsomi-ui';
import { DRAWER_NAME_CACHE } from '../../../../../../../../../utils/constants';

const labelRunTest = getTranslateMessage(
  TRANSLATE_KEY._ACT_RUN_TEST,
  'Run Test',
);
const labelButtonCancel = getTranslateMessage(
  TRANSLATE_KEY._ACT_CANCEL,
  'Cancel',
);
const labelButtonRemoveAll = getTranslateMessage(
  TRANSLATE_KEY._ACT_REMOVE_ALL,
  'Remove all',
);
const titleModyfiColumns = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_AUDI_TESTING,
  'Select audiences for testing journey',
);

const titleResults = getTranslateMessage(
  TRANSLATE_KEY._TITL_TEST_RESULT,
  'Testing Results',
);
function Testing(props) {
  const {
    initData = {},
    isOpenModal,
    toggleModal,
    mainCreateMark,
    activeNode,
    nodeDetail,
    configure,
    isBlastCampaign = false,
  } = props;
  const {
    itemTypeId,
    destinationId,
    catalogId,
    destinationInput,
    variantExtraData,
    objectWidgetInput,
    testingAudienceIds,
    storyId,
    nodeIds,
  } = initData;
  const properties = configure.main.nodes.get(nodeIds).get('destination');

  const [state, setState] = useImmer({
    selectedData: {
      list: [],
      map: {},
    },
    openResult: false,
    disabled: true,
    isInitDone: false,
    triggerOut: 0,
    experimentId: 0,
    experimentResults: {},
  });
  const params = {
    data: {
      limit: 50,
      page: 1,
      sd: 'asc',
      search: '',
      ids: testingAudienceIds,
      scope: 3,
      propertyCode: 'customer_id',
      itemTypeId: -1003,
      itemTypeName: 'name',
      itemPropertyName: 'customer_id',
      systemDefined: 0,
      isPk: 1,
      decryptFields: [],
    },
    objectId: storyId !== 0 ? storyId : itemTypeId,
    objectType: '',
    experimentId: 0,
  };
  if (parseInt(itemTypeId) === -1003) {
    params.objectType = 'JRN_TEST_CUS_AUDIENCE';
  } else {
    params.objectType = 'JRN_TEST_USR_AUDIENCE';
  }
  // useEffect(() => {
  //   if (testingAudienceIds.length > 0) {
  //     CustomerService.suggestion.lookupByIds(params).then(res => {
  //       if (parseInt(itemTypeId) === -1003) {
  //         setState(draft => {
  //           draft.selectedData.list = toEntryCustomer(res.list);
  //           draft.selectedData.map = res.map;
  //           if (draft.selectedData.list.length >= 1) {
  //             draft.disabled = false;
  //           }
  //         });
  //       } else {
  //         setState(draft => {
  //           const map = toEntryVisistor(testingAudienceIds);
  //           draft.selectedData.list = map.list;
  //           draft.selectedData.map = map.map;
  //           if (draft.selectedData.list.length >= 1) {
  //             draft.disabled = false;
  //           }
  //         });
  //       }
  //     });
  //   }
  // }, [itemTypeId]);

  useEffect(() => {
    if (isOpenModal) {
      QuickTestServices.quicktest
        .getLastResultExperiment(params)
        .then(res => {
          const map = toEntryExpermenID(res);
          // console.log(map.map);
          if (map.map.audienceIds[nodeId]) {
            params.data.ids = map.map.audienceIds[nodeId].audienceIds;
          }
          params.experimentId = map.map.experimentId;
          setState(draft => {
            draft.experimentId = map.map.experimentId;
            draft.experimentResults = map.map.audienceIds;
          });
          return true;
        })
        .then(() => {
          // console.log(object)
          if (params.data.ids.length) {
            if (params.data.ids.length > 0) {
              CustomerService.suggestion.lookupByIds(params).then(res => {
                if (parseInt(itemTypeId) === -1003) {
                  setState(draft => {
                    draft.selectedData.list = toEntryCustomer(res.list);
                    draft.selectedData.map = res.map;
                    if (draft.selectedData.list.length >= 1) {
                      draft.disabled = false;
                    }
                  });
                } else {
                  setState(draft => {
                    const map = toEntryVisistor(params.data.ids);
                    draft.selectedData.list = map.list;
                    draft.selectedData.map = map.map;
                    if (draft.selectedData.list.length >= 1) {
                      draft.disabled = false;
                    }
                  });
                }
              });
            }
          }
        });
      // console.log(params.ids.length)
      // if (params.ids.length > 0) {
      //   CustomerService.suggestion.lookupByIds(params).then(res => {
      //     if (parseInt(itemTypeId) === -1003) {
      //       setState(draft => {
      //         draft.selectedData.list = toEntryCustomer(res.list);
      //         draft.selectedData.map = res.map;
      //         if (draft.selectedData.list.length >= 1) {
      //           draft.disabled = false;
      //         }
      //       });
      //     } else {
      //       setState(draft => {
      //         const map = toEntryVisistor(testingAudienceIds);
      //         draft.selectedData.list = map.list;
      //         draft.selectedData.map = map.map;
      //         if (draft.selectedData.list.length >= 1) {
      //           draft.disabled = false;
      //         }
      //       });
      //     }
      //   });
      // }
    }
  }, [isOpenModal, state.triggerOut]);

  // useEffect(() => {
  //   if (state.triggerOut > 0) {
  //     const audienceIds = state.selectedData.list.map(each => each.id);
  //     props.callback('ON_CHANGE_AUDIENCEIDS', audienceIds);
  //   }
  // }, [state.triggerOut]);

  useEffect(() => {}, [mainCreateMark.isLoading]);

  const callback = (type, data) => {
    switch (type) {
      case 'ADD_TO_LIST': {
        setState(draft => {
          draft.disabled = false;
          draft.selectedData.list.push(data);
          draft.selectedData.map[data.id] = data;
        });
        break;
      }
      case 'SELECT_ALL_LIST': {
        setState(draft => {
          draft.disabled = false;
          data.forEach(result => {
            const index = draft.selectedData.list.findIndex(
              item => item.id === result.id,
            );
            if (index === -1 && draft.selectedData.list.length < 50) {
              draft.selectedData.list.push(result);
              draft.selectedData.map[result.id] = result;
            }
          });
        });
        break;
      }
      case 'TRIGGER_OUT': {
        setState(draft => {
          draft.triggerOut += 1;
        });
        break;
      }
      case 'UPDATE_VALUE_TESTING': {
        props.callback('UPDATE_VALUE_TESTING', data);
        break;
      }

      default: {
        props.callback(type, data);
        break;
      }
    }
  };

  const onRendered = () => {
    setState(draft => {
      draft.isInitDone = true;
    });
  };

  const onRemoveItem = key => {
    const index = state.selectedData.list.findIndex(item => item.id === key);
    setState(draft => {
      draft.selectedData.list.splice(index, 1);
      delete draft.selectedData.map[key];
      if (state.selectedData.list.length === 1) {
        draft.disabled = true;
      }
    });
  };
  const onRemoveAllItem = () => {
    setState(draft => {
      draft.selectedData = { list: [], map: {} };
      if (draft.selectedData.list.length === 0) {
        draft.disabled = true;
      }
    });
  };
  const onOpenModifyColumn = () => {
    setState(draft => {
      draft.openResult = true;
      // draft.triggerOut += 1;
    });
  };
  const toggleResult = () => {
    setState(draft => {
      draft.openResult = !state.openResult;
    });
  };

  useEffect(() => {
    if (isOpenModal) {
      window.postMessage({
        type: POST_MESSAGE_TYPES.TOGGLE_DRAWER_DETAIL_FULLSCREEN,
        data: {
          name: DRAWER_NAME_CACHE.JOURNEY_CREATE,
        },
      });
    }

    return () => {
      window.postMessage({
        type: POST_MESSAGE_TYPES.TOGGLE_DRAWER_DETAIL_FULLSCREEN,
        data: {
          name: DRAWER_NAME_CACHE.JOURNEY_CREATE,
          value: false,
        },
      });
    };
  }, [isOpenModal]);

  return (
    <>
      <DrawerDetail
        open={isOpenModal}
        onClose={toggleModal}
        menuProps={{
          show: false,
        }}
        maxWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
        name={DRAWER_NAME_CACHE.DRAWER_QUICK_TEST}
        defaultSize="min"
        headerProps={{
          children: (
            <>
              {!state.openResult && (
                <WrapperHeader>
                  <Title>Quick Test </Title>
                  <WrapperButton>
                    <Button theme="outline" onClick={() => toggleModal(false)}>
                      {labelButtonCancel}
                    </Button>
                    <Button
                      type="primary"
                      disabled={state.disabled}
                      onClick={onOpenModifyColumn}
                    >
                      {labelRunTest}
                    </Button>
                  </WrapperButton>
                </WrapperHeader>
              )}
            </>
          ),
          showBorderBottom: true,
          height: 50,
          style: {
            padding: '0 15px',
          },
        }}
      >
        {!state.openResult && (
          <>
            <ContainerCustomize style={{ padding: '0 15px 10px 15px' }}>
              <WrapperContentCustomize>
                <List
                  storyId={storyId}
                  itemTypeId={itemTypeId}
                  selectedData={state.selectedData}
                  callback={callback}
                />
                <WrapperFilterContent />
              </WrapperContentCustomize>
              <WrapperSuggestion>
                <SuggestionTitle>
                  <SpanSelect style={{ color: '#000000', fontWeight: 500 }}>
                    {getTranslateMessage(
                      TRANSLATE_KEY._TITL_SELECT_AUDIENCE,
                      `Selected audience (${state.selectedData.list.length})`,
                      { x: state.selectedData.list.length },
                    )}
                  </SpanSelect>

                  <Button
                    type="text"
                    disabled={state.disabled}
                    onClick={onRemoveAllItem}
                  >
                    {labelButtonRemoveAll}
                  </Button>
                </SuggestionTitle>
                <SuggestionColumn>
                  <ColumnPreview
                    isOpen={isOpenModal}
                    mapSelecteds={state.selectedData.list}
                    onRemoveItem={onRemoveItem}
                  />
                </SuggestionColumn>
              </WrapperSuggestion>
            </ContainerCustomize>
          </>
        )}
        {state.openResult && (
          <ResultsTesting
            toggleModal={toggleModal}
            selectedData={state.selectedData}
            itemId={itemTypeId}
            handleCloseModal={toggleResult}
            isOpenModal={state.openResult}
            destinationId={destinationId}
            catalogId={catalogId}
            destinationInput={destinationInput}
            variantExtraData={variantExtraData}
            objectWidgetInput={objectWidgetInput}
            experimentId={state.experimentId}
            objectType={params.objectType}
            callback={callback}
            objectId={params.objectId}
            experimentResults={state.experimentResults}
            nodeDetail={nodeDetail}
            nodeIds={nodeIds}
            properties={properties}
            configure={configure}
            triggerType={props.triggerType}
            isBlastCampaign={isBlastCampaign}
          />
        )}
      </DrawerDetail>
    </>
  );
}
// export default memo(Testing);

const mapStateToProps = createStructuredSelector({
  mainCreateMark: makeSelectMainCreateWorkflow(),
  activeNode: makeSelectActiveIdNodeData(),
});

export default connect(
  mapStateToProps,
  null,
)(Testing);

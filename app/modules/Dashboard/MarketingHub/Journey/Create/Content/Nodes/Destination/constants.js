// Libraries
import React from 'react';
import { flatten } from 'lodash';

// Constants
import { TEMPLATE_TYPES } from '../../../../../Destination/CreateV2/Design/Templates/constants';

// Components
import { Favorite, FavoriteBorder, Star, StarBorder } from '@material-ui/icons';

export const DESTINATION_TYPE = {
  CUSTOM: 1,
  COPY: 2,
  MEDIA_TEMPLATE: 3,
  EMAIL_TEMPLATE: 4,
  JSON_TEMPLATE: 5,
};

export const JOURNEY_TYPE = {
  EMAIL: 1,
  WEB_PERSONALIZATION: 2,
  WEB_PUSH: 3,
  APP_PUSH: 4,
  CONVERSATION: 5,
  WEBHOOK: 6,
  SMS: 7,
  ORCHES: 8,
  VIBER: 9,
};

export const TRACK_URL = {
  KEY: 'tracking_url',
  VALUE: '#{tracking_url || ""}',
};

/* <PERSON><PERSON><PERSON> App Push config start */
export const initPlaceholder = {
  title: 'Title',
  content: 'Content',
};
export const initRatingTextValue = {
  1: 'Bad',
  2: 'Poor',
  3: 'Fair',
  4: 'Good',
  5: 'Excellent',
};
export const GEN_RATING_TEXT = Array.from(
  { length: 5 },
  (_v, i) => `ratingText${i + 1}`,
);
export const ICON_TYPE_KEY = Object.freeze({
  SYSTEM: 'systemIcon',
  TEXT_EMOJI: 'textEmoji',
  UPLOAD: 'uploadIcon',
});

export const RATING_ICON_TYPE = {
  [ICON_TYPE_KEY.SYSTEM]: {
    label: 'System icon',
    value: ICON_TYPE_KEY.SYSTEM,
  },
  [ICON_TYPE_KEY.TEXT_EMOJI]: {
    label: 'Text/Emoji',
    value: ICON_TYPE_KEY.TEXT_EMOJI,
  },
  [ICON_TYPE_KEY.UPLOAD]: {
    label: 'Upload icon',
    value: ICON_TYPE_KEY.UPLOAD,
  },
};

export const ICON_OPTION = {
  SELECTED: [
    {
      node: <Star />,
      value: 'Star.Selected',
      style: {
        color: 'rgba(252, 167, 38, 1)',
      },
    },
    {
      node: <StarBorder />,
      value: 'StarBorder.Selected',
      style: {
        color: 'rgba(252, 167, 38, 1)',
      },
    },

    {
      node: <Favorite />,
      value: 'Favorite.Selected',
      style: {
        color: 'rgba(239, 51, 64, 1)',
      },
    },
    {
      node: <FavoriteBorder />,
      value: 'FavoriteBorder.Selected',
      style: {
        color: 'rgba(239, 51, 64, 1)',
      },
    },
  ],
  UNSELECTED: [
    {
      node: <Star />,
      value: 'Star.Unselected',
      style: {
        color: 'rgba(89, 89, 89, 1)',
      },
    },
    {
      node: <StarBorder />,
      value: 'StarBorder.Unselected',
      style: {
        color: 'rgba(89, 89, 89, 1)',
      },
    },

    {
      node: <Favorite />,
      value: 'Favorite.Unselected',
      style: {
        color: 'rgba(89, 89, 89, 1)',
      },
    },
    {
      node: <FavoriteBorder />,
      value: 'FavoriteBorder.Unselected',
      style: {
        color: 'rgba(89, 89, 89, 1)',
      },
    },
  ],
};

export const GROUP_CONFIG_RATING = {
  groupCode: null,
  groupName: null,
  panelCode: 'rating_setting',
  panelName: 'Rating Setting',
  groupOrder: null,
  panelOrder: 2,
};

export const RECOMMEND_FIELDS = {
  TYPE: 'contentType',
  CONTENT_SOURCES: 'contentSources',
};

export const RATING_SETTING_KEYS = Object.freeze({
  ICON_TYPE: 'ratingIconType',
  SCALE: 'ratingScale',
  RATING_1: 'ratingText1',
  RATING_2: 'ratingText2',
  RATING_3: 'ratingText3',
  RATING_4: 'ratingText4',
  RATING_5: 'ratingText5',
  UNSELECTED: 'ratingUnselected',
  SELECTED: 'ratingSelected',
});

export const MODIFIED_FIELDS = Object.freeze([
  'template',
  ...Object.values(RECOMMEND_FIELDS),
  'imageCarousel',
  'bgColor',
  'imageAlignment',
  'advPriority',
  'advCollapseId',
  'title',
  'content',
  'imageUrl',
  'defaultAction',
  'url',
  'androidContentSummary',
  'deeplink',
  'subTitle',
  'advField',
  'advButton',
  'androidSound',
]);
export const DEFAULT_ACTION_KEYS = {
  URL: 'url',
  DEEP_LINK: 'deeplink',
};

export const DEFAULT_ACTION_TYPES = {
  [DEFAULT_ACTION_KEYS.URL]: {
    label: 'Redirect to an URL',
    value: DEFAULT_ACTION_KEYS.URL,
  },
  [DEFAULT_ACTION_KEYS.DEEP_LINK]: {
    label: 'Deeplink',
    value: DEFAULT_ACTION_KEYS.DEEP_LINK,
  },
};

export const DEFAULT_BASIC_INPUTS = [
  'template',
  ...Object.values(RECOMMEND_FIELDS),
  'bgColor',
  'imageAlignment',
  'title',
  'content',
  'imageCarousel',
  'imageUrl',
  'defaultAction',
  'url',
  'deeplink',
  ...Object.values(RATING_SETTING_KEYS),
  'advButton',
  'subTitle',
  'iosBadgeType',
  // 'iosSound',
  // 'iosIsNotification',
  // 'iosUnNotiCategory',
  // 'iosContentId',
  // 'iosInterruptionLevel',
  // 'iosRelevance',
  'androidContentSummary',
  // 'androidSmallIconUrl',
  // 'androidCategory',
  // 'androidSound',
  // 'androidLocks',
  // 'androidLedColor',
  // 'androidGroupKey',
  // 'androidGroupMessage',
  // 'androidBgrData',
  // 'advCollapseId',
  // 'advPriority',
  // 'advTtl',
  'advField',
];
/* Antsomi App Push config end */

export const SNAPSHOT_RENDER_FIELD = 'line';

export const OBSERVER_VIEW_DEVICES = ['ios', 'android'];

export const PRE_SELECT_STICKER_SET = ['11537', 'e8a44e6172249b7ac235'];

export const TEMPLATE_SMART_INBOX = {
  ANNOUN: 'announcements',
  COUPON: 'coupon',
  ORDER: 'order_verified',
};
export const LIST_ID_TEMPLATE_SMART_INBOX = [
  TEMPLATE_SMART_INBOX.ANNOUN,
  TEMPLATE_SMART_INBOX.COUPON,
  TEMPLATE_SMART_INBOX.ORDER,
];

export const RESET_DATA_LIST_TEMPLATE = {
  [TEMPLATE_SMART_INBOX.ANNOUN]: [
    'couponCheckUrl',
    'couponAppUrl',
    'couponButton',
    'couponLaunchUrl',
    'couponWebUrl',
    'orderAppUrl1',
    'orderAppUrl2',
    'orderButton1',
    'orderButton2',
    'orderCheckUrl1',
    'orderCheckUrl2',
    'orderLaunchUrl1',
    'orderLaunchUrl2',
    'orderWebUrl1',
    'orderWebUrl2',
  ],
  [TEMPLATE_SMART_INBOX.COUPON]: [
    'orderAppUrl1',
    'orderAppUrl2',
    'orderButton1',
    'orderButton2',
    'orderCheckUrl1',
    'orderCheckUrl2',
    'orderLaunchUrl1',
    'orderLaunchUrl2',
    'orderWebUrl1',
    'orderWebUrl2',
    'announceAppUrl',
    'announceCheckUrl',
    'announceLaunchUrl',
    'announceWebUrl',
  ],
  [TEMPLATE_SMART_INBOX.ORDER]: [
    'announceAppUrl',
    'announceCheckUrl',
    'announceLaunchUrl',
    'announceWebUrl',
    'couponCheckUrl',
    'couponAppUrl',
    'couponButton',
    'couponLaunchUrl',
    'couponWebUrl',
  ],
};

export const UPDATE_REQUIRED_FIELDS = [
  'appTitle',
  'appSubtitle',
  'appContent',
  'webContent',
  'webTitle',
];

export const KEYS_ORDER_TEMPLATE_FIRST = [
  'orderWebUrl1',
  'orderLaunchUrl1',
  'orderAppUrl1',
];
export const KEYS_ORDER_TEMPLATE_SECOND = [
  'orderLaunchUrl2',
  'orderWebUrl2',
  'orderAppUrl2',
];

// HARD FOLLOW KEYS FROM API
export const LIST_HIDE_WHEN_CHECKED_URL = [
  'announceLaunchUrl',
  'couponLaunchUrl',
  'orderLaunchUrl1',
  'orderLaunchUrl2',
];

// HARD FOLLOW KEYS FROM API
export const LIST_FIELD_HIDE_WHEN_UNCHECKED_URL = [
  'announceWebUrl',
  'announceAppUrl',
  'orderWebUrl1',
  'orderAppUrl1',
  'orderWebUrl2',
  'orderAppUrl2',
  'couponWebUrl',
  'couponAppUrl',
];

export const PREVIEW_FIELDS = Object.freeze([
  'previewText',
  'previewTextButton',
  'previewTextConfirm',
  'previewTextImage',
  'previewTextImagemap',
]);

export const LINE_FIELDS = Object.freeze({
  [TEMPLATE_TYPES.TEXT]: ['text'],
  [TEMPLATE_TYPES.IMAGE]: ['imageUrl', 'previewImageUrl'],
  [TEMPLATE_TYPES.CAROUSEL]: ['carousel', 'previewText'],
  [TEMPLATE_TYPES.STICKER]: ['stickerType', 'sticker'],
  [TEMPLATE_TYPES.BUTTONS]: ['previewTextButton', 'buttonTemplate'],
  [TEMPLATE_TYPES.IMAGE_CAROUSE]: ['previewTextImage', 'imageCarousel'],
  [TEMPLATE_TYPES.CONFIRM]: ['previewTextConfirm', 'confirmation'],
});

export const LINE_ALL_FIELD = Object.freeze(
  flatten(Object.values(LINE_FIELDS)),
);

export const CATALOG_CODE = {
  ONE_SIGNAL_WEB_PUSH: 'onesignal_web_push',
  MEPUZZ: 'mepuzz',
  FIREBASE_CLOUD_MESS: 'firebase_cloud_messaging',
  PUSH_ALERT: 'push_alert',
  WONDER_PUSH: 'wonder_push',
  ANTSOMI_WEB_PUSH: 'antsomi_web_push',
  ONE_SIGNAL_APP_PUSH: 'onesignal_app_push',
  NKID_APP_PUSH: 'nkid_app_push',
  ANTSOMI_APP_PUSH: 'antsomi_app_push',
  SMART_INBOX_APP: 'smart_inbox_app',
  VIBER_YONDU: 'viber_yondu',
  VIBER: 'viber',
  INFOBIP_VIBER: 'infobip_viber',
  ESMS_VIBER: 'esms_viber',
  ZALO_OFFICIAL_ACCOUNT: 'zalo_official_account',
  INFOBIP_WHATSAPP: 'infobip_whatsapp',
  WHATSAPP_APP: 'whatsapp_app',
  TELEGRAM_APP: 'telegram_app',
  LINE_APP: 'line_app',
  DAC_LINE_MESSAGE: 'dac_line_message',
};

export const LIST_FIELD_BY_CATALOG = {
  [CATALOG_CODE.ONE_SIGNAL_WEB_PUSH]: ['landingPageUrl'],
  [CATALOG_CODE.MEPUZZ]: ['url'],
  [CATALOG_CODE.FIREBASE_CLOUD_MESS]: ['url'],
  [CATALOG_CODE.PUSH_ALERT]: ['url'],
  [CATALOG_CODE.WONDER_PUSH]: ['targetUrl'],
  [CATALOG_CODE.ANTSOMI_WEB_PUSH]: ['landingPageUrl'],
  [CATALOG_CODE.ONE_SIGNAL_APP_PUSH]: ['url'],
  [CATALOG_CODE.NKID_APP_PUSH]: ['landingPageUrl'],
  [CATALOG_CODE.ANTSOMI_APP_PUSH]: ['landingPageUrl'],
  [CATALOG_CODE.SMART_INBOX_APP]: [
    'announceLaunchUrl',
    'couponLaunchUrl',
    'orderLaunchUrl1',
    'announceWebUrl',
    'couponWebUrl',
    'orderWebUrl1',
    'announceAppUrl',
    'couponAppUrl',
    'orderAppUrl1',
    'appLaunchUrl',
    'webLaunchUrl',
  ],
  [CATALOG_CODE.VIBER_YONDU]: ['richMediaLink'],
  [CATALOG_CODE.VIBER]: ['landingPageUrl'],
  [CATALOG_CODE.INFOBIP_VIBER]: ['landingPage'],
  [CATALOG_CODE.ESMS_VIBER]: ['landingPage'],
  [CATALOG_CODE.ZALO_OFFICIAL_ACCOUNT]: [
    'text',
    'imgContent',
    'requestSubtitle',
  ],
  [CATALOG_CODE.INFOBIP_WHATSAPP]: ['text'],
  [CATALOG_CODE.WHATSAPP_APP]: ['content'],
  [CATALOG_CODE.TELEGRAM_APP]: ['content'],
  [CATALOG_CODE.LINE_APP]: ['text'],
  [CATALOG_CODE.DAC_LINE_MESSAGE]: ['content'],
};
export const SMALL_HEIGHT_FIELDS = Object.freeze([
  'requestTitle',
  'transactionTitle',
  'transactionSubtitle',
  'transactionMessage',
  'mediaTitle',
  'mediaSubtitle',
  'mediaMessage',
]);

export const DEFAULT_ICON_SELECT_VARIANTS = {
  [ICON_TYPE_KEY.SYSTEM]: {
    selected: 'Star.Selected',
    unSelected: 'Star.Unselected',
  },
  [ICON_TYPE_KEY.UPLOAD]: {
    selected:
      'https://st-media-template.antsomi.com/icons/rating/Star.Selected.png',
    unSelected:
      'https://st-media-template.antsomi.com/icons/rating/Star.Unselected.png',
  },
  [ICON_TYPE_KEY.TEXT_EMOJI]: {
    selected: '#DEEFFE',
    unSelected: '#000000',
  },
};

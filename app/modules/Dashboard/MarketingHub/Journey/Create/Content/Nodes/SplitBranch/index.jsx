/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import Grid from '@material-ui/core/Grid';
import { UINumber } from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';

import { connect } from 'react-redux';

import { createStructuredSelector } from 'reselect';
import { makeStyles } from '@material-ui/styles';
import { makeSelectConfigureMainCreateWorkflow } from '../../../selectors';
import { updateValue } from '../../../../../../../../redux/actions';
import UISplit from '../../../../../../../../containers/UIDev/UISplit';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { generateKey } from '../../../../../../../../utils/common';
import { getDefaultBranchNodeSplit } from './utils';

function SplitBranch(props) {
  // const number =
  //   props.mainConfigure.nodes.getIn([props.activeNode.nodeId, 'numBranchs']) ||
  //   2;
  const branches =
    (props.mainConfigure.nodes.getIn([props.activeNode.nodeId, 'branches']) &&
      props.mainConfigure.nodes.getIn([props.activeNode.nodeId, 'branches'])
        .length > 0 &&
      props.mainConfigure.nodes.getIn([props.activeNode.nodeId, 'branches'])) ||
    (props.mainConfigure.flattenNodes.filter(each => (each.label === 'Branch 1' || each.label === 'Group 1'))
      .length > 0 &&
      getDefaultBranchNodeSplit(props.mainConfigure.flattenNodes));

  // const onChangeTimes = value => {
  //   props.onChange('numBranchs', value);
  //   props.onChangeNumberBranchNode({
  //     activeNode: props.activeNode,
  //     number: value,
  //   });
  // };

  // const initData = [
  //   { id: '1', value: 50 },
  //   { id: '2', value: 1 },
  //   { id: '3', value: 16 },
  //   { id: '4', value: 16 },
  //   { id: '5', value: 16 },
  //   { id: '6', value: 1 },
  // ];
  const callback = (type, data) => {
    props.onChange('branches', data.tmp);
    if (type === 'UPDATE_LIST_DATA') {
      props.onChangeListBranchNode({
        activeNode: props.activeNode,
        branches: data.tmp,
        oldBranches: branches,
        id: data.id,
      });
      props.onChangeDataSplitNodeParent({
        activeNode: props.activeNode,
        branches: data.tmp,
      });
    } else if (type === 'UPDATE_VALUE_DATA') {
      props.onChangeDataSplitNodeParent({
        activeNode: props.activeNode,
        branches: data.tmp,
      });
    }
  };

  const useStyles = makeStyles(() => ({
    textHeader: {
      fontSize: '12px',
      color: '#000',
    },
    textFooterViewMode: {
      fontSize: '11px',
      color: '#000',
      marginTop: '28px',
    },
  }));

  const classes = useStyles();

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/SplitBranch/index.jsx">
      <Grid
        container
        className="p-all-4"
        style={{ minWidth: !props.isViewMode ? '720px' : 'unset' }}
      >
        <Grid item sm={8} lg={6} className="flex-column d-flex">
          <span className={classes.textHeader}>
            {getTranslateMessage(
              TRANSLATE_KEY._TITL_SPLIT_AUDIENCE,
              'Split audiences who enter node by',
            )}
          </span>
          <div className="p-top-5">
            <UISplit
              maxBranch={20}
              callbackData={callback}
              initData={branches}
              isViewMode={props.isViewMode}
              activeNode={props.activeNode}
            />
            <p className={props.isViewMode && classes.textFooterViewMode}>
              {getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_TOTAL_BRANCH,
                'Note: Total 100%',
              )}
            </p>
          </div>
        </Grid>
      </Grid>
      {/* <div>
        <UISplit maxBranch={10} callbackData={callback} initData={initData} />
      </div> */}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectConfigureMainCreateWorkflow(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    onChangeListBranchNode: value =>
      dispatch(updateValue(`${prefix}@@SPLIT_NODE_PARENT@@`, value)),
    onChangeDataSplitNodeParent: value =>
      dispatch(updateValue(`${prefix}@@SPLIT_NODE_DATA@@`, value)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(SplitBranch);

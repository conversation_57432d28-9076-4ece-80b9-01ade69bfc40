/* eslint-disable prefer-const */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */
/* eslint-disable prefer-destructuring */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable import/no-cycle */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-useless-escape */
/* eslint-disable react/prop-types */
import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useImmer } from 'use-immer';
import { Map } from 'immutable';
import _isEmpty from 'lodash/isEmpty';
import _omit from 'lodash/omit';
import _cloneDeep from 'lodash/cloneDeep';
import _union from 'lodash/union';
import _has from 'lodash/has';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _keyBy from 'lodash/keyBy';
import _uniqBy from 'lodash/uniqBy';
import { isFunction, toString, isString, isObject } from 'lodash';
import produce from 'immer';
import {
  ColorPicker,
  ContentSources,
  QueryClientProviderAntsomiUI,
  Switch,
  Tooltip,
  UploadImage,
  queryClientAntsomiUI,
  Flex,
  Spin,
  Button,
  useDeepCompareEffect,
} from '@antscorp/antsomi-ui';
import { RenderWorkspace } from '@antscorp/form-design';
import Grid from '@material-ui/core/Grid';
import AccessTimeIcon from '@material-ui/icons/AccessTime';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';

import {
  UIButton,
  UICheckbox,
  UIInputTime,
  UINumber,
  UIPassword,
  UITextField,
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import TextareaAutosize from '@material-ui/core/TextareaAutosize';
import { CustomFields } from '../../../../../../../../components/common/CustomFields';
import classNames from 'classnames';
import ToggleButton from 'components/Atoms/ToggleButton/index';
import IconXlab from 'components/common/UIIconXlab';

// import UISelect from 'components/form/UISelectCondition';
import PairKeyValue, {
  TYPE_RENDER_PAIR,
} from 'components/common/UIPairKeyValue';
import UISelect from 'components/form/UISelectCondition';
// import UIEditor from 'components/form/UIEditor';
import Sticker from 'components/Molecules/Sticker';
import LineTemplate from 'components/Organisms/LineTemplate';
import InputLanguage from 'components/common/InputLanguage';
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import EmailTemplateEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization/EmailTemplateEditorV2';
import JSONTemplateEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization/JSONTemplateEditor';
import MediaTemplateEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization/MediaTemplateEditor';
import UITagButtons from 'containers/UILabelTagButtons';
import UINodeFilter from 'modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/components/UINodeFilter';
import ThirdPartyService from 'services/3rd';
import RadioGroup from '../../../../../../../../components/Molecules/RadioGroup';
import {
  EMAIL_TEMPLATE,
  JOURNEY_TEMPLATE,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../../../../components/common/UIEditorPersonalization/utils';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import {
  isProduction,
  validateEmail,
} from '../../../../../../../../utils/web/utils';
import {
  AccordionDetailsCustom,
  AccordionStyled,
  AccordionSummaryCustom,
  CalloutTextInfo,
  // UIGrid,
  StyledFormHelperText as FormHelperText,
  HeadingAccordion,
  P,
  PromptText,
  StyledUIInputCalendar,
  Title,
  TitleToggle,
  WarningSMSRules,
  Wrapper,
  WrapperCenter,
  WrapperCenterFlexEnd,
  WrapperCenterFlexStart,
  TextAreaStyled,
  WrapperAreaTextMessage,
  TextExtraInfo,
  SupportiveText,
  WrapCountryFlag,
  WrapMultiLangDropdown,
  WrapDropdownCountryFlag,
  WrapItemCountryFlag,
} from './styles';
import DateTime from './_UI/DateTime';

// Atoms
import InputPreview from '../../../../../../../../components/Atoms/InputPreview';
import TextAreaBorder, {
  WrapperTextArea,
} from '../../../../../../../../components/Atoms/Text/TextAreaBorder';
import {
  CATALOG_CODE,
  CHANNEL_CODE,
  DATA_CATALOG_CODE,
  DATA_CHANNEL_SMS,
} from '../constant';
import {
  GEN_RATING_TEXT,
  ICON_TYPE_KEY,
  PREVIEW_FIELDS,
  RATING_SETTING_KEYS,
  SMALL_HEIGHT_FIELDS,
  TRACK_URL,
} from './constants';
// eslint-disable-next-line import/no-cycle
import { CONTENT_TYPES } from '../../../../../../../../components/Organisms/LineTemplate/constants';
import {
  handleValidateSlide,
  validateSameButtonAllSlides,
} from '../../../../../../../../components/Organisms/LineTemplate/utils';
import { DYNAMIC_KEY } from '../../../../../../../../components/common/UIEditorPersonalization/Input/utils';
import { getObjectPropSafely } from '../../../../../../../../components/common/UIEditorPersonalization/utils.3rd';
import {
  getCurrentOwnerId,
  getCurrentUserId,
  getToken,
} from '../../../../../../../../utils/web/cookie';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
// import Emojis from './Emojis';
import {
  checkEnableShortLink,
  deleteSymbolCharacters,
  getEditorWidth,
  getSmsLimit,
  getStyleEmojiIcon,
  handleChangeEffectExtra,
  hasTags,
  validateActionButton,
} from './utils';

import DateFnsUtils from '@date-io/date-fns';
import {
  FormControlLabel,
  RadioGroup as MuiRadioGroup,
  Radio,
} from '@material-ui/core';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import moment from 'moment';
import ActionButtons from '../../../../../../../../components/Molecules/ActionButtons';
import SelectIcon from '../../../../../../../../components/Molecules/SelectIcon';
import {
  getValidateFields,
  validateSetting,
} from '../../../../../../../../components/Organisms/DestinationSnapRender/utils';
import { REQUIRED_KEYS_TEXT } from '../../../../../../../../components/common/UIPairKeyValue/utils';
import MapGroupFields from '../../../../../../../../containers/MapGroupFields';
import MultiLanguageDropdown from '../../../../../../../../containers/MultiLanguageDropdown';
import { PreviewImage } from '../../../../../../../../containers/PreviewImage';
import { SlideBarWithFields } from '../../../../../../../../containers/SlideBarWithFields';
import TemplateListing from '../../../../../../../../containers/TemplateListing';
import useDebounce from '../../../../../../../../hooks/useDebounce';
import {
  CATALOG_CODES,
  TEMPLATE_TYPES,
  TEMPLATE_ZALO_OA_TYPES,
} from '../../../../../Destination/CreateV2/Design/Templates/constants';
import {
  PREFIX_PATTERN_LINE_MESSAGE,
  getPatternRegexEmoji,
  previewEmoji,
} from './Emojis/utils';
import ImageSizeChecker from './_UI/ImageSizeCheckerError';
import {
  getInputValueObjType,
  pickBroadcastDestDelivery,
  calculateSMSCount,
  determineShowExtraInfo,
  useChannelFlags,
} from './utils.variant';
import { ViewDetailsInformationIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { CountryFlag } from '../../../../../../../../components/common/InputLanguage/styled';
import { Category } from '../../../../../../../../containers/UIPreview/Webhook/AeonMall/constants';
import HelpText from './_UI/HelpText';
import WhatsappTemplate from './_UI/WhatsappTemplate';
import { validateWhatsappTemplate } from './_UI/WhatsappTemplate/validate';
import BasicInput from './_UI/BasicInput';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const {
  ACFC_APP_PUSH,
  SMART_INBOX,
  ANTSOMI_APP_PUSH,
  LINE,
  ZNS,
} = CATALOG_CODE;

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.form.js';

const MAP_TRANSLATE = {
  nameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `Name can't be empty`,
  ),
  spaceError: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_NAME,
    'Invalid name',
  ),
  inputEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_REQUIRED_INPUT,
    'Input can’t be empty',
  ),
  fieldNotEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `This field can't be empty
  `,
  ),
  messageRequired: getTranslateMessage(
    TRANSLATE_KEY._EMPTY_TEMPLATE_MESSAGE,
    'Please choose at least 1 template',
  ),
  quickTest: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Quick Test'),
  maxLengthError: getTranslateMessage(
    TRANSLATE_KEY._,
    'Limit of 120 characters',
  ),
  recommendedInfo: getTranslateMessage(
    TRANSLATE_KEY._,
    'Recommended size: 1040x1040',
  ),
  recommendedRatio: getTranslateMessage(
    TRANSLATE_KEY._,
    'Use image size of around 25 KB. Recommended aspect ratio (width:height): Android:  2.9:1 with buttons, 2.8:1 without buttons and iOS: 2:1',
  ),
  recommendedSize: getTranslateMessage(
    TRANSLATE_KEY._,
    'Supported file format is .png. Use image size of around 10 KB. Recommended aspect ratio (width:height) 1:1',
  ),
  smsError: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Limit of 459 characters, the characters must be in the first 128 characters of the ASCII code table.',
  ),
  notASCII: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_ASCII,
    'The content must not contain any accents',
  ),
  warnDynamicContent: getTranslateMessage(
    TRANSLATE_KEY._WARN_SMS_DYNAMIC_CONTENT,
    'The count of character is for reference only if the content  contains dynamic content or shortlink',
  ),
  errDestinationRemoved: getTranslateMessage(
    TRANSLATE_KEY._ERROR_JOURNEY_DES_REMOVE,
    'Destination does not exist',
  ),
  errDestinationDisabled: getTranslateMessage(
    TRANSLATE_KEY._ERROR_JOURNEY_DES_DISABLE,
    'Destination is disabled',
  ),
  hintSmsIMedia: getTranslateMessage(
    TRANSLATE_KEY._,
    'The content must end by a dot “.” and not contain any accents',
  ),
  errorNonAccented: getTranslateMessage(
    TRANSLATE_KEY._,
    'Kindly use non-accented characters when entering content',
  ),
  maxLengthUnder: x =>
    getTranslateMessage(
      TRANSLATE_KEY._,
      `The number of character must be under ${x}`,
      { x },
    ),
};

const styleHorizontal = { display: 'flex', flexDirection: 'row', gap: '65px' };

export const MAP_VALIDATE = {
  none: () => {
    let [isValidate, errors] = [true, []];

    return { errors, isValidate };
  },
  singleLineText: ({ isRequired, maxLength, errors = [], ...restProps }) => {
    const value = toString(restProps.value);
    // console.log(
    //   'value, isRequired, maxLength, errors = []',
    //   value,
    //   isRequired,
    //   maxLength,
    //   errors,
    // );
    const isAntsomiLine =
      restProps.channelCode === CHANNEL_CODE.LINE &&
      restProps.catalogCode === CATALOG_CODE.LINE;

    let [isValidate, errorsTmp] = [true, []];
    if (errors.length > 0) {
      isValidate = false;
      errorsTmp = errors;
    } else if (value === undefined && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim() === '' && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim().length === 0 && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errorsTmp = [`Invalid: maximum ${maxLength} characters`];
    } else if (isAntsomiLine && PREVIEW_FIELDS.includes(restProps.name)) {
      const smsLimit = getSmsLimit(restProps.name, restProps.catalogCode);
      const isMaxLength = value.length > smsLimit;

      if (isMaxLength) {
        isValidate = false;
        errorsTmp = [MAP_TRANSLATE.maxLengthError.replace('120', smsLimit)];
      }
    }

    return { errors: errorsTmp, isValidate };
  },
  numberCustomInput: ({ value, isRequired, maxLength, errors = [] }) => {
    let [isValidate, errorsTmp] = [true, []];
    if (errors.length > 0) {
      isValidate = false;
      errorsTmp = errors;
    } else if (value === undefined && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.nameEmpty];
    } else if (value === '' && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.nameEmpty];
    } else if (value && value.length === 0 && isRequired) {
      isValidate = false;
      errorsTmp = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value && value.length > maxLength) {
      isValidate = false;
      errorsTmp = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors: errorsTmp, isValidate };
  },
  imageUrl: ({ value, isRequired, channelCode, catalogCode, ...restProps }) => {
    const isChannelAppPush = channelCode === CHANNEL_CODE.APP_PUSH;
    const isAntsomiAppPush =
      isChannelAppPush && catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;

    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (isAntsomiAppPush) {
      const hasCheckSizeErrorAlready = [
        'ratingSelected',
        'ratingUnselected',
        'imageUrl',
      ].includes(restProps.name);

      if (hasCheckSizeErrorAlready) {
        return { errors: restProps.errors, isValidate: restProps.isValidate };
      }
    }

    return { errors, isValidate };
  },
  editor: ({ value, isRequired, ...props }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }

    return { errors, isValidate };
  },
  htmlEditor: ({ value, isRequired, ...props }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }

    return { errors, isValidate };
  },
  mediaTemplate: ({ value }) => {
    let [isValidate, errors] = [true, []];
    if (!value) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    } else if (!!value && value.properties && !value.properties.id) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    }

    return { errors, isValidate };
  },
  jsonTemplate: ({ value }) => {
    let [isValidate, errors] = [true, []];
    if (!value) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    } else if (!!value && value.properties && !value.properties.id) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    }

    return { errors, isValidate };
  },
  emailTemplate: ({ value }) => {
    let [isValidate, errors] = [true, []];
    if (!value) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    } else if (!!value && value.properties && !value.properties.id) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    }
    return { errors, isValidate };
  },
  journeyTemplate: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if (isRequired && _isEmpty(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.messageRequired];
    }

    return { isValidate, errors };
  },
  multiLineText: ({ value, isRequired, destinationInput, ...props }) => {
    let [isValidate, errors] = [true, []];
    if ((value || '').trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }

    const isChannelAppPush = props.channelCode === CHANNEL_CODE.APP_PUSH;
    const isAntsomiAppPush =
      isChannelAppPush && props.catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;
    const isZaloOA = props.catalogCode === CATALOG_CODE.ZALO_OA;
    const isAntsomiLine =
      props.channelCode === CHANNEL_CODE.LINE &&
      props.catalogCode === CATALOG_CODE.LINE;
    const isOneSignalAppPush =
      props.catalogCode === CATALOG_CODE.ONE_SIGNAL_APP_PUSH;

    const hasValidateMaxLength =
      isAntsomiAppPush || isZaloOA || isOneSignalAppPush;

    const smsLimit = getSmsLimit(
      props.name,
      props.catalogCode,
      _omit({ ...props, value, isRequired }, ['name', 'catalogCode']),
    );

    if (isAntsomiLine && props.name === 'text') {
      const valueSnap = _cloneDeep(value);
      const regexReplaceEmoji = getPatternRegexEmoji(
        PREFIX_PATTERN_LINE_MESSAGE,
      );

      const isMaxLength =
        typeof valueSnap === 'string' &&
        valueSnap.replaceAll(regexReplaceEmoji, '$').length > smsLimit;

      if (isMaxLength) {
        isValidate = false;
        errors = [MAP_TRANSLATE.maxLengthError.replace('120', smsLimit)];
      }

      return { errors, isValidate };
    }

    if (hasValidateMaxLength) {
      const isMaxLength = props.maxLength
        ? value.length > props.maxLength
        : value.length > smsLimit;

      if (isMaxLength) {
        isValidate = false;
        errors = [MAP_TRANSLATE.maxLengthError.replace('120', smsLimit)];
      }

      if (GEN_RATING_TEXT.includes(props.name)) {
        const ratingIconType = getInputValueObjType(
          _get(destinationInput, 'ratingIconType.value', ''),
        );
        const ratingScale = getInputValueObjType(
          _get(destinationInput, 'ratingScale.value', ''),
        );
        const numericName = Number(props.name.replace(/[^0-9]/g, ''));

        if (
          (ratingScale && numericName > +ratingScale) ||
          ratingIconType !== ICON_TYPE_KEY.TEXT_EMOJI
        ) {
          isValidate = true;
          errors = [];
        }
      }

      return { errors, isValidate };
    }

    return { errors, isValidate };
  },
  selectDropdown: ({
    value,
    isRequired,
    name = '',
    disabledOptions = [],
    arrDestinationNoPermission = [],
    ...props
  }) => {
    // console.log('props', props, disabledOptions, value, name);
    let [isValidate, errors] = [true, []];
    if (((isObject(value) && _isEmpty(value)) || !value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      name === 'workflowDestination' &&
      disabledOptions.length &&
      value.destinationId
    ) {
      const findIndex = disabledOptions.find(
        item => +item.destinationId === +value.destinationId,
      );

      if (findIndex) {
        errors = ['Destination is disabled'];
        isValidate = false;
      }
    } else if (
      name === 'workflowDestination' &&
      arrDestinationNoPermission.length &&
      value.destinationId
    ) {
      const findIndex = arrDestinationNoPermission.find(
        item => +item.destinationId === +value.destinationId,
      );

      if (findIndex) {
        errors = [`You don't have permission to use this destination anymore`];
        isValidate = false;
      }
    }
    return { errors, isValidate };
  },
  email: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    } else {
      isValidate = validateEmail(value);
      errors = isValidate ? [] : ['Invalid email'];
    }
    return { errors, isValidate };
  },
  password: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    }
    return { errors, isValidate };
  },
  selectIcon: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.fieldNotEmpty];
    }

    return { errors, isValidate };
  },
  customFields: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    const invalidValue =
      !value || (isString(value) && value.trim().length === 0);

    if (invalidValue && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.fieldNotEmpty];
    }

    return { errors, isValidate };
  },
  keyvalue: ({
    value,
    isRequired,
    catalogCode = '',
    templateId = '',
    ...props
  }) => {
    let [isValidate, errors] = [true, []];
    const isZaloZNS = catalogCode === CATALOG_CODE.ZNS;

    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (Array.isArray(value) && isRequired) {
      const isZaloOA = catalogCode === CATALOG_CODE.ZALO_OA;

      // Checking do not has same key in array
      if (isZaloOA) {
        let isEmptyItem = true;
        let isInValidRequireField = true;

        value.forEach((eachItem, index) => {
          let hasSameKey = false;
          const { key = '', value: valueItem = '' } = eachItem;
          const nextKeyIndex = index + 1;
          for (let i = nextKeyIndex; i <= value.length; i += 1) {
            const { key: keyNext = '' } = value[i] || '';

            if (key && keyNext && key === keyNext) {
              hasSameKey = true;
            }
          }

          if (
            !_isEmpty(key.trim()) &&
            [0, 1].includes(index) &&
            (key === REQUIRED_KEYS_TEXT.VI.NAME ||
              key === REQUIRED_KEYS_TEXT.EN.NAME ||
              key.includes(REQUIRED_KEYS_TEXT.VI.RAW_CODE) ||
              key.includes(REQUIRED_KEYS_TEXT.EN.RAW_CODE))
          ) {
            isInValidRequireField = false;
          }

          if (!_isEmpty(key.trim()) && !_isEmpty(valueItem.trim()))
            isEmptyItem = false;

          if (hasSameKey) {
            isValidate = !hasSameKey;
            errors = [
              getTranslateMessage(
                TRANSLATE_KEY._CONTENT_TABL_ERR_MESS,
                'The label could not be duplicates',
              ),
            ];
          }
        });

        if (
          isInValidRequireField &&
          templateId === TEMPLATE_ZALO_OA_TYPES.TRANSACTION
        ) {
          isValidate = false;
          errors = [getTranslateMessage(TRANSLATE_KEY._, 'Invalid label')];
        }

        if (isEmptyItem) {
          isValidate = false;
          errors = [MAP_TRANSLATE.fieldNotEmpty];
        }
      }
    } else if (isZaloZNS) {
      isValidate = Object.keys(value).every(
        each => each.trim() !== '' && value[each].trim() !== '',
      );
      errors = Object.keys(value).map(each => {
        if (each.trim() !== '' && value[each].trim() === '') {
          return MAP_TRANSLATE.fieldNotEmpty;
        }
        return null;
      });
    } else {
      isValidate = Object.keys(value).every(
        each => each.trim() !== '' && value[each].trim() !== '',
      );
      errors = isValidate ? [] : [MAP_TRANSLATE.inputEmpty];
    }

    return { errors, isValidate };
  },
  validated: () => ({ errors: [], isValidate: true }),
  checkbox: () => ({ errors: [], isValidate: true }),
  checkboxGrid: () => ({ errors: [], isValidate: true }),
  radio: () => ({ errors: [], isValidate: true }),
  inputUnit: () => ({ errors: [], isValidate: true }),
  group: ({
    value,
    isRequired,
    name = '',
    catalogCode = '',
    currentTemplateInput = '',
    options,
    ...props
  }) => {
    const errors = [{}];
    let isValidate = true;

    if (isRequired) {
      if (_isEmpty(value)) {
        errors[0].global = MAP_TRANSLATE.fieldNotEmpty;
        isValidate = false;
      }
    }

    if (
      catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH &&
      name === 'advButton'
      // && currentTemplateInput === TEMPLATE_KEYS.RATING
    ) {
      Object.keys(value).forEach(each => {
        const { advBtnLabel = '' } = value[each] || {};
        const buttonSetting = getObjectPropSafely(
          () => options[0].inputs.advBtnLabel,
          {},
        );
        let errorMessage = '';

        if (buttonSetting.isRequired && !advBtnLabel.trim().length) {
          errorMessage = MAP_TRANSLATE.fieldNotEmpty;
        }

        if (advBtnLabel && typeof buttonSetting.validate === 'function') {
          const error = buttonSetting.validate({
            ...buttonSetting,
            value: advBtnLabel,
          });

          if (error && !error.isValidate) {
            errorMessage = getObjectPropSafely(() => error.errors[0], '');
          }
        }

        if (errorMessage) {
          _set(errors[0], `${each}.advBtnLabel`, errorMessage);
          isValidate = _isEmpty(errorMessage);
        }
      });
    }

    return { errors, isValidate };
  },
  tagButtons: () => ({ errors: [], isValidate: true }),
  actionButton: ({ value, isRequired }) => {
    const errors = [];

    if (isRequired && Array.isArray(value)) {
      value.forEach(each => {
        const errKeys = validateActionButton(each);

        errors.push(errKeys);
      });
    }

    return { errors, isValidate: errors.every(err => _isEmpty(err)) };
  },
  markup: () => ({ errors: [], isValidate: true }),
  anchorSwitch: () => ({ errors: [], isValidate: true }),
  previewImage: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if (isRequired && _isEmpty(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.fieldNotEmpty];
    }

    return {
      errors,
      isValidate,
    };
  },
  contentSource: csProps => {
    const { value, isRequired, isHidden } = csProps;
    let [isValidate, errors] = [true, []];

    if (isRequired && !isHidden) {
      const itemTypeId = _get(value, 'groups[0].itemTypeId');

      if (_isEmpty(value) || _isEmpty(value.groups) || !itemTypeId) {
        isValidate = false;
        errors = [MAP_TRANSLATE.fieldNotEmpty];
      }
    }

    return { isValidate, errors };
  },
  snapshotRender: ({
    value = {},
    isRequired,
    validateConfigs = {},
    channelCode = '',
    catalogCode = '',
  }) => {
    let [isValidate, errors] = [true, []];
    if (isRequired) {
      const { data = [] } = value;

      if (_isEmpty(value) || _isEmpty(data)) {
        isValidate = false;
        errors = [MAP_TRANSLATE.fieldNotEmpty];
      } else {
        // Data của destinationInputs được cache khi init nên value & initValue có thể cần cần phải refill lại value sau khi "getValidateFields"
        const { dynamicFields = [], destinationInputs = {} } = validateConfigs;

        data.forEach(each => {
          const { template = '' } = each;

          let templateId = template;
          if (typeof templateId === 'object') {
            templateId = template.value;
          }
          let destCloned = _cloneDeep(destinationInputs);

          if (_has(destCloned, 'imagemapType')) {
            destCloned = {
              ...(destCloned || {}),
              imagemapType: {
                ...(getObjectPropSafely(() => destCloned.imagemapType) || {}),
                value: getObjectPropSafely(() => each.imagemapType),
              },
            };
          }

          const validateFields = getValidateFields({
            dynamicFields,
            destinationInputs: destCloned,
            activeTemplate: templateId,
          });

          if (!validateFields.includes('template')) {
            validateFields.push('template');
          }

          // refill data into destination inputs -> make data destination input exactly
          validateFields.forEach(eachField => {
            if (destCloned[eachField]) {
              destCloned[eachField].value = each[eachField];
              destCloned[eachField].isRequired = true;
            }
          });

          const isValidateItem = validateSetting({
            activeId: 'exampleId',
            catalogCode,
            channelCode,
            dynamicFields: validateFields,
            destinationInputs: destCloned,
            setData: () => {},
          });

          if (!isValidateItem) {
            isValidate = false;
            errors.push(MAP_TRANSLATE.fieldNotEmpty);
          }
        });
      }
    }

    return { errors, isValidate };
  },
  sticker: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.fieldNotEmpty];
    }

    return { errors, isValidate };
  },
  lineTemplate: ({ value: dataLine = {}, templateId = '', isRequired }) => {
    if (!isRequired) return { errors: [], isValidate: true };

    const { contentType = '' } = dataLine;
    const slideListValidate = [];
    let errors = [];

    if (contentType === CONTENT_TYPES.STATIC && !_isEmpty(dataLine)) {
      const { slideList = [], slideMaps = {} } = dataLine[contentType];

      if (Array.isArray(slideList)) {
        slideList.forEach(eachSlide => {
          const slideItem = slideMaps[eachSlide.slideId];
          const isValid = handleValidateSlide({
            contentType,
            templateId,
            slideActiveId: eachSlide.slideId,
            slideInfo: slideItem,
            buttonId: '',
            setValidateSlides: '',
          });
          slideListValidate.push(isValid);

          if (!isValid) errors.push(eachSlide.slideId);
        });
      }

      if (templateId === TEMPLATE_TYPES.CAROUSEL) {
        const listSlideError = validateSameButtonAllSlides(dataLine);

        if (!_isEmpty(listSlideError)) {
          slideListValidate.push(false);
          errors = _union(_cloneDeep(errors), listSlideError);
        }
      }
    }

    return { errors, isValidate: slideListValidate.every(each => each) };
  },
  number: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (Number.isNaN(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLangInput: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(getDefaultVal(value).trim()) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multilanguage: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  singleLineAddPersonalize: ({
    name,
    value,
    isRequired,
    baseHintInfo,
    maxLength,
    contentAccentType,
    maxLengthAccented,
    maxLengthUnaccented,
    ...props
  }) => {
    // console.log('validate singleLineAddPersonalize', {
    //   value,
    //   isRequired,
    //   errors,
    // });
    let [isValidate, errors] = [true, []];

    if (props.isHidden) {
      return { isValidate, errors };
    }

    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }

    const isChannelSMS = props.channelCode === CHANNEL_CODE.SMS;
    const isChannelAppPush = props.channelCode === CHANNEL_CODE.APP_PUSH;
    const isChannelSmartInbox = props.channelCode === CHANNEL_CODE.SMART_INBOX;
    const isAntsomiAppPush =
      isChannelAppPush && props.catalogCode === ANTSOMI_APP_PUSH;
    const isAntsomiLine = props.catalogCode === LINE;
    const isZaloZNS = props.catalogCode === ZNS;

    const smsLimit = getSmsLimit(name, props.catalogCode, {
      baseHintInfo,
      contentAccentType,
      maxLength,
      maxLengthAccented,
      maxLengthUnaccented,
    });

    // Not validate for assigneeId if has tags
    if (
      name === 'assigneeId' &&
      baseHintInfo?.isCountCharacter &&
      hasTags(value)
    ) {
      return { errors, isValidate };
    }

    if (
      isChannelSmartInbox ||
      isAntsomiAppPush ||
      baseHintInfo?.isCountCharacter ||
      (isAntsomiLine && name !== 'imagemapLandingPageUrl') ||
      (isZaloZNS && name === 'trackingId')
    ) {
      const isMaxLength = value.length > smsLimit;

      if (isMaxLength) {
        isValidate = false;
        errors = [MAP_TRANSLATE.maxLengthError.replace('120', smsLimit)];
      }
      return { errors, isValidate };
    }

    if (isChannelSMS) {
      const removePattern = /#{.*?}|[\n\t\r]/gm; // tags || newlines || carriage return

      // remove personalization tags and new lines to validate
      const valueValidate = value.replace(removePattern, '');

      // const isNotASCII = /[^[:ascii:]]/g.test(valueValidate);
      const isNotASCII = /[^ -~]/gi.test(valueValidate);
      // const isContainSpecial = /[@_{}[\]|~\^\\\$]/gi.test(valueValidate);
      // const isContainSpecial = /[@{}[\]|~\^\\\$]/gi.test(valueValidate);
      const isMaxLength = value.length > smsLimit;
      const validateByAccent = ['unaccented_text', 'accented_text'].includes(
        contentAccentType,
      );

      if (validateByAccent) {
        const accented = contentAccentType === 'accented_text';
        const unaccented = contentAccentType === 'unaccented_text';

        const isErrorAccented = accented && maxLengthAccented && isMaxLength;
        const isErrorUnaccented =
          unaccented && maxLengthUnaccented && (isMaxLength || isNotASCII);

        if (isErrorAccented || isErrorUnaccented) {
          isValidate = false;

          let errorMsg = MAP_TRANSLATE.smsError?.replace('459', smsLimit);

          if (isErrorAccented) {
            errorMsg = MAP_TRANSLATE.maxLengthError?.replace('120', smsLimit);
          }

          errors = [errorMsg];
        }
      } else if (isNotASCII || isMaxLength) {
        isValidate = false;
        errors = [MAP_TRANSLATE.smsError.replace('459', smsLimit)];
      }
      return { errors, isValidate };
    }

    if (
      name === 'preheader' &&
      typeof maxLength === 'number' &&
      value.length > maxLength
    ) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }

    return { errors, isValidate };
  },
  textArea: props => {
    const { allowASCII = false, value = '', isRequired } = props;
    let [isValidate, errors] = [true, []];

    if (isRequired && value.trim() === '') {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }

    if (!allowASCII) {
      const removePattern = /#{.*?}|[\n\t]/gm; // tags || newlines

      // remove personalization tags and new lines to validate
      const valueValidate = value.replace(removePattern, '');

      const isNotASCII = /[^ -~]/gi.test(valueValidate);

      if (isNotASCII) {
        isValidate = false;
        errors = [MAP_TRANSLATE.errorNonAccented];
      }
    }
    return { isValidate, errors };
  },
  slideBar: props => {
    let errors = [];
    const { value = [], isRequired = false } = props;
    const { basicInputs = [], inputs = {} } = _get(props, 'fields', {});

    if (isRequired) {
      if (_isEmpty(value)) {
        errors = [{ global: MAP_TRANSLATE.fieldNotEmpty }];
      } else if (Array.isArray(value) && Array.isArray(basicInputs)) {
        value.forEach(item => {
          const errItem = {};

          basicInputs.forEach(itemKey => {
            const { isRequired: isRequiredItem = false } = _get(
              inputs,
              itemKey,
              {},
            );

            if (isRequiredItem && _isEmpty(item[itemKey])) {
              errItem[itemKey] = MAP_TRANSLATE.fieldNotEmpty;
            }
          });

          errors.push(errItem);
        });
      }
    }

    return {
      isValidate: errors.every(_isEmpty),
      errors,
    };
  },

  variantName: ({ ...params }) => {
    const { value, isRequired, maxLength, mapVariantName, activeId } = params;
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim().length === 0 && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    } else if (typeof mapVariantName === 'object') {
      // validate duplicate name
      Object.keys(mapVariantName).forEach(key => {
        // console.log('key', key, mapVariantName[key]);
        if (key != activeId && mapVariantName[key] == value) {
          isValidate = false;
          errors = [
            getTranslateMessage(
              TRANSLATE_KEY._NOTI_SAME_NAME,
              'This name already existed',
            ),
          ];
        }
      });
    }
    return { errors, isValidate };
  },
  multiLangPersonalize: props => {
    const { value, isRequired, maxLength } = props;
    let [isValidate, errors] = [true, []];

    for (const input of value) {
      if (isRequired && !input?.value?.length) {
        isValidate = false;
        errors.push({
          lang: input.lang,
          message: MAP_TRANSLATE.nameEmpty,
        });
      } else if (maxLength && input?.value?.length > maxLength) {
        isValidate = false;
        errors.push({
          lang: input.lang,
          message: MAP_TRANSLATE.maxLengthUnder(maxLength),
        });
      }
    }

    return { errors, isValidate };
  },
  multiLangDropdown: props => {
    const { value, isRequired, isHidden } = props;

    let [isValidate, errors] = [true, []];

    if (isHidden) {
      return { errors, isValidate };
    }

    if (isRequired && !value.targetId) {
      isValidate = false;
      errors.push(MAP_TRANSLATE.nameEmpty);
    }

    return { errors, isValidate };
  },
  datetime: props => {
    const { value, isRequired, isHidden } = props;
    let [isValidate, errors] = [true, []];

    if (isHidden) {
      return { errors, isValidate };
    }

    if ((!value || !value?.date || !value?.time) && isRequired) {
      isValidate = false;
      errors.push(MAP_TRANSLATE.fieldNotEmpty);
    }

    return { errors, isValidate };
  },
  whatsappTemplate: props => {
    return validateWhatsappTemplate(props);
  },
  basicInput: props => {
    const { value, isRequired, isHidden, maxLength } = props;

    let [isValidate, errors] = [true, []];

    if (isHidden) {
      return { errors, isValidate };
    }

    if (isRequired && typeof value === 'string') {
      if (value.trim() === '') {
        isValidate = false;
        errors = [MAP_TRANSLATE.fieldNotEmpty];
      } else if (typeof maxLength === 'number' && value.length > maxLength) {
        isValidate = false;
        errors = [MAP_TRANSLATE.maxLengthUnder(maxLength)];
      }
    }

    return { isValidate, errors };
  },
};

export const MAP_INPUT_TYPE = {
  none: () => null,
  singleLineText: props => {
    const isChannelEmail = props.channelElement === CHANNEL_CODE.EMAIL;
    const isChannelMessenger =
      props.channelElement === CHANNEL_CODE.CONVERSATION;
    const isChannelWebHook = props.channelElement === CHANNEL_CODE.WEBHOOK;
    const isAntsomiLine = props.catalogCode === CATALOG_CODE.LINE;
    const isHideQuickTest =
      [CATALOG_CODE.LINE_RICH_MENU].includes(props.catalogCode) ||
      props.isHideQuickTest;
    const isExistDataDestination = DATA_CATALOG_CODE.some(
      // 1 SỐ DESTINATION DÙNG CHUNG VỚI NHỮNG CHANNELCODE LÀ WEBHOOK NÊN CẦN STYLE RIÊNG NHỮNG CASE NÀY
      data => data === props.catalogCode,
    );
    const isExistChannelSms = DATA_CHANNEL_SMS.some(
      data => data === props.channelElement,
    );
    const isChannelEmailBlast = isChannelEmail && props.isBlastCampaign;

    const { isSendMultiple, isBroadcast } = pickBroadcastDestDelivery(
      props.destinationSettings,
    );
    const [valueBlast, setValueBlast] = useState(props.value);
    const debounceValueBlast = useDebounce(valueBlast, 500);

    useEffect(() => {
      if (props.isBlastCampaign) {
        props.onChange(props.name)(debounceValueBlast);
      }
    }, [debounceValueBlast]);

    useEffect(() => {
      if (props.isBlastCampaign && props.value !== valueBlast) {
        setValueBlast(props.value);
      }
    }, [props.value]);

    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;
    const handleGetStyle = () => {
      if (props.isBlastCampaign) {
        return '50%';
      }
      if (props.step === 1) {
        return '30%';
      }

      if (props.name === 'variantName') {
        const elementWidth = [
          CHANNEL_CODE.EMAIL,
          CHANNEL_CODE.WEB_PER,
        ].includes(props.channelElement)
          ? '50%'
          : '100%';

        return `calc(${elementWidth} - 120px)`;
      }

      return '100%';
    };

    const maxLengthTxt = getSmsLimit(props.name, props.catalogCode);
    // const [caretPosition, setCaretPosition] = useState({});

    return (
      // variantName, campaignName
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title
              $colorText={props.colorText}
              className={props.classes.spacingTitle}
              style={
                props.isBlastCampaign
                  ? {
                      whiteSpace: 'break-spaces',
                      wordBreak: 'initial',
                      textAlign: 'right',
                    }
                  : {}
              }
            >
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
          style={isAllowAdjustColumn ? { flex: 1 } : {}}
        >
          <InputPreview
            value={props.value}
            type="input"
            className={`${props.classes.fontSize}, ${props.classes.content}`}
            isViewMode={props.isViewMode}
            inputWrapperStyle={
              props.isFullWidth && props.componentName === 'variantName'
                ? { display: 'flex' }
                : {}
            }
          >
            {props.enableShortLinkSpecific ? (
              <TinymceEditor
                {...props}
                isAppendBOContentSource={props.isAppendBOContentSource}
                appendPersonalizeType={props.appendPersonalizeType}
                typeComponent="input"
                onChange={value => {
                  props.onChange(props.name)(deleteSymbolCharacters(value));
                }}
                enableShortLink
                isForceHideBtnPersonalization={isBroadcast && !isSendMultiple}
                onChangeOthers={props.onChangeOthers(props.name)}
                initData={props.initValue}
                value={props.value}
                // setCaretPosition={setCaretPosition}
                hiddenDynamicOption={[DYNAMIC_KEY.addPer]}
                width={props.isFullWidth ? '100%' : handleGetStyle()}
              />
            ) : (
              <UITextField
                id={props.name}
                value={props.isBlastCampaign ? valueBlast : props.value}
                onChange={value =>
                  props.isBlastCampaign
                    ? setValueBlast(value)
                    : props.onChange(props.name)(value)
                }
                firstText={
                  isAntsomiLine && PREVIEW_FIELDS.includes(props.name)
                    ? null
                    : props.errors[0]
                }
                placeholder={props.placeholder}
                maxLength={!isAntsomiLine ? maxLengthTxt : null}
                textFieldProps={{
                  disabled: props.disabled,
                  size: 'small',
                  multiline: false,
                  rowsMax: 1,
                  style: {
                    width: props.isFullWidth
                      ? '100%'
                      : // : props.step === 2 &&
                        //   props.componentName === 'variantName' &&
                        //   props.isTitleAlignLeft
                        // ? 'calc(100% - 120px)'
                        handleGetStyle(),
                  },
                  className: classNames({
                    'input-text-field-error':
                      isAntsomiLine &&
                      PREVIEW_FIELDS.includes(props.name) &&
                      !!props.errors[0],
                  }),
                  // className: isChannelEmail && props.step === 2 ||
                  //            isChannelMessenger || isChannelWebHook ?
                  //           (props.step === 2 && (isChannelMessenger || isChannelWebHook) ? 'width-50' : 'width-30')
                  //           :'width-100',
                  // id: 'standard-basic',
                  error:
                    isAntsomiLine && PREVIEW_FIELDS.includes(props.name)
                      ? false
                      : !!props.errors[0],
                }}
              />
            )}
            {isAntsomiLine && PREVIEW_FIELDS.includes(props.name) && (
              <WarningSMSRules width="100%" style={{ marginTop: 4 }}>
                <div className="d-flex align-items-center">
                  <FormHelperText
                    id="component-helper-text"
                    error={!!props.errors[0]}
                    style={{ width: '100%' }}
                  >
                    {props.errors[0]}
                  </FormHelperText>
                  <Fragment>
                    <UITippy
                      content={MAP_TRANSLATE.maxLengthError.replace('120', 400)}
                    >
                      <InfoOutlinedIcon className="icon-info-rule" />
                    </UITippy>

                    <p
                      className="text-rule"
                      style={{
                        whiteSpace: 'nowrap',
                        margin: '0px',
                      }}
                    >
                      {(props.value && props.value.length) || 0}/{400}{' '}
                      characters
                    </p>
                  </Fragment>
                </div>
              </WarningSMSRules>
            )}
            {props.step === 2 &&
            props.componentName === 'variantName' &&
            !isHideQuickTest ? (
              <Button
                onClick={props.toggleDestinationModalTesting}
                disabled={props.disabledQuickTest}
                className={`${
                  props.isTitleAlignLeft || props.name === 'variantName'
                    ? 'm-right-0'
                    : 'm-right-2'
                } m-left-4`}
                style={{
                  flexShrink: props.isFullWidth ? 0 : 'unset',
                }}
              >
                <Flex align="center">
                  <IconXlab
                    name="visibility"
                    fontSize="20px"
                    color={props.disabledQuickTest ? '#A2A2A2' : '#005eb8'}
                    className="m-right-1"
                  />
                  {MAP_TRANSLATE.quickTest}
                </Flex>
              </Button>
            ) : null}
          </InputPreview>
        </Grid>
      </>
    );
  },
  password: props => (
    <>
      <Grid
        item
        sm={props.colLeft || 3}
        className={classNames('grid-col-left', props.classes.styledChannel)}
      >
        <WrapperCenterFlexEnd>
          <Title className={props.classes.spacingTitle}>
            {props.isRequired && !props.isViewMode && (
              <span className={props.classes.redStar}>* </span>
            )}
            {props.label}
          </Title>
          {!!props.errors[0] && (
            <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
          )}
        </WrapperCenterFlexEnd>
      </Grid>
      <Grid
        item
        sm={props.colRight || 9}
        className={classNames('grid-col-right')}
      >
        <UIPassword
          disabled
          id={props.name}
          value={props.value}
          onChange={props.onChange(props.name)}
          firstText={props.errors[0]}
          textFieldProps={{
            disabled: props.disabled,
            type: 'password',
            size: 'small',
            multiline: false,
            rowsMax: 1,
            className: 'width-100',
            error: !!props.errors[0],
          }}
        />
      </Grid>
    </>
  ),
  imageUrl: props => {
    const { placeholder = '', uploadConfig = {} } = props;
    const {
      checkingSize = false,
      showExtraInfo = false,
      message = '',
      alignment = '',
    } = uploadConfig;
    const isAlignLeft = alignment === 'left';
    const isAlignCenter = alignment === 'center';

    const isChannelWebHook = props.channelElement === CHANNEL_CODE.WEBHOOK;
    const isExistDataDestination = DATA_CATALOG_CODE.some(
      // 1 SỐ DESTINATION DÙNG CHUNG VỚI NHỮNG CHANNELCODE LÀ WEBHOOK NÊN CẦN STYLE RIÊNG NHỮNG CASE NÀY
      data => data === props.catalogCode,
    );
    const isExistChannelSms = DATA_CHANNEL_SMS.some(
      data => data === props.channelElement,
    );
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;
    const isZaloOA = props.catalogCode === CATALOG_CODES.ZALO_OA;
    const isAntsomiLine = props.catalogCode === CATALOG_CODES.LINE_APP;
    const isAntsomiAppPush =
      props.catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;
    const isChannelEmail = props.channelElement === CHANNEL_CODE.EMAIL;
    const isChannelEmailBlast = isChannelEmail && props.isBlastCampaign;

    const hasCheckSizeWithCatalog = [CATALOG_CODE.ANTSOMI_APP_PUSH].includes(
      props.catalogCode,
    );
    const hasCheckSizeWithName = [
      'ratingUnselected',
      'ratingSelected',
      'imageUrl',
    ].includes(props.name);
    const isShowCheckSize =
      (hasCheckSizeWithCatalog && hasCheckSizeWithName) || checkingSize;
    let limitSize = 10;
    if (props.limitSizeImage || props.name === 'imageUrl') {
      limitSize = props.limitSizeImage || 25;
    }

    let isShowExtraInfo = showExtraInfo;
    let calloutText = message || MAP_TRANSLATE.recommendedInfo;
    if (
      (isAntsomiLine && props.name === 'imagemapUrl') ||
      (isAntsomiAppPush &&
        [
          'imageUrl',
          RATING_SETTING_KEYS.SELECTED,
          RATING_SETTING_KEYS.UNSELECTED,
        ].includes(props.name))
    ) {
      isShowExtraInfo = true;

      if (isAntsomiAppPush) {
        if (props.name === 'imageUrl') {
          calloutText = MAP_TRANSLATE.recommendedRatio;
        }

        if (
          [
            RATING_SETTING_KEYS.SELECTED,
            RATING_SETTING_KEYS.UNSELECTED,
          ].includes(props.name)
        ) {
          calloutText = MAP_TRANSLATE.recommendedSize;
        }
      }
    }

    const isColumn = isAntsomiAppPush || isAlignLeft || isAlignCenter;

    // Constants
    const userId = getCurrentUserId();
    const token = getToken();
    return (
      <>
        <Grid
          item
          sm={
            isChannelEmailBlast
              ? 2
              : isAllowAdjustColumn
              ? 'auto'
              : props.colLeft || 3
          }
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
            ...(isZaloOA && { paddingTop: 0 }),
          }}
        >
          <WrapperCenterFlexEnd>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          <InputPreview
            value={props.value}
            isViewMode={props.isViewMode}
            type="input"
            className="pd-left-5"
          >
            <UploadImage
              isInputMode
              domainMedia={
                isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
              }
              slug="api/v1"
              width={
                props.isFullWidth
                  ? '100%'
                  : isExistChannelSms
                  ? isChannelWebHook
                    ? isExistDataDestination
                      ? '75%'
                      : '50%'
                    : '75%'
                  : '50%'
              }
              paramConfigs={{
                token,
                userId,
                accountId: userId,
              }}
              selectedImage={{
                url: props.value,
              }}
              placeholder={placeholder || 'Upload or input URL'}
              onChangeImage={image => {
                props.onChange(props.name)((image && image.url) || '');
              }}
              onRemoveImage={() => {
                props.onChange(props.name)('');
              }}
              maxSize={props.maxSize}
            />
            {/* <UIModalInsert
              value={props.value}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              width={
                props.isFullWidth
                  ? '100%'
                  : isExistChannelSms
                  ? isChannelWebHook
                    ? isExistDataDestination
                      ? '75%'
                      : '50%'
                    : '75%'
                  : '50%'
              }
              // onChange={() => {}}
            /> */}
            {isShowCheckSize && (
              <ImageSizeChecker
                name={props.name}
                url={props.value}
                limitSize={limitSize}
                unit="KB"
                ms={400}
                callback={props.callback}
                errors={props.errors}
              />
            )}
            {isShowExtraInfo && (
              <WrapperCenter
                style={{
                  flexDirection: isColumn ? 'column' : 'row',
                  alignItems:
                    isAntsomiAppPush || isAlignLeft ? 'flex-start' : 'center',
                }}
              >
                {!isShowCheckSize && (
                  <FormHelperText
                    id="component-helper-text"
                    error={!!props.errors[0]}
                  >
                    {props.errors}
                  </FormHelperText>
                )}
                <CalloutTextInfo
                  style={{
                    textAlign: isAntsomiAppPush ? 'left' : alignment || 'right',
                  }}
                >
                  {calloutText}
                </CalloutTextInfo>
              </WrapperCenter>
            )}
          </InputPreview>
          {!isShowExtraInfo && !isShowCheckSize && (
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          )}
        </Grid>
      </>
    );
  },
  selectDropdown: props => {
    // const isChannelMessenger =
    //   props.channelElement === CHANNEL_CODE.CONVERSATION;
    const isChannelWebHook = props.channelElement === CHANNEL_CODE.WEBHOOK;
    const isChannelSmartInbox =
      props.channelElement === CHANNEL_CODE.SMART_INBOX;
    const isSmartInbox =
      isChannelSmartInbox && props.catalogCode === CATALOG_CODE.SMART_INBOX;
    const isExistDataDestination = DATA_CATALOG_CODE.some(
      // 1 SỐ DESTINATION DÙNG CHUNG VỚI NHỮNG CHANNELCODE LÀ WEBHOOK NÊN CẦN STYLE RIÊNG NHỮNG CASE NÀY
      data => data === props.catalogCode,
    );
    // const isDestionation = getObjectPropSafely(() => props.value.destinationId);
    // const statusDestation = getObjectPropSafely(() => props.value.status);
    const isExistChannelSms = DATA_CHANNEL_SMS.some(
      data => data === props.channelElement,
    );
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;

    const handleGetStyle = (isFullWidth = false) => {
      if (isFullWidth) return '100%';

      if (props.isBlastCampaign) {
        return '50%';
      }
      if (props.step === 1) {
        return '30%';
      }
      if (isExistChannelSms) {
        if (isChannelWebHook) {
          if (isExistDataDestination) {
            return '75%';
          }
          return '50%';
        }
        return '75%';
      }
      return '50%';
      // if (isExistDataDestination) {
      //   return '50%';
      // }
      // if (isChannelWebHook || isChannelMessenger) {
      //   return '50%';
      // }
      // return '50%';
    };

    let valueTemp = props?.value || '';

    if (typeof valueTemp === 'string') {
      valueTemp = [...(props.options || [])].find(
        each => each?.value === valueTemp,
      );
    }

    const handleChangeDataDropdown = dataIn => {
      try {
        if (
          isSmartInbox &&
          props.name === 'inboxTemplate' &&
          typeof props.setEmitSetDefaultSmartInbox === 'function'
        ) {
          // For case reset data Heading & Content of Smart Inbox
          props.setEmitSetDefaultSmartInbox(prev => prev + 1);
        }

        if (typeof props.onChange(props.name) === 'function') {
          props.onChange(props.name)(dataIn);
        }

        handleChangeEffectExtra({
          channelCode: props.channelElement,
          catalogCode: props.catalogCode,
          name: props.name,
          data: dataIn,
          callback: props.callback,
        });
      } catch (error) {
        addMessageToQueue({
          path:
            'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.form.js',
          func: 'handleChangeDataDropdown',
          data: error.stack,
        });
        // eslint-disable-next-line no-console
        console.log(error);
      }
    };

    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
          style={isAllowAdjustColumn ? { flex: 1 } : {}}
        >
          <InputPreview
            isViewMode={props.isViewMode}
            type="input"
            className={props.classes.content}
            value={
              typeof props.value !== 'object' ? props.value : props.value.label
            }
          >
            <UISelect
              errors={props.errors}
              onlyParent
              use="tree"
              isSearchable
              options={props.options}
              value={valueTemp || props.value}
              onChange={handleChangeDataDropdown}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              disabled={props.disabled}
              labelWidth={() => handleGetStyle(props.isFullWidth)}
              searchNodataLabel="No data available"
              // isErrBtnBottom={
              //   isDestionation &&
              //   (statusDestation === '2' || statusDestation === '3')
              // }
            />
            {/* {isDestionation && (
              <StyleTextError>
                {statusDestation === '2'
                  ? MAP_TRANSLATE.errDestinationDisabled
                  : statusDestation === '3' &&
                    MAP_TRANSLATE.errDestinationRemoved}
              </StyleTextError>
            )} */}
          </InputPreview>
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </Grid>
      </>
    );
  },
  multiLineText: props => {
    const transformValue = deleteSymbolCharacters(props.value);
    const isChannelViber = props.channelElement === CHANNEL_CODE.VIBER;
    const isChannelAppPush = props.channelElement === CHANNEL_CODE.APP_PUSH;
    const isAntsomiAppPush =
      isChannelAppPush && props.catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;

    const isOneSignalAppPush =
      props.catalogCode === CATALOG_CODE.ONE_SIGNAL_APP_PUSH;
    const isChannelLine = props.channelElement === CHANNEL_CODE.LINE;
    const isZaloOA = props.catalogCode === CATALOG_CODE.ZALO_OA;
    const isChannelTelegram = props.channelElement === CHANNEL_CODE.TELEGRAM;
    const isBlastTelegram = isChannelTelegram && props.isBlastCampaign;
    // eslint-disable-next-line react-hooks/rules-of-hooks
    // const inputRef = useRef();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    // const [caretPosition, setCaretPosition] = useState({});
    const { isViewMode } = props;

    const isSerializeEmoji = [isChannelLine].every(isSerialize => isSerialize);
    const smsLimit = getSmsLimit(
      props.name,
      props.catalogCode,
      _omit(props, ['name', 'catalogCode']),
    );
    let emojiCount = 0;
    let contentLineLength = 0;

    if (isChannelLine) {
      const regexEmojiCode = getPatternRegexEmoji(PREFIX_PATTERN_LINE_MESSAGE);
      const valueSnap = _cloneDeep(props.value);

      if (typeof valueSnap === 'string') {
        contentLineLength =
          valueSnap.replaceAll(regexEmojiCode, '$').length || 0;
      }

      emojiCount =
        document.querySelectorAll('.insert-word.insert-emoji').length || 0;
    }

    const { isBroadcast, isSendMultiple } = pickBroadcastDestDelivery(
      props.destinationSettings,
    );

    const isShowCountCharacter =
      props.isCountCharacters || isChannelLine || isZaloOA || isBlastTelegram;
    const useLength = props.isCountCharacters || isZaloOA || isChannelTelegram;
    const isShowCountEmoji = isChannelLine || isBlastTelegram;
    const isShowErrorOuter =
      !isAntsomiAppPush && !isChannelLine && !isZaloOA && !isOneSignalAppPush;

    const enableShortLink = checkEnableShortLink({
      defaultEnabled: props.defaultEnableShortLink,
      channelCode: props.channelElement,
      catalogCode: props.catalogCode,
      name: props.name,
      isHideShortLink: props.isHideShortLink,
    });

    const hiddenDynamicOption = [];

    if (!props.canAddPersonalization) {
      hiddenDynamicOption.push(DYNAMIC_KEY.addPer);
    }

    // const {
    //   position: positionEmojiIcon,
    //   iconName: iconXlabName,
    //   alignment,
    //   iconStyle,
    // } = getStyleEmojiIcon({
    //   catalogCode: props.catalogCode,
    //   channelCode: props.channelElement,
    //   name: props.name,
    //   extraData: {
    //     isBlastCampaign: props.isBlastCampaign,
    //   },
    // });

    const hasEmojis =
      isChannelViber ||
      isChannelLine ||
      isZaloOA ||
      props.canAddPersonalization ||
      isAntsomiAppPush;

    // const editorWidth = getEditorWidth(
    //   props.channelElement,
    //   props.isBlastCampaign,
    // );

    const renderContent = (isBroadcastTiny, isSendMultipleTiny) => {
      if (isViewMode) {
        return (
          <InputPreview
            id={props.id}
            name={props.name}
            className={props.className}
            borderStyle={props.borderStyle}
            height={props.height}
            minHeight={props.minHeight}
            maxHeight={props.maxHeight}
            isViewMode={props.isViewMode}
            value={
              isSerializeEmoji
                ? previewEmoji(
                    props.value,
                    {
                      style: { height: '12px', width: '12px' },
                    },
                    props.channelElement,
                  )
                : props.value
            }
          />
        );
      }

      if (hasEmojis || _get(props, 'emoji.use', false)) {
        // const isNoBorder =
        //   _get(props, 'emoji.isInputHeightSmall', false) ||
        //   (isZaloOA && SMALL_HEIGHT_FIELDS.includes(props.name)) ||
        //   (props.isBlastCampaign && isAntsomiAppPush) ||
        //   (isChannelTelegram && !props.isBlastCampaign) ||
        //   (isAntsomiAppPush && !props.isBlastCampaign);

        return (
          <>
            <TinymceEditor
              {...props}
              placeHolder={props.placeholder}
              typeComponent="input"
              onChange={value => {
                props.onChange(props.name)(deleteSymbolCharacters(value));
              }}
              enableShortLink={enableShortLink || props.enableShortLinkSpecific}
              isAppendBOContentSource={props.isAppendBOContentSource}
              appendPersonalizeType={props.appendPersonalizeType}
              onlyAddIcon={props.onlyAddIcon}
              isForceHideBtnPersonalization={
                (isBroadcastTiny && !isSendMultipleTiny) || props.onlyAddIcon
              }
              showPersonalization={!props.onlyAddIcon}
              onChangeOthers={props.onChangeOthers(props.name)}
              initData={props.initValue}
              value={props.value}
              isChannelEmail={false}
              isChannelLine={isChannelLine}
              // setCaretPosition={setCaretPosition}
              // isInputHeightSM={
              //   _get(props, 'emoji.isInputHeightSmall', false) ||
              //   (isZaloOA && SMALL_HEIGHT_FIELDS.includes(props.name)) ||
              //   (props.isBlastCampaign && isAntsomiAppPush) ||
              //   (isChannelTelegram && !props.isBlastCampaign) ||
              //   (isAntsomiAppPush && !props.isBlastCampaign)
              // }
              hasEmoji
              hiddenDynamicOption={hiddenDynamicOption}
            />
            {/* <Emojis */}
            {/*   value={transformValue} */}
            {/*   inputRef={inputRef} */}
            {/*   caretPosition={caretPosition} */}
            {/*   disabled={isChannelLine && emojiCount >= 20} */}
            {/*   channelCode={props.channelElement} */}
            {/*   catalogCode={props.catalogCode} */}
            {/*   selectionStartIndex={props.selectionStartIndex} */}
            {/*   setSelectionStartIndex={props.setSelectionStartIndex} */}
            {/*   isHiddenCustomCollection={isAntsomiAppPush} */}
            {/*   onlyUseCommonCollection={_get( */}
            {/*     props, */}
            {/*     'emoji.onlyUseCommonCollection', */}
            {/*     isZaloOA || isAntsomiAppPush || isBlastTelegram, */}
            {/*   )} */}
            {/*   customPositionEmojiIcon={positionEmojiIcon} */}
            {/*   iconXlab={_get(props, 'emoji.iconName', iconXlabName)} */}
            {/*   iconStyle={_get(props, 'emoji.style', iconStyle)} */}
            {/*   alignment={_get(props, 'emoji.alignment', alignment)} */}
            {/*   onChange={props.onChange(props.name)} */}
            {/*   isPersonalization={props.canAddPersonalization} */}
            {/*   useEmoji={[ */}
            {/*     _get(props, 'emoji.use', false), */}
            {/*     isChannelViber, */}
            {/*     isChannelLine, */}
            {/*     isAntsomiAppPush, */}
            {/*     isZaloOA, */}
            {/*     isBlastTelegram, */}
            {/*   ].some(each => Boolean(each))} */}
            {/*   isViewMode={isViewMode} */}
            {/* > */}
            {/*   <WrapperTextArea */}
            {/*     style={{ */}
            {/*       position: 'relative', */}
            {/*       width: editorWidth, */}
            {/*       minHeight: '35px', */}
            {/*     }} */}
            {/*     onlyAddIcon={props.onlyAddIcon} */}
            {/*     isNoBorder={ */}
            {/*       _get(props, 'emoji.isInputHeightSmall', false) || */}
            {/*       (isZaloOA && SMALL_HEIGHT_FIELDS.includes(props.name)) || */}
            {/*       (props.isBlastCampaign && isAntsomiAppPush) || */}
            {/*       (isChannelTelegram && !props.isBlastCampaign) || */}
            {/*       (isAntsomiAppPush && !props.isBlastCampaign) */}
            {/*     } */}
            {/*   > */}
            {/*     <TinymceEditor */}
            {/*       {...props} */}
            {/*       placeHolder={props.placeholder} */}
            {/*       typeComponent="input" */}
            {/*       onChange={value => { */}
            {/*         props.onChange(props.name)(deleteSymbolCharacters(value)); */}
            {/*       }} */}
            {/*       enableShortLink={ */}
            {/*         enableShortLink || props.enableShortLinkSpecific */}
            {/*       } */}
            {/*       isAppendBOContentSource={props.isAppendBOContentSource} */}
            {/*       appendPersonalizeType={props.appendPersonalizeType} */}
            {/*       onlyAddIcon={props.onlyAddIcon} */}
            {/*       isForceHideBtnPersonalization={ */}
            {/*         isBroadCastTiny || props.onlyAddIcon */}
            {/*       } */}
            {/*       showPersonalization={!props.onlyAddIcon} */}
            {/*       onChangeOthers={props.onChangeOthers(props.name)} */}
            {/*       initData={props.initValue} */}
            {/*       value={props.value} */}
            {/*       isChannelEmail={false} */}
            {/*       isChannelLine={isChannelLine} */}
            {/*       setCaretPosition={setCaretPosition} */}
            {/*       isInputHeightSM={ */}
            {/*         _get(props, 'emoji.isInputHeightSmall', false) || */}
            {/*         (isZaloOA && SMALL_HEIGHT_FIELDS.includes(props.name)) || */}
            {/*         (props.isBlastCampaign && isAntsomiAppPush) || */}
            {/*         (isChannelTelegram && !props.isBlastCampaign) || */}
            {/*         (isAntsomiAppPush && !props.isBlastCampaign) */}
            {/*       } */}
            {/*       hasEmoji */}
            {/*       hiddenDynamicOption={hiddenDynamicOption} */}
            {/*     /> */}
            {/*   </WrapperTextArea> */}
            {/* </Emojis> */}
            {isShowCountCharacter ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                }}
              >
                <FormHelperText
                  id="component-helper-text"
                  style={{ width: '100%' }}
                  error={!!props.errors[0]}
                >
                  {props.errors}
                </FormHelperText>
                <WarningSMSRules width="100%">
                  <div
                    className="d-flex align-items-end justify-content-end"
                    style={{ flexDirection: 'column', marginTop: 5 }}
                  >
                    <div style={{ display: 'flex' }}>
                      <UITippy
                        content={MAP_TRANSLATE.smsError.replace(
                          '459',
                          smsLimit,
                        )}
                      >
                        <InfoOutlinedIcon className="icon-info-rule" />
                      </UITippy>
                      <p className="text-rule" style={{ margin: 0 }}>
                        {useLength ? props.value.length : contentLineLength}/
                        {smsLimit} characters
                      </p>
                    </div>
                    {isShowCountEmoji && (
                      <p className="text-rule" style={{ margin: 0 }}>
                        {emojiCount}/20 emoji
                      </p>
                    )}
                  </div>
                </WarningSMSRules>
              </div>
            ) : null}
          </>
        );
      }

      if (enableShortLink) {
        return (
          <WrapperTextArea
            className="overwrite-personalize-input"
            style={{ position: 'relative' }}
            isViewMode={props.isViewMode}
          >
            <TinymceEditor
              {...props}
              isAppendBOContentSource={props.isAppendBOContentSource}
              appendPersonalizeType={props.appendPersonalizeType}
              typeComponent="input"
              onChange={value => {
                props.onChange(props.name)(deleteSymbolCharacters(value));
              }}
              enableShortLink
              isForceHideBtnPersonalization={
                isBroadcastTiny && !isSendMultipleTiny
              }
              onChangeOthers={props.onChangeOthers(props.name)}
              initData={props.initValue}
              value={props.value}
              isChannelEmail={false}
              isChannelLine={isChannelLine}
              // setCaretPosition={setCaretPosition}
              hiddenDynamicOption={[DYNAMIC_KEY.addPer]}
            />
          </WrapperTextArea>
        );
      }

      return (
        <TextAreaBorder
          aria-label="minimum height"
          value={transformValue}
          onChange={evt =>
            props.onChange(props.name)(deleteSymbolCharacters(evt.target.value))
          }
          style={{ width: '100%' }}
        />
      );
    };

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
            ...((isZaloOA && { paddingTop: 6 }) || {}),
          }}
        >
          <WrapperCenterFlexStart>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          {enableShortLink ? (
            renderContent(isBroadcast, isSendMultiple)
          ) : (
            <InputPreview
              id={props.id}
              name={props.name}
              className={props.className}
              borderStyle={props.borderStyle}
              height={props.height}
              minHeight={props.minHeight}
              maxHeight={props.maxHeight}
              isViewMode={props.isViewMode}
              value={
                isChannelLine
                  ? previewEmoji(
                      props.value,
                      {
                        style: { height: '12px', width: '12px' },
                      },
                      props.channelElement,
                    )
                  : props.value
              }
            >
              <WrapperDisable disabled={props.disabled}>
                {renderContent(isBroadcast, isSendMultiple)}
              </WrapperDisable>
              {isAntsomiAppPush ? (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <FormHelperText
                    id="component-helper-text"
                    style={{ width: '100%' }}
                    error={!!props.errors[0]}
                  >
                    {props.errors}
                  </FormHelperText>
                  <WarningSMSRules width="100%">
                    <div className="d-flex align-items-center justify-content-end">
                      <UITippy
                        content={MAP_TRANSLATE.smsError.replace(
                          '459',
                          smsLimit,
                        )}
                      >
                        <InfoOutlinedIcon className="icon-info-rule" />
                      </UITippy>
                      <p className="text-rule">
                        {props.value.length}/{smsLimit} characters
                      </p>
                    </div>
                  </WarningSMSRules>
                </div>
              ) : null}
            </InputPreview>
          )}
          {isShowErrorOuter && (
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          )}
        </Grid>
      </>
    );
  },
  editor: props => {
    const isChannelEmail = props.channelElement === CHANNEL_CODE.EMAIL;
    return (
      // body
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
        >
          <WrapperCenterFlexStart>
            <Title className={props.classes.spacingTitle}>
              {props.isRequired && !props.isViewMode && (
                <span className={props.classes.redStar}>* </span>
              )}
              {props.label}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right', props.classes.alignContent)}
          style={{ paddingTop: props.isViewMode && 11 }}
        >
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              isViewMode={props.isViewMode}
              {...props}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              onChangeOthers={props.onChangeOthers(props.name)}
              typeComponent="editor"
              // showPopupEditorHTML={false}
              showPopupEditorHTML
              showPersonalization
              showObjectWidget
              step={props.step}
              isChannelEmail={isChannelEmail}
            />
          </WrapperDisable>
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </Grid>
      </>
    );
  },
  keyvalue: props => {
    const { useLayoutTable = false, componentKey } = props;
    const isUsePairKeyValue = true;
    const [isTrackingTime, setIsTrackingTime] = useState(false);

    useEffect(() => {
      setIsTrackingTime(!!(props.initValue && props.initValue[TRACK_URL.KEY]));
    }, [componentKey]);

    const isShowPersonalizationInKey = ['antbuddy'].includes(props.catalogCode);
    const isOutputArray = [CATALOG_CODE.ZALO_OA].includes(props.catalogCode);
    const isZaloOA = props.catalogCode === CATALOG_CODE.ZALO_OA;
    const isZaloZNS = props.catalogCode === CATALOG_CODE.ZNS;
    const isZaloPayZNS = props.catalogCode === CATALOG_CODE.ZALO_PAY_ZNS;
    const isAntsomiAppPush =
      props.catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;
    const typeRenderPair =
      [CATALOG_CODE.ZALO_OA].includes(props.catalogCode) || useLayoutTable
        ? TYPE_RENDER_PAIR.TABLE
        : TYPE_RENDER_PAIR.KEY_VALUE;
    const isTableMode =
      typeRenderPair === TYPE_RENDER_PAIR.TABLE || useLayoutTable;

    const disabledAddLength = props.catalogCode === CATALOG_CODE.ZALO_OA && 5;

    const enableTracking =
      [
        CATALOG_CODE.FIRE_BASE_APP_PUSH,
        CATALOG_CODE.ONE_SIGNAL_APP_PUSH,
      ].includes(props.catalogCode) || props.enableTracking;

    const { isBroadcast, isSendMultiple } = pickBroadcastDestDelivery(
      props.destinationSettings,
    );

    const trackingClickNode = () => {
      const clsName = ['grid-col-right', 'd-flex', 'align-items-center'];
      if (!isTableMode) {
        clsName.push(...['m-top-2', 'm-bottom-4']);
      }

      return (
        <Grid item sm={props.colRight || 9} className={clsName.join(' ')}>
          <ToggleButton
            name="update-segment-status"
            className="node-status"
            isToggle={isTrackingTime}
            handleClick={() => setIsTrackingTime(prev => !prev)}
            disabled={props.isViewMode}
          />
          <TitleToggle>
            {props.trackingLabel || 'Enable tracking click message?'}
          </TitleToggle>
        </Grid>
      );
    };

    return (
      <>
        {enableTracking && !isTableMode && (
          <Fragment>
            <Grid
              item
              sm={props.colLeft || 3}
              className={classNames(
                'grid-col-left',
                props.classes.styledChannel,
              )}
            />
            {trackingClickNode()}
          </Fragment>
        )}
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames(
            !isTableMode || enableTracking ? 'grid-col-left' : '',
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
            marginTop: isZaloPayZNS && '10px',
          }}
        >
          <WrapperCenterFlexStart>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && !isTableMode ? (
              <div style={{ height: '1.75rem', width: '100%' }} />
            ) : null}
          </WrapperCenterFlexStart>
        </Grid>
        {enableTracking && isTableMode && trackingClickNode()}
        <Grid
          item
          sm={isTableMode || isZaloZNS ? 12 : props.colRight || 9}
          className={classNames(
            !(isTableMode || isZaloZNS) ? 'grid-col-right' : '',
          )}
        >
          <WrapperDisable disabled={props.disabled}>
            <PairKeyValue
              key={componentKey}
              {...props}
              global={{ ...props }}
              isSimplifyUI={isAntsomiAppPush}
              labelButton={isAntsomiAppPush && 'Add data'}
              showPersonalizationInKey={isShowPersonalizationInKey}
              typeRender={typeRenderPair}
              isOutputArray={isOutputArray}
              disabledAddLength={disabledAddLength}
              errors={props.errors}
              isRequired={props.isRequired}
              isForceHideBtnPersonalization={isBroadcast && !isSendMultiple}
              initData={props.initValue}
              onChange={value => props.onChange(props.name)(value)}
              onChangeOthers={props.onChangeOthers(props.name)}
              isViewMode={props.isViewMode} // using for display element in HTTP API
              isUsePairKeyValue={isUsePairKeyValue}
              isTrackingTime={isTrackingTime}
            />
            {isZaloOA && (
              <div
                style={{
                  marginTop: 4,
                  fontSize: '11px',
                  color: '#A2A2A2',
                  fontWeight: '400',
                  textAlign: 'right',
                }}
              >
                {getTranslateMessage(
                  TRANSLATE_KEY._CONTENT_TABL_HINT,
                  'Label, Content must not exceed 25, 100 characters',
                )}
              </div>
            )}
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  checkbox: props => {
    const isSmartInbox = getObjectPropSafely(
      () => props.catalogCode === CATALOG_CODE.SMART_INBOX,
      false,
    );

    const handleChangeCheckbox = (dataIn = '') => {
      try {
        if (typeof props.onChange(props.name) === 'function') {
          props.onChange(props.name)(dataIn);
        }

        if (isSmartInbox && typeof props.callback === 'function') {
          props.callback('SET_DEFAULT_URL_SMART_INBOX', {
            name: props.name,
            status: dataIn,
          });
        }
      } catch (error) {
        addMessageToQueue({
          path:
            'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.form.js',
          func: 'handleChangeCheckbox',
          data: error.stack,
        });
        // eslint-disable-next-line no-console
        console.log(error);
      }
    };

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames('grid-col-left')}
          style={{ display: isSmartInbox ? 'none' : 'flex' }}
        />
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
          style={
            props.name === 'isFitContent'
              ? {
                  paddingLeft: 8,
                }
              : {}
          }
        >
          <WrapperDisable disabled={props.disabled}>
            <UICheckbox
              name={props.name}
              checked={!!props.value}
              onChange={handleChangeCheckbox}
              disabled={props.isViewMode}
            >
              <span
                style={props.name === 'isFitContent' ? { marginLeft: 10 } : {}}
              >
                {props.label}
              </span>
            </UICheckbox>
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  checkboxGrid: props => {
    const {
      label = '',
      options = [],
      value: valueIn = {},
      isTitleAlignLeft = false,
      isViewMode = false,
      isRequired = false,
      isBlastCampaign = false,
    } = props;
    const hasOptions = !_isEmpty(options) && Array.isArray(options);

    return (
      <>
        {label && (
          <Grid
            item
            sm={props.colLeft || 3}
            className={classNames('grid-col-left', props.classes.styledChannel)}
            style={{
              justifyContent: isTitleAlignLeft ? 'flex-start' : 'flex-end',
              paddingTop: isBlastCampaign ? 6 : 0,
            }}
          >
            <WrapperCenterFlexEnd>
              <Title className={props.classes.spacingTitle}>
                {!isTitleAlignLeft && isRequired && !isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
                {label}
                {isTitleAlignLeft && isRequired && !isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
              </Title>
              {!!props.errors[0] && (
                <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
              )}
            </WrapperCenterFlexEnd>
          </Grid>
        )}
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          <WrapperDisable disabled={props.disabled}>
            <Wrapper>
              {hasOptions &&
                options.map(option => (
                  <UICheckbox
                    label={option.label}
                    name={props.name}
                    checked={getObjectPropSafely(
                      () => valueIn[option.value],
                      false,
                    )}
                    className="custom-text-label"
                    onChange={_value => {
                      props.onChange(props.name)({
                        ...(valueIn || {}),
                        [option.value]: _value,
                      });
                    }}
                    disabled={props.isViewMode}
                  />
                ))}
            </Wrapper>
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  radio: props => {
    const {
      name = '',
      label = '',
      isRequired = false,
      options = [],
      value: valueIn = {},
      isTitleAlignLeft = false,
      isViewMode = false,
      catalogCode = '',
      direction = '',
      isBlastCampaign = false,
      alignment = '',
    } = props;
    const { value = '', enterValue = {} } = valueIn;

    const styleGroup = useMemo(() => {
      if ([direction, alignment].includes('horizontal')) {
        return styleHorizontal;
      }

      return {};
    }, [direction, alignment]);

    return (
      <>
        {label && (
          <Grid
            item
            sm={props.colLeft || 3}
            className={classNames('grid-col-left', props.classes.styledChannel)}
            style={{
              justifyContent: isTitleAlignLeft ? 'flex-start' : 'flex-end',
              paddingTop: 6,
            }}
          >
            <WrapperCenterFlexEnd>
              <Title
                className={props.classes.spacingTitle}
                style={
                  !isBlastCampaign
                    ? { whiteSpace: 'pre-wrap', textAlign: 'right' }
                    : { whiteSpace: 'pre-wrap' }
                }
              >
                {!isTitleAlignLeft && isRequired && !isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
                {label}
                {isTitleAlignLeft && isRequired && !isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
              </Title>
              {!!props.errors[0] && (
                <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
              )}
            </WrapperCenterFlexEnd>
          </Grid>
        )}
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          <WrapperDisable disabled={props.disabled}>
            <RadioGroup
              options={options}
              // label={label}
              name={name}
              value={value}
              useEnterValueMode
              enterValue={enterValue}
              defaultValue={props}
              catalogCode={catalogCode}
              styleGroup={styleGroup}
              onChange={(_, dataOut) => {
                props.onChange(props.name)({ ...valueIn, ...dataOut });
              }}
              disabled={props.isViewMode}
            />
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  inputUnit: props => {
    const {
      value: valueIn = {},
      isBlastCampaign = false,
      label = '',
      isTitleAlignLeft = false,
      isRequired = false,
      isViewMode = false,
    } = props;

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const getValueSafe = useCallback(
      _name => getObjectPropSafely(() => valueIn[_name], ''),
      [valueIn],
    );

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames('grid-col-left', props.classes.styledChannel)}
          style={{
            justifyContent: isTitleAlignLeft ? 'flex-start' : 'flex-end',
            paddingTop: isBlastCampaign ? 6 : 0,
          }}
        >
          <WrapperCenterFlexEnd>
            <Title
              className={props.classes.spacingTitle}
              style={
                !isBlastCampaign
                  ? { whiteSpace: 'pre-wrap', textAlign: 'right' }
                  : { whiteSpace: 'pre-wrap' }
              }
            >
              {!isTitleAlignLeft && isRequired && !isViewMode && (
                <span className={props.classes.redStar}>* </span>
              )}
              {label}
              {isTitleAlignLeft && isRequired && !isViewMode && (
                <span className={props.classes.redStar}> *</span>
              )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          <WrapperDisable disabled={props.disabled || isViewMode}>
            <WrapperCenter
              style={{ justifyContent: 'flex-start', gap: '20px' }}
            >
              <Wrapper style={{ minWidth: 50 }}>
                <UINumber
                  min={0}
                  defaultValue={0}
                  defaultEmptyValue={0}
                  width={60}
                  value={getValueSafe('input')}
                  onChange={valueOut =>
                    props.onChange(props.name)({
                      ...(valueIn || {}),
                      input: valueOut,
                    })
                  }
                />
              </Wrapper>
              <Wrapper>
                <UISelect
                  onlyParent
                  use="tree"
                  isSearchable={false}
                  options={props.options}
                  value={getValueSafe('unit')}
                  onChange={valueOut => {
                    props.onChange(props.name)({
                      ...(valueIn || { input: 0 }),
                      unit: valueOut,
                    });
                  }}
                  placeholder={getTranslateMessage(
                    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                    'Select an item',
                  )}
                  fullWidthPopover
                  disabled={props.disabled}
                  labelWidth={() => props.isFullWidth && '100%'}
                />
              </Wrapper>
            </WrapperCenter>
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  group: props => {
    const {
      configure = {},
      options = [],
      minOptions = 1,
      maxOptions = 1,
      name = '',
      design = '',
      isViewMode = false,
      currentTemplateInput = '',
      componentKey = '',
      errors = [],
      callback = () => {},
      onChange = () => {},
      ...restConfigs
    } = props;

    // let calloutsText = '';
    // if (currentTemplateInput === TEMPLATE_KEYS.SIMPLE_IMAGE_CAROUSEL) {
    //   calloutsText = 'Applicable only for IOS';
    // }

    return (
      <MapGroupFields
        {...restConfigs}
        isViewMode={isViewMode}
        name={name}
        key={name}
        design={design}
        componentKey={componentKey}
        // calloutsText={calloutsText}
        minOptions={minOptions}
        maxOptions={maxOptions}
        errors={errors}
        currentTemplateInput={currentTemplateInput}
        options={options}
        configure={configure}
        onChange={dataOut => onChange(name)(dataOut)}
        callback={callback}
      />
    );
  },
  htmlEditor: props => (
    <>
      <Grid
        item
        sm={props.colLeft || 3}
        className={classNames('grid-col-left', props.classes.styledChannel)}
      >
        <WrapperCenterFlexStart>
          <Title className={props.classes.spacingTitle}>
            {props.isRequired && !props.isViewMode && (
              <span className={props.classes.redStar}>* </span>
            )}
            {props.label}
          </Title>
          {!!props.errors[0] && (
            <div style={{ height: '1.75rem', width: '100%' }} />
          )}
        </WrapperCenterFlexStart>
      </Grid>
      <Grid
        item
        sm={props.colRight || 9}
        className={classNames('grid-col-right')}
      >
        <WrapperDisable disabled={props.disabled}>
          <TinymceEditor
            isViewMode={props.isViewMode}
            {...props}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            onChangeOthers={props.onChangeOthers(props.name)}
            typeComponent="htmlEditor"
            showPersonalization
            showPopupEditorHTML
            showObjectWidget
          />
        </WrapperDisable>
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Grid>
    </>
  ),
  // optinEditor
  mediaTemplate: props => {
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;
    const gridWidthContent =
      props.value?.template || props.value?.template_type ? 9 : 12;
    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            'm-bottom-6',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <InputLabel
              classes={props.classes}
              isRequired={props.isRequired}
              isViewMode={props.isViewMode}
              isTitleAlignLeft={props.isTitleAlignLeft}
            >
              {props.label}
            </InputLabel>
            {/* <div className={props.classes.noChooseErrorWrapper}>
              <FormHelperText
                id="component-helper-text"
                error={!!props.errors[0]}
                className={`m-top-2 ${props.classes.noChooseErrorMessage}`}
              >
                {props.errors}
              </FormHelperText>
            </div> */}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={gridWidthContent}
          className={classNames('pos-relative', {
            // 'm-top-2': !!props.errors[0],
          })}
        >
          <FormHelperText
            id="component-helper-text"
            error={!!props.errors[0]}
            className="m-top-3"
          >
            {props.errors}
          </FormHelperText>
          <WrapperDisable disabled={props.disabled}>
            <MediaTemplateEditor
              {...props}
              onChange={props.onChange(props.name)}
              onChangeOthers={props.onChangeOthers(props.name)}
              classNameTabNav="styled-position"
            />
          </WrapperDisable>
          {/* <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
                {props.errors}
              </FormHelperText> */}
        </Grid>
      </>
    );
  },
  jsonTemplate: props => {
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;
    const gridWidthContent = Object.keys(props.value.properties || {}).length
      ? 9
      : 12;
    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            'm-bottom-6',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <InputLabel
              classes={props.classes}
              isRequired={props.isRequired}
              isViewMode={props.isViewMode}
              isTitleAlignLeft={props.isTitleAlignLeft}
            >
              {props.label}
            </InputLabel>
            {/* {!!props.errors[0] && (
                <div style={{ height: '1.75rem', width: '100%' }} />
              )} */}
            <div className={props.classes.noChooseErrorWrapper}>
              <FormHelperText
                id="component-helper-text"
                error={!!props.errors[0]}
                className={`m-top-2 ${props.classes.noChooseErrorMessage}`}
              >
                {props.errors}
              </FormHelperText>
            </div>
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={gridWidthContent}
          className={classNames('pos-relative', {
            'm-top-2': !!props.errors[0],
          })}
        >
          <WrapperDisable disabled={props.disabled}>
            <JSONTemplateEditor
              {...props}
              onChange={props.onChange(props.name)}
              onChangeOthers={props.onChangeOthers(props.name)}
              classNameTabNav="styled-json"
              isDisplayUseTemplate
            />
          </WrapperDisable>
          {/* <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
              {props.errors}
            </FormHelperText> */}
        </Grid>
      </>
    );
  },
  emailTemplate: props => {
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;
    const isChannelEmail = props.channelElement === CHANNEL_CODE.EMAIL;
    const isChannelEmailBlast = isChannelEmail && props.isBlastCampaign;
    const gridWidthContent = props.isViewMode
      ? 9
      : props.value?.template ||
        props.value?.template_type ||
        (typeof props.value === 'string' && props.value)
      ? 9
      : 12;

    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 3 : 2}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          // className={classNames(
          //   'm-bottom-6',
          //   props.classes.title,
          //   props.classes.styledChannel,
          //   isAllowAdjustColumn && props.classes.adjustCol,
          // )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd style={{ height: '40px' }}>
            <InputLabel
              classes={props.classes}
              isRequired={props.isRequired}
              isViewMode={props.isViewMode}
              isTitleAlignLeft={props.isTitleAlignLeft}
              isChannelEmailBlast={isChannelEmailBlast}
            >
              {props.label}
            </InputLabel>
            {/* {!!props.errors[0] && (
                <div style={{ height: '1.75rem', width: '100%' }} />
              )} */}
            <div className={props.classes.noChooseErrorWrapper}>
              <FormHelperText
                id="component-helper-text"
                error={!!props.errors[0]}
                className={`m-top-2 ${props.classes.noChooseErrorMessage}`}
              >
                {props.errors}
              </FormHelperText>
            </div>
            {isChannelEmailBlast && (
              <div
                style={{ color: '#AAAAAA', fontSize: '12px', width: '100px' }}
              >
                {props.label}
              </div>
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={gridWidthContent || 10}
          className={classNames('pos-relative', {
            'm-top-2': !!props.errors[0],
          })}
        >
          <WrapperDisable disabled={props.disabled}>
            <EmailTemplateEditor
              {...props}
              onChange={props.onChange(props.name)}
              onChangeOthers={props.onChangeOthers(props.name)}
              classNameTabNav="styled-position"
            />
          </WrapperDisable>
          {/* <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
              {props.errors}
            </FormHelperText> */}
        </Grid>
      </>
    );
  },
  journeyTemplate: props => {
    const {
      value = '',
      name = '',
      label = '',
      isRequired = false,
      isViewMode = false,
      errors = [],
      catalogCode = '',
      selectedTemplateDashboard = {},
      tokenLineRichMenu = '',
      isDefaultRichMenu = false,
      onChange = () => {},
    } = props;

    return (
      <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
        <TemplateListing
          value={value}
          label={label}
          isDashboard={false}
          isRequired={isRequired}
          errors={errors}
          selectedTemplateDashboard={selectedTemplateDashboard}
          isViewMode={isViewMode}
          catalogCode={catalogCode}
          isDefaultRichMenu={isDefaultRichMenu}
          token={tokenLineRichMenu}
          onChange={dataOut => {
            const { template = {} } = dataOut;
            const tmp = _omit(template, ['dashboardMode', 'catalogInfo']);
            onChange(name)(tmp);
          }}
        />
      </QueryClientProviderAntsomiUI>
    );
  },
  multiLangInput: props => (
    <>
      <Grid
        item
        sm={props.colLeft || 3}
        className={classNames(
          'grid-col-left',
          props.classes.title,
          props.classes.styledChannel,
        )}
      >
        <WrapperCenterFlexEnd>
          <Title className={props.classes.spacingTitle}>
            {props.isRequired && !props.isViewMode && (
              <span className={props.classes.redStar}>* </span>
            )}
            {props.label}
          </Title>
          {!!props.errors[0] && (
            <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
          )}
        </WrapperCenterFlexEnd>
      </Grid>
      <Grid
        item
        sm={props.colRight || 9}
        className={classNames('grid-col-right')}
      >
        <InputLanguage
          disabled={props.disabled}
          initData={props.initValue}
          onChange={props.onChange(props.name)}
          errors={props.errors}
        />
      </Grid>
    </>
  ),
  multilanguage: props => {
    const {
      name = '',
      initValue = {},
      isViewMode = false,
      design = '',
      default: defaultData,
      onChange,
      optionLanguages = [],
      options = [],
    } = props;
    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;

    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title
              className={props.classes.spacingTitle}
              style={
                props.isBlastCampaign
                  ? {
                      whiteSpace: 'break-spaces',
                      wordBreak: 'initial',
                      textAlign: 'right',
                    }
                  : {}
              }
            >
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
          style={isAllowAdjustColumn ? { flex: 1 } : {}}
        >
          <MultiLanguageDropdown
            name={name}
            design={design}
            initValue={initValue}
            isViewMode={isViewMode}
            wrapperStyle={{ marginBottom: 8 }}
            options={options}
            optionLanguages={optionLanguages}
            defaultData={defaultData}
            onChange={dataOut => {
              onChange(name)(dataOut);
            }}
          />
        </Grid>
      </>
    );
  },
  number: props => (
    <InputTemplate {...props}>
      <WrapperDisable disabled={props.disabled}>
        <InputPreview
          isViewMode={props.isViewMode}
          type="input"
          value={props.value}
        >
          <UINumber
            type="number"
            onChange={props.onChange(props.name)}
            value={props.value}
            min={props.minLength}
            max={props.maxLength}
            width="4rem"
          />
        </InputPreview>
      </WrapperDisable>
      <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
        {props.errors}
      </FormHelperText>
    </InputTemplate>
  ),
  // return (
  //   <>
  //     <Grid
  //       item
  //       sm={props.colLeft || 3}
  //       className={props.classes.styledChannel}
  //     >
  //       <WrapperCenterFlexStart>
  //         <Title className={props.classes.spacingTitle}>
  //           {props.isRequired && !props.isViewMode && (
  //             <span className={props.classes.redStar}>* </span>
  //           )}
  //           {props.label}
  //         </Title>
  //         {!!props.errors[0] && (
  //           <div style={{ height: '1.75rem', width: '100%' }} />
  //         )}
  //       </WrapperCenterFlexStart>
  //     </Grid>
  //     <Grid item sm={props.colRight || 9}>
  //       <WrapperDisable disabled={props.disabled}>
  //         <InputPreview
  //           isViewMode={props.isViewMode}
  //           type="input"
  //           value={props.value}
  //         >
  //           <UINumber
  //             type="number"
  //             onChange={props.onChange(props.name)}
  //             value={props.value}
  //             min={props.minLength}
  //             max={props.maxLength}
  //             width="4rem"
  //           />
  //         </InputPreview>
  //       </WrapperDisable>
  //       <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
  //         {props.errors}
  //       </FormHelperText>
  //     </Grid>
  //   </>
  // );
  singleLineAddPersonalize: props => {
    const baseHintInfo = _get(props, 'baseHintInfo', {
      isShow: false,
      isCountCharacter: false,
      label: '',
    }); // config from API

    const channelFlags = useChannelFlags(props.channelElement);
    const {
      isChannelEmail,
      isChannelSMS,
      isChannelTelegram,
      isChannelLine,
      isChannelAppPush,
      isChannelSmartInbox,
      isChannelWebhook,
    } = channelFlags;
    const { SMART_INBOX, LINE, VIETTEL } = CATALOG_CODE;

    const isSmartInbox =
      isChannelSmartInbox && props.catalogCode === SMART_INBOX;
    const isAntsomiLine = props.catalogCode === LINE;
    const isViettel = props.catalogCode === VIETTEL;

    const isLineLandingPageURL =
      isAntsomiLine && props.name === 'imagemapLandingPageUrl';

    const helpTextSpecific = [ACFC_APP_PUSH, ZNS].includes(props.catalogCode);
    const isOrchestrationLine = !props.isBlastCampaign && isLineLandingPageURL;
    const showHelpText =
      (helpTextSpecific || isOrchestrationLine || isChannelWebhook) &&
      !baseHintInfo?.isCountCharacter;

    const initValue = isChannelEmail ? props.value : props.initValue;

    let isBroadcast = false;
    let isSendMultiple = false;
    let method = '';
    const activeNode = getObjectPropSafely(
      () => props.configure.main.activeNode,
      {},
    );
    const nodes = getObjectPropSafely(
      () => props.configure.main.nodes,
      Map({}),
    );
    const destPath = [
      activeNode.nodeId,
      'destination',
      'data',
      'workflowDestination',
      'value',
      'destinationSetting',
    ];
    if (
      (nodes &&
        activeNode &&
        activeNode.nodeId &&
        activeNode.type === 'DESTINATION') ||
      props.isBlastCampaign
    ) {
      const data = getObjectPropSafely(() => nodes.getIn(destPath), {});
      if (!_isEmpty(data) && data.isBroadcast) {
        isBroadcast = data.isBroadcast;
      }
      if (_has(data, 'isSendMultiple')) {
        isSendMultiple = data.isSendMultiple;
      }
    }

    if (nodes && activeNode && activeNode.nodeId) {
      if (props.isBlastCampaign) {
        const destinationId = nodes.getIn([
          activeNode.nodeId,
          'destination',
          'destinationId',
        ]);
        const cachedDestInfos = nodes.getIn([
          activeNode.nodeId,
          'destination',
          'data',
          'cachedWorkflowDestinationInfo',
        ]);

        if (destinationId && Array.isArray(cachedDestInfos)) {
          const destActiveInfo = cachedDestInfos.find(
            dest => +dest.destinationId === +destinationId,
          );

          if (destActiveInfo) {
            method = _get(destActiveInfo, 'destinationSetting.method', '');
          }
        }
      } else {
        method = nodes.getIn([...destPath, 'method']);
      }
    }

    const isCenterBlast = isChannelAppPush && props.isBlastCampaign;

    const enableShortLink = checkEnableShortLink({
      defaultEnabled: props.defaultEnableShortLink,
      channelCode: props.channelElement,
      catalogCode: props.catalogCode,
      name: props.name,
      isHideShortLink: props.isHideShortLink,
    });

    const editorWidth = props.channelElement === 'email' ? '50%' : '100%';

    let styledComponentBlast = {};
    if (!props.isViewMode && props.isBlastCampaign) {
      switch (props.channelElement) {
        case 'sms':
          if (props.isSingleLineText) {
            styledComponentBlast = {
              width: '100% !important',
              border: '1px solid #b8cfe6',
              borderRadius: '3px',
              minHeight: '6rem',
            };
          }

          break;

        default:
          break;
      }
    }
    const isUnsetPositionInputWrapper = props.isBlastCampaign && isChannelSMS;
    const isValidBroadcast = isBroadcast && !isSendMultiple;
    let isForceHideBtnPersonal = isValidBroadcast;
    // if (isSmsIMedia) {
    //   isForceHideBtnPersonal = false;
    // }

    const inputWrapperEle = document.getElementById(props.inputPersonalId); // Id config from API
    const limitTagLength = _get(props, 'limitTagLength', 0); // Limit number of tag config from API

    if (inputWrapperEle) {
      const numericCountTag = inputWrapperEle.querySelectorAll('.insert-word');
      const currentTagLength = numericCountTag.length;

      if (limitTagLength && +limitTagLength <= currentTagLength) {
        isForceHideBtnPersonal = true;
      }
    }

    let hiddenDynamicOption = [];
    if (isLineLandingPageURL) {
      hiddenDynamicOption = [DYNAMIC_KEY.addPer];
    }
    if (props.isHideShortLink) {
      hiddenDynamicOption.push(DYNAMIC_KEY.shortLink);
    }

    const isDynamicContent =
      (isChannelSMS || enableShortLink) && /#{.*?}/gm.test(props.value);

    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;

    const isShowExtraInfo = determineShowExtraInfo({
      defaultShow: baseHintInfo?.isShow,
      channelFlags,
      catalogCode: props.catalogCode,
      name: props.name,
      isHideExtraInfo: props.isHideExtraInfo,
    });

    const isChannelEmailBlast = isChannelEmail && props.isBlastCampaign;
    const isShowStar = props.isRequired && !props.isViewMode;
    const starAtStart = !props.isTitleAlignLeft && isShowStar;
    const starAtEnd = props.isTitleAlignLeft && isShowStar;

    return (
      // subject
      <>
        <Grid
          item
          sm={
            isChannelEmailBlast
              ? 2
              : isAllowAdjustColumn
              ? 'auto'
              : props.colLeft || 3
          }
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title className={props.classes.spacingTitle}>
              {starAtStart && <span className={props.classes.redStar}>* </span>}
              {props.label}
              {starAtEnd && <span className={props.classes.redStar}> *</span>}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={isChannelEmailBlast ? 10 : props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content, {
            'unset-position-input-wrapper': isUnsetPositionInputWrapper,
          })}
        >
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              isCenterBlast={isCenterBlast}
              isAppendBOContentSource={props.isAppendBOContentSource}
              appendPersonalizeType={props.appendPersonalizeType}
              typeComponent="input"
              inputPersonalId={props.inputPersonalId}
              isForceHideBtnPersonalization={isForceHideBtnPersonal}
              showShortLinkWithBroadcast={
                isValidBroadcast && props.showShortLinkWithBroadcast
              }
              onlyShowGeneralShortlink={props.onlyShowGeneralShortlink}
              placeholder={props.placeholder}
              onChange={props.onChange(props.name)}
              onChangeOthers={props.onChangeOthers(props.name)}
              initData={initValue}
              isChannelEmail={isChannelEmail}
              isChannelLine={isChannelLine}
              isChannelSMS={isChannelSMS}
              isChannelTelegram={isChannelTelegram}
              isSmartInbox={isSmartInbox}
              enableShortLink={enableShortLink || props.enableShortLinkSpecific}
              width={editorWidth}
              styledComponentBlast={styledComponentBlast}
              isBlastCampaign={props.isBlastCampaign}
              hiddenDynamicOption={hiddenDynamicOption}
            />
          </WrapperDisable>

          {props.prompt ? <PromptText>{props.prompt}</PromptText> : null}

          {isShowExtraInfo ? (
            <WarningSMSRules
              isBlastCampaign={props.isBlastCampaign}
              width={editorWidth}
              style={isViettel ? { margin: 0 } : {}}
              // width={props.isFullWidth ? '100%' : editorWidth}
            >
              <ExtraInfo
                {...props}
                baseHintInfo={baseHintInfo}
                method={method}
                editorWidth={editorWidth}
              />

              {isDynamicContent && (
                <p className="text-warn">{MAP_TRANSLATE.warnDynamicContent}</p>
              )}
            </WarningSMSRules>
          ) : null}

          {showHelpText && (
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
              width={props.isFullWidth ? '100%' : editorWidth}
            >
              {props.errors}
            </FormHelperText>
          )}
        </Grid>
      </>
    );
  },
  workspaces: props => (
    <>
      <Grid
        item
        sm={props.colLeft || 3}
        className={classNames(
          'grid-col-left',
          props.classes.title,
          props.classes.styledChannel,
        )}
      >
        <WrapperCenterFlexEnd>
          <Title className={props.classes.spacingTitle}>
            {props.isRequired && !props.isViewMode && (
              <span className={props.classes.redStar}>* </span>
            )}
            {props.label}
          </Title>
          {!!props.errors[0] && (
            <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
          )}
        </WrapperCenterFlexEnd>
      </Grid>
      <Grid
        item
        sm={props.colRight || 9}
        className={classNames('grid-col-right', props.classes.content)}
      >
        <RenderWorkspace
          workspaces={props.value}
          callback={props.callback}
          onChange={props.onChange}
          {...props}
        />
      </Grid>
    </>
  ),
  tagButtons: props => {
    const callback = (type = '', dataIn = '') => {
      switch (type) {
        case 'UPDATE_DEFAULT_DATA': {
          if (
            typeof props.onChange(props.name) === 'function' &&
            typeof dataIn === 'object'
          ) {
            props.onChange(props.name)([
              {
                labelId: dataIn.labelId || '',
                labelName: dataIn.labelName || '',
              },
            ]);
          }

          break;
        }
        default: {
          break;
        }
      }
    };

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames('grid-col-left', props.classes.title)}
          style={{
            display: 'flex',
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title
              className={props.classes.spacingTitle}
              style={
                props.isBlastCampaign
                  ? {
                      whiteSpace: 'break-spaces',
                      wordBreak: 'initial',
                      textAlign: 'right',
                    }
                  : {}
              }
            >
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
        >
          <UITagButtons
            design={props.design}
            value={props.value}
            isViewMode={props.isViewMode}
            callback={callback}
            onChange={props.onChange(props.name)}
          />
        </Grid>
      </>
    );
  },
  actionButton: props => {
    const {
      label = '',
      initValue = '',
      value = '',
      design = '',
      isViewMode = false,
      isRequired = false,
      name = '',
      errors = [],
      onChange = () => {},
    } = props;

    return (
      <ActionButtons
        initValue={initValue}
        label={label}
        design={design}
        errors={errors}
        isViewMode={isViewMode}
        isRequired={false}
        value={value}
        name={name}
        onChange={dataOut => onChange(name)(dataOut)}
      />
    );
  },
  markup: props => (
    <>
      <Grid
        item
        sm={props.colLeft || 3}
        className={classNames(
          'grid-col-left',
          props.classes.title,
          props.classes.styledChannel,
        )}
        style={{
          justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
        }}
      >
        <WrapperCenterFlexEnd>
          <Title
            className={props.classes.spacingTitle}
            style={
              props.isBlastCampaign
                ? {
                    whiteSpace: 'break-spaces',
                    wordBreak: 'initial',
                    textAlign: 'right',
                    color: '#333333',
                  }
                : { color: '#333333' }
            }
          >
            {!props.isTitleAlignLeft &&
              props.isRequired &&
              !props.isViewMode && (
                <span className={props.classes.redStar}>* </span>
              )}
            {props.label}
            {props.isTitleAlignLeft &&
              props.isRequired &&
              !props.isViewMode && (
                <span className={props.classes.redStar}> *</span>
              )}
          </Title>
          {!!props.errors[0] && (
            <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
          )}
        </WrapperCenterFlexEnd>
      </Grid>
      <Grid
        item
        sm={props.colRight || 9}
        className={classNames('grid-col-right', props.classes.content)}
      />
    </>
  ),
  anchorSwitch: props => {
    const { label = '', value = false, name = '' } = props;

    const isSmartInbox = getObjectPropSafely(
      () => props.catalogCode === CATALOG_CODE.SMART_INBOX,
      false,
    );

    const handleChangeSwitch = isChecked => {
      try {
        if (isSmartInbox && typeof props.callback === 'function') {
          props.callback('SET_ACTIVE_FIRST_LAYOUT_TAB');
        }

        if (typeof props.onChange(name) === 'function') {
          props.onChange(name)(isChecked);
        }
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleChangeSwitch',
          data: error.stack,
        });
        // eslint-disable-next-line no-console
        console.log(error);
      }
    };

    return (
      <Wrapper
        style={{
          position: 'absolute',
          top: '56px',
          right: '32px',
          display: 'flex',
          gap: '6px',
          alignItems: 'center',
          height: '36px',
          lineHeight: '36px',
        }}
      >
        <Switch
          checked={value}
          size="small"
          onChange={handleChangeSwitch}
          className={`${!value ? 'inactive' : ''} inbox-switch-custom`}
          disabled={props.isViewMode}
        />
        {label}
      </Wrapper>
    );
  },
  lineTemplate: props => {
    const {
      name = '',
      value = '',
      errors = [],
      templateId = '',
      isViewMode = false,
      design = 'create',
      onChange = () => {},
    } = props;

    return (
      <LineTemplate
        {...props}
        name={name}
        isViewMode={isViewMode}
        templateId={templateId}
        errors={errors}
        design={design}
        value={value}
        onChangeOthers={props.onChangeOthers(name)}
        onChange={dataOut => onChange(name)(dataOut)}
      />
    );
  },
  sticker: props => {
    const {
      name = '',
      errors = [],
      value = '',
      stickerSet = '',
      catalogCode = '',
      isViewMode = false,
      onChange = () => {},
    } = props;

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title
              className={props.classes.spacingTitle}
              style={
                props.isBlastCampaign
                  ? {
                      whiteSpace: 'break-spaces',
                      wordBreak: 'initial',
                      textAlign: 'right',
                      color: '#333333',
                    }
                  : { color: '#333333' }
              }
            >
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
        >
          <Sticker
            value={value}
            catalogCode={catalogCode}
            stickerSet={
              typeof stickerSet === 'object' ? stickerSet.value : stickerSet
            }
            isViewMode={isViewMode}
            errors={errors}
            onChange={valueOut => onChange(name)(valueOut)}
          />
        </Grid>
      </>
    );
  },
  filter: props => {
    const { name, onChange } = props;

    return (
      <InputTemplate {...props}>
        <UINodeFilter {...props} onChange={val => onChange(name)(val)} />
      </InputTemplate>
    );
  },
  color: props => {
    const {
      initValue = '',
      name = '',
      isViewMode = false,
      disabled = false,
      catalogCode = '',
      onChange = () => {},
    } = props;

    const isHideAlpha = [CATALOG_CODES.ANTSOMI_APP_PUSH].includes(catalogCode);

    return (
      <InputTemplate {...props}>
        <WrapperDisable disabled={disabled || isViewMode}>
          <ColorPicker
            isHideAlpha={isHideAlpha}
            color={initValue}
            onChange={colorOut => onChange(name)(colorOut)}
            style={{ margin: 0, alignItems: 'center' }}
          />
        </WrapperDisable>
      </InputTemplate>
    );
  },
  selectIcon: props => {
    const {
      name = '',
      value = '',
      disabled = false,
      isViewMode = false,
      errors = [],
      options = [],
      onChange = () => {},
    } = props;

    return (
      <InputTemplate {...props}>
        <SelectIcon
          isViewMode={isViewMode}
          disabled={disabled}
          errors={errors}
          value={value}
          options={options}
          onChange={iconOut => onChange(name)(iconOut)}
        />
      </InputTemplate>
    );
  },
  slideBar: props => {
    const {
      name = '',
      initValue = [],
      fields = {},
      classes = {},
      errors = [],
      catalogCode = '',
      disabled = false,
      isBlastCampaign = false,
      isViewMode = false,
      isRecommendation = false,
      isAppendBOContentSource = false,
      appendPersonalizeType = [],
      minOptions = 1,
      maxOptions = 5,
      onChange = () => {},
    } = props;

    return (
      <SlideBarWithFields
        name={name}
        classes={classes}
        errors={errors}
        initValue={initValue}
        catalogCode={catalogCode}
        isAppendBOContentSource={isAppendBOContentSource}
        appendPersonalizeType={appendPersonalizeType}
        isRecommendation={isRecommendation}
        limit={{ min: minOptions, max: maxOptions }}
        isBlastCampaign={isBlastCampaign}
        isViewMode={isViewMode}
        disabled={disabled}
        fields={fields}
        onChange={dataOut => onChange(name)(dataOut)}
      />
    );
  },
  selectOptions: props => {
    const {
      name,
      disabled,
      options,
      mapOptions,
      value,
      isViewMode,
      errors = [],
    } = props;
    const { type, specific = {}, after = {} } = value;
    const dateFormat = 'DD/MM/YYYY';
    const timeFormat = 'HH:mm';

    const onChangeValue = (path, data) => {
      const newValue = _set(value, path, data);
      props.onChange(name)(newValue);
    };

    const renderOptionSelected = () => {
      if (type === 'specific') {
        return (
          <div className="d-flex align-items-center">
            <StyledUIInputCalendar
              placeholder={dateFormat}
              isViewMode={isViewMode}
              value={moment(specific.date, dateFormat).valueOf()}
              onChange={date =>
                onChangeValue('specific.date', moment(date).format(dateFormat))
              }
              disablePast
              isShowLabel={false}
            />
            <P
              className="m-x-3"
              color="#666"
              fontSize="11px"
              style={{ whiteSpace: 'nowrap' }}
            >
              at
            </P>
            <MuiPickersUtilsProvider utils={DateFnsUtils}>
              <UIInputTime
                margin="none"
                id="time-picker"
                value={moment(specific.time, timeFormat).valueOf()}
                isViewMode={isViewMode}
                onChange={time =>
                  onChangeValue(
                    'specific.time',
                    moment(time).format(timeFormat),
                  )
                }
                keyboardIcon={<AccessTimeIcon />}
              />
            </MuiPickersUtilsProvider>
          </div>
        );
      }

      if (type === 'after') {
        const afterOptions = getObjectPropSafely(() => options[2].options, []);

        if (!afterOptions.length) return null;

        return (
          <MuiRadioGroup
            value={after.type}
            onChange={event => onChangeValue('after.type', event.target.value)}
          >
            {afterOptions.map(option => (
              <FormControlLabel
                value={option.value}
                control={<Radio color="primary" size="small" />}
                label={
                  <div className="d-flex align-items-center m-left-2">
                    <div style={{ minWidth: 60, maxWidth: 60 }}>
                      <UINumber
                        min={1}
                        max={100}
                        width="60px"
                        value={after[option.value]}
                        onChange={newData =>
                          onChangeValue(`after.${option.value}`, newData)
                        }
                      />
                    </div>
                    <P className="m-x-3">{option.label}</P>
                  </div>
                }
              />
            ))}
          </MuiRadioGroup>
        );
      }

      return null;
    };

    return (
      <InputTemplate {...props}>
        <WrapperDisable disabled={props.disabled}>
          <UISelect
            errors={errors}
            onlyParent
            use="tree"
            isSearchable
            options={options}
            value={mapOptions[type]}
            onChange={newType => onChangeValue('type', newType.value)}
            placeholder="Select an item"
            fullWidthPopover
            disabled={disabled}
            isViewMode={isViewMode}
          />
          {type !== 'none' && (
            <div className="m-top-2">{renderOptionSelected()}</div>
          )}
        </WrapperDisable>
      </InputTemplate>
    );
  },
  previewImage: props => {
    const {
      name = '',
      design = '',
      disabled = false,
      value = '',
      isViewMode = false,
      errors = [],
      onChange = () => {},
    } = props;

    return (
      <InputTemplate {...props}>
        <PreviewImage
          design={design}
          disabled={disabled}
          errors={errors}
          isViewMode={isViewMode}
          value={value}
          onChange={dataOut => onChange(name)(dataOut)}
        />
      </InputTemplate>
    );
  },
  contentSource: props => {
    const {
      label = '',
      name = '',
      initValue = null,
      disabled = false,
      isViewMode = false,
      errors = [],
      journeySettings = {},
      onChange = () => {},
    } = props;

    const isProd = isProduction();
    const userId = getCurrentUserId();
    const accountId = getCurrentOwnerId();
    const token = getToken();

    const serviceAuth = useMemo(
      () => ({
        url: `${isProd ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX}/api/v1`,
        userId,
        accountId,
        token,
      }),
      [userId, token, accountId, isProd],
    );

    return (
      <AccordionStyled
        defaultExpanded
        style={{
          marginTop: 0,
          borderRadius: '10px',
          borderColor: !_isEmpty(errors[0]) ? '#f44336' : '#B8CFE6',
        }}
      >
        <AccordionSummaryCustom
          expandIcon={<ExpandMoreIcon style={{ color: '#585858' }} />}
          aria-controls="content-content-source"
          id="header-content-source"
          IconButtonProps={{
            edge: 'start',
          }}
        >
          <HeadingAccordion style={{ fontSize: '12px', color: '#000000' }}>
            {label}
          </HeadingAccordion>
        </AccordionSummaryCustom>
        <AccordionDetailsCustom className="first-div-full">
          <WrapperDisable disabled={disabled || isViewMode}>
            <ContentSources
              useQueryClient={false}
              hideOptionTypes={{
                ranking: true,
              }}
              serviceAuth={serviceAuth}
              initValue={initValue}
              journeySettings={journeySettings}
              onChange={contentSourceOut => onChange(name)(contentSourceOut)}
            />
          </WrapperDisable>
        </AccordionDetailsCustom>
      </AccordionStyled>
    );
  },
  textArea: props => {
    const {
      name,
      maxLength,
      errors = [],
      disabled = false,
      isViewMode = false,
      showExtraInfo = true,
      value = '',
      tooltipTitle = 'The content must not contain any accents',
      onChange = () => {},
    } = props;
    const [text, setText] = useState(value);
    const textDebounce = useDebounce(text, 350);

    const currentLength = useMemo(() => text.length, [text]);
    const numericCountSms = useMemo(() => {
      if (currentLength <= 122) {
        return 1; // 1 message for <= 122 characters
      }
      if (currentLength <= 268) {
        return 2; // 2 messages for 123-268 characters
      }
      return 3; // 3 messages for > 268 characters
    }, [currentLength]);

    useEffect(() => {
      onChange(name)(textDebounce);
    }, [textDebounce, name]);

    return (
      <InputTemplate {...props}>
        <WrapperAreaTextMessage isError={!!errors[0]}>
          <TextAreaStyled
            maxLength={maxLength}
            value={text}
            bordered={false}
            disabled={disabled || isViewMode}
            onChange={event => {
              if (event.target) {
                setText(event?.target?.value);
              }
            }}
          />
        </WrapperAreaTextMessage>
        <SupportiveText isShow={!!errors[0]}>{errors[0]}</SupportiveText>
        <TextExtraInfo isShow={showExtraInfo}>
          <Tooltip
            title={tooltipTitle}
            placement="top"
            mouseEnterDelay={0.5}
            destroyTooltipOnHide
          >
            <ViewDetailsInformationIcon
              width={20}
              height={20}
              color="#595959"
              style={{ cursor: 'pointer' }}
            />
          </Tooltip>
          <span style={{ height: 20, lineHeight: '20px' }}>
            {currentLength}/{maxLength} characters | SMS: {numericCountSms}
          </span>
        </TextExtraInfo>
      </InputTemplate>
    );
  },
  multiLangPersonalize: props => {
    const handleChange = ({ lang, value }) => {
      props.onChange(props.name)(
        produce(props.value, draft => {
          const index = draft.findIndex(input => input.lang === lang);

          if (index > -1) {
            draft[index].value = value;
          }
        }),
      );
    };

    const initValueByLang = useMemo(() => {
      return _keyBy(props.initValue, 'lang');
    }, [props.initValue, props.componentKey]);

    const enableShortLink = checkEnableShortLink({
      defaultEnabled: props.defaultEnableShortLink,
      channelCode: props.channelElement,
      catalogCode: props.catalogCode,
      name: props.name,
    });

    return (
      <>
        <Grid
          item
          sm={props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={props.colRight || 9}
          className={classNames('grid-col-right')}
        >
          <Flex vertical gap={5}>
            {props.value.map(input => {
              const errMessage = props.errors.find(
                err => err.lang === input.lang,
              )?.message;
              const initData = initValueByLang[input.lang]?.value;

              return (
                <React.Fragment key={input.lang}>
                  <Flex align="flex-start" gap={5}>
                    <WrapCountryFlag
                      height="35px"
                      isViewMode={props.isViewMode}
                    >
                      <CountryFlag
                        className={`country-flag ${input.lang.toLowerCase()} `}
                      />
                    </WrapCountryFlag>
                    <div style={{ flex: 1 }}>
                      <TinymceEditor
                        componentKey={`${props.componentKey}-${input.lang}`}
                        typeComponent="input"
                        placeholder={props.placeholder}
                        appendPersonalizeType={props.appendPersonalizeType}
                        groupCodes={props.groupCodes}
                        eventValue={props.eventValue}
                        isApendBOContentSource
                        showPersonalization
                        initData={initData}
                        value={input.value}
                        canMultipleLine={props.shouldEscapeHtml}
                        shouldEscapeHtml={props.shouldEscapeHtml}
                        onChange={value =>
                          handleChange({ lang: input.lang, value })
                        }
                        enableShortLink={
                          enableShortLink || props.enableShortLinkSpecific
                        }
                        fullWidth
                        isViewMode={props.isViewMode}
                        errors={errMessage ? [errMessage] : []}
                      />
                    </div>
                  </Flex>
                  {errMessage && (
                    <FormHelperText id="component-helper-text" error>
                      {errMessage}
                    </FormHelperText>
                  )}
                </React.Fragment>
              );
            })}
          </Flex>
        </Grid>
      </>
    );
  },
  multiLangDropdown: props => {
    const language = _get(props, 'value.lang', 'EN');
    const targetId = _get(props, 'value.targetId', null);
    const error = _get(props, 'errors[0]', '');
    const destinationId = _get(props, 'destinationInfo.value', null);
    const category =
      _get(props, 'destinationInput.category.value.value') ||
      _get(props, 'destinationInput.category.value') ||
      null;

    const isAllowAdjustColumn =
      props.isAdjustCol && props.isBlastCampaign && !props.isViewMode;

    const [categoryTargets, setCategoryTargets] = useImmer({
      loading: false,
      data: {
        [Category.Promotions]: { EN: [], VI: [] },
        [Category.News]: { EN: [], VI: [] },
        [Category.Events]: { EN: [], VI: [] },
        [Category.Voucher]: { EN: [], VI: [] },
      },
    });

    useEffect(() => {
      if (category && destinationId) {
        const shouldRefetch = getObjectPropSafely(
          () =>
            !categoryTargets.data[category].EN.length &&
            !categoryTargets.data[category].VI.length,
          true,
        );

        if (shouldRefetch) {
          fetchListTargets({ category, destinationId });
        }
      }
    }, [category, destinationId]);

    const fetchListTargets = async params => {
      try {
        setCategoryTargets(draft => {
          draft.loading = true;
        });

        const response = await ThirdPartyService.AeonMall.getListTargets({
          data: {
            category: params.category,
            destinationId: params.destinationId,
          },
        });

        if (Array.isArray(response?.data)) {
          const options = response.data.reduce(
            (acc, targetItem) => {
              if (Array.isArray(targetItem?.translations)) {
                for (const translation of targetItem?.translations) {
                  const lang = translation.locale?.toUpperCase();

                  acc[lang].push({
                    label: translation.title,
                    value: translation.targetId,
                  });
                }
              }

              return acc;
            },
            { EN: [], VI: [] },
          );

          setCategoryTargets(draft => {
            draft.data[params.category] = options;
          });

          const validTargetId = options[language].some(
            item => item?.value === targetId,
          );

          if (!targetId || !validTargetId) {
            const defaultValue = _get(options, `${language}[0].value`, null);

            handleChange({
              targetId: defaultValue,
            });
          }
        }
      } finally {
        setCategoryTargets(draft => {
          draft.loading = false;
        });
      }
    };

    const handleChange = data => {
      props.onChange(props.name)(
        produce(props.value, draft => {
          for (const [key, value] of Object.entries(data)) {
            draft[key] = value;
          }
        }),
      );
    };

    const targets = useMemo(() => {
      const list = _get(categoryTargets, ['data', category, language], []);
      const map = _keyBy(list, 'value');

      return {
        list,
        map,
      };
    }, [categoryTargets.data, category, language]);

    const languages = useMemo(() => {
      const list = [
        {
          value: 'EN',
          label: 'English',
        },
        {
          value: 'VI',
          label: 'Việt Nam',
        },
      ];
      const map = _keyBy(list, 'value');

      return { list, map };
    }, []);

    return (
      <>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colLeft || 3}
          className={classNames(
            'grid-col-left',
            props.classes.title,
            props.classes.styledChannel,
            isAllowAdjustColumn && props.classes.adjustCol,
          )}
          style={{
            justifyContent: props.isTitleAlignLeft ? 'flex-start' : 'flex-end',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title className={props.classes.spacingTitle}>
              {!props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}>* </span>
                )}
              {props.label}
              {props.isTitleAlignLeft &&
                props.isRequired &&
                !props.isViewMode && (
                  <span className={props.classes.redStar}> *</span>
                )}
            </Title>
          </WrapperCenterFlexEnd>
        </Grid>
        <Grid
          item
          sm={isAllowAdjustColumn ? 'auto' : props.colRight || 9}
          className={classNames('grid-col-right', props.classes.content)}
          style={isAllowAdjustColumn ? { flex: 1 } : {}}
        >
          <WrapMultiLangDropdown>
            <Flex align="center" gap={5}>
              <UISelect
                onlyParent
                use="tree"
                isSearchable
                options={languages.list}
                value={languages.map[language]}
                onChange={({ value }) => handleChange({ lang: value })}
                isShowLabelFlagSelected={false}
                fullWidthPopover={false}
                isViewMode={props.isViewMode}
                searchNodataLabel="No data available"
                ItemComponent={({ name, label }) => (
                  <WrapItemCountryFlag>
                    <CountryFlag
                      className={`country-flag ${name.toLowerCase()}`}
                    />
                    <span className="country-flag-label">{label}</span>
                  </WrapItemCountryFlag>
                )}
                DropdownButnComponent={() => (
                  <WrapDropdownCountryFlag>
                    <CountryFlag
                      className={`country-flag ${language.toLowerCase()} `}
                    />
                  </WrapDropdownCountryFlag>
                )}
                renderViewMode={option => (
                  <WrapItemCountryFlag isViewMode>
                    <CountryFlag
                      className={`country-flag ${option?.value?.toLowerCase()}`}
                    />
                  </WrapItemCountryFlag>
                )}
              />
              <div style={{ width: 'calc(100% - 46px)' }}>
                <Spin spinning={categoryTargets.loading}>
                  <UISelect
                    onlyParent
                    use="tree"
                    feKey={language}
                    isSearchable
                    options={targets.list}
                    value={targets.map[targetId]}
                    onChange={({ value }) => handleChange({ targetId: value })}
                    searchNodataLabel="No data available"
                    placeholder={getTranslateMessage(
                      TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                      'Select an item',
                    )}
                    fullWidthPopover
                    isViewMode={props.isViewMode}
                    disabled={props.disabled}
                    errors={error ? [error] : []}
                  />
                </Spin>
              </div>
            </Flex>
            {error && (
              <FormHelperText id="component-helper-text" error>
                {error}
              </FormHelperText>
            )}
          </WrapMultiLangDropdown>
        </Grid>
      </>
    );
  },
  datetime: props => {
    const {
      name,
      // design,
      isViewMode,
      disabled,
      placeholder,
      value,
      showTimeZonePortal,
      disabledPastDate,
      onChange = () => {},
    } = props;

    return (
      <InputTemplate {...props}>
        <DateTime
          // design={design}
          isViewMode={isViewMode}
          disabled={disabled}
          placeholder={placeholder}
          disabledPastDate={disabledPastDate}
          showTimeZonePortal={showTimeZonePortal}
          value={value}
          onChange={datetimeOut => {
            onChange(name)(datetimeOut);
          }}
        />
        <FormHelperText
          id="component-helper-text"
          error={!!props.errors[0]}
          style={{ width: '100%', marginTop: '-5px' }}
        >
          {props.errors[0]}
        </FormHelperText>
      </InputTemplate>
    );
  },
  customFields: props => {
    const {
      initValue,
      label,
      isViewMode,
      isRequired,
      name,
      destinationInfo,
      errors,
      configure,
      groupCodes,
      componentKey,
      otherData,
      onChange,
      onChangeOthers,
    } = props;

    const enableShortLink = checkEnableShortLink({
      defaultEnabled: props.defaultEnableShortLink,
      channelCode: props.channelElement,
      catalogCode: props.catalogCode,
      name: props.name,
      isHideShortLink: props.isHideShortLink,
    });

    const { isSendMultiple, isBroadcast } = pickBroadcastDestDelivery(
      props.destinationSettings,
    );

    const handleChangeFields = useCallback(
      newData => {
        if (isFunction(onChange)) {
          onChange(name)(newData);
        }
      },
      [name, onChange],
    );

    return (
      <CustomFields
        componentKey={componentKey}
        errors={errors}
        initValue={initValue}
        groupCodes={groupCodes}
        label={label}
        destinationId={destinationInfo?.destinationId}
        isViewMode={isViewMode}
        isRequired={isRequired}
        otherData={otherData}
        isForceHideBtnPersonalization={isBroadcast && !isSendMultiple}
        enableShortLink={enableShortLink || props.enableShortLinkSpecific}
        onChange={handleChangeFields}
        onChangeOthers={onChangeOthers}
      />
    );
  },
  whatsappTemplate: props => {
    const {
      name,
      initValue,
      configure,
      eventValue,
      groupCodes,
      otherData,
      itemTypeId,
      componentKey,
      isViewMode,
      errors,
      options,
      isBlastCampaign,
      destinationInfo,
      callbackWhatsappTemplate,
    } = props;
    // console.log('whatsappTemplate props :>>', props);
    const destinationId = destinationInfo?.destinationId;

    const { isSendMultiple, isBroadcast } = pickBroadcastDestDelivery(
      props.destinationSettings,
    );
    const onChange = useCallback(props.onChange(name), [name, props.onChange]);
    const onChangeOthers = useCallback(props.onChangeOthers(name), [
      name,
      props.onChangeOthers,
    ]);

    return (
      <WhatsappTemplate
        key={componentKey}
        templateList={options}
        destinationId={destinationId}
        componentKey={componentKey}
        initValue={initValue}
        otherData={otherData}
        itemTypeId={itemTypeId}
        isBlastCampaign={isBlastCampaign}
        isForceHideBtnPersonalization={isBroadcast && !isSendMultiple}
        eventValue={eventValue}
        groupCodes={groupCodes}
        isViewMode={isViewMode}
        errors={errors}
        onChangeOthers={onChangeOthers}
        onChange={onChange}
        callbackWhatsappTemplate={callbackWhatsappTemplate}
      />
    );
  },
  basicInput: props => {
    const {
      name,
      value,
      errors,
      styles,
      disabled,
      isViewMode,
      maxLength,
      placeholder,
      onChange,
    } = props;

    return (
      <InputTemplate {...props}>
        <BasicInput
          name={name}
          value={value}
          errors={errors}
          styles={styles}
          disabled={disabled || isViewMode}
          maxLength={maxLength}
          placeholder={placeholder}
          onChange={onChange(name)}
        />
      </InputTemplate>
    );
  },
};

const ExtraInfo = props => {
  const {
    errors,
    name,
    isBlastCampaign,
    value,
    isHideExtraInfo,
    catalogCode,
    isFullWidth,
    editorWidth,
    method,
    channelElement,
    baseHintInfo,
    contentAccentType,
    maxLength,
    maxLengthAccented,
    maxLengthUnaccented,
  } = props;

  const channelFlags = useChannelFlags(channelElement);
  const {
    isChannelSMS,
    isChannelTelegram,
    isChannelAppPush,
    isChannelViber,
    isChannelSmartInbox,
  } = channelFlags;

  const { SMART_INBOX, LINE, ANTSOMI_APP_PUSH, ZNS, SMS_IMEDIA } = CATALOG_CODE;

  const isSmartInbox = isChannelSmartInbox && props.catalogCode === SMART_INBOX;
  const isAntsomiLine = catalogCode === LINE;
  const isAntsomiAppPush = isChannelAppPush && catalogCode === ANTSOMI_APP_PUSH;
  const isZaloZNS = catalogCode === ZNS;
  const isSmsIMedia = catalogCode === SMS_IMEDIA;

  const isOrchestrationIMedia = !isBlastCampaign && isSmsIMedia;
  const helperTextAndIcon = isSmartInbox || isAntsomiLine || isChannelSMS;
  let hintTooltip = MAP_TRANSLATE.notASCII;
  if (isSmsIMedia && method === 'sendAdvertisementMessage') {
    hintTooltip = MAP_TRANSLATE.hintSmsIMedia;
  }

  const notShowNumSMSLabel =
    isAntsomiAppPush || isSmartInbox || isAntsomiLine || isZaloZNS;
  const haveSMSCount = isChannelSMS || isChannelTelegram || isChannelViber;
  const numSMS = haveSMSCount ? calculateSMSCount(value?.length) : null;
  const smsLimit = getSmsLimit(name, catalogCode, {
    baseHintInfo,
    contentAccentType,
    maxLength,
    maxLengthAccented,
    maxLengthUnaccented,
  });

  const showHelpText1stOrder =
    isBlastCampaign || isOrchestrationIMedia || !helperTextAndIcon;

  const infoIcon = (
    <InfoOutlinedIcon
      className="icon-info-rule"
      style={
        isBlastCampaign
          ? {
              alignSelf: 'self-start',
              marginLeft: 'auto',
              marginTop: 2,
            }
          : {
              marginLeft: isOrchestrationIMedia ? 4 : 0,
              marginTop: isOrchestrationIMedia ? 0 : 3,
            }
      }
    />
  );

  const hasTag = name === 'assigneeId' ? hasTags(value) : false;

  return (
    <HelpText
      baseHintInfo={baseHintInfo}
      catalogCode={catalogCode}
      currentLength={value?.length}
      limitLength={smsLimit}
      contentAccentType={contentAccentType}
      errors={errors}
      hasTags={hasTag}
    >
      <div className="d-flex align-items-center justify-content-end">
        {showHelpText1stOrder && (
          <FormHelperText
            id="component-helper-text"
            error={!!errors[0]}
            style={{
              marginRight: isOrchestrationIMedia ? 'auto' : '8px',
              marginTop: 0,
              display: 'flex',
              width: 'fit-content',
              color: '#f44336',
            }}
            width={isFullWidth ? '100%' : editorWidth}
          >
            {errors}
          </FormHelperText>
        )}

        {!isChannelViber && (
          <>
            {helperTextAndIcon ? (
              <Flex
                gap={10}
                className={classNames({ 'width-100': !isOrchestrationIMedia })}
              >
                {!showHelpText1stOrder && (
                  <FormHelperText
                    id="component-helper-text"
                    error={!!errors[0]}
                    width={isFullWidth ? '100%' : editorWidth}
                  >
                    {errors}
                  </FormHelperText>
                )}
                {infoIcon}
              </Flex>
            ) : (
              <>
                {!isHideExtraInfo && (
                  <UITippy content={hintTooltip}>{infoIcon}</UITippy>
                )}
              </>
            )}
            {!isHideExtraInfo && (
              <p
                className="text-rule"
                style={{
                  margin: '2px 0px',
                  alignSelf: 'self-start',
                  lineHeight: '20px',
                  whiteSpace: 'nowrap',
                }}
              >
                {value.length}/{smsLimit} characters&nbsp;
                {notShowNumSMSLabel ? '' : `| SMS: ${numSMS}`}
              </p>
            )}
          </>
        )}
      </div>
    </HelpText>
  );
};

export const InputTemplate = props => {
  const {
    isAdjustCol,
    isBlastCampaign,
    isViewMode,
    colLeft,
    colRight,
    isTitleAlignLeft,
    classes,
    isRequired,
    label,
    errors,
    children,
  } = props;

  const isAllowAdjustColumn = isAdjustCol && isBlastCampaign && !isViewMode;

  const titleAlignment = isTitleAlignLeft ? 'flex-start' : 'flex-end';
  const titleClassNames = classNames(
    classes.title,
    classes.styledChannel,
    isAllowAdjustColumn && classes.adjustCol,
  );
  const wrapperStyle = { justifyContent: titleAlignment };

  return (
    <>
      <Grid
        item
        sm={isAllowAdjustColumn ? 'auto' : colLeft || 3}
        className={classNames('grid-col-left', titleClassNames)}
        style={wrapperStyle}
      >
        <WrapperCenterFlexEnd>
          <Title className={classes.spacingTitle}>
            {!isTitleAlignLeft && isRequired && !isViewMode && (
              <span className={classes.redStar}>* </span>
            )}
            {label}
            {isTitleAlignLeft && isRequired && !isViewMode && (
              <span className={classes.redStar}> *</span>
            )}
          </Title>
          {!!errors[0] && <div style={{ height: '1.75rem', width: '100%' }} />}
        </WrapperCenterFlexEnd>
      </Grid>
      <Grid
        item
        sm={isAllowAdjustColumn ? 'auto' : colRight || 9}
        className={classNames('grid-col-right', classes.content)}
        style={isAllowAdjustColumn ? { flex: 1 } : {}}
      >
        {children}
      </Grid>
    </>
  );
};

const InputLabel = props => {
  const {
    classes,
    isRequired,
    isViewMode,
    isTitleAlignLeft,
    children,
    isChannelEmailBlast = false,
  } = props;
  return (
    <Title
      isChannelEmailBlast={isChannelEmailBlast}
      className={classes.spacingTitle}
    >
      {!isTitleAlignLeft && isRequired && !isViewMode && (
        <span className={classes.redStar}>* </span>
      )}
      {isChannelEmailBlast ? 'Body' : children}
      {isTitleAlignLeft && isRequired && !isViewMode && (
        <span className={classes.redStar}> *</span>
      )}
    </Title>
  );
};

export const getTypeRender = ({
  inputType,
  inputFormat,
  canImportFromMediaLibrary,
  canAddPersonalization,
}) => {
  let typeRender = 'singleLineText';
  if (inputType === 'multitext') {
    typeRender = 'multiLineText';
  } else if (inputType === 'keyvalue') {
    typeRender = 'keyvalue';
  } else if (inputType === 'editor') {
    typeRender = 'editor';
  } else if (inputType === 'checkbox') {
    typeRender = 'checkbox';
  } else if (inputType === 'checkboxGrid') {
    typeRender = 'checkboxGrid';
  } else if (inputType === 'radioButton') {
    typeRender = 'radio';
  } else if (inputType === 'inputUnit') {
    typeRender = 'inputUnit';
  } else if (inputType === 'group') {
    typeRender = 'group';
  } else if (inputType === 'select') {
    typeRender = 'selectDropdown';
  } else if (inputType === 'text') {
    if (inputFormat === 'password') {
      typeRender = 'password';
    } else if (inputFormat === 'number') {
      typeRender = 'number';
    } else if (canAddPersonalization) {
      typeRender = 'singleLineAddPersonalize';
    } else if (canImportFromMediaLibrary) {
      typeRender = 'imageUrl';
    }
  } else if (inputType === 'html_editor') {
    typeRender = 'htmlEditor';
  } else if (inputType === MEDIA_TEMPLATE) {
    typeRender = 'mediaTemplate';
  } else if (inputType === JSON_TEMPLATE) {
    typeRender = 'jsonTemplate';
  } else if (inputType === EMAIL_TEMPLATE) {
    typeRender = 'emailTemplate';
  } else if (inputType === JOURNEY_TEMPLATE) {
    typeRender = 'journeyTemplate';
  } else if (inputType === 'tagButtons') {
    typeRender = 'tagButtons';
  } else if (inputType === 'markup') {
    typeRender = 'markup';
  } else if (inputType === 'anchorSwitch') {
    typeRender = 'anchorSwitch';
  } else if (inputType === 'lineTemplate') {
    typeRender = 'lineTemplate';
  } else if (inputType === 'sticker') {
    typeRender = 'sticker';
  } else if (inputType === 'multilanguage') {
    typeRender = 'multilanguage';
  } else if (inputType === 'action_button') {
    typeRender = 'actionButton';
  } else if (inputType === 'snapshotRender') {
    typeRender = 'snapshotRender';
  } else if (inputType === 'selectOptions') {
    typeRender = 'selectOptions';
  } else if (inputType === 'color') {
    typeRender = 'color';
  } else if (inputType === 'selectIcon') {
    typeRender = 'selectIcon';
  } else if (inputType === 'slideBar') {
    typeRender = 'slideBar';
  } else if (inputType === 'preview_image') {
    typeRender = 'previewImage';
  } else if (inputType === 'contentSource') {
    typeRender = 'contentSource';
  } else if (inputType === 'textArea') {
    typeRender = 'textArea';
  } else if (inputType === 'multiLangPersonalize') {
    typeRender = 'multiLangPersonalize';
  } else if (inputType === 'multiLangDropdown') {
    typeRender = 'multiLangDropdown';
  } else if (inputType === 'datetime') {
    typeRender = 'datetime';
  } else if (inputType === 'none') {
    typeRender = 'none';
  } else if (inputType === 'custom_fields') {
    typeRender = 'customFields';
  } else if (inputType === 'whatsappTemplate') {
    typeRender = 'whatsappTemplate';
  } else if (inputType === 'basicInput') {
    typeRender = 'basicInput';
  }

  return typeRender;
};

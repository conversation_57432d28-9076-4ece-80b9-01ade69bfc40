import { Map } from 'immutable';
import {
  safeParse,
  safeParseInt,
  generateKey,
} from '../../../../../../../../utils/common';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { NODE_TYPE } from '../constant';

export function getDefaultBranchNodeSplit(flattenNodes) {
  return [
    {
      id: flattenNodes.filter(each => (each.label === 'Branch 1' || each.label === 'Group 1')).length > 0
        ? flattenNodes.filter(each => (each.label === 'Branch 1' || each.label === 'Group 1'))[0].nodeId
        : generateKey(),
      // id: generateKey(),
      value: 50,
      label: getTranslateMessage(TRANSLATE_KEY._TITL_GROUP, 'Group 1', {
        number_group: 1,
      }),
    },
    {
      id: flattenNodes.filter(each => (each.label === 'Branch 2' || each.label === 'Group 2')).length > 0
        ? flattenNodes.filter(each => (each.label === 'Branch 2' || each.label === 'Group 2'))[0].nodeId
        : generateKey(),
      value: 50,
      label: getTranslateMessage(TRANSLATE_KEY._TITL_GROUP, 'Group 2', {
        number_group: 2,
      }),
    },
  ];
}

export function toNodeSplitBranchAPI(nodeInfo) {
  const branches = safeParse(nodeInfo.get('branches'), []);
  const meta = {
    numBranchs: branches.length,
    // fe_branches: branches,
  };

  return meta;
}

export function toNodeSplitBranchUI(metadata, node, fullInfoflattenNodes) {
  const data = Map({
    numBranchs: metadata.numBranchs,
    // branches: metadata.fe_branches,
    branches: getBranchesFromFlattenNodesFromAPI(node, fullInfoflattenNodes),
    // branches: [],
  });

  return data;
}

export function getBranchesFromFlattenNodesFromAPI(
  activeNode,
  fullInfoflattenNodes,
) {
  const listChildren = fullInfoflattenNodes.filter(
    item => item.parentId === activeNode.nodeId,
  );
  const branches = [];
  listChildren.forEach((item, index) => {
    const metadata = safeParse(item.metadata, {});

    const tmp = {
      id: item.nodeId,
      label:
        metadata.branchName ||
        getTranslateMessage(TRANSLATE_KEY._TITL_GROUP, 'Group', {
          number_group: index + 1,
        }),
      value: (metadata.weight || 0) * 100,
    };

    if (item.type === NODE_TYPE.AB_SPLIT_CONTROL_NODE) {
      tmp.isControlGroup = true;
    };
    // console.log('item.metadata', tmp, metadata);
    branches.push(tmp);
  });
  // console.log('branches', JSON.stringify(branches));
  return branches;
}

export function getBranchesFromFlattenNodesFromUI(
  nodeData,
  activeNode,
  fullInfoflattenNodes,
) {
  const listChildren = fullInfoflattenNodes.filter(
    item => item.parentId === activeNode.nodeId,
  );
  const branches = [];
  listChildren.forEach((item, index) => {
    const metadata = nodeData.get(item.nodeId);

    if (!metadata) return;

    const tmp = {
      id: item.nodeId,
      label:
        metadata.get('branchName') ||
        getTranslateMessage(TRANSLATE_KEY._TITL_GROUP, 'Group', {
          number_group: index + 1,
        }),
      value: metadata.get('weight'),
    };

    if (item.type === NODE_TYPE.AB_SPLIT_CONTROL_NODE) {
      tmp.isControlGroup = true;
      tmp.label = getTranslateMessage(TRANSLATE_KEY._TITL_CONTROL_GROUP, 'Control Group');
    }
    // console.log('item.metadata', tmp, metadata);
    branches.push(tmp);
  });
  // console.log('branches', JSON.stringify(branches));
  return branches;
}

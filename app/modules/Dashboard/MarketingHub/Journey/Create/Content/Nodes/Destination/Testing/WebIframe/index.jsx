/* eslint-disable react/prop-types */
import React, { useEffect, memo, useRef } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';

// import ErrorBoundary from 'components/common/ErrorBoundary';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';

// Components
// import { UILoading as Loading } from '@xlab-team/ui-components';
import { MODULE_CONFIG, ZONE_RENDER_TYPE_MAPPING } from './config';
import makeSelectCustomer, {
  makeSelectCustomerFilter,
  makeSelectCustomerColumn,
  makeSelectSourceInfo,
  makeSelectZoneInfo,
  makeSelectIsLoading,
} from './selectors';
import saga from './saga';
import reducer from './reducer';
import {
  init,
  getList,
  reset,
} from '../../../../../../../../../../redux/actions';
// import LayoutContent from '../../../../../../../../../../components/Templates/LayoutContent';

export function WebIframe(props) {
  const {
    // isLoading,
    itemTypeId,
    storyId,
    insightPropertyId,
    zoneId,
    zoneRenderType,
    sourceInfo,
    variantExtraData,
    isOpenModal,
    zoneInfo,
    toggleWebIframe
  } = props;

  // const iframeRef = useRef();
  const initRef = useRef(false);

  useEffect(() => {
    props.init({ itemTypeId, storyId, insightPropertyId, zoneId });
    return () => {
      props.reset();
      if (typeof toggleWebIframe === 'function') {
        toggleWebIframe(false);
      }
    };
  }, []);

  useEffect(() => {
    let child = null;
    if (
      sourceInfo &&
      sourceInfo.domain &&
      zoneInfo &&
      (initRef.current || isOpenModal)
    ) {
      child = window.open(sourceInfo.domain);
      initRef.current = true;

      const getEvent = e => {
        if (
          e.data &&
          e.data.type === 'preview-antsomi-cdp-loading-script-done'
        ) {
          child.postMessage(
            {
              type: 'preview-antsomi-cdp-load-script',
            },
            '*',
          );
        } else if (
          e.data &&
          e.data.type === 'preview-antsomi-cdp-campaign-waiting'
        ) {
          const buildViewPreview = Object.entries(
            variantExtraData.content.views,
          ).reduce((res, cur) => [...res, { ...cur[1], id: cur[0] }], []);

          child.postMessage(
            {
              ...variantExtraData.content.template_settings,
              views: buildViewPreview,
              messageType: 'preview-antsomi-cdp-campaign',
              targetScript: e.data.id,
              deliveryConfig: {
                zoneSelector: zoneInfo.css_selector,
                zoneRenderType: ZONE_RENDER_TYPE_MAPPING[zoneRenderType],
              },
            },
            '*',
          );
        }
      };

      if (child) {
        window.addEventListener('message', getEvent);
      }

      return () => {
        window.removeEventListener('message', getEvent);
      };
    }

    return () => {};
  }, [sourceInfo, zoneInfo, isOpenModal]);

  return (
    <></>
    // <ErrorBoundary path="app/modules/Dashboard/Profile/Customer/List/index.jsx">
    //   <LayoutContent style={{ padding: '0rem 0.75rem 0rem 0.75rem' }}>
    //     <Loading isLoading={isLoading} />
    //   </LayoutContent>
    // </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  customer: makeSelectCustomer(),
  filter: makeSelectCustomerFilter(),
  column: makeSelectCustomerColumn(),
  sourceInfo: makeSelectSourceInfo(),
  zoneInfo: makeSelectZoneInfo(),
  isLoading: makeSelectIsLoading(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
  memo,
)(WebIframe);

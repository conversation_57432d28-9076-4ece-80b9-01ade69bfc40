import styled, { css } from 'styled-components';

import ModalFooter from 'components/Molecules/ModalFooter/index';
import colors from 'utils/colors';
import {
  transition,
  fonts,
  breakdownMd,
} from '../../../../../../../../../utils/variables';
import { Button } from '@antscorp/antsomi-ui';
export const ContainerCustomize = styled.div`
  height: calc(100vh - 50px);
  overflow: hidden;
  display: flex;
`;

export const WrapperMenuCustomize = styled.ul`
  margin-top: 0;
  margin-bottom: 0;
  list-style: none;
  padding-left: 0;
  max-width: 18.5rem;
  flex-shrink: 0;
  flex-grow: 1;
  border-right: 1px solid ${colors.platinum};
`;

export const MenuItemCustomize = styled.li`
  height: 2.6875rem;
  padding-left: 2rem;
  display: flex;
  align-items: center;
  transition: all ${transition} ease-in-out;
  font-size: 0.875rem;
  font-family: ${fonts.roBold};
  cursor: pointer;
  border-left: 4px solid
    ${props => (props.active ? colors.primary : 'transparent')};
  background-color: ${props =>
    props.active ? colors.azureishWhite : 'transparent'};
  &:hover {
    background-color: ${colors.azureishWhite};
    border-left-color: ${colors.primary};
  }
`;

export const WrapperContentCustomize = styled.div`
  /* max-width: 40.0625rem; */
  flex-grow: 1;
  /* border-right: 1px solid ${colors.platinum}; */
  height: ${props => `${props.height}`};
`;

export const WrapperFilterContent = styled.div`
  max-height: 4.0625rem;
  display: flex;
  align-items: center;
  padding: 14.5px 1.5rem;
  border-bottom: 1px solid ${colors.platinum};
  > div {
    width: 100%;
  }
`;

export const WrapperSelectedColumn = styled.div`
  position: relative;
  height: 34.875rem;
  height: calc(100vh - 265px);
`;

export const WrapperListColumn = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
`;

export const WrapperColumnItem = styled.div`
  margin-top: 1rem;
  margin-left: 1.5rem;
  &:first-child {
    margin-top: 0;
  }
  position: relative;
`;

export const WrapperSuggestion = styled.div`
  max-width: 19.9375rem;
  width: 100%;
  border-left: 1px solid #e5e5e5;
`;

export const SuggestionColumn = styled.div`
  height: 100%;
  padding: 15px;
`;

export const SuggestionTitle = styled.p`
  display: flex;
  justify-content: space-between;
  color: #000000;

  margin-top: 0;
  margin-bottom: 0;
  height: 50px;
  padding: 15px;

  border-bottom: 1px solid #e5e5e5;
  /* span {
    color: ${colors.primary};
  } */
  align-items: center;
`;

export const WrapperColumnSelection = styled.div`
  margin-top: 0.5rem;
  height: calc(100% - 40px);
  position: relative;
  overflow: auto;
  padding-bottom: 0.25rem;
`;
export const ButtonRemoveAll = styled(Button)`
  padding: 0px;
`;

export const WrapperDrawer = styled.div`
  width: ${props => props.width};
  overflow: ${props => props.isShowDrawer && 'hidden'};
  height: 100%;
`;

export const ModalFooterExtend = styled(ModalFooter)`
  padding: 0 1.5rem;
  height: 64px;
`;

export const WrapperFooter = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const WrapperButton = styled.div`
  /* margin-left: 56rem; */
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
`;

export const WrapperButtonNotInput = styled.div`
  display: flex;
  width: 100%;
  justify-content: flex-end;
`;

export const Nodata = styled.div`
  display: flex;
  justify-content: center;
`;

export const SpanSelect = styled.span`
  display: flex;
  justify-content: space-between;
`;

export const WrapperDropdown = styled.div`
.addFill {
  width: 7.313rem;
  position: relative;
  
  &-wrapper {
    display: none;
    position: absolute;
    top: 1rem;
    left: 0.125rem;
    width: 24rem;
    z-index: 2;
    box-shadow: 0 0 0.5625rem 0 ${colors.blackBlur};
    border: 1px solid ${colors.platinum};
    background-color: ${colors.white};
    &-show {
      display: block;
    }
    &_input {
      padding: 1rem 0.75rem 0;
    }
  }
  &-recents_wrapper {
    padding: 0.5rem 0;
    border-bottom: 1px solid ${colors.platinum};
  }
  &-filter_wrapper {
    padding: 0.5rem 0;
    max-height: 336px;
    overflow: auto;
    ::-webkit-scrollbar {
      -webkit-appearance: none;
      width: 7px;
      height: 7px;
      -webkit-overflow-scrolling: auto;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.45);
      -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
    }
  }
  &-filter_collapse {
    &_inner {
      /* transition: all ${transition} ease-in-out; */
      height: 0;
      visibility: hidden;
      opacity: 0;
      &.show {
        height: auto;
        visibility: visible;
        opacity: 1;
      }
    }
    &__item {
      height: 2rem;
      display: flex;
      align-items: center;
      padding-left: 3.5rem;
      ${breakdownMd(css`
        font-size: 0.75rem;
      `)}
      cursor: pointer;
      &:hover {
        background-color: ${colors.primary50};
      }
    }
  }
  &-line {
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all ${transition} ease-in-out;
    &:hover {
      background-color: ${colors.primary50};
    }
    &-pointer {
      cursor: pointer;
    }
  }
  &-label {
    padding-left: 2rem;
    padding-right: 0.5rem;
    font-size: 0.875rem;
    ${breakdownMd(css`
      font-size: 0.75rem;
    `)}
  }
  &-value {
    padding-left: 3.5rem;
    font-size: 0.875rem;
    ${breakdownMd(css`
      font-size: 0.75rem;
    `)}
  }
  ul {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    li {
      list-style: none;
    }
  }
}
`;

export const StyleButtonTesting = styled.div`
  position: absolute;
  right: 4px;
  top: 16px;
`;

export const WrapperHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 50px;
`;

export const Title = styled.span`
  font-size: 20px;
  color: #000000;
  text-wrap: nowrap;
`;

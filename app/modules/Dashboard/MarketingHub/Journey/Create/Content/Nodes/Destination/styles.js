/* eslint-disable indent */
// Libraries
import styled, { css } from 'styled-components';

// Components
import { FormHelperText, StepButton, Stepper } from '@material-ui/core';
import { Input } from '@antscorp/antsomi-ui';
import {
  UINumber,
  UIButton as Button,
  UIInputCalendar,
} from '@xlab-team/ui-components';
import { makeStyles, withStyles } from '@material-ui/core/styles';
import MuiAccordionSummary from '@material-ui/core/AccordionSummary';
import MuiAccordionDetails from '@material-ui/core/AccordionDetails';
import MuiAccordion from '@material-ui/core/Accordion';
import Grid from '@material-ui/core/Grid';
import UISquareV2 from 'components/common/UISquareV2';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const { TextArea } = Input;

export const UIGrid = styled(Grid)`
  position: relative;
`;

export const UINumberStyled = styled(UINumber)`
  width: 8rem;
  margin-bottom: 0.25rem;
`;

export const RootContainer = styled.div`
  /* padding-bottom: 30px; */
  .MuiFormControl-marginNormal {
    margin-top: 0 !important;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }

  .MuiTypography-body1 {
    font-size: 0.813rem !important;
  }

  .grid-col-left {
    min-width: 200px !important;
    max-width: 200px !important;
  }

  .grid-col-right {
    min-width: calc(100% - 200px) !important;
    max-width: calc(100% - 200px) !important;
  }

  .unset-position-input-wrapper .input-wrapper {
    position: unset;
  }

  .spin-compose-wrapper {
    width: 100%;
  }

  .tagify--empty .tagify__input {
    min-width: 130px !important;
  }
`;

export const Title = styled.div`
  color: ${props => props.$colorText || '#666'};
  white-space: nowrap;
  font-size: 12px;
  width: ${props => props.isChannelEmailBlast && '100px'};

  /* margin-right: 0.5rem; */
`;

export const CalloutTextInfo = styled.div`
  margin-top: 4px;
  font-size: 11px;
  font-weight: 400;
  color: #999999;
  text-align: right;
`;

export const CalloutText = styled.div`
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 12px;
`;

export const WrapperCenter = styled.div`
  display: flex;
  /* flex-direction: column; */
  justify-content: space-between;
  align-items: center;
  height: 100%;
`;
export const WrapperCenterFlexEnd = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
`;
export const WrapperTitleCustomInput = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  ${({ isBlastCampaign, isTitleAlignLeft }) =>
    isBlastCampaign || isTitleAlignLeft
      ? css`
          align-items: flex-start;
        `
      : css`
          align-items: flex-end;
          padding-right: 30px;
        `};
`;

export const WrapperCenterFlexStart = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
`;

export const ButtonPre = styled(Button)`
  padding-left: 5px;
  margin-top: 12px;
`;

export const WrapperGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 2fr;
  grid-template-rows: 1.5rem;
  align-items: center;
`;
export const WrapperMargin = styled.div`
  width: 100%;
  height: '1rem';
  margin: ${props => props.margin || '1rem'};
`;

export const useStyles = makeStyles(() => ({
  root: {
    fontSize: '0.813rem !important',
    height: '100%',
    paddingBottom: '0',
  },
  paper: {
    textAlign: 'center',
  },
  padding: {
    padding: '10px 20px',
    // alignItems: 'center',
    alignItems: 'start',
    height: '100%',
    paddingTop: '0',
    paddingBottom: '0',
  },
  paddingLeft: {
    paddingLeft: '2rem',
    // position: 'relative',
  },
  paddingRight: {
    paddingRight: '2rem',
    // borderRight: '1px solid #e6e6e6',
    // position: 'relative',
  },
  paddingContainer: {
    padding: '0 2rem 3rem 0',
    // borderRight: '1px solid #e6e6e6',
  },
  alignContentContainer: {
    alignContent: 'flex-start',
  },
  marginBottom: {
    marginBottom: '10px',
  },
  relativePosition: {
    position: 'relative',
  },
  preview: {
    // borderLeft: '1px solid #e6e6e6',
    fontSize: '12px',
    width: '100%',
    height: '100%',
  },
  fontSize: {
    fontSize: '12px !important',
  },
  titlePreview: {
    fontSize: '12px !important',
    color: '#000000',
    fontWeight: 500,
  },
  maxHeight100: {
    height: '100%',
    width: '100%',
    // paddingTop: '12px'
  },
  maxWidth100: { width: '100%' },
  title: {
    paddingTop: '0.8rem',
  },
  content: {
    paddingTop: '0.4rem',
  },
  alignVariantTab: {
    marginLeft: '-7px',
  },
  styledChannel: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  spacingTitle: {
    paddingRight: '30px',
  },
  redStar: {
    color: '#ff0000',
    fontWeight: 900,
  },
  item: {
    padding: '5px 0',
    '&.custom-space:nth-of-type(3)': {
      marginTop: '-10px',
    },
    '& .pd-left-5': {
      paddingLeft: '5px',
    },
  },
  alignContent: {
    paddingTop: '0.5rem',
    marginTop: '-2px',
  },
  noChooseErrorWrapper: {
    // position: 'relative',
    width: '100%',
  },
  noChooseErrorMessage: {
    position: 'absolute',
    minWidth: '250px',
    left: 'calc((1 / 6) * 100%)',
  },
  adjustCol: {
    // do width của Forecast đc set cố định 300px, để căn chỉnh cho col phía settings thẳng hàng với compose ta cộng thêm 300px để có được tổng width bằng width compose rôi đem chia cho 2 để có được width của compose phía bên trái và tiến hành nhân 3 chia 12 (tỉ lệ Col)
    width: 'calc((100% + 300px) / 8)', // <=> ((100% + 300px) / 2 ) * 3 / 12
  },
}));
export const GroupAction = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding-top: 7px;
  /* border-top: 1px solid #80808057; */
`;

export const WrapperIcon = styled.div`
  margin: auto 0;
  margin-right: 8px;
  cursor: ${props =>
    // console.log('props', props.disable);
    props.disable ? 'not-allowed' : 'pointer'};
`;

export const WrapperToggle = styled.div`
  margin: auto 0;
`;

export const WrapperTextAudiences = styled.div`
  /* width: 100%; */
  text-align: right;
  margin: 0 0 0 auto;
  font-weight: bold;
  color: #005eb8;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
`;

export const WrapperDesignNodeDestination = styled.div`
  width: fit-content;
  margin: auto;
  min-width: 0 !important;
  /* margin-top: 28px; */
`;

export const WrapperUISquare = styled(UISquareV2)`
  margin: 0 20px;
`;

export const WrapperContent = styled.div`
  display: flex;
  justify-content: center;
  gap: 20px;
  .wrapperUISquare {
    border-radius: 10px;

    ${props =>
      props.isNewLayout
        ? css`
            margin-bottom: 0px;
          `
        : css``}
  }
`;

export const WrapperTitle = styled.p`
  font-weight: 700;
`;

// export const P = styled.p`
//   color: red;
//   display: flex;
//   width: 630px;
//   justify-content: left;
// `;
export const WrapperErrors = styled.div`
  /* display: flex;
  justify-content: center; */
`;

export const DisplayContent = styled.div`
  display: ${props => (props.isShow ? 'contents' : 'none')};

  ${({ isBlastCampaign }) =>
    isBlastCampaign
      ? css`
          display: block;
          width: 100%;
          padding: 10px 40px 10px 15px;

          .wrapper-inputs {
            /* width: calc(100% - 300px); */
          }

          .wrapper-hard-fields {
            /* & > div {
              width: calc(100% - 300px);

              .wrapper-media-template {
                width: calc(100% + 300px);
              }
            } */
          }
        `
      : css`
          padding: unset;
        `};
`;

export const StyledStepper = styled(Stepper)`
  width: 305px;
  padding-left: 0 !important;
  padding-right: 0 !important;
  .MuiStepConnector-root.MuiStepConnector-horizontal {
    .MuiStepConnector-lineHorizontal {
      border-top-width: 2px;
    }
    .MuiStepConnector-line {
      border-color: #005eb8;
    }
  }
`;

export const DividerDash = styled.div`
  height: 1px;
  width: 100%;
  border-bottom: 2px dotted #d4d4d4;
  margin: 10px 0;
`;

export const StyledStepButton = styled(StepButton)`
  margin: 0;
  padding: 0;
  .MuiSvgIcon-root.MuiStepIcon-root {
    &:not(.MuiStepIcon-active) {
      fill: #fff !important;
      border: 1px solid #e0e0e0;
      border-radius: 50%;
      text {
        fill: #005eb8;
        font-size: 14px;
      }
    }
  }
  .MuiStepLabel-label {
    color: ${props =>
      props.isError ? '#f44336' : 'rgba(0, 0, 0, 0.87)'} !important;
  }
`;

export const IconWrapper = styled.div`
  height: 22px;

  span.icon-xlab {
    font-size: 24px;
    color: #005fb8;
  }
`;

export const StyledFormHelperText = styled(FormHelperText)`
  &.MuiFormHelperText-root {
    font-size: 0.688rem;
  }
  width: ${({ width = 'auto' }) => width};
`;

export const WarningSMSRules = styled.div`
  margin: ${props => (props.isBlastCampaign ? '10px 0' : '')};
  text-align: right;
  .icon-info-rule {
    font-size: 16px;
    color: #595959;
    margin-right: 6px;
  }

  .text-rule {
    font-size: 12px;
    color: #999;
  }

  .text-warn {
    font-style: italic;
    font-size: 10px;
    color: #999;
    margin: 0;
  }

  width: ${({ width = 'auto' }) => width};
`;

export const StyleTextError = styled.span`
  color: #ff0000;
  font-size: 11px;
`;

export const WrapperTargetAudience = styled.div`
  padding: 5px 10px 0 3px;
`;

export const Divider = styled.div`
  width: 100%;
  height: 2px;
  background-image: linear-gradient(
    to right,
    #b8cfe6 33%,
    rgba(255, 255, 255, 0) 0%
  );
  background-repeat: repeat-x;
  background-position: center center;
  background-size: 7px 4px;
`;

export const TitleToggle = styled.span`
  color: #666;
  white-space: nowrap;
  font-size: 12px;
  margin-left: 10px;
`;

export const Wrapper = styled.div`
  .custom-text-label {
    color: #333333 !important;
    font-size: 12px !important;
  }

  .is-bold {
    padding: 0px 4px !important;
    margin-bottom: -4px;
  }

  .antsomi-switch {
    border-color: #bebebe !important;
  }
  .antsomi-switch.antsomi-switch-checked {
    border-color: rgb(0, 95, 184) !important;
  }

  .antsomi-switch .antsomi-switch-handle::before {
    background-color: #ffffff !important;
  }

  .antsomi-switch.inactive .antsomi-switch-inner {
    background-color: #bebebe;
  }
`;
export const WrapperIconXLab = styled.div`
  span {
    cursor: pointer;
    display: block;
    color: #005eb8;
  }
`;

export const HeadingAccordion = styled.div`
  font-weight: bold;
  font-size: 14px;
  color: #333333;
`;

export const SubHeadingAccordion = styled.div`
  font-weight: bold;
  font-size: 12px;
  color: #333333;
`;

export const AccordionCustom = withStyles({
  root: {
    width: '100%',
    border: 'none',
    borderTop: '1px solid #b8cfe6',
    marginTop: '15px',
    boxShadow: 'none',
    '&:not(:last-child)': {
      borderBottom: 0,
    },
    '&:before': {
      display: 'none',
    },
    '&$expanded': {
      margin: 'auto',
    },
  },
})(MuiAccordion);

export const AccordionStyled = withStyles({
  root: {
    width: '100%',
    borderRadius: '3px',
    border: '1px solid #D4D4D4',
    marginTop: '15px',
    boxShadow: 'none',
    padding: '0px 15px',

    '&.Mui-expanded': {
      paddingBottom: '15px',
    },
    '&:before': {
      display: 'none',
    },
    '&$expanded': {
      margin: 'auto',
    },
  },
})(MuiAccordion);

export const AccordionSummaryCustom = withStyles({
  root: {
    padding: 0,

    '&#header-content-source': {
      margin: '0px',
      height: '50px',
      minHeight: '50px',
      flexDirection: 'row-reverse',
    },
    '&#header-recommendation': {
      margin: '0px',
      height: '50px',
      minHeight: '50px',
      flexDirection: 'row-reverse',
    },
  },
  content: {
    margin: '15px 0',
  },
})(MuiAccordionSummary);

export const AccordionDetailsCustom = withStyles({
  root: {
    padding: 0,
    flexWrap: 'wrap',
    width: '100%',
    alignContent: 'flex-start',

    '&.first-div-full .contentsource-container': {
      width: '100%',
    },
  },
})(MuiAccordionDetails);

export const WrapperListTemplate = styled.div`
  position: absolute;
  top: 100px;
  right: 0;
  width: 50%;

  .list-template {
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #e6e6e6;
    width: 100%;
    justify-content: center;
    &.view-mode {
      pointer-events: none;
    }

    &.no-template {
      .template-container {
        display: flex;
      }
    }
    .template-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 50px;
    }
  }

  .title-message-template {
    color: #33475b;
    font-size: 14px;
    padding: 10px 35px;
    font-weight: bold;
  }
`;

export const P = styled.p`
  color: ${({ color }) => color || '#000'};
  font-size: ${({ fontSize }) => fontSize || '12px'};
  /* white-space: nowrap; */
`;

export const StyledUIInputCalendar = styled(UIInputCalendar)`
  width: 164px;

  .MuiInputBase-root {
    max-width: 164px;
  }
`;

export const Hint = styled.div`
  margin-right: auto;
  margin-top: ${props => props.$marginTop || '0px'};
  font-size: 12px;
  color: #999999;
`;

export const PromptText = styled.div`
  margin-top: 6px;
  font-size: 11px;
  color: #999999;
`;

export const WrapperListTemplateZNS = styled.div`
  max-width: 827px;
  position: relative;

  ${props =>
    props.isViewMode &&
    css`
      pointer-events: none;
    `}

  .template-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 15px;
    margin-bottom: 15px;
    padding-top: 0px;

    .wrapperUISquare {
      width: 406px;
      height: 65px;
      margin-right: 0px;
      margin-bottom: 0px;
      &.active-box {
        border: 2px solid #005eb8 !important;
      }

      &:hover {
        background-color: #f2f9ff;
        border: 1px solid #81bcf4;
        outline: none;
      }

      .head-label {
        text-align: left;
        margin-top: 0px;
        font-weight: 400;
        font-size: 14px !important;
      }
    }
  }
`;

export const WrapperAreaTextMessage = styled.div`
  border: 1px solid ${props => (props.isError ? '#EF3340' : '#b8cfe6')} !important;
  border-radius: 10px !important;
  padding: 10px 0px 10px 10px;
  height: 150px;
  overflow: hidden;
`;

export const SupportiveText = styled.div`
  display: ${props => (props.isShow ? 'block' : 'none')};
  color: #ef3340;
  margin-top: 8px;
  font-size: 11px;
  font-weight: 400;
  line-height: 14px;
`;

export const TextAreaStyled = styled(TextArea)`
  height: 100% !important;
  padding: 0px 10px 0px 0px !important;
  resize: none !important;
`;

export const TextExtraInfo = styled.div`
  margin-top: 10px;
  text-align: right;
  font-weight: 400;
  font-size: 11px;
  color: #a2a2a2;
  display: ${props => (props.isShow ? 'flex' : 'none')};
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
`;

export const WrapCountryFlag = styled.div`
  display: flex;
  align-items: center;
  padding: 0 5px;
  border-bottom: 1px solid ${globalToken.blue1};
  height: ${({ height = '30px' }) => height};

  ${({ isViewMode }) =>
    isViewMode &&
    css`
      border-bottom: none;
    `}
`;

export const WrapMultiLangDropdown = styled.div``;

export const WrapItemCountryFlag = styled.div`
  width: 100%;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  ${({ isViewMode }) =>
    isViewMode &&
    css`
      padding: 0 5px;
      margin-right: 5px;
    `}
`;

export const WrapDropdownCountryFlag = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ isViewMode }) => (isViewMode ? '15px' : 0)};
`;

import styled, { css } from 'styled-components';
import Button from 'components/Atoms/Button/index';

import { breakdownMd } from 'utils/variables';

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  margin-top: 8px;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: calc(100% - 130px);
  top: 0;
  left: 0;
  /* ${breakdownMd(
  css`
      height: calc(100vh - 108px);
      top: 108px;
      left: 0.75rem;
      right: 0.75rem;
    `,
)} */
`;
export const ButtonCancelExtend = styled(Button)`
  margin-left: 0.75rem;
  /* padding: 0.375rem 0;
  font-size: 0.875rem;
  width: 6.375rem;
  min-width: 6.375rem; */
`;

export const Iframe = styled.iframe`
  width: 100%;
  height: 100%;
`;

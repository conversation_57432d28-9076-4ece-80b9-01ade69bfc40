/* eslint-disable no-shadow */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useState, useMemo, useEffect, useRef } from 'react';
// import { DragDropContext } from 'react-beautiful-dnd';

import { UILoading } from '@xlab-team/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { WrapperDnD } from '@xlab-team/workflows';
import { useParams, useHistory } from 'react-router-dom';

import '@xlab-team/workflows/main.css';

import Menu from './Menu';
// import Drawer from './Drawer';

import {
  makeSelectConfigureCreateWorkflow,
  makeSelectIsExpandNodeData,
  makeSelectMainCreateWorkflow,
  makeSelectIsCloseDataflow,
  makeSelectConfigureMainCreateFlattenNodes,
  selectModalSaveAsTemplate,
} from '../selectors';
import { MODULE_CONFIG } from '../config';
import { updateValue } from '../../../../../../redux/actions';
// import WrapperNode from './Nodes';
import { WrapperContent, StyleContentLoading } from './styles';
import HeaderNode from './HeaderNode';
import PreviewCapture from './Nodes/Destination/PreviewCapture';
// import ErrorNode from '../_UI/ErrorNode';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getStoryRoleActions } from '../utils.story.rules';
import {
  getPortalId,
  getCurrentAccessUserId,
} from '../../../../../../utils/web/cookie';
import { getToggleState, updateUrl } from '../../../../../../utils/common';
import APP from '../../../../../../appConfig';
import FlowChartWrapper from './FlowChart/index';
import SidebarContent from '../../../../../../components/Templates/LayoutContent/SidebarContent';
import { makeSelectJourneyChannelActive } from '../../selectors';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import { makeUrlPermisison } from '../../../../../../utils/web/permission';
import { isShowFlowChartOnly } from '../../Detail/utils';
import { getPrefixCreate } from '../../Detail/VersionHistory2/Detail/utils';
import { PREFIX_DETAIL } from '../../Detail/ActionHistory2/Detail/config';
import { TYPE_RESUME_WHOLE_PROCESS } from '../../Detail/ScheduleHistory2/Detail/List/utils';
import { isDetailDrawer } from '../../utils';
// import ModalConfirmExit from '../../../../../../containers/modals/ModalConfirmExit';

const toggleKey = `toggle-${MODULE_CONFIG.key}`;
function Workflow(props) {
  // State
  const [dropNode, setDropNode] = useState({});
  const [isCollapsedSidebar, setCollapsedSidebar] = useState(
    getToggleState(toggleKey),
  );
  const [valueSearch, setValueSearch] = useState('');
  const [zoomSize, setZoomSize] = useState(1);
  const [draggingNode, setDraggingNode] = useState(null);
  const params = useParams();
  const history = useHistory();
  const captureRef = useRef();

  const activeId = props.activeId || params.activeId;

  const configureMain = props.configure.main;
  const {
    activeNode,
    triggerNode,
    node,
    nodes,
    errors,
    activeNodeMulti,
    treeNodes,
    cacheNodes,
    isCapturing,
  } = configureMain;
  const searchParams = new URLSearchParams(window.location.search);

  const { activeRow } = props.main;
  const {
    use = 'setting',
    hasEditRole,
    isViewMode,
    typeResume,
    keyResume,
  } = props;
  const tablesNodeMemo = useMemo(() => {
    const newNodeTables = { ...node.tableNodes };
    const result =
      newNodeTables &&
      newNodeTables.body &&
      newNodeTables.body.filter(item => item.status !== 0 && item.status !== 3);
    return {
      header: newNodeTables && newNodeTables.header,
      body: result,
    };
  }, [node.tableNodes]);

  const menusNodeMemo = useMemo(() => {
    let newNodeMenus = [...node.menus];
    newNodeMenus =
      newNodeMenus &&
      newNodeMenus.map(menu => {
        if (menu.groupId === 'destination') {
          return {
            ...menu,
            nodes:
              menu.nodes &&
              menu.nodes.filter(item => item.status !== 0 && item.status !== 3),
          };
        }
        return menu;
      });
    return newNodeMenus;
  }, [node.menus]);

  useEffect(() => {
    setDropNode({});
  }, [isViewMode]);
  useEffect(() => {
    let node = {};
    if (props.flattenNodes.length > 0 && typeResume) {
      const newFlattenNodes = props.flattenNodes.map(nodes => {
        const newNode = {
          ...nodes,
          moreInfo: { ...nodes.moreInfo, borderColor: '' },
        };
        if (newNode.type === 'SCHEDULED' || newNode.type === 'EVENT_BASED') {
          node = { ...newNode };
        }
        if (TYPE_RESUME_WHOLE_PROCESS.includes(newNode.type)) {
          newNode.moreInfo = { ...newNode.moreInfo, borderColor: '#139B00' };
        } else {
          newNode.moreInfo = { ...newNode.moreInfo, borderColor: '#005EB8' };
        }
        return newNode;
      });
      props.updateFlattenNodes({
        flattenNodes: newFlattenNodes,
      });
    }
    if (typeResume === 'whole_process') {
      // props.onActiveNode(node);
      props.onNodeValidation({ type: 'ON_ACTIVE_NODE', payload: node });
      props.callback('IS_CLICK_NODE', true);
    }

    return () => {
      setDropNode({});
    };
    // setStateCommon({ isClickNode: true });
  }, [props.isLoading]);
  const roleActions = useMemo(
    () => getStoryRoleActions(activeRow.accepted_actions),
    [activeRow.status, activeRow.accepted_actions],
  );
  // console.log('roleActions', roleActions);

  const onClickToggleSidebar = () => {
    setCollapsedSidebar(!isCollapsedSidebar);
    localStorage.setItem(toggleKey, !isCollapsedSidebar);
  };

  const onDragStart = node => {
    setDraggingNode(node);
  };

  const onDragEnd = value => {
    setDraggingNode(null);

    const { destination, draggableId } = value;

    if (!destination) {
      return;
    }

    setDropNode({
      id: draggableId,
      droppableId: destination.droppableId,
    });
  };

  const onChangeZoomInOut = number => {
    try {
      if (number > 0 && typeof number === 'number') {
        setZoomSize(number);
      }
    } catch (e) {
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Journey/Create/Content/index.jsx',
        func: 'onChangeZoomInOut',
        data: e.stack,
      });
    }
  };

  const handleChangeDesign = (designMode = 'update') => {
    if (props.isShowDrawer) {
      const searchParams = new URLSearchParams(window.location.search);

      searchParams.set('design', 'update');

      history.push({ search: searchParams.toString() });
    } else {
      const newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
        activeRow.channel_id
      }/detail/${activeId}/settings?design=${designMode}`;
      updateUrl(makeUrlPermisison(newUrl));
    }

    props.onChangeDesign(designMode);
  };

  const handleCancel = data => {
    if (props.main.disabled === false) {
      props.toggleModal(data);
    } else {
      props.goToList();
    }
  };

  // console.log('activeNode', activeNode);

  if (props.isLoading) {
    return (
      <StyleContentLoading>
        <UILoading isLoading />
      </StyleContentLoading>
    );
  }

  const onCreateCopy = () => {
    props.onCreateCopy(activeRow);
  };

  const onChangeSearchMenu = event => {
    const { value = '' } = event.target;

    setValueSearch(value);
  };
  // const onNodeValidation = () => {};
  if (isShowFlowChartOnly(props.page)) {
    return (
      <WrapperDnD onDragEnd={onDragEnd} onDragStart={onDragStart}>
        <WrapperContent id="automation">
          <UILoading isLoading={props.main.isLoadingFlowChart} />
          <main id="wrapper-automation">
            <div
              id="wrapper-content"
              className={isCollapsedSidebar ? 'collapsed' : ''}
            >
              <FlowChartWrapper
                zoomSize={zoomSize}
                roleActions={roleActions}
                dropNode={dropNode}
                nodes={node.list}
                activeNodeMulti={activeNodeMulti}
                // rules={rules}
                activeNode={activeNode}
                tableNodes={node.tableNodes}
                errors={errors}
                design={props.main.design}
                triggerNode={triggerNode}
                isExpand={props.isExpand}
                onChangeExpand={props.onChangeExpand}
                isCloseDataflow={props.isCloseDataflow}
                onChangeCloseDataflow={props.onChangeCloseDataflow}
                moduleConfig={props.moduleConfig}
                onChangeZoomInOut={onChangeZoomInOut}
                isViewMode={isViewMode}
                isShowHeader={false}
                versionId={props.versionId}
                page={props.page}
                typeResume={props.typeResume}
                draggingNode={draggingNode}
                isDisplayDiagramBlastCampaign={
                  props.isDisplayDiagramBlastCampaign
                }
                isShowDrawer={props.isShowDrawer}
                keyResume={keyResume}
              />
            </div>
          </main>
        </WrapperContent>
      </WrapperDnD>
    );
  }

  function getClassNameIcon(isCollapsed, isRtl) {
    if (!isRtl) {
      return isCollapsed
        ? 'toggle-icon icon-xlab icon-xlab-arrow-right'
        : 'toggle-icon icon-xlab icon-xlab-arrow-left';
    }
    if (isRtl) {
      return !isCollapsed
        ? 'toggle-icon icon-xlab icon-xlab-arrow-right'
        : 'toggle-icon icon-xlab icon-xlab-arrow-left';
    }
    return '';
  }
  return (
    <WrapperDnD onDragEnd={onDragEnd} onDragStart={onDragStart}>
      <WrapperContent id="automation">
        <main
          id="wrapper-automation"
          style={{
            ...((props.main.design === 'create' && !isDetailDrawer()) ||
              (!searchParams.has('ui') && {
                margin: '0 15px',
                boxShadow: '-1px 0 3px 0 rgb(0 0 0 / 10%)',
              })),
          }}
        >
          {props.main.design !== 'preview' && (
            <SidebarContent
              toggle={onClickToggleSidebar}
              isCollapsed={false}
              className="sidebar"
              maxWidthCollapsed="140px"
              maxWidthExpanded="220px"
              height="calc(100vh - 54px)"
              style={{
                top: -1,
                boxShadow: 'unset',
                borderTop: '1px solid #d4d4d4',
              }}
            >
              <div className="d-flex align-items-center justify-content-between search-box">
                {/* <span>Data flow step</span> */}
                <input
                  type="text"
                  className="input-search"
                  placeholder={getTranslateMessage(
                    TRANSLATE_KEY._,
                    'Search...',
                  )}
                  onChange={onChangeSearchMenu}
                />
                <div className="d-flex align-items-center full-width justify-content-end">
                  <i className="icon-antsomi-search" />
                  {/* <span
                    onClick={onClickToggleSidebar}
                    className={getClassNameIcon(isCollapsedSidebar, false)}
                  /> */}
                </div>
              </div>
              <Menu
                menus={menusNodeMemo}
                valueSearch={valueSearch}
                roleActions={roleActions}
                moduleConfig={props.moduleConfig}
              />
              {/* </aside> */}
            </SidebarContent>
          )}
          <div
            id="wrapper-content"
            className={isCollapsedSidebar ? 'collapsed' : ''}
          >
            <UILoading isLoading={props.main.isLoadingFlowChart} />
            <HeaderNode
              roleActions={roleActions}
              activeRow={activeRow}
              design={props.main.design}
              onChangeZoomInOut={onChangeZoomInOut}
              zoomSize={zoomSize}
              moduleConfig={props.moduleConfig}
              versionId={props.versionId}
              isViewMode={props.isViewMode}
              use={use}
              cacheCopyId={props.cacheCopyId}
              hasEditRole={hasEditRole}
              handleChangeDesign={handleChangeDesign}
              onCancel={handleCancel}
              isShowDrawer={props.isShowDrawer}
              isJourneyV2={props.isJourneyV2}
            />

            <FlowChartWrapper
              zoomSize={zoomSize}
              roleActions={roleActions}
              dropNode={dropNode}
              nodes={node.list}
              nodesSetting={nodes}
              // rules={rules}
              activeNode={activeNode}
              tableNodes={tablesNodeMemo}
              errors={errors}
              design={props.main.design}
              triggerNode={triggerNode}
              isExpand={props.isExpand}
              onChangeExpand={props.onChangeExpand}
              isCloseDataflow={props.isCloseDataflow}
              onChangeCloseDataflow={props.onChangeCloseDataflow}
              moduleConfig={props.moduleConfig}
              onChangeZoomInOut={onChangeZoomInOut}
              isShowHeader
              versionId={props.versionId}
              isViewMode={isViewMode}
              typeResume={props.typeResume}
              draggingNode={draggingNode}
              isDisplayDiagramBlastCampaign={
                props.isDisplayDiagramBlastCampaign
              }
              isShowDrawer={props.isShowDrawer}
              keyResume={keyResume}
            />
            <PreviewCapture
              ref={captureRef}
              isCapturing={isCapturing}
              nodes={nodes}
              design={props.main.design}
              nodesSetting={nodes}
              treeNodes={treeNodes}
              cacheNodes={cacheNodes}
              updateThumbnails={props.updateThumbnails}
            />
          </div>
        </main>
        {/* {props.main.design === 'preview' && use === 'setting' && ( */}
        {/*  <ButtonEditPreviewDesign> */}
        {/*    <WrapperDisable noPermission={!hasEditRole}> */}
        {/*      <UIButton */}
        {/*        variant="contained" */}
        {/*        theme="primary" */}
        {/*        // style={styleButton} */}
        {/*        onClick={handleChangeDesign} */}
        {/*        // disabled={props.main.disabled || props.main.isDoing} */}
        {/*      > */}
        {/*        {getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit')} */}
        {/*      </UIButton> */}
        {/*    </WrapperDisable> */}
        {/*  </ButtonEditPreviewDesign> */}
        {/* )} */}
      </WrapperContent>
    </WrapperDnD>
  );
}

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigureCreateWorkflow(),
  main: makeSelectMainCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
  isExpand: makeSelectIsExpandNodeData(),
  isCloseDataflow: makeSelectIsCloseDataflow(),
  flattenNodes: state =>
    makeSelectConfigureMainCreateFlattenNodes()(state, {
      moduleConfig: { key: getPrefixCreate(PREFIX_DETAIL) },
    }),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    onNodeValidation: value =>
      dispatch(updateValue(`${prefix}@@NODE_VALIDATION@@`, value)),
    onChangeDesign: value =>
      dispatch(updateValue(`${prefix}@@DESIGN@@`, value)),
    onChangeExpand: value =>
      dispatch(updateValue(`${prefix}@@IS_EXPAND@@`, value)),
    updateThumbnails: thumbnails =>
      dispatch(updateValue(`${prefix}@@STORY_THUMBNAIL@@`, thumbnails)),
    onChangeCloseDataflow: value =>
      dispatch(updateValue(`${prefix}@@IS_CLOSE_DATAFLOW@@`, value)),
    toggleModal: data => dispatch(updateValue(`${prefix}@@TOGGLE_MODAL`, data)),
    goToList: () => dispatch(updateValue(`${prefix}@@GO_TO_LIST`)),
    updateFlattenNodes: payload => {
      dispatch(
        updateValue(
          `${getPrefixCreate(PREFIX_DETAIL)}@@FLATTEN_NODES@@`,
          payload,
        ),
      );
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Workflow);

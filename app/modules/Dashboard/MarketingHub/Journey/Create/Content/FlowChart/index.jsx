/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable indent */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/prop-types */
/* eslint-disable func-names */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { isEmpty } from 'lodash';
import useDraggableScroll from 'hooks/useDraggableScroll';
import Drawer, { getMinHeightDrawer, STORAGE_KEY_HEIGHT } from '../Drawer';
import WrapperNode from '../Nodes';
import ErrorNode from '../../_UI/ErrorNode';
import useElementSize from '../../../../../../../hooks/useElementSize';
import classnames from 'classnames';

import FlowChart from './FlowChart';
import { WrapperFlowChartStyle } from './styles';
import { getLocalStorage } from '../../../../../../../utils/web/cookie';
import { safeParse } from '../../../../../../../utils/common';
import { Zoom } from './Zoom';
import { CATALOG_CODE } from '../Nodes/constant';
import { getObjectPropSafely } from '../../../../../../../components/common/UIEditorPersonalization/utils.3rd';
import { validateSameButtonAllSlides } from '../../../../../../../components/Organisms/LineTemplate/utils';

// Translations
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';

// Constants
import { TEMPLATE_TYPES } from '../../../../Destination/CreateV2/Design/Templates/constants';

function FlowChartWrapper(props) {
  const {
    roleActions,
    zoomSize,
    dropNode,
    nodes,
    nodesSetting = {},
    activeNode,
    triggerNode,
    tableNodes,
    errors,
    design,
    isExpand,
    onChangeExpand,
    isCloseDataflow,
    onChangeCloseDataflow,
    moduleConfig,
    isViewMode,
    isShowHeader,
    versionId,
    page,
    typeResume,
    activeNodeMulti = [],
    isDisplayDiagramBlastCampaign = false,
    draggingNode,
    isShowDrawer,
    // verionId,
    keyResume,
  } = props;
  const wrapperDrawerId = `wrapper-drawer-${versionId}`;

  const [isClickNode, setIsClickNode] = useState(false);

  const [wrapperRef, { height: wrapperHeight }] = useElementSize();

  const scrollRef = useRef(null);
  const { onMouseDown } = useDraggableScroll(scrollRef);

  // Check error for channel Line only
  const isErrorNotSameBtn = useMemo(() => {
    let isError = false;
    const errorsLine = [];

    if (
      !isEmpty(activeNode) &&
      !isEmpty(nodesSetting) &&
      activeNode.catalogCode === CATALOG_CODE.LINE &&
      errors[activeNode.nodeId]
    ) {
      const destinationInputData = getObjectPropSafely(() =>
        nodesSetting.getIn([
          activeNode.nodeId,
          'destination',
          'data',
          'destinationInput',
        ]),
      );
      const lineData = getObjectPropSafely(
        () =>
          !isEmpty(destinationInputData) &&
          destinationInputData.line &&
          destinationInputData.line.value,
      );

      if (!isEmpty(lineData)) {
        const { data = [] } = lineData;

        if (Array.isArray(data)) {
          data.forEach(eachLineItem => {
            const { template = '', carousel = {} } = eachLineItem;

            if (template === TEMPLATE_TYPES.CAROUSEL) {
              const listErrorId = validateSameButtonAllSlides(carousel);
              errorsLine.push(...(listErrorId || []));
            }
          });
        }
      }

      isError = errorsLine && errorsLine.length > 0;
    }

    return isError;
  }, [nodesSetting, activeNode, errors]);

  useEffect(() => {
    const wrapperDrawer = document.querySelector(`#${wrapperDrawerId}`);

    if (wrapperDrawer) {
      const drawer = wrapperDrawer.querySelector(
        `#${wrapperDrawerId} .antsomi-custom-drawer`,
      );
      const wrapperDrawerStyle = {
        position: '',
        height: '',
        width: '',
        top: '',
        right: '',
        zIndex: '',
        backgroundColor: '',
      };
      const drawerStyle = {
        // height: '',
        width: '',
        marginLeft: '',
        top: '',
      };

      if (isExpand) {
        if (!isCloseDataflow) {
          // wrapperDrawerStyle.position = 'fixed';
          // wrapperDrawerStyle.top = '-4px'; // -4px top for slider
          // wrapperDrawerStyle.right = '0';
          // wrapperDrawerStyle.height = 'calc(100vh + 4px)'; // +4px for top
          // wrapperDrawerStyle.width = '100vw';
          wrapperDrawerStyle.zIndex = '1000';
          // wrapperDrawerStyle.backgroundColor = 'rgb(0, 0, 0, 0.5)';

          drawerStyle.height = '100%'; // 20px of slider
          drawerStyle.width = '100%';
          drawerStyle.marginLeft = 'auto';
          drawerStyle.top = '0px'; // hide resizeButton
        } else {
          // onChangeExpand({ isExpand: false });
          setIsClickNode(false);
          drawerStyle.height = `${getMinHeightDrawer(page)}px`;
        }
      } else if (!isExpand && isCloseDataflow) {
        drawerStyle.height = `${getMinHeightDrawer(page)}px`;
        onChangeExpand({ isExpand: false });
      } else if (!isCloseDataflow && isClickNode) {
        const defaultHeight = parseInt(
          safeParse(getLocalStorage(STORAGE_KEY_HEIGHT), wrapperHeight / 2),
        );
        drawerStyle.height = `${defaultHeight}px`;
        setIsClickNode(false);
      }

      Object.entries(wrapperDrawerStyle).forEach(([k, v]) => {
        wrapperDrawer.style[k] = v;
      });
      Object.entries(drawerStyle).forEach(([k, v]) => {
        drawer.style[k] = v;
      });
    }

    const handleKeyUp = function(event) {
      if (isExpand) {
        if (event.keyCode === 27 || event.code === 'Escape') {
          onChangeExpand({ isExpand: false });
        }
      }
    };

    const handleClick = function(event) {
      if (isExpand) {
        if (event.path[0] && event.path[0].id === wrapperDrawerId) {
          onChangeExpand({ isExpand: false });
        }
      }
    };

    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('click', handleClick);
    };
  }, [isExpand, isCloseDataflow, isClickNode]);

  const callback = (type, data) => {
    switch (type) {
      case 'IS_CLICK_NODE': {
        setIsClickNode(data);
        onChangeCloseDataflow(false);
        break;
      }
      case 'IS_RESIZE': {
        onChangeExpand({ isExpand: false });
        onChangeCloseDataflow(false);
        break;
      }
      default:
        break;
    }
  };
  return (
    <WrapperFlowChartStyle
      ref={wrapperRef}
      className="wrapper-dataflow-tree"
      page={page}
      $isShowHeader={isShowHeader}
    >
      <div
        className={classnames('dataflow-tree', {
          'height-100 p-0': isShowDrawer,
        })}
      >
        <div
          id="flowchart-wrapper"
          ref={scrollRef}
          onMouseDown={onMouseDown}
          style={{
            transform: `scale(${zoomSize})`,
            transformOrigin: 'top left',
            width: `calc(100% / ${zoomSize})`,
            height: `calc(100% / ${zoomSize})`,
          }}
        >
          {/* <ScrollBar> */}
          <FlowChart
            roleActions={roleActions}
            typeResume={typeResume}
            dropNode={dropNode}
            nodes={nodes}
            // rules={rules}
            activeNode={activeNode}
            tableNodes={tableNodes}
            errors={errors}
            design={design}
            isViewMode={isViewMode}
            callback={callback}
            moduleConfig={moduleConfig}
            mode="edit"
            draggingNode={draggingNode}
            versionId={versionId}
          />
          {/* </ScrollBar> */}
        </div>
        <Zoom zoomSize={zoomSize} onChangeZoomInOut={props.onChangeZoomInOut} />
      </div>
      {(Object.keys(activeNode).length > 0 || activeNodeMulti.length > 0) && (
        <div id={wrapperDrawerId}>
          <Drawer
            design={design}
            isExpand={isExpand}
            isClickNode={isClickNode}
            isCloseDataflow={isCloseDataflow}
            onChangeCloseDataflow={onChangeCloseDataflow}
            callback={callback}
            // 20 is height of Resizer with position absolute
            wrapperHeight={wrapperHeight}
            page={page}
          >
            {errors[activeNode.nodeId] && (
              <ErrorNode
                errorMessage={
                  isErrorNotSameBtn
                    ? getTranslateMessage(
                        TRANSLATE_KEY._,
                        'The number of buttons must be the same in all slides',
                      )
                    : getTranslateMessage(
                        TRANSLATE_KEY._NOTI_FINISH_SETUP_NODE,
                        'Please finish setups of this node before perform another action',
                      )
                }
              />
            )}
            <WrapperNode
              moduleConfig={moduleConfig}
              roleActions={roleActions}
              activeNodeMulti={activeNodeMulti}
              activeNode={activeNode}
              triggerNode={triggerNode}
              typeResume={typeResume}
              design={design}
              isExpand={isExpand}
              onChangeExpand={onChangeExpand}
              isCloseDataflow={isCloseDataflow}
              onChangeCloseDataflow={onChangeCloseDataflow}
              isViewMode={isViewMode}
              isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
              keyResume={keyResume}
            />
          </Drawer>
        </div>
      )}
    </WrapperFlowChartStyle>
  );
}

export default FlowChartWrapper;

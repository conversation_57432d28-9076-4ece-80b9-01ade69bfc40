import styled, { css } from 'styled-components';
import ScrollBar from 'react-scrollbars-custom';
import ToggleButton from 'components/Atoms/ToggleButton/index';
import Button from 'components/Atoms/Button/index';
import { Menu } from '@xlab-team/workflows';

export const StyleScrollBar = styled(ScrollBar)`
  .ScrollbarsCustom-Track {
    border-radius: 0px !important;
    width: 8px !important;
    height: 100% !important;
    top: 0px !important;
    background: rgb(230, 230, 230) !important;
    .ScrollbarsCustom-Thumb {
      border-radius: 0px !important;
      background: rgb(112, 112, 112) !important;
    }
  }
  /* height: 100%; */
`;
export const ButtonQuickTest = styled(Button)`
  /* padding: 0.375rem 0;
  font-size: 0.875rem;
  width: 6.375rem;
  min-width: 6.375rem; */
`;
export const WrapperContent = styled.main`
  .popup-main {
    overflow-y: auto;
    height: 100%;
    /* padding: 1rem 0rem; */
    position: relative;
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .popup-wrapper {
    display: flex;
    height: 100%;
    /* overflow-y: auto; */
  }

  .resizable {
    border-left: 2px solid;
    border-color: #e5e5e5;
    :hover {
      border-color: #b8cfe6 !important;
    }
    :hover {
      .drag-resize-vert {
        /* position: absolute; */
        /* top: 50%;
      left: -17px; */

        width: 10px;
        height: 10px;
        position: absolute;
        left: -11px;
        top: 50%;
        transform: translateX(50%);
        z-index: 1;
        touch-action: none;
        border: 1px solid rgb(184, 207, 230);
        border-radius: 50%;
        background: #ffffff;
      }
    }

    & > span > div {
      left: -10px !important;
      border-left: solid 1px rgba(0, 0, 0, 0.1);
      border-right: solid 1px rgba(0, 0, 0, 0.1);
    }
  }

  .antsomi-custom-drawer {
    position: absolute;
    background-color: #fff;
    z-index: 200;
    width: 100%;
    bottom: 0;
    display: block;
    box-shadow: 0 0 9px rgba(0, 0, 0, 0.1);

    .drag-resize {
      width: 100%;
      height: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 5px;
      i {
        cursor: grab;
      }
    }
    .handle-wrapper-resize {
      & > div {
        width: 100%;
        height: 20px !important;
        top: 0 !important;
        cursor: grab !important;
      }
    }
    .popup {
      display: flex;
      flex-direction: column;
      height: 100% !important;
      /* margin-top: 20px; */
      padding-bottom: 1px;
      .popup-title {
        white-space: nowrap;
        height: 50px;
        padding: 10px 20px;
        gap: 10px;
        .node-status {
          /* position: absolute !important;
          right: 1rem; */
        }
        .btn-trigger {
          padding-left: 0;
          .MuiSvgIcon-root {
            font-size: 24px;
            margin: 0 8px;
          }
        }
      }
    }
  }
  .sidebar {
    .search-box {
      max-width: 100%;
      height: 50px;
      min-height: 50px;
      padding: 10px;
      // box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
      border-right: 1px solid #c2cee0;
      border-bottom: 1px solid #c2cee0;
      justify-content: space-between;
      z-index: 1;

      .input-search {
        outline: none;
        border-bottom: none !important;

        padding: 6px 0;
        border: none;
        border-bottom: 1px solid #dadce0;
        font-size: 12px;
        color: #999999;

        &::-webkit-input-placeholder {
          font-weight: bold;
        }
      }

      .icon-antsomi-search {
        color: #666;
        font-size: 18px;
        cursor: pointer;
      }
      > span:not(.toggle-icon) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #005eb8;
        font-size: 16px;
        font-weight: 600;
      }
    }
    &.collapsed {
      > .search-box {
        justify-content: center;
        > .input-search {
          display: none;
        }
      }
    }

    .toggle-icon {
      font-size: 20px;
      position: absolute;
      right: 5px;
      cursor: pointer;
      color: #33475b;

      &:hover {
        color: #005fb8;
      }
    }
  }
`;

export const StyleContent = styled.div`
  display: flex;
  flex-direction: column;
  height: fit-content;
  width: 100%;
`;

export const StyleContentLoading = styled.div`
  position: relative;
  height: 100%;
  width: 100%;
  padding: 0;
  .private-overlay {
    position: relative;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
    height: 100%;
  }
`;

export const DivMessageComputing = styled.div`
  background: ${({ bgColor }) => bgColor};
  display: flex;
  padding: 20px 0;
  justify-content: center;
  margin-bottom: 12px;
  font-weight: 600;
`;

export const ImageIcon = styled.img`
  height: 18px;
  width: 18px;
`;

export const UnderConstruction = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const WrapperHeaderNode = styled.div`
  justify-content: space-between;
  position: relative;
  background-color: #fff;
  width: 100%;
  height: 50px;
  //box-shadow: 0 0 9px rgb(0 0 0 / 10%);
  //border-radius: 5px 5px 0 0;
  padding: ${props => !props.isShowDrawer && ' 0.8rem 1rem'}
  border-bottom: ${props => !props.isShowDrawer && '1px solid #c2cee0'};
  border-bottom: ${props => props.isJourneyV2 && '1px solid #D4D4D4'};

  .form-field-name {
    border: none;
    padding: 0;
    max-width: 500px;
    min-width: 10px;
  }

  .MuiInputBase-input {
    font-size: 1rem;
    color: #005eb8;
    font-weight: bold;
  }

  .wrapper-input-edit-name {
    display: flex;
    align-items: center;
    max-width: 50%;
    .form-field-name {
      font-size: 1rem;
      color: #005eb8;
      font-weight: bold;
    }
  }
  .right-content {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 15px;
    .story-ui-status {
      display: inline-flex;
    }
    ${({ isJourneyV2 }) =>
      isJourneyV2 &&
      css`
        padding: 0 15px;
      `}
  }
`;

export const MenuNodata = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
`;

export const ButtonEditPreviewDesign = styled.div`
  position: absolute;
  bottom: 28px;
  right: 28px;
`;

export const GreyOutlineButton = styled.div`
  button {
    height: 30px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    gap: ${({ gap = '5px' }) => gap};
    border: 1px solid #e0e0e0;
    font-size: 12px;
    font-weight: bold;
    color: ${({ color = '#005fb8' }) => color};
    cursor: pointer;
    background-color: ${({ bgColor = '#fff' }) => bgColor};
    span {
      font-size: 18px;
      color: #005fb8;
    }

    &[disabled] {
      color: rgba(0, 0, 0, 0.38);
      background: rgba(0, 0, 0, 0.04);
      pointer-events: none;
      cursor: default;
    }
  }
`;

export const StyleToggleButton = styled(ToggleButton)``;

export const Box = styled.div`
  height: 18px;
  width: 38px;
`;

export const WrapperDragAndDropItem = styled.div`
  background-color: ${props => props.active && 'rgb(242, 247, 252)'};
`;

export const Selection = styled.div`
  border: solid 1px #b8cfe6;
  font-size: 12px;
  display: flex;
  align-items: center;
  border-radius: 5px;
  background: #fff;
  margin: 0 0.7rem;
  height: 38px;
  color: #000;
  &:not(:first-child) {
    margin-top: 10px;
  }
  .tippy-assign-attrs {
    display: flex;
  }
  &.selection-active {
    background-color: #fff;
    &:hover {
      background-color: rgba(0, 94, 184, 0.04);
      .active {
        color: #005fb8;
      }
    }
  }
  &.selection-disabled {
    background-color: #f5f5f5;
    border: none;
  }
`;

export const GroupHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 4px 10px;
  margin-bottom: 10px;

  .group-label {
    font-family: Roboto;
    font-size: 11px;
    font-weight: 700;
    line-height: 13px;
    color: #595959;
  }
`;

export const GroupBody = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-rows: max-content;
  gap: 10px;

  & > div {
    justify-self: center;
  }
`;

export const Group = styled.div`
  padding-top: 10px;
  padding-bottom: 15px;
`;

export const LeftSide = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  flex-wrap: nowrap;
  overflow: hidden;
  margin-left: -20px;
  & > div:first-of-type {
    /* margin-left: -18px;
    height: 35px; */
  }
  &.left-side-disabled {
    .content-menu-item {
      color: #999999;
      font-size: 12px;
    }
    .wrapper-icon {
      color: #999999;
    }
  }
  &.left-side-active {
    .content-menu-item {
      color: #000000;
      font-size: 12px;
    }
    .wrapper-icon {
      color: #666666;
    }
  }
  .content-menu-item {
    width: 100px;
    font-weight: normal !important;
  }
`;

export const RightSide = styled.div`
  flex-shrink: 1;
  display: flex;

  .MuiIconButton-root {
    padding: 0;
  }

  .active {
    color: rgba(102, 102, 102, 0.4);
    &:hover {
      color: #005fb8 !important;
    }
  }

  .disabled {
    color: rgba(102, 102, 102, 0.4);
    cursor: not-allowed;
  }
`;

export const WrapperDraggable = styled.div`
  /* display: flex;
  align-items: center; */
`;

export const WrapperResize = styled.div`
  flex-grow: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
  /* margin-right: 10px; */

  & > div {
    min-width: 1000px;

    & .auto-width {
      min-width: unset;
    }
  }
`;

export const StyledNameNode = styled.span`
  font-size: 16px !important;
  color: #000 !important;
  font-weight: bold !important;
  text-overflow: ellipsis;
  overflow: hidden;
  flex: 1;
`;

export const StyledMenu = styled(Menu)`
  height: 100%;
  width: 100%;
  overflow: auto;
  border-right: 1px solid #c2cee0;

  .menu-group:not(:last-child) {
    border-bottom: none !important;
  }
`;

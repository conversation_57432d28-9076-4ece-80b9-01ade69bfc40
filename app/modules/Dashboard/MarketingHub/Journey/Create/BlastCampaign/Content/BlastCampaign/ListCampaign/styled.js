import styled from 'styled-components';

export const Container = styled.div`
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  width: 100%;
  /* height: 100%; */
  padding: 15px;
  /* border-right: 1px solid #e5e5e5; */
`;

export const ChipList = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
`;

export const Chip = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  border-radius: 35px;
  border: 1px solid #e0e0e0;
  color: #005fb8;
  cursor: pointer;

  span.icon-more {
    visibility: hidden;
  }

  &:hover {
    background-color: #f2f9ff;
    color: #005fb8;
    border: 1px solid #81bcf4;

    span.icon-more {
      color: #005fb8;
      visibility: visible;
    }
  }

  &.active {
    background-color: #005fb8 !important;
    color: #ffffff !important;
    border: unset !important;

    span.icon-more {
      color: #ffffff !important;
    }
  }
  &.error {
    border: 1px solid #ef3340;
    color: #005fb8;
    background-color: #fecaca;
  }

  &.use-only-icon {
    border: unset;
    border-radius: 20px;
    justify-content: center;
    background-color: #f2f9ff;

    > div {
      padding: unset;
    }
  }
`;

export const ChipText = styled.div`
  padding-left: 7px;
  font-size: 12px;
  font-weight: bold;
  line-height: 32px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`;

export const WrapperIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-right: 10px;
`;

export const Divider = styled.div`
  width: 100%;
  height: 2px;
  margin: 10px 0px;
  background-image: linear-gradient(
    to right,
    #ced3d9 33%,
    rgba(255, 255, 255, 0) 0%
  );
  background-position: center center;
  background-repeat: repeat-x;
  background-size: 7px 4px;
`;

export const PopoverContent = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 70px;
`;

export const PopoverItem = styled.div`
  padding: 8px 10px;
  color: #000000;
  text-align: center;
  font-size: 12px;
  cursor: pointer;

  &:hover {
    background-color: #f3f3f6;
  }
`;

/* eslint-disable indent */
/* eslint-disable import/order */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
// Libraries
import { Box } from '@material-ui/core';
import queryString from 'query-string';
import _, { get } from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import React, { useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useImmer } from 'use-immer';

// Components
import { UILoading } from '@xlab-team/ui-components';
import Destination from '../../../Content/Nodes/Destination';
import ListCampaign from './ListCampaign';
import TriggerEventBased from './TriggerEventBased';
import TriggerScheduled from './TriggerScheduled';
import WrapperTrigger from '../../components/WrapperTrigger';
import ThirdPartyScheduled from './ThirdPartyScheduled';
import Error from './_UI/Error';

// Servicse
import JourneyServices from 'services/Journey';
import {
  convertNodeList,
  getConfigBlastCampaign,
  getDestinationTypeWebPersonal,
} from './utils';

// Utils
import { getAudiencesByActiveId } from 'containers/Segment/Preview/PieForecast/utils';
import { addMessageToQueue } from 'utils/web/queue';

// Actions
import { updateValue } from 'redux/actions';
import {
  mapCustomInputToFe,
  mapCustomInputToFeActive,
} from '../../../Content/Nodes/TriggerEventBased/utils';

// Constants
import { THIRD_PARTY_CAMPAIGN } from '../../../../../../../../containers/Journey/constant';
import {
  CATALOG_CODE,
  CHANNEL_TYPES,
  NODE_TYPE,
} from '../../../Content/Nodes/constant';
import { MENU_TYPE } from '../../../../../../../../containers/TemplateListing/constants';
import { CHANNEL } from '../../../../constant';

// Selectors
import {
  makeSelectMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
} from '../../../selectors';

// Styles
import { getObjectPropSafely } from '../../../../../../../../utils/common';
import { WrapperBlastCampaign, WrapperCampaigns } from './styles';
import {
  DATA_DESIGN_NODE_DESTINATION,
  DATA_DESIGN_NODE_DESTINATION_EMAIL,
  DATA_DESIGN_NODE_DESTINATION_WEB_EMBEDDED,
} from '../../../Content/Nodes/Destination/utils';
import { serializeDataInputAttrs } from '../../../Content/Nodes/utils';
import { makeSelectCreatingJourneyInfo } from '../../../../selectors';

// Locales
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';

const BlastCampaign = props => {
  const {
    initData,
    initDataMain,
    activeNode,
    main,
    configure,
    moduleConfig,
    mainReducer,
    creatingJourneyInfo,
    activeRow,
  } = props;
  const {
    destinationNode: { isEnableTesting },
    updateFreshNodesKey,
    channelActive,
    triggerEvent,
    errors,
  } = configure.main;
  const {
    scheduledThirdParty = {},
    isThirdParty = false,
  } = mainReducer?.trigger;
  const initDataBlast = props.mainNodesBlast;

  const { type, catalogCode = '' } = activeNode;
  const [state, setState] = useImmer({
    inputStories: [],
    customInputAttributes: [],
    isLoadingInputViaUI: true,
  });

  const isReachedRichMenu = useMemo(() => {
    if (
      _isEmpty(activeNode) ||
      !Array.isArray(props.campaignList) ||
      channelActive.code !== CHANNEL.LINE.code ||
      catalogCode !== CATALOG_CODE.LINE_RICH_MENU
    ) {
      return false;
    }

    const campaignActive = props.campaignList.find(
      campaign => campaign.actionId === activeNode.nodeId,
    );

    if (campaignActive) {
      return get(campaignActive, ['isReachedLimitMenu'], false);
    }

    return false;
  }, [channelActive, catalogCode, activeNode, props.campaignList]);

  useEffect(() => {
    const params = {
      itemTypeId: -1009,
    };
    setState(draft => {
      draft.isLoadingInputViaUI = true;
    });

    JourneyServices.data
      .getAttrsCustomInput(params)
      .then(res => {
        if (res.code === 200 && !_isEmpty(res.data)) {
          setState(draft => {
            const customInputs =
              props.design === 'create'
                ? get(main, 'activeRowClone.custom_inputs', {}) || {}
                : get(main, 'activeRow.custom_inputs', {}) || {};
            const inputAttributes = get(
              res,
              'data[0].customInputAttributes',
              [],
            ).map(item => ({
              ...item,
              propertiesValue: get(customInputs, `[${item.itemPropertyName}]`),
            }));

            if (initDataBlast.get('customInput')) {
              const tmp = initDataBlast.get('customInput');

              const {
                dataOut,
                customInputAttributes,
              } = serializeDataInputAttrs({
                customInputAttributes: inputAttributes,
                positions: res.data[0].positions,
              });
              draft.inputStories = Array.isArray(tmp)
                ? mapCustomInputToFeActive(tmp)
                : _.cloneDeep(mapCustomInputToFe(tmp, dataOut));
              draft.customInputAttributes = customInputAttributes;
              // });
            } else {
              const {
                dataOut,
                customInputAttributes,
              } = serializeDataInputAttrs({
                customInputAttributes: inputAttributes,
                positions: res.data[0].positions,
              });
              draft.inputStories = dataOut;
              draft.customInputAttributes = customInputAttributes;
            }

            if (
              type === NODE_TYPE.SCHEDULED ||
              type === NODE_TYPE.EVENT_BASED
            ) {
              props.initDataCustomInput({
                nodeId: activeNode.nodeId,
                data: draft.inputStories,
              });
            }

            draft.isLoadingInputViaUI = false;
          });
          return () => {
            setState(draft => {
              draft.isLoadingInputViaUI = false;
            });
          };
        }
      })
      .catch(err => {
        if (!err.isCanceled) {
          addMessageToQueue({
            path:
              'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/Content/BlastCampaign/index.jsx',
            func: 'cancellablePromise',
            data: err.stack,
          });
          console.warn('err', err);
        }
      });

    // }
  }, []);

  const callback = (type, data) => {
    props.callback(type, data);
    props.onChangeData(type, data);
  };

  const configComponent = useMemo(() => {
    const { channelId } = activeNode;
    const newListNode = convertNodeList(props.listNodes);
    const config = getConfigBlastCampaign(
      channelId,
      newListNode,
      activeNode,
      props.design,
    );
    return config;
  }, [props.listNodes, activeNode.channelId]);

  const { isThirdPartyCampaigns, sendMethodThirdParty } = useMemo(() => {
    let [checkThirdParty, sendMethod] = [false, null];

    if (props.design !== 'create') {
      checkThirdParty = isThirdParty && !!scheduledThirdParty?.sendMethod;
      sendMethod = scheduledThirdParty?.sendMethod;
    } else {
      const templateType = get(creatingJourneyInfo, 'templateType', '');
      checkThirdParty = templateType === THIRD_PARTY_CAMPAIGN.value;
      sendMethod = get(creatingJourneyInfo, 'sendMethod', '');
    }

    return {
      isThirdPartyCampaigns: checkThirdParty,
      sendMethodThirdParty: sendMethod,
    };
  }, [creatingJourneyInfo, props.design, isThirdParty, scheduledThirdParty]);

  const isDefaultRichMenu = useMemo(() => {
    if (
      channelActive.code !== CHANNEL.LINE.code ||
      catalogCode !== CATALOG_CODE.LINE_RICH_MENU
    )
      return false;

    const { copyId } = queryString.parse(window.location.search);

    if (props.design !== 'create' || !_.isEmpty(copyId)) {
      let defaultRichPath = 'activeRow.is_default_rich_menu';
      if (!_isEmpty(main.activeRowClone) && !_.isEmpty(copyId)) {
        defaultRichPath = 'activeRowClone.is_default_rich_menu';
      }

      const numbericDefaultRichMenu = get(main, defaultRichPath, 0);

      return +numbericDefaultRichMenu === 1;
    }

    const dashboardMode = get(
      creatingJourneyInfo,
      'data.template.dashboardMode',
      '',
    );

    return dashboardMode === MENU_TYPE.DEFAULT;
  }, [
    channelActive,
    catalogCode,
    creatingJourneyInfo,
    props.design,
    main && main.activeRow,
    main && main.activeRowClone,
  ]);

  const onChangeData = (type, dataOut) => {
    props.onChangeData(type, dataOut);
  };

  useEffect(() => {
    if (
      Array.isArray(props.campaignList) &&
      !_isEmpty(props.activeNode) &&
      !_isEmpty(props.campaignList)
    ) {
      const audiences = getAudiencesByActiveId(
        props.campaignList,
        props.activeNode.nodeId,
      );
      if (!_isEmpty(audiences)) {
        props.onValidateTypeForecast({ data: audiences });
      }
    }
  }, [props.activeNode, props.campaignList]);

  const defaultCampaignDesign = useMemo(() => {
    switch (activeNode.channelCode) {
      case CHANNEL_TYPES.WEB_PER: {
        let typeCreateTemplate = mainReducer && mainReducer.templateDesign;

        if (props.design === 'create' && !typeCreateTemplate) {
          const { templateType } = creatingJourneyInfo;
          typeCreateTemplate = templateType;
        }

        const destinationType = getDestinationTypeWebPersonal(
          typeCreateTemplate,
        );

        if (destinationType) {
          const designNodeConfig = DATA_DESIGN_NODE_DESTINATION_WEB_EMBEDDED.find(
            node => node.value === destinationType,
          );
          if (designNodeConfig) {
            return designNodeConfig;
          }
        }
        break;
      }
      case CHANNEL_TYPES.VIBER:
        return DATA_DESIGN_NODE_DESTINATION[0];
      case CHANNEL_TYPES.CONVERSATION:
        return DATA_DESIGN_NODE_DESTINATION[0];
      case CHANNEL_TYPES.EMAIL:
        return DATA_DESIGN_NODE_DESTINATION_EMAIL[0];
      default:
        break;
    }
  }, [activeNode.channelCode, mainReducer.templateDesign]);

  const renderTriggers = () => {
    if (isThirdPartyCampaigns) {
      return (
        <ThirdPartyScheduled
          isViewMode={props.isViewMode}
          scheduled={scheduledThirdParty}
          sendMethodInit={creatingJourneyInfo?.sendMethod}
          catalogCodeInit={creatingJourneyInfo?.catalogInfo?.catalogCode}
          dataInitInputViaUI={state.inputStories}
          customInputAttributes={state.customInputAttributes}
          isLoadingInputViaUI={state.isLoadingInputViaUI}
          onChange={props.dispatchChangeScheduledThirdParty}
          onChangeData={onChangeData}
        />
      );
    }

    if (type === NODE_TYPE.EVENT_BASED) {
      return (
        <WrapperTrigger>
          <TriggerEventBased
            moduleConfig={moduleConfig}
            roleActions={props.roleActions}
            validateKey={props.validateKey}
            onChange={onChangeData}
            initData={initDataBlast}
            design={props.design}
            isViewMode={props.isViewMode}
            callback={callback}
            // hasOpenModalConfirm={false}
            channelActive={channelActive}
            dataInitInputViaUI={state.inputStories}
            customInputAttributes={state.customInputAttributes}
            isLoadingInputViaUI={state.isLoadingInputViaUI}
            isBlastCampaign
            activeNode={activeNode}
            activeRow={main.activeRow}
          />
        </WrapperTrigger>
      );
    }

    return (
      <WrapperTrigger>
        <TriggerScheduled
          validateKey={props.validateKey}
          isBlastCampaign
          onChange={callback}
          initData={initDataMain}
          dataInitInputViaUI={state.inputStories}
          isLoadingInputViaUI={state.isLoadingInputViaUI}
          customInputAttributes={state.customInputAttributes}
          isValidTimeScheduled={props.isValidTimeScheduled}
          activeNode={activeNode}
          isViewMode={props.isViewMode}
          design={props.design}
          moduleConfig={moduleConfig}
        />
      </WrapperTrigger>
    );
  };

  return (
    <WrapperBlastCampaign
      className="blast-grid-container"
      data-test="blast-campaign-container"
      // style={{ marginBottom: props.design !== 'create' ? '15px' : 'unset' }}
    >
      <Box>
        {renderTriggers()}
        <WrapperCampaigns>
          <UILoading isLoading={props.isLoading} />
          {isThirdPartyCampaigns ? null : (
            <Box
              width="12%"
              style={{
                borderRight: '1px solid #e5e5e5',
              }}
            >
              <ListCampaign
                activeRow={props.main.activeRow}
                campaignActive={props.activeNode}
                campaignList={props.campaignList}
                callback={props.callback}
                isViewMode={props.isViewMode || isDefaultRichMenu}
              />
            </Box>
          )}
          <Box width={isThirdPartyCampaigns ? '100%' : '88%'}>
            {isReachedRichMenu && (
              <Error
                message={getTranslateMessage(
                  TRANSLATE_KEY._,
                  'Not able to create menu due to exceed Line quantity limit (1,000)',
                )}
              />
            )}
            {errors &&
              activeNode.nodeId &&
              errors[activeNode.nodeId] &&
              Object.keys(errors[activeNode.nodeId]).length > 0 && (
                <Error
                  message={Object.values(errors[activeNode.nodeId])?.[0]}
                />
              )}
            {props.campaignList && props.campaignList.length ? (
              <Destination
                activeRow={activeRow}
                isThirdPartyCampaigns={isThirdPartyCampaigns}
                sendMethodThirdParty={sendMethodThirdParty}
                isBlastCampaign
                moduleConfig={props.moduleConfig}
                roleActions={props.roleActions}
                previousNodes={props.previousNodes || {}}
                validateKey={props.validateKey}
                validateKeyBlast={props.validateKeyBlast}
                updateFreshNodesKey={updateFreshNodesKey}
                initValueTargetAudience={initData}
                onChange={onChangeData}
                activeNode={activeNode}
                campaignList={props.campaignList}
                previewForecast={props.previewForecast}
                initData={initData.get('destination')}
                targetAudience={initData.get('targetAudience')}
                triggerType={props.triggerType}
                eventValue={triggerEvent}
                itemTypeId={props.itemTypeId}
                isFirstCampaign={props.isFirstCampaign}
                isDefaultRichMenu={isDefaultRichMenu}
                dataValidateSwitchTabAudiences={
                  props.dataValidateSwitchTabAudiences
                }
                refreshKeyValidateAudiences={props.refreshKeyValidateAudiences}
                audienceTypeBlastCampaign={props.audienceTypeBlastCampaign}
                callback={callback}
                isEnableTesting={isEnableTesting || false} // ok  cần callback ra ngoài để xử lý quickTest
                errorKey={props.errorKey || 1} // ok         number 1
                // errorSendTo={props.errorSendTo}
                componentId={activeNode.nodeId}
                isViewMode={props.isViewMode}
                configComponent={configComponent}
                onUpdateValidateKeyBlast={props.onUpdateValidateKeyBlast}
                itemSquareSelected={defaultCampaignDesign}
                // templateInputViaUI={state.inputStories}
                // isLoadingInputViaUI={state.isLoadingInputViaUI}
              />
            ) : null}
          </Box>
        </WrapperCampaigns>
      </Box>
    </WrapperBlastCampaign>
  );
};

const mapStateToProps = createStructuredSelector({
  main: makeSelectMainCreateWorkflow(),
  mainReducer: makeSelectMainReducerCreateWorkflow(),
  creatingJourneyInfo: makeSelectCreatingJourneyInfo(),
});

const mapDispatchToProps = (dispatch, props) => {
  const prefix = props.moduleConfig.key;
  return {
    initDataCustomInput: value =>
      dispatch(updateValue(`${prefix}@@GET_DATA_INIT_INPUT@@`, value)),
    onValidateTypeForecast: data => {
      dispatch(updateValue(`${prefix}@@VALIDATE_FORECAST@@`, data));
    },
    onUpdateValidateKeyBlast: data => {
      dispatch(updateValue(`${prefix}@@UPDATE_VALIDATE_KEY_BLAST@@`, data));
    },
    dispatchChangeScheduledThirdParty: payload => {
      dispatch(
        updateValue(`${prefix}@@CHANGE_DATA_SCHEDULED_THIRD_PARTY`, payload),
      );
    },
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(BlastCampaign);

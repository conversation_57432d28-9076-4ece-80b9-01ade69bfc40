/* eslint-disable indent */
import {
  call,
  cancel,
  delay,
  fork,
  put,
  select,
  take,
  takeLatest,
} from 'redux-saga/effects';
import DestinationServices from 'services/Destination';
import { push, replace } from 'react-router-redux';
import { Map } from 'immutable';
import JourneyServices from 'services/Journey';
import ObjectServices from 'services/Object';
import LinkManagementServices from 'services/LinkManagement';
import {
  get,
  isEmpty,
  isArray,
  cloneDeep,
  has,
  set,
  isEqual,
  sortBy,
} from 'lodash';
import ReduxTypes from '../../../../../../redux/constants';
import {
  generateKey,
  getObjectPropSafely,
  safeParse,
} from '../../../../../../utils/common';
import {
  addNotification,
  dashboardSetOwnerIdFromData,
  getDetail,
  getListDone,
  init,
  reset,
  updateDone,
  updateValue,
} from '../../../../../../redux/actions';
import { getDataFilter, getInitCampaign } from './constants';
import {
  selectDomainMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
} from '../selectors';
import {
  generateFeConfigWithAPI,
  getAndUpdateAvailabelStatusAndpermission,
  handleStoryFetchListNode,
} from '../saga.fetch';
import {
  makeSelectDashboardOwnerId,
  makeSelectSettingPersonalizations,
} from '../../../../selector';
import { getPortalTimeZone } from '../../../../../../utils/web/portalSetting';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getCurrentOwnerIds,
  getPortalId,
} from '../../../../../../utils/web/cookie';
import {
  hardAudienceFilterForDefaultRichMenu,
  removeDataUnUseForAPI,
  updateDataVariantWithFeConfigID,
} from '../utils.map';
import {
  setCampaignVariantInfo,
  toAPI,
  handleUploadWorkflowThumbs,
  getListVariantId,
} from '../utils';
import { getChannelCodeById } from '../../utils';
import selectStoryDetailDomain from '../../Detail/selectors';
import { getPrefixDetail } from '../../Detail/VersionHistory2/Detail/utils';
import {
  validateMessage,
  validateNodeActive,
  validateNodesDuplicateCampaignId,
  validateCampaignEmptySelected,
} from '../utils.validate';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import {
  CATALOG_CODE,
  DEFAULT_TRIGGER_TYPE,
  NODE_TYPE,
  TRIGGER_NODE,
} from '../Content/Nodes/constant';
import {
  handleValidateRuleActionDeleteNode,
  rebuildBranchSplitNodeName,
} from '../saga.flow';
import { actionTypes } from '../_reducer/configure.flow';
import APP from '../../../../../../appConfig';
import {
  makeSelectCreatingJourneyInfo,
  makeSelectCache3rdPartyCampaign,
  makeSelectJourneyChannelActive,
} from '../../selectors';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import {
  convertJourneyToBlast,
  getCatalogByChannel,
  toDestinationList,
  validateSwitchTabAudiences,
  getActiveNode,
  mapCampaignSettingToAPI,
  convertThirdPartyCampaignToBlast,
} from './utils';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  MENU_CODE,
  checkingRoleScope,
  getOwnerIdCreateRole,
} from '../../../../../../utils/web/permission';
import { PortalDate } from '../../../../../../utils/date';
import { getErrorMessageV2Translate } from '../../../../../../utils/web/message';

import {
  toNodeTriggerScheduleUI,
  wrapperToUITargetAudience,
} from '../Content/Nodes/TriggerScheduled/utils';
import { initCreateRolesAction } from '../utils.story.rules';

import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../../Detail/config';
import { getUntitledCopyName } from '../../../../../../utils/web/properties';
import {
  getIsUpdatedDestination,
  toNodeDestinationUI,
} from '../Content/Nodes/Destination/utils';
import { handleCloneNodeCampaign } from '../Content/FlowChart/utils';

import { CHANNEL, UI_DETAIL_DRAWER } from '../../constant';
import { toNodeTriggerEventBasedUI } from '../Content/Nodes/TriggerEventBased/utils';
import { validateConversion } from '../../../../../../containers/UIDev/ConversionGoal/utils';
import { handleGetJTSettings, handleSaveAsTemplate } from '../saga.template';
import { MENU_TYPE } from '../../../../../../containers/TemplateListing/constants';

import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../../config';
import { MODULE_CONFIG as MODULE_CONFIG_LISTING } from '../../List/config';
import { KEY_PARAMS } from '../../Main/constants';
import { THIRD_PARTY_CAMPAIGN } from '../../../../../../containers/Journey/constant';
import { isValidThirdPartyTimestamp } from './Content/BlastCampaign/ThirdPartyScheduled/utils';
import {
  PROMOTION_CODE,
  SHORT_LINK_V2,
} from '../../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';
import {
  getPermissionTreeNodes,
  hasPoolRemoved,
} from '../utils.checkPermission';
import {
  startPersonalizations,
  updateSettingPersonalizations,
} from '../../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';
import {
  toUIDataPromotionCode,
  toUIDataShortener,
} from '../../../../../../components/common/UIEditorPersonalization/utils';
import {
  dtoPromotionCode,
  dtoShortLinkShortener,
} from '../../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';
import { translate, translations } from '@antscorp/antsomi-locales';

const PREFIX_DETAIL = MODULE_CONFIG_DETAIL.key;

export default function* workerCustomerSaga(args) {
  const { moduleConfig } = args;
  const { key: prefix } = moduleConfig;

  yield takeLatest(`${prefix}${ReduxTypes.INIT}`, handleInit, args);
  yield takeLatest(`${prefix}${ReduxTypes.UPDATE}`, handleSave, args);
  yield takeLatest(
    `${prefix}@@VALIDATE_SCHEDULED_TIME@@${ReduxTypes.VALIDATE_VALUE}`,
    validateScheduledTime,
    args,
  );

  // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
  // yield takeLatest(
  //   `${prefix}@@SEND_TO@@${ReduxTypes.VALIDATE_VALUE}`,
  //   validateSendTo,
  //   args,
  // );
  yield takeLatest(
    `${prefix}${ReduxTypes.VALIDATE_VALUE}`,
    handleValidateBeforeAction,
    args,
  );
  yield takeLatest(
    `${prefix}@@REMOVE_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`,
    onUpdateHeaderDataNode,
    args,
  );

  yield takeLatest(
    `${prefix}@@NODE_STATUS@@${ReduxTypes.UPDATE}`,
    handleSaveChangeStatus,
    args,
  );
  yield takeLatest(
    `${prefix}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
    args,
  );

  yield takeLatest(
    `${prefix}@@DATA_NODE@@${ReduxTypes.UPDATE_VALUE}`,
    handleUpdateListCampaign,
    args,
  );

  yield takeLatest(
    `${prefix}@@INIT_SETTING_BLASTCAMPAIGN_DONE@@${ReduxTypes.UPDATE_VALUE}`,
    handleMapErrorAttributesTreeNodes,
    { ...args, isInit: true },
  );

  yield takeLatest(
    `${prefix}@@MAP_ERROR_ATTRIBUTES@@${ReduxTypes.INIT}`,
    handleMapErrorAttributesTreeNodes,
    args,
  );
}

export function* handleInit(args, action) {
  const { design, channelActive = {}, channelId } = action.payload;
  // const { value: channelId } = channelActive;

  const searchParams = new URLSearchParams(window.location.search);
  const channelIdSearchParams = searchParams.get('channelId');
  const channelCode = getChannelCodeById(channelIdSearchParams || channelId);
  const prefix = args.moduleConfig.key;

  let listNode = yield call(handleStoryFetchListNode);
  const { catalogInfo, ...restCreatingInfo } = yield select(
    makeSelectCreatingJourneyInfo(),
  );
  const reducerWorkflow = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const { trigger = {} } = reducerWorkflow?.mainReducer || {};
  const isChannelSms = +channelId === CHANNEL.SMS.id;
  let catalogCode = get(catalogInfo, 'catalogCode', '');
  const templateTypeCreating = get(restCreatingInfo, 'templateType', '');
  let sendMethod = get(restCreatingInfo, 'sendMethod', '');
  let is3rdPartyCampaigns = templateTypeCreating === THIRD_PARTY_CAMPAIGN.value;

  if (design !== 'create') {
    // Checking whether the trigger of the third-party campaign
    is3rdPartyCampaigns = get(trigger, 'isThirdParty', false);

    // Re-assign checking for third party campaign
    if (is3rdPartyCampaigns) {
      catalogCode = get(trigger, 'scheduledThirdParty.catalogCode', '');
      sendMethod = get(trigger, 'scheduledThirdParty.sendMethod', '');
    }
  } else if (get(action, 'payload.copyId', '')) {
    const cache3rdParty = yield select(makeSelectCache3rdPartyCampaign());
    if (!isEmpty(cache3rdParty)) {
      is3rdPartyCampaigns = true;
      sendMethod = cache3rdParty?.sendMethod;
      catalogCode = cache3rdParty?.catalogCode;
    }

    // Reset cache of third party campaign
    yield put(
      updateValue(
        `${MODULE_CONFIG_COMMON.key}@@CACHE_COPY_3RD_PARTY_CAMPAIGN`,
        {},
      ),
    );
  }

  if (isChannelSms && !is3rdPartyCampaigns) {
    // Hard to exclude destination has send method "sendCSKH" from SMS channel in fetch list destination
    sendMethod = 'sendCSKH';
  }

  // NOTE: in case init Blast of the journey of the some channel, need to filter node destination by catalog
  let isReFilterByCatalog = false;

  // NOTE: handle case: select channel of third party campaign
  if (is3rdPartyCampaigns) {
    isReFilterByCatalog = is3rdPartyCampaigns;
  }
  switch (channelCode) {
    case CHANNEL.LINE.code: {
      if (
        [CATALOG_CODE.LINE_RICH_MENU, CATALOG_CODE.LINE].includes(catalogCode)
      ) {
        isReFilterByCatalog = true;
      }
      break;
    }
    case CHANNEL.APP_PUSH.code: {
      if (CATALOG_CODE.ANTSOMI_APP_PUSH === catalogCode) {
        isReFilterByCatalog = true;
      }
      break;
    }
    default: {
      break;
    }
  }
  if (isReFilterByCatalog && Array.isArray(listNode)) {
    listNode = listNode.map(node => {
      const { groupId = '', nodes = [] } = node;

      if (groupId === 'destination' && Array.isArray(nodes)) {
        return {
          ...node,
          nodes: nodes.filter(nodeItem => nodeItem.catalogCode === catalogCode),
        };
      }

      return node;
    });
  }
  yield put(getListDone(`${prefix}@@NODE@@`, listNode));

  let listDestination;

  // channel web_personalization đang lấy destinations theo catalog web_embedded, các channel còn lại đang lấy theo all catalogs
  if (channelId === CHANNEL.WEB_PERSONALIZATION.id) {
    const resCatalogs = yield call(JourneyServices.node.getList, {
      channelId,
    });
    const catalogs = get(resCatalogs, 'data[1].nodes', []);

    const catalog = getCatalogByChannel(catalogs, channelId);

    const destinations = yield call(
      JourneyServices.data.getDestinationByCatalogIds,
      {
        objectId: catalog.code,
        _owner_id: getCurrentOwnerId(),
      },
    );

    listDestination = toDestinationList(destinations, catalog);
  } else {
    const destinations = yield call(
      DestinationServices.data.getListDestToolkit,
      {
        data: getDataFilter(channelId, ['send_method'], {
          is3rdParty: is3rdPartyCampaigns,
          sendMethod,
        }),
      },
    );

    if (isReFilterByCatalog && Array.isArray(destinations.data)) {
      listDestination = destinations.data.filter(destination => {
        const isEqualCatalog = isEqual(destination.catalog_code, catalogCode);
        return isEqualCatalog;
      });
    } else {
      listDestination = destinations.data;
    }
  }

  let initNode = listDestination[0];

  if (Object.keys(catalogInfo || {}).length) {
    const newInitNode = listDestination.find(
      each => each.catalog_dest_id === catalogInfo.catalogId,
    );
    if (newInitNode) {
      initNode = newInitNode;
    }
  }

  yield put(
    getListDone(`${prefix}@@NODE_LIST@@`, { nodeList: listDestination }),
  );

  const triggerType = DEFAULT_TRIGGER_TYPE[channelCode];

  if (triggerType) {
    yield put(updateValue(`${prefix}@@UPDATE_VALUE@@`, { triggerType }));
  }

  // if (design === 'create' && !activeNode.nodeId && nodeListSelectedChannel.length) {
  //   const campaign = getInitCampaign(initNode, activeNode.nodeId);
  //   yield put(updateValue(`${prefix}@@UPDATE_LIST_CAMPAIGN@@`, {campaign}));

  //   yield put(updateValue(`${prefix}@@FLOW_UPDATE_BLAST_CAMPAIGN@@`, {campaign}));
  // }

  if (design === 'create') {
    // for case create
    const ownerId = yield select(makeSelectDashboardOwnerId());
    // console.log('ownerId', ownerId, getCurrentAccessUserId());

    if (Number.isNaN(Number(ownerId.value))) {
      yield put(dashboardSetOwnerIdFromData(getCurrentAccessUserId()));
    }

    const { copyId } = action.payload;

    const { settings } = yield call(handleGetJTSettings, {
      moduleConfig: args.moduleConfig,
    });

    if (copyId !== undefined || settings) {
      yield call(handleCreateCopy, { copyId, settings }, args);

      // The current instruction is not to use this function for the Blast Campaign
      // yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);
    } else {
      const activeNode = {
        nodeId: generateKey(),
        value: initNode?.catalog_dest_id,
        channelCode,
        catalogCode: initNode?.catalog_code,
        channelId,
        type: triggerType,
      };
      yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, activeNode));

      if (is3rdPartyCampaigns) {
        yield call(handleInitCampaignThirdParty, args, activeNode);
      }
    }
  } else {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );

    const { mainReducer } = reducer;
    const {
      blastCampaignData,
      blastCampaignNode,
      blastCampaignNodeMain,
    } = action.payload.blastCampaign;

    const { campaigns, trigger } = blastCampaignData;
    const data = {
      cacheNodes: blastCampaignNode,
      mainNodesBlast: blastCampaignNodeMain,
    };

    yield put(init(`${prefix}@@FLOW_UPDATE@@`, data));

    if (!mainReducer.activeNode.nodeId && campaigns.length) {
      const campaignInit = campaigns[0];
      const initActiveNode = getActiveNode(campaignInit, mainReducer.listNodes);
      yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, initActiveNode));
      yield put(updateValue(`${prefix}@@INIT_DONE@@`, { initDone: true }));
    }
  }
}

function* onUpdateHeaderDataNode(args) {
  try {
    const prefix = args.moduleConfig.key;
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { main } = reducer;

    if (main.design === 'update') {
      yield put(updateValue(`${prefix}@@DATA_HEADER_NODE@@`));
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'onUpdateHeaderDataNode',
      data: error.stack,
    });
    console.log('error :>', error);
  }
}

function* handleCreateCopy({ copyId, settings }, args) {
  try {
    const prefix = args.moduleConfig.key;

    const oid = new URLSearchParams(window.location.search).get('oid');

    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { mainReducer } = reducer;
    const hasRoleViewEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.VIEW,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const params = {
      objectId: copyId,
      // versionId: !typeResume && versionId,
      _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
    };

    let journeyDetail = { ...settings };

    if (copyId) {
      const res = yield call(JourneyServices.versionHistory.getDetail, params);
      journeyDetail = getObjectPropSafely(() => res.data, {});
    }
    const storyName = getUntitledCopyName(journeyDetail.story_name);
    const actionType = getObjectPropSafely(
      () => mainReducer.trigger.actionType,
      null,
    );
    let frequencyCapping = null;
    let frequencyCappingV2 = [];


    if (Object.keys(journeyDetail).length > 0) {
      let newMapMain = Map({});

      if (actionType === NODE_TYPE.EVENT_BASED) {
        const mapTrigger = toNodeTriggerEventBasedUI(
          journeyDetail.workflow_setting.metadata,
          journeyDetail.custom_inputs,
        );

        const selectTime = mapTrigger.get('selectTime');
        const peformEvent = mapTrigger.get('peformEvent');
        const selectDayOfWeek = mapTrigger.get('selectDayOfWeek');
        const customInput = mapTrigger.get('customInput');
        const journeyGoals = mapTrigger.get('journeyGoals');

        frequencyCapping = mapTrigger.get('frequencyCapping');
        frequencyCappingV2 = mapTrigger.get('frequencyCappingV2');
        newMapMain = newMapMain
          .set('selectTime', selectTime)
          .set('peformEvent', peformEvent)
          .set('frequencyCapping', frequencyCapping)
          .set('frequencyCappingV2', frequencyCappingV2)
          .set('selectDayOfWeek', selectDayOfWeek)
          .set('customInput', customInput)
          .set('journeyGoals', journeyGoals);
      } else if (actionType === NODE_TYPE.SCHEDULED) {
        const mapTrigger = toNodeTriggerScheduleUI(
          journeyDetail.workflow_setting.metadata,
          journeyDetail.custom_inputs,
        );
        const scheduled = mapTrigger.get('scheduled');
        frequencyCapping = mapTrigger.get('frequencyCapping');
        const customInput = mapTrigger.get('customInput');

        newMapMain = newMapMain
          .set('scheduled', scheduled)
          .set('frequencyCapping', frequencyCapping)
          .set('customInput', customInput);
      }
      const isThirdParty = get(
        journeyDetail,
        'workflow_setting.metadata.isThirdParty',
        false,
      );
      const blastCampaignData = isThirdParty
        ? convertThirdPartyCampaignToBlast(journeyDetail)
        : convertJourneyToBlast(journeyDetail);

      const { campaigns = [] } = blastCampaignData;

      const campaignsClone = cloneDeep(campaigns);

      campaignsClone[0].actionId = generateKey();

      let newMap = Map({});
      campaignsClone.forEach(campaign => {
        const newMapDestination = toNodeDestinationUI(campaign, 'RESET');
        const destination = newMapDestination.get('destination');

        newMap = newMap.setIn([campaign.actionId, 'destination'], destination);

        if (actionType === NODE_TYPE.EVENT_BASED) {
          newMap = newMap.setIn([campaign.actionId, 'filter'], campaign.filter);
        } else if (actionType === NODE_TYPE.SCHEDULED) {
          const targetAudience = wrapperToUITargetAudience(campaign.audiences);

          newMap = newMap.setIn(
            [campaign.actionId, 'targetAudience'],
            targetAudience,
          );
        }
      });

      const data = {
        cacheNodes: newMap,
        mainNodesBlast: newMapMain,
      };

      if (copyId) {
        yield put(updateValue(`${prefix}@@STORY_NAME@@`, storyName));
        yield put(updateValue(`${prefix}@@PAGE_TITLE@@`, storyName));
      }

      yield put(init(`${prefix}@@FLOW_UPDATE@@`, data));
      yield put(updateDone(`${prefix}@@CREATE_COPY_DONE@@`));
      yield put(
        updateValue(`${prefix}@@ACTIVE_ROW_CLONE@@`, {
          activeRowClone: journeyDetail,
        }),
      );
      yield put(
        updateValue(`${prefix}@@FREQUENCY_CAPPING_CREATE_COPY@@`, {
          frequencyCapping,
          frequencyCappingV2,
        }),
      );

      if (!mainReducer.activeNode.nodeId && campaignsClone.length) {
        const campaignInit = campaignsClone[0];
        const initActiveNode = getActiveNode(
          campaignInit,
          mainReducer.listNodes,
        );

        yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, initActiveNode));
        yield put(updateValue(`${prefix}@@INIT_DONE@@`, { initDone: true }));
        yield put(
          updateValue(`${prefix}@@UPDATE_CAMPAIGNS_CREATE_COPY@@`, {
            campaigns: campaignsClone,
          }),
        );
      }

      if (isThirdParty && copyId) {
        const scheduledThirdParty = get(
          blastCampaignData,
          'trigger.scheduledThirdParty',
          {},
        );

        yield put(
          updateValue(`${MODULE_CONFIG_COMMON.key}@@CREATING_JOURNEY_INFO`, {
            sendMethod: scheduledThirdParty.sendMethod,
            templateType: THIRD_PARTY_CAMPAIGN.value,
            catalogInfo: {
              catalogCode: scheduledThirdParty.catalogCode,
            },
            data: {
              sendMethod: scheduledThirdParty.sendMethod,
              value: THIRD_PARTY_CAMPAIGN.value,
            },
          }),
        );
        yield put(
          updateValue(`${prefix}@@SET_DATA_SCHEDULED_THIRD_PARTY`, {
            isThirdParty,
            scheduledThirdParty,
          }),
        );
      }

      // updateUrl(
      //   makeUrlPermisison(
      //     `${
      //       APP.PREFIX
      //     }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
      //       journeyDetail.channel_id
      //     }/blast-campaign/create`,
      //   ),
      // );

      let search = `?channelId=${journeyDetail.channel_id}`;

      if (oid) {
        search += `&oid=${oid}`;
      }
      yield put(
        replace({
          search,
        }),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleCreateCopy',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleInitCampaignThirdParty(args, payload) {
  try {
    const prefix = args.moduleConfig.key;
    const { nodeId = '' } = payload;
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { mainReducer } = reducer;
    const { design, campaigns, listNodes, channelId } = mainReducer;

    const campaignUpdate = campaigns.find(camp => camp.actionId === nodeId);

    if (!campaignUpdate && design === 'create') {
      const newCampaign = getInitCampaign(nodeId, listNodes[0], channelId);
      yield put(
        updateValue(`${prefix}@@UPDATE_LIST_CAMPAIGN@@`, {
          campaign: newCampaign,
        }),
      );
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleInitCampaignThirdParty',
      data: error.stack,
    });
    console.error(error);
  }
}

function* handleUpdateListCampaign(args, action) {
  const prefix = args.moduleConfig.key;
  const { name, nodeId, data } = action.payload;
  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const { mainReducer } = reducer;
  const {
    design,
    campaigns,
    listNodes,
    channelId,
    // canTriggerValidateSendTo,
  } = mainReducer;
  const campaignUpdate = campaigns.find(camp => camp.actionId === nodeId);
  if (!campaignUpdate && design === 'create') {
    const newCampaign = getInitCampaign(nodeId, listNodes[0], channelId);
    yield put(
      updateValue(`${prefix}@@UPDATE_LIST_CAMPAIGN@@`, {
        campaign: newCampaign,
      }),
    );
    yield put(
      updateValue(`${prefix}@@UPDATE_CAMPAIGN@@`, { name, nodeId, data }),
    );
  } else {
    yield put(
      updateValue(`${prefix}@@UPDATE_CAMPAIGN@@`, { name, nodeId, data }),
    );
  }

  // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
  // if (canTriggerValidateSendTo) {
  //   yield call(validateSendTo, args, {
  //     payload: { isValidateBeforeAction: true },
  //   });
  // }
}

// eslint-disable-next-line consistent-return
function* handleValidateThirdPartyScheduled(
  args,
  extraData = { isValidateBeforeActivate: false },
) {
  const prefix = args.moduleConfig.key;
  const { isValidateBeforeActivate = false } = extraData;

  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const scheduledThirdParty = getObjectPropSafely(
      () => reducer?.mainReducer?.trigger?.scheduledThirdParty,
      {},
    );
    const triggerType = getObjectPropSafely(
      () => reducer.configure.main.triggerType,
      '',
    );

    if (triggerType === NODE_TYPE.SCHEDULED) {
      const { value } = scheduledThirdParty;
      let thirdPartyTimes = value;

      // in case of edit, validate time after saving in raw data
      if (isValidateBeforeActivate) {
        thirdPartyTimes = get(
          reducer,
          'mainReducer.activeRow.workflow_setting.metadata.scheduledThirdParty.startTime',
        );
      }

      let error = '';
      const isValid = isValidThirdPartyTimestamp(thirdPartyTimes);

      if (!isValid) {
        error = getTranslateMessage(
          TRANSLATE_KEY._3RD_CAMPAIGN_ERROR_START_TIME,
          'Start time should be at least 3 hour after saving journey',
        );
      }

      yield put(
        updateValue(`${prefix}@@CHANGE_DATA_SCHEDULED_THIRD_PARTY`, {
          error,
        }),
      );

      return {
        isValid,
        error,
      };
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleValidateThirdPartyScheduled',
      data: error.stack,
    });
    console.error(error);
  }
}

function* handleValidateAll(args, action) {
  const prefix = args.moduleConfig.key;

  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const creatingJourneyInfo = yield select(makeSelectCreatingJourneyInfo());
    const {
      nodes,
      isValidateFormatDateTime,
      mainNodesBlast,
    } = reducer.configure.main;
    const {
      activeNode = {},
      campaigns = [],
      trigger = {},
    } = reducer.mainReducer;
    const channelCode = get(activeNode, 'channelCode', '');
    const catalogCode = get(activeNode, 'catalogCode', '');
    const design = get(reducer, 'main.design', '');
    let isDefaultRichMenu = false;
    const templateTypeCreating = get(creatingJourneyInfo, 'templateType', '');

    let isThirdPartyCampaigns =
      templateTypeCreating === THIRD_PARTY_CAMPAIGN.value;

    if (design !== 'create') {
      isThirdPartyCampaigns = get(trigger, 'isThirdParty', false);
    }

    if (
      channelCode === CHANNEL.LINE.code &&
      catalogCode === CATALOG_CODE.LINE_RICH_MENU
    ) {
      const copyId = get(action, 'payload.copyId', '');

      if (design === 'update' || !isEmpty(copyId)) {
        let defaultRichMenuPath = 'main.activeRow.is_default_rich_menu';
        if (!isEmpty(copyId)) {
          defaultRichMenuPath = `main.activeRowClone.is_default_rich_menu`;
        }

        const numbericDefaultRichMenu = get(reducer, defaultRichMenuPath, 0);

        isDefaultRichMenu = +numbericDefaultRichMenu === 1;
      } else if (!isEmpty(creatingJourneyInfo)) {
        const dashboardMode = get(
          creatingJourneyInfo,
          'data.template.dashboardMode',
          '',
        );

        if (dashboardMode === MENU_TYPE.DEFAULT) {
          isDefaultRichMenu = true;
        }
      }
    }

    const triggerType = getObjectPropSafely(
      () => reducer.configure.main.triggerType,
      '',
    );
    const dataServeTabAudiences = validateSwitchTabAudiences(
      activeNode,
      campaigns,
    );
    yield put(
      updateValue(
        `${prefix}@@UPDATE_DATA_VALIDATE_AUDIENCES@@`,
        dataServeTabAudiences,
      ),
    );
    yield put(updateValue(`${prefix}@@REFRESH_KEY_VALIDATE_AUDIENCES@@`));

    let errorsMessage = '';
    let nodesError = {};

    nodes.forEach((nodeInfo, key) => {
      // console.log('nodeInfo', nodeInfo, key);
      const destination = safeParse(nodeInfo.get('destination'), {});
      if (!has(destination, 'isFetchInfoData')) {
        nodesError = {
          ...nodesError,
          [key]: true,
        };
      } else {
        // Only validate if isUpdated of Destination === 1
        const journeyGoals = safeParse(mainNodesBlast.get('journeyGoals'), {});
        const { dataValidateConversion } = validateConversion(journeyGoals);
        const nodesErrors = dataValidateConversion;
        const isUpdated = getIsUpdatedDestination(destination);
        if (isUpdated === 1) {
          const nodesErrorTemp = validateNodeActive(
            { nodeId: key, type: NODE_TYPE.DESTINATION },
            nodeInfo,
            isValidateFormatDateTime,
          );
          nodesErrorTemp[key] = nodesErrors
            ? nodesErrorTemp[key]
            : !nodesErrors;
          nodesError = {
            ...nodesError,
            ...nodesErrorTemp,
          };
          // validate nodeInfo schedule trigger
          errorsMessage = validateMessage(
            { nodeId: key, type: NODE_TYPE.DESTINATION },
            nodeInfo,
            isValidateFormatDateTime,
          );
        }
      }
    });

    // Validate Time Scheduled
    // const scheduledValidateTemp = validateScheduledBlast(
    //   mainNodesBlast,
    //   isValidateFormatDateTime,
    // );

    // nodesError = {
    //   ...nodesError,
    //   ...scheduledValidateTemp,
    // };

    if (isThirdPartyCampaigns && triggerType === NODE_TYPE.SCHEDULED) {
      yield call(handleValidateThirdPartyScheduled, args);
    } else if (triggerType === NODE_TYPE.SCHEDULED) {
      const scheduled = mainNodesBlast.get('scheduled');
      const { triggerType: scheduleType = '', frequencyTime = {} } = scheduled;

      if (scheduleType === 'specific_date') {
        yield call(validateScheduledTime, args, {
          payload: {
            startDate: frequencyTime.startDate,
            startTime: frequencyTime.startTime,
          },
        });
      }
    }
    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: nodesError,
        errorsMessage,
      }),
    );

    const newCampaigns = campaigns.map(campaign => {
      const indexCampaignError = Object.entries(nodesError || {}).findIndex(
        ([key, isError], _index) => key === campaign.actionId && isError,
      );

      return {
        ...campaign,
        isError: indexCampaignError !== -1,
      };
    });
    yield put(
      updateValue(`${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`, newCampaigns),
    );
    // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
    // yield put(
    //   updateValue(`${prefix}@@UPDATE_CAN_TRIGGER_VALIDATE_SENDTO@@`, true),
    // )
    // yield call(validateAllCampaignSendTo, args, {
    //   payload: { isValidateBeforeAction: true },
    // });
    if (triggerType === NODE_TYPE.SCHEDULED && !isDefaultRichMenu) {
      yield call(validateCampaignEmptyAudiencesSelected, args, {
        payload: { isValidateAll: true },
      });
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleValidateAll',
      data: err.stack,
    });
    const notification = {
      id: 'error',
      message: getTranslateMessage(
        TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        'Fail to action the journey, please try again',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 2000,
      timestamp: new PortalDate().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
    yield put(updateDone(prefix, false));
    console.log(err);
  }
}

function* validateScheduledTime(args, action) {
  const { startDate = {}, startTime = {} } = action.payload;
  const prefix = args.moduleConfig.key;

  const timeZone = getPortalTimeZone();
  const currentDate = new Date().toLocaleString('en-US', { timeZone });
  const currentTime = new Date(currentDate).getTime();
  const timeRangeValid = 1800000;

  const startDateTemp = new Date(startDate);
  const [year, month, day] = [
    startDateTemp.getFullYear(),
    startDateTemp.getMonth(),
    startDateTemp.getDate(),
  ];
  const startTimeTemp = new Date(startTime);
  const [hours, minutes, seconds] = [
    startTimeTemp.getHours(),
    startTimeTemp.getMinutes(),
    startTimeTemp.getSeconds(),
  ];
  const selectedTime = new Date(
    year,
    month,
    day,
    hours,
    minutes,
    seconds,
  ).getTime();

  const isValid = selectedTime - currentTime > timeRangeValid;

  yield put(
    updateValue(`${prefix}@@UPDATE_VALID_TIME_SCHEDULED@@`, { isValid }),
  );
}

function* handleValidateBeforeAction(args, action) {
  try {
    yield delay(800);
    const prefix = args.moduleConfig.key;
    const { type = '', data = {} } = action.payload;
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { design = '' } = reducer.main;
    const { nodes, isValidateFormatDateTime } = reducer.configure.main;
    const { activeNode } = reducer.mainReducer;
    const triggerType = getObjectPropSafely(
      () => reducer.configure.main.triggerType,
      null,
    );

    const targetCampaignId =
      type === 'DUPLICATE'
        ? getObjectPropSafely(() => data.node.campaignActive.actionId, '')
        : activeNode.nodeId;

    if (!targetCampaignId) {
      yield put(updateValue(`${prefix}@@LOADING@@`, false));
      return;
    }

    const infoNode = safeParse(nodes.get(targetCampaignId), Map({}));
    const reducerDetail = yield select(state =>
      selectStoryDetailDomain(state, {
        moduleConfig: { key: getPrefixDetail(prefix) },
      }),
    );
    const { main } = reducerDetail;
    const { isViewMode } = main;

    let nodeActiveError = {};
    const dataNode = infoNode.get('destination') || {};
    const { isFetchInfoData = false } = dataNode;

    if (!has(dataNode, 'isFetchInfoData')) {
      yield put(
        updateValue(`${prefix}@@UPDATE_ERROR_CAMPAIGN@@`, {
          campaignId: targetCampaignId,
          isError: true,
        }),
      );
      yield put(updateValue(`${prefix}@@LOADING@@`, false));
      return;
    }

    if (!isViewMode && isFetchInfoData) {
      nodeActiveError = validateNodeActive(
        { nodeId: targetCampaignId, type: NODE_TYPE.DESTINATION },
        infoNode,
        isValidateFormatDateTime,
      );
      // validate node schedule trigger
      const errorsMessage = validateMessage(
        { nodeId: targetCampaignId, type: NODE_TYPE.DESTINATION },
        infoNode,
        isValidateFormatDateTime,
      );
      yield put(
        updateValue(`${prefix}@@STORY_ERRORS@@`, {
          errors: nodeActiveError,
          errorsMessage,
        }),
      );
      // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
      // yield call(validateSendTo, args, {
      //   payload: { isValidateBeforeAction: true },
      // });
      // yield put(updateValue(`${prefix}@@UPDATE_CAN_VALIDATE@@`, true));
    }

    if (triggerType === NODE_TYPE.SCHEDULED && !isViewMode) {
      yield call(validateCampaignEmptyAudiencesSelected, args, {
        payload: { isValidateAll: false, nodeId: targetCampaignId },
      });
    }

    const newReducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const audiencesErrors = safeParse(
      newReducer.configure.main.errorsSelectedAudiences,
    );
    const newErrors = safeParse(newReducer.configure.main.errors, {});
    // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
    // const sendToError = safeParse(
    //   newReducer.configure.main.sendToError,
    //   Map({}),
    // );

    if (
      !newErrors[targetCampaignId] &&
      !audiencesErrors[targetCampaignId]
      // !sendToError.has(activeNode.nodeId)
    ) {
      if (type === 'ACTIVE') {
        const listNodes = safeParse(newReducer.mainReducer.listNodes, []);

        const newActiveNode = getActiveNode(data, listNodes);

        // Reset validate forecast and audience type serve for forecast before change active new campaign
        yield put(updateValue(`${prefix}@@RESET_VALIDATE_FORECAST@@`));
        yield put(
          updateValue(`${prefix}@@UPDATE_AUDIENCE_TYPE_BLAST_CAMPAIGN@@`, {
            itemTypeId: null,
          }),
        );
        yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, newActiveNode));
      } else if (type === 'DUPLICATE') {
        const { node = {}, campaign = {} } = data;
        const nodesTemp = safeParse(newReducer.configure.main.nodes, Map({}));
        const listNodesTemp = safeParse(newReducer.mainReducer.listNodes, []);
        const cataItem = listNodesTemp.find(
          item => item.catalog_dest_id === node.campaignActive.catalogId,
        );
        const nodeMap = nodesTemp.get(node.copyId);

        const {
          newNode,
          arrCampaignId,
          arrVariantId,
        } = handleCloneNodeCampaign(nodeMap);

        const channelCode = getChannelCodeById(cataItem.channel_code_id);

        let tempNewNode = {
          nodeId: node.newId,
          catalogCode: cataItem.catalog_code,
          channelCode,
          map: newNode,
          type: 'DESTINATION',
        };

        if (arrCampaignId.length > 0) {
          const resCampaign = yield call(JourneyServices.campaigns.getByIds, {
            data: {
              campaign_ids: arrCampaignId,
              columns: [
                'campaign_id',
                'campaign_name',
                'status',
                'campaign_setting',
                'custom_inputs',
              ],
            },
          });
          let campaignInfo = [];
          if (resCampaign.data.length > 0) {
            campaignInfo = resCampaign.data;
          }
          const resVariant = yield call(JourneyServices.variant.getByIds, {
            data: {
              variant_ids: arrVariantId,
              columns: [
                'variant_id',
                'variant_name',
                'content_setting',
                'status',
                'custom_inputs',
              ],
            },
          });
          let variantInfo = [];
          if (resVariant.data.length > 0) {
            variantInfo = resVariant.data;
          }
          tempNewNode = setCampaignVariantInfo(
            tempNewNode,
            variantInfo,
            campaignInfo,
          );
        }

        const nameNode = tempNewNode.map.get('destination').campaignName;
        const dataMap = tempNewNode.map.setIn(['destination'], {
          ...tempNewNode.map.get('destination'),
          campaignName: `${nameNode} - Copy`,
          isCopyNode: true,
          data: {
            ...tempNewNode.map.get('destination').data,
            campaignName: {
              ...tempNewNode.map.get('destination').data.campaignName,
              value: `${nameNode} - Copy`,
            },
          },
        });

        yield put(updateValue(`${prefix}@@DUPLICATE_CAMPAIGN@@`, campaign));
        yield put(
          updateValue(`${prefix}@@DUPLICATE_NODE@@`, {
            ...node,
            dataMap,
          }),
        );

        if (campaign?.data) {
          // build active node
          const selectedActiveNode = getActiveNode(
            campaign.data,
            listNodesTemp,
          );
          yield put(
            updateValue(`${prefix}@@ACTIVE_NODE@@`, selectedActiveNode),
          );
        }

        if (design === 'update') {
          yield put(updateValue(`${prefix}@@DATA_HEADER_NODE@@`));
        }
      }

      if (!data.isSameName) {
        yield put(
          updateValue(`${prefix}@@UPDATE_ERROR_CAMPAIGN@@`, {
            campaignId: targetCampaignId,
            isError: false,
          }),
        );
      }
      // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
      // if (design === 'create') {
      //   yield put(
      //     updateValue(`${prefix}@@UPDATE_CAN_TRIGGER_VALIDATE_SENDTO@@`, false),
      //   );
      // }
    } else {
      // yield put(
      //   updateValue(`${prefix}@@UPDATE_CAN_TRIGGER_VALIDATE_SENDTO@@`, true),
      // );
      yield put(
        updateValue(`${prefix}@@UPDATE_ERROR_CAMPAIGN@@`, {
          campaignId: targetCampaignId,
          isError: true,
        }),
      );
    }
    yield put(updateValue(`${prefix}@@LOADING@@`, false));
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleValidateBeforeAction',
      data: err.stack,
    });
    console.log(err);
  }
}

// Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
// function* validateAllCampaignSendTo(args, action) {
//   try {
//     const prefix = args.moduleConfig.key;
//     const reducer = yield select(state =>
//       selectDomainMainCreateWorkflow(state, args),
//     );
//     const { design = '' } = reducer.main;
//     const { nodes } = reducer.configure.main;
//     const { campaigns = [] } = reducer.mainReducer;
//     const sendToListError = [];

//     const newCampaigns = campaigns.map(campaign => {
//       const infoNode = safeParse(nodes.get(campaign.actionId), Map({}));

//       const errorSendTo = validateCampaignSenTo(
//         infoNode.get('targetAudience'),
//         campaign.actionId,
//         design,
//       );

//       sendToListError.push(errorSendTo);
//       return {
//         ...campaign,
//         isError:
//           (design !== 'create' &&
//             isEmpty(campaign.campaignId) &&
//             isEmpty(campaign.audiences)) ||
//           errorSendTo.isEmpty,
//       };
//     });

//     // console.log({ sendToListError, newCampaigns });
//     yield put(
//       updateValue(`${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`, newCampaigns),
//     );
//     yield put(updateValue(`${prefix}@@SEND_TO_LIST_ERROR@@`, sendToListError));
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
//       func: 'validateAllCampaignSendTo',
//       data: err.stack,
//     });
//   }
// }

function* validateCampaignEmptyAudiencesSelected(args, action = {}) {
  try {
    const { isValidateAll = false, nodeId = '' } = action.payload || {};
    const prefix = args.moduleConfig.key;

    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { campaigns = [] } = reducer.mainReducer;

    if (isValidateAll) {
      let errorsTemp = {};

      const campaignListErrors = campaigns.map(campaignItem => {
        const errorsInfo = validateCampaignEmptySelected(campaignItem);
        errorsTemp = {
          ...errorsTemp,
          ...errorsInfo,
        };

        return {
          ...campaignItem,
          isError:
            getObjectPropSafely(() => campaignItem.isError, false) ||
            Boolean(errorsInfo[campaignItem.actionId]),
        };
      });

      yield put(
        updateValue(`${prefix}@@EMPTY_AUDIENCES_SELECTED@@`, {
          errorsInfo: errorsTemp,
        }),
      );
      yield put(
        updateValue(
          `${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`,
          campaignListErrors,
        ),
      );
    } else {
      const campaignActive = campaigns.find(
        campaign => campaign.actionId === nodeId,
      );

      if (campaignActive) {
        const errorsInfo = validateCampaignEmptySelected(campaignActive);
        yield put(
          updateValue(`${prefix}@@EMPTY_AUDIENCES_SELECTED@@`, { errorsInfo }),
        );
        yield put(
          updateValue(`${prefix}@@UPDATE_ERROR_CAMPAIGN@@`, {
            campaignId: campaignActive.actionId,
            isError: errorsInfo[campaignActive.actionId],
          }),
        );
      }
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'validateCampaignEmptyAudiencesSelected',
      data: err.stack,
    });
  }
}

// Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
// function* validateSendTo(args, action) {
//   try {
//     // const { data = {}, isValidateBeforeAction = true } = action.payload;
//     const prefix = args.moduleConfig.key;
//     const reducer = yield select(state =>
//       selectDomainMainCreateWorkflow(state, args),
//     );
//     const { design = '' } = reducer.main;
//     const { nodes } = reducer.configure.main;
//     const { activeNode = {} } = reducer.mainReducer;

//     const infoNode = safeParse(nodes.get(activeNode.nodeId), Map({}));
//     const dataAudience = infoNode.get('targetAudience') || {};
//     const { currentData = {}, backup = {} } = dataAudience;
//     if (!isEmpty(currentData) || !isEmpty(backup)) {
//       console.log('insideee.dev - currentData :>', currentData, '-', backup);

//       const errorSendTo = validateCampaignSenTo(
//         dataAudience,
//         activeNode.nodeId,
//         design,
//       );

//       yield put(updateValue(`${prefix}@@SEND_TO_ERROR@@`, errorSendTo));
//       return errorSendTo;
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
//       func: 'validateSendTo',
//       data: err.stack,
//     });
//   }
// }

function* handleSaveChangeStatus(args, action) {
  const prefix = args.moduleConfig.key;
  const { value: status, nextStatus } = action.payload;
  // console.log('status, nextStatus', status, action.payload);
  let prevStatus = 8;
  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { main, configure } = reducer;
    const { design } = main;
    let isValidateAll = true;
    const isThirdParty = get(
      main,
      'activeRow.workflow_setting.metadata.isThirdParty',
      false,
    );

    // in third party mode, have to validate before activate
    if (isThirdParty && nextStatus === 1) {
      const { isValid = false } = yield call(
        handleValidateThirdPartyScheduled,
        args,
        {
          isValidateBeforeActivate: true,
        },
      ) || {};

      isValidateAll = isValid;
    }

    if (!isValidateAll) {
      const cachePrevStatus = main.activeRow.status;
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, cachePrevStatus));
      yield put(updateDone(`${prefix}@@NODE_STATUS@@`, { status: false }));

      return;
    }

    // const data = toAPI(main, configure, design);
    yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, nextStatus));
    const objectId = main.activeRow.activeId;
    prevStatus = main.activeRow.status;
    //
    const data = {
      story_ids: [objectId],
      status,
      owner_id: parseInt(getCurrentOwnerIds()),
    };

    // check quyền VIEW EVERYTHING và truyền param _owner_id tương ứng
    const hasRoleEditEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.UPDATE,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const res = yield call(JourneyServices.data.updateStatusOwner, {
      data,
      _owner_id: hasRoleEditEverything ? null : getCurrentAccessUserId(),
    });

    if (res.code === 200) {
      const notification = {
        id: 'success',
        message: design === 'create' ? 'Created' : 'Updates saved!',
        translateCode: TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
        timeout: 1000,
        timestamp: new PortalDate().getTime(),
        type: 'success',
      };
      yield put(addNotification(notification));

      // mỗi status có mỗi AvailabelStatus khác nhau, cần phải fetch và update lại khi thay đổi status
      yield call(
        getAndUpdateAvailabelStatusAndpermission,
        main.activeRow.activeId,
        args,
      );
      yield put(
        updateDone(`${prefix}@@NODE_STATUS@@`, {
          status: true,
          data: nextStatus,
        }),
      );
      // Tạm thời không dùng cho blast journey
      // yield call(
      //   handleValidateRuleActionDeleteNode,
      //   main.activeRow.channel_id,
      //   args,
      // );

      // yield put(
      //   updateValue(`${MODULE_CONFIG_DETAIL.key}@@STORY_STATUS@@`, {
      //     status: nextStatus,
      //   }),
      // );
      // yield delay(1000);
      if (design === 'create') {
        yield call(handleGoToList);
      }
    } else {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, prevStatus));
      yield put(updateDone(`${prefix}@@NODE_STATUS@@`, { status: false }));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleSaveChangeStatus',
      data: err.stack,
    });
    const notification = {
      id: 'error',
      message: getTranslateMessage(
        TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        'Fail to action the journey, please try again',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 2000,
      timestamp: new PortalDate().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
    yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, prevStatus));
    yield put(updateDone(`${prefix}@@NODE_STATUS@@`, { status: false }));
    // yield put(updateDone(MODULE_CONFIG_CREATE));
    console.log(err);
  }
}

function* handleGoToList() {
  try {
    const channelActive = yield select(makeSelectJourneyChannelActive());

    yield put(
      push(
        `${APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelActive.value
        }`,
      ),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleGoToList',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleUpdateValueLastTesting(args, action) {
  // console.log('action', action);
  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const reducerMain = reducer.main;
  const data = { ...reducerMain.dataRunTest, objectId: action };

  // console.log('reducerMain.dataRunTest', reducerMain.dataRunTest);
  if (action !== 1 && reducerMain.dataRunTest.isOpenModal) {
    // console.log('reducerMain', reducerMain)
    const res = yield call(
      JourneyServices.quicktest.getLastResultExperiment,
      data,
    );

    if (get(data, 'data.experimentId')) {
      data.data.experimentId = res.data[0].experimentId;
      yield call(JourneyServices.quicktest.updateExperiment, data);
    }
  }
}

// eslint-disable-next-line consistent-return
function* handleValidateReachedLimitRichMenu(args) {
  try {
    const prefix = get(args, ['moduleConfig', 'key']);
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { mainReducer = {} } = reducer;
    const { campaigns = [], activeNode = {}, listNodes = [] } = mainReducer;
    const catalogActiveId = get(activeNode, 'value', '');

    const params = {
      objectId: catalogActiveId,
      _owner_id: getCurrentOwnerId(),
    };
    const response = yield call(
      JourneyServices.data.getDestinationByCatalogIds,
      params,
    );
    const destinationInfo = get(response, 'data', []);

    if (!isArray(campaigns) || !isArray(destinationInfo)) return false;

    const campaignInfo = mapCampaignSettingToAPI({
      campaigns,
      destinationInfo,
    });

    const reachedLimitResponse = yield call(
      DestinationServices.verifies.reachedLimitRichMenu,
      {
        body: {
          campaignInfo,
        },
      },
    );
    let isValidRichMenu = true;

    if (
      reachedLimitResponse &&
      reachedLimitResponse.code === 200 &&
      isArray(reachedLimitResponse.data)
    ) {
      const responseData = get(reachedLimitResponse, 'data', []);
      const reachedLimitSerialized = responseData.reduce((acc, curr) => {
        const {
          destinationId = '',
          status = '',
          total = '',
          totalLimit = '',
        } = curr;
        acc[destinationId] = {
          total,
          totalLimit,
          reachedLimit: status,
        };
        return acc;
      }, {});
      if (isEmpty(reachedLimitSerialized)) return false;

      const reachedIdx = Object.values(reachedLimitSerialized).findIndex(
        item => item.reachedLimit,
      );
      isValidRichMenu = reachedIdx === -1;

      if (!isValidRichMenu) {
        const campaignErrors = campaigns.map((campaign, campaignIdx) => {
          let isError = false;
          const destinationId = get(campaign, 'sendAs', '');

          const campaignListAtIdx = campaignInfo.slice(0, campaignIdx + 1);
          const campaignsByDestId = campaignListAtIdx.filter(
            item => +item.destinationId === +destinationId,
          );
          const accumulateMenu = campaignsByDestId.reduce(
            (acc, current) => acc + current.totalMenu,
            0,
          );

          const { reachedLimit = false, total = 0, totalLimit = 0 } = get(
            reachedLimitSerialized,
            destinationId,
            {},
          );
          const numericOverLimit = total - totalLimit;

          // Error if campaign reached & total menus of the destination had over limit
          if (reachedLimit && accumulateMenu >= numericOverLimit) {
            isError = true;
          }

          return {
            ...campaign,
            isError,
            isReachedLimitMenu: isError,
          };
        });

        yield put(
          updateValue(
            `${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`,
            campaignErrors,
          ),
        );

        const firstCampaignError = campaignErrors.find(
          campaign => campaign.isError,
        );
        if (
          activeNode &&
          firstCampaignError &&
          isArray(listNodes) &&
          listNodes.length > 0
        ) {
          const newActiveNode = getActiveNode(firstCampaignError, listNodes);

          if (!isEqual(newActiveNode.nodeId, activeNode.nodeId)) {
            yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, newActiveNode));
          }
        }
      }
    }

    return isValidRichMenu;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleValidateReachedLimitRichMenu',
      data: {
        err: err.stack,
        args,
      },
    });
  }
}

function* handleSave(args, action) {
  const prefix = args.moduleConfig.key;

  const {
    copyId = '',
    saveAsTemplate = false,
    isShowDrawer,
    isJourneyV2 = false,
  } = action.payload;

  try {
    yield delay(800);
    yield call(handleValidateAll, args, action);

    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerDetail = yield select(state =>
      selectStoryDetailDomain(state, {
        moduleConfig: { key: getPrefixDetail(prefix, -13) },
      }),
    );
    const channelActive = yield select(makeSelectJourneyChannelActive());
    const isChannelLine = isEqual(channelActive.code, CHANNEL.LINE.code);
    const creatingJourneySetting = yield select(
      makeSelectCreatingJourneyInfo(),
    );
    const reducerDetailVariant = reducerDetail.main;
    const templateTypeCreating = get(
      creatingJourneySetting,
      'templateType',
      '',
    );

    const { main, configure, mainReducer } = reducer;
    const { campaigns, activeNode, trigger = {} } = mainReducer;
    const { design, isValidTimeScheduled, listNodes } = mainReducer;
    const { scheduledThirdParty = {} } = trigger;

    if (design === 'create') {
      // Turn off drawer icon closable
      yield put(
        updateValue(
          `${MODULE_CONFIG_COMMON.key}@@DRAWER_JOURNEY_INFO_CLOSABLE_STT`,
          false,
        ),
      );
    }

    // For case trigger is third party
    let isThirdPartyCampaigns =
      templateTypeCreating === THIRD_PARTY_CAMPAIGN.value;
    if (design !== 'create') {
      isThirdPartyCampaigns = trigger?.isThirdParty;
    }

    const invalidScheduledThirdParty =
      isThirdPartyCampaigns && !isEmpty(scheduledThirdParty?.error);

    // reset data third party if not in a third party campaign
    if (!isThirdPartyCampaigns) {
      set(trigger, 'scheduledThirdParty', {});
    }

    const {
      feKeyVariantId,
      versionId,
      rootJourneyDetail,
      activeRow,
    } = reducerDetailVariant;
    const {
      nodes,
      errors,
      errorsSelectedAudiences,
      // sendToError,
      // isValidateFormatDateTime
    } = configure.main;
    const catalogCode = get(activeNode, 'catalogCode', '');
    const isRichMenu =
      isChannelLine && catalogCode === CATALOG_CODE.LINE_RICH_MENU;
    let isValidRichMenu = true;

    if (isRichMenu) {
      isValidRichMenu = yield call(handleValidateReachedLimitRichMenu, args);
    }

    // const infoNode = safeParse(nodes.get(activeNode.nodeId), Map({}));

    // console.log(
    //   'errors',
    //   errors,
    //   'selected ->',
    //   errorsSelectedAudiences,
    //   isValidTimeScheduled,
    // );
    if (
      !isEmpty(errors) ||
      !isEmpty(errorsSelectedAudiences) ||
      !isValidTimeScheduled ||
      !isValidRichMenu ||
      invalidScheduledThirdParty
      // sendToError.size > 0
    ) {
      /** Update MapErrorAttributes */
      yield put(init(`${prefix}@@MAP_ERROR_ATTRIBUTES@@`));

      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      return true;
    }

    /** Update MapErrorAttributes */
    yield put(
      init(`${prefix}@@MAP_ERROR_ATTRIBUTES@@`, { showNotification: true }),
    );
    yield take(updateSettingPersonalizations().type);
    const updatedReducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const {
      errors: updatedErrors,
      errorsNotification,
    } = updatedReducer?.configure?.main;

    /** Error Promotion Pool tags */
    if (Object.keys(updatedErrors).length > 0 && errorsNotification) {
      const notification = {
        id: 'error',
        // translateCode: translations._JOURNEY_SAVE_VALIDATE_POOL,
        title: translate(
          translations._JOURNEY_DESTINATION_ERR_LINK_UNVAIL_TITLE,
          'Failed to action',
        ),
        message: errorsNotification,
        timeout: 5000,
        type: 'danger',
      };
      yield put(addNotification(notification));

      yield put(updateDone(prefix, false));
      return true;
    }

    if (saveAsTemplate) {
      yield put(updateValue(`${prefix}@@CAPTURING_THUMBNAIL@@`, true));
    }

    /* eslint-disable camelcase */
    const _owner_id =
      design === 'update'
        ? safeParse(mainReducer.activeRow, {}).c_user_id
        : getOwnerIdCreateRole(MENU_CODE.JOURNEY);

    let newCampaigns = cloneDeep(campaigns);
    if (!isEmpty(copyId)) {
      // Reset campaignId & Variant Id for case create copy
      newCampaigns = newCampaigns.map(campaignItem => ({
        ...campaignItem,
        campaignId: null,
        variantIds: [],
        variantInfo: [...campaignItem.variantInfo].map(item => ({
          ...item,
          variantId: null,
        })),
      }));
    }

    let data = toAPI({
      main,
      channelActive,
      configure,
      campaigns: !isEmpty(copyId) ? newCampaigns : campaigns,
      trigger,
      isBlastCampaign: true,
      isThirdPartyCampaigns,
      design,
      copyId,
      rootJourneyDetail,
      saveAsTemplate,
    });

    const isDuplicateCampaignId = validateNodesDuplicateCampaignId(data);
    if (isDuplicateCampaignId) {
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
        func: 'isDuplicateCampaignId',
        data: `JOURNEY isDuplicateCampaignId ${JSON.stringify(data)}`,
      });
      return true;
    }

    const dataVariantFeConfigId = yield call(generateFeConfigWithAPI, {
      ...data,
      copyId,
    });

    if (dataVariantFeConfigId.length === 0) {
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
        func: 'generateFeConfigWithAPI',
        data: `JOURNEY generateFeConfigWithAPI invalid params`,
      });
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      return true;
    }

    // call and update fe_config_id data
    // tại hàm này cần reset lại fe_config_id cho variantExtraData,
    updateDataVariantWithFeConfigID(data, dataVariantFeConfigId);
    const objectId = main.activeRow.activeId;
    //

    let flattenNodes = [];
    if (Array.isArray(listNodes)) {
      flattenNodes = listNodes.map(each => ({
        catalogCode: each.catalog_code || '',
        name: each.destination_name || '',
        value: each.catalog_dest_id || '',
        channelId: each.channel_id || '',
      }));
    }
    // dùng thêm 1 hàm cho việc remove data không cần thiết
    data = removeDataUnUseForAPI(data, flattenNodes, { saveAsTemplate });

    // NOTE: in channel Line & catalog line rich menu
    // have to set more is_default_rich_menu to check create mode of the line rich menu
    if (isRichMenu) {
      let isDefault = false;
      const richMenuMode = get(
        creatingJourneySetting,
        'data.template.dashboardMode',
        '',
      );

      if (design === 'update' || !isEmpty(copyId)) {
        let defaultRichMenuPath = 'activeRow.is_default_rich_menu';
        if (!isEmpty(copyId)) {
          defaultRichMenuPath = `activeRowClone.is_default_rich_menu`;
        }

        const numbericDefaultRichMenu = get(main, defaultRichMenuPath, 0);

        isDefault = +numbericDefaultRichMenu === 1;
      } else if (richMenuMode === MENU_TYPE.DEFAULT) {
        isDefault = true;
      }

      set(data, 'is_default_rich_menu', isDefault ? 1 : 0);

      // NOTE: Hard filter audiences when default line rich menu
      // Because in case default rich menu has only 1 campaign 1 variant and don't use audiences
      if (isDefault) {
        const audienceFilters = hardAudienceFilterForDefaultRichMenu();
        const firstFilter = audienceFilters.filters[0];
        set(data, 'workflow_setting.metadata.audiences', audienceFilters);
        set(
          data,
          'workflow_setting.branchs[0].branchs[0].metadata.filters',
          firstFilter,
        );
      }
    }

    const logicDesgin = parseInt(objectId) > 0 ? 'update' : 'create';
    // return;
    let res = {};
    // push startTime
    yield put(
      updateValue(`${prefix}@@STORY_START_TIME@@`, {
        startTime: data.workflow_setting.metadata.startTime,
      }),
    );

    if (saveAsTemplate) {
      let isCaptureDone = false;
      let capturedThumbnails = null;

      while (!isCaptureDone) {
        const curReducer = yield select(state =>
          selectDomainMainCreateWorkflow(state, args),
        );
        const { isCapturing, thumbnails } = curReducer.configure.main;
        isCaptureDone = !isCapturing;
        capturedThumbnails = thumbnails;

        yield delay(500);
      }

      yield call(handleSaveAsTemplate, {
        moduleConfig: args.moduleConfig,
        savedData: {
          ...data,
          thumbnails: capturedThumbnails || [],
        },
        design,
      });

      yield put(updateDone(prefix, false));
      return true;
    }

    data.properties = JSON.stringify(data.properties);

    // console.log('object', data, _owner_id);
    if (design === 'create' && logicDesgin === 'create') {
      res = yield call(JourneyServices.createV2_1, { data, _owner_id });
    } else {
      res = yield call(JourneyServices.updateV2_1, {
        objectId,
        data,
        _owner_id,
      });
    }

    if (res.code === 200) {
      const notification = {
        message: logicDesgin === 'create' ? 'Created' : 'Updates saved!',
        translateCode:
          logicDesgin === 'create'
            ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
            : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
        timeout: 1000,
        type: 'success',
      };
      yield put(addNotification(notification));
      // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
      // yield put(
      //   updateValue(`${prefix}@@UPDATE_CAN_TRIGGER_VALIDATE_SENDTO@@`, false),
      // );
      yield call(handleUpdateValueLastTesting, args, res.data[0]);
      // update activeId
      if (logicDesgin === 'create') {
        yield put(
          updateValue(`${prefix}@@STORY_ACTIVE_ID@@`, {
            activeId: res.data[0],
            accepted_actions: initCreateRolesAction(),
          }),
        );

        if (isJourneyV2) {
          const prefixCommon = MODULE_CONFIG_COMMON.key;
          yield put(
            updateValue(`${prefixCommon}@@DRAWER_JOURNEY_INFO`, {
              design: 'update',
              storyId: res.data[0],
              isSaveDone: true,
            }),
          );

          // link to detail
          const url = `${window.location.href}`.split('?')[0];

          const newUrl = `${url}?ui=detail-drawer&journeyId=${res.data[0]
            }&channelId=${channelActive.value}&tab=settings&design=update`;

          window.history.pushState({ path: newUrl }, '', newUrl);
        } else if (isShowDrawer) {
          yield put(
            push(
              `${APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelActive.value
              }?${KEY_PARAMS.UI}=${UI_DETAIL_DRAWER}&journeyId=${res.data[0]
              }&tab=settings&design=update`,
            ),
          );
        } else {
          yield put(
            push(
              `${APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelActive.value
              }/detail/${res.data[0]}/settings?design=update`,
            ),
          );
        }

        // http://localhost:5000/gen2/marketing-hub/33167/journeys/7/detail/167898/settings?design=update
        // call api get detail for update action_ace
      } else if (!versionId) {
        yield put(
          getDetail(`${PREFIX_DETAIL}`, {
            activeId: objectId,
            tab: 'settings',
            channelId: channelActive.value,
            isAfterSave: true,
          }),
        );
      }
      yield put(reset(`${prefix}@@RESET_STATUS_ERROR_CAMPAIGN@@`));
      yield put(updateDone(prefix, true));

      if (isThirdPartyCampaigns && design !== 'create') {
        const newScheduledStartTime = get(
          data,
          'workflow_setting.metadata.scheduledThirdParty.startTime',
        );

        // Force update start time in schedule 3rd party to use validate before activate in the next time
        yield put(
          updateValue(
            `${prefix}@@FORCE_UPDATE_START_TIME_ACTIVE_ROW_SCHEDULE_3RD_PARTY`,
            newScheduledStartTime,
          ),
        );
      }
      // call and validate lại node có được xóa không theo status ||
      // tạm thời chưa dùng đối với blast campaign vì check delete trong List Campaign components
      // yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);
    } else if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      yield put(updateValue(`${prefix}@@MAIN_ERROR@@`, res.codeMessage));
    } else if (res.codeMessage === '_NOTI_SAME_JOURNEY_CAMPAIGN_NAME') {
      const errorsDetails = res.data[0];
      errorsDetails.errors = getTranslateMessage(
        TRANSLATE_KEY._NOTIFICATION_NAMESAKE,
        'This name already existed',
      );
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      const listErrorId = [...(res.data || [])].map(item => item.actionId);
      const newCampaignTemp = cloneDeep(campaigns);
      const campaignListErrors = newCampaignTemp.map(campaign => ({
        ...campaign,
        isError: listErrorId.includes(campaign.actionId),
      }));
      const activeFirstCampaignError = newCampaignTemp.find(
        item => item.actionId === errorsDetails.actionId,
      );

      yield call(handleValidateBeforeAction, args, {
        payload: {
          type: 'ACTIVE',
          data: { ...(activeFirstCampaignError || {}), isSameName: true },
        },
      });
      const errorsDetails1 = { [errorsDetails.actionId]: true };
      yield put(
        updateValue(`${prefix}@@STORY_ERRORS@@`, { errors: errorsDetails1 }),
      );
      yield put(
        updateValue(`${prefix}@@STORY_DETAILS_BLAST_ERRORS@@`, {
          errorsList: res.data,
        }),
      );
      yield put(updateValue(`${prefix}@@API_ERRORS@@`, res.data));
      // Active first campaign has error
      yield put(
        updateValue(
          `${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`,
          campaignListErrors,
        ),
      );
    } else {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      // dung res.data for loop and render error
      yield put(updateValue(`${prefix}@@API_ERRORS@@`, res.data));
    }
    if (versionId) {
      yield put(
        push(
          `${APP.PREFIX
          }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelActive.value
          }/detail/${objectId}/settings?design=update`,
        ),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/saga.js',
      func: 'handleSave',
      data: err.stack,
    });
    const notification = {
      id: 'error',
      message: getTranslateMessage(
        TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        'Fail to action the journey, please try again',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 2000,
      timestamp: new PortalDate().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
    yield put(updateDone(prefix, false));
    console.log(err);
  } finally {
    // Turn off drawer icon closable
    yield put(
      updateValue(
        `${MODULE_CONFIG_COMMON.key}@@DRAWER_JOURNEY_INFO_CLOSABLE_STT`,
        true,
      ),
    );
  }
  // yield put(
  //   push(
  //     `${APP.PREFIX
  //     }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/7/detail/1107514/settings?design=update`,
  //   ),
  // );
}

function* handleMapErrorAttributesTreeNodes(args, action) {
  const { design, showNotification = false } = action.payload || {};
  if (design === 'create') return;

  const prefix = args.moduleConfig.key;

  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const { main, configure, mainReducer } = reducer;
  const { nodes, node, mainNodesBlast, cacheNodes } = configure.main;
  const { campaigns = [] } = mainReducer;

  const settings = yield select(makeSelectSettingPersonalizations());
  const { personalizationDataError } = settings || {};

  const { mapErrorAttributes, mapNodeError } = yield call(
    getPermissionTreeNodes,
    {
      treeNodes: [main?.activeRow?.workflow_setting],
      nodes,
      isBlastCampaign: true,
      nodeMap: node?.map,
    },
  );

  switch (true) {
    case Object.keys(mapErrorAttributes?.[PROMOTION_CODE]).length > 0: {
      yield put(
        updateValue(
          `${prefix}@@ERRORS_NOTIFICATION@@`,
          translate(
            translations?._JOURNEY_SAVE_VALIDATE_POOL_MESS,
            'Promotion Pools has been removed, please change to another available one.',
          ),
        ),
      );
      break;
    }

    case Object.keys(mapErrorAttributes?.[SHORT_LINK_V2]).length > 0: {
      yield put(
        updateValue(
          `${prefix}@@ERRORS_NOTIFICATION@@`,
          translate(
            translations._JOURNEY_DESTINATION_ERR_LINK_UNVAIL,
            'The associated link shortener is not available',
          ),
        ),
      );
      break;
    }

    default: {
      yield put(updateValue(`${prefix}@@ERRORS_NOTIFICATION@@`, ''));
      break;
    }
  }

  if (!isEqual(personalizationDataError, mapErrorAttributes)) {
    const payloads = {
      [PROMOTION_CODE]: {
        savedKey: PROMOTION_CODE,
        serviceFn: ObjectServices.suggestionMultilang.getList,
        dtoFn: dtoPromotionCode,
        payload: {
          data: {
            filters: { OR: [{ AND: [{}] }] },
            limit: 2000,
            page: 0,
            search: '',
            sort: 'asc',
            objectType: 'PROMOTION_POOL',
            retrieveCode: {
              isForceUpdateNew: true,
            },
          },
        },
        normalizeData: toUIDataPromotionCode,
      },
      [SHORT_LINK_V2]: {
        savedKey: SHORT_LINK_V2,
        serviceFn: LinkManagementServices.shortener.getPersonalizations,
        dtoFn: dtoShortLinkShortener,
        payload: {
          data: {
            filters: {
              OR: [
                {
                  AND: [
                    {
                      column: 'status',
                      data_type: 'number',
                      operator: 'matches',
                      value: [53],
                    },
                  ],
                },
              ],
            },
            properties: ['link_shortener_id', 'shortener_name', 'properties'],
            limit: 2000,
            page: 1,
            sort: 'ctime',
            sd: 'desc',
            search: '',
          },
        },
        normalizeData: toUIDataShortener,
        isForceUpdateNew: true,
      },
    };
    yield put(startPersonalizations(payloads));
  }

  /** Update MapNodeError Done */
  if (showNotification) {
    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: mapNodeError,
        errorsMessage: mapNodeError,
      }),
    );

    const newCampaigns = campaigns.map(campaign => {
      const isError = !!mapNodeError?.[campaign.actionId];

      return {
        ...campaign,
        isError,
      };
    });
    yield put(
      updateValue(`${prefix}@@UPDATE_CAMPAIGNS_LIST_DATA@@`, newCampaigns),
    );
  }

  /** Update MapErrorAttributes Done */
  yield put(
    updateSettingPersonalizations({
      metadata: {},
      entries: {
        personalizationDataError: mapErrorAttributes,
      },
    }),
  );
}

export { validateScheduledTime };

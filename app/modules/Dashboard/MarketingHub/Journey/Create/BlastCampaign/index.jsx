/* eslint-disable react/prop-types */
import React from 'react';
import ErrorBoundary from 'components/common/ErrorBoundary';
import Design from './Design';
import { StylesWrapper } from './styles';

const CreateBlastCampaign = props => {
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/index.jsx">
      <StylesWrapper>
        <Design
          design={props.design}
          moduleConfig={props.moduleConfig}
          isJourneyV2={props.isJourneyV2}
          isShowDrawer={props.isShowDrawer}
          channelId={props.channelId}
          isDrawerCreateCopy={props.isDrawerCreateCopy}
        />
      </StylesWrapper>
    </ErrorBoundary>
  );
};

export default CreateBlastCampaign;

/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React from 'react';

import ErrorBoundary from 'components/common/ErrorBoundary';
import { StyleWrapper } from './styles';
import Design from './Desgin';

export function CreateJourney(props) {
  const [width, setWidth] = React.useState(window.innerWidth * 0.7);
  // console.log('design', props.design);
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/index.jsx">
      <StyleWrapper>
        <Design
          width={width}
          design={props.design}
          moduleConfig={props.moduleConfig}
          isJourneyV2={props.isJourneyV2}
          isShowDrawer={props.isShowDrawer}
          isDrawerCreateCopy={props.isDrawerCreateCopy}
        />
      </StyleWrapper>
    </ErrorBoundary>
  );
}

export default CreateJourney;

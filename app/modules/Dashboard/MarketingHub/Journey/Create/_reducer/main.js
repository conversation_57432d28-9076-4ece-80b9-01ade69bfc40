/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-eval */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import ReduxTypes from '../../../../../../redux/constants';
// import { validate } from '../utils';
import { initCreateRolesAction } from '../utils.story.rules';
import { actionTypes } from './configure.flow';
import { initialStateMainCreate } from './utils';
import { get } from 'lodash';

const mainReducerFor = moduleConfig => {
  const PREFIX = moduleConfig.key;

  const mainReducer = (state = initialStateMainCreate(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          draft.itemTypeId = 0;
          draft.isLoading = true;
          draft.disabled = true;
          draft.actionDisabled = true;
          draft.firstActionDisabled = true;
          draft.isLoadingFlowChart = false;
          draft.isDoing = false;
          draft.activeRow = {};
          draft.isCollapse = false;
          draft.design = 'preview';
          draft.isExpand = false;
          draft.isCloseDataflow = false;
          draft.activeRowClone = {};
          draft.modalSaveAsTemplate = {
            step: 0,
            show: false,
            savedData: null,
          };
          draft.isBlastCampaign = false;
          draft.isInitSettingBlastCampaignDone = false;
          return;
        }

        case `${PREFIX}@@CREATE_JOURNEY${ReduxTypes.RESET}`: {
          return initialStateMainCreate();
        }

        case `${PREFIX}${ReduxTypes.INIT}`: {
          const {
            activeRow,
            design,
            paramDesignUrl,
            isBlastCampaign,
          } = action.payload;

          draft.design = design;
          if (paramDesignUrl && paramDesignUrl.length > 0) {
            draft.design = paramDesignUrl;
          }

          draft.disabled = true;
          draft.actionDisabled = true;
          draft.isBlastCampaign = isBlastCampaign;

          if (design === 'create') {
            draft.activeRow = {
              accepted_actions: initCreateRolesAction(),
            };
          } else {
            draft.activeRow = activeRow;

            if (+activeRow.status === 9) {
              draft.disabled = true;
              draft.actionDisabled = false;
            }
          }
          return;
        }
        case `${PREFIX}@@IS_LOADING@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = action.payload;
          return;
        }
        case `${PREFIX}@@DESIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.design = action.payload;
          return;
        }
        case `${PREFIX}@@STORY_ACTIVE_ID@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.activeRow = action.payload;
          draft.disabled = true;
          draft.actionDisabled = false;
          return;
        }
        case `${PREFIX}@@NODE_STATUS@@${ReduxTypes.UPDATE}`:
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.isDoing = true;
          return;
        }
        case `${PREFIX}@@NODE_STATUS@@${ReduxTypes.UPDATE_DONE}`: {
          // const { status, data } = action.payload;
          draft.isDoing = false;
          return;
        }
        case `${PREFIX}@@ACTIVE_ROW_STATUS@@${ReduxTypes.UPDATE_DONE}`: {
          const status = action.payload;
          // update activeRow status = data coming
          draft.activeRow.status = parseInt(status);
          return;
        }
        case `${PREFIX}@@AVAILABLE_STATUS&ACCEPTED_ACTIONS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // const status = action.payload;
          const { accepted_actions, available_status } = action.payload;
          // update activeRow status = data coming
          draft.activeRow.accepted_actions = accepted_actions;
          draft.activeRow.available_status = available_status;

          return;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          const status = action.payload;
          // console.log('status', status);
          if (status) {
            draft.disabled = true;
            draft.actionDisabled = false;
            draft.isDoing = false;
          } else {
            draft.disabled = false;
            draft.actionDisabled = true;
            draft.isDoing = false;
          }

          return;
        }

        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_NODE${ReduxTypes.UPDATE_VALUE}`:
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_CONDITION_YES_NODE${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_NODE_TRIGGER${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_BASIC_NODE${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@FLOW@@${actionTypes.REMOVE_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@STORY_NAME_BRANCH@@${ReduxTypes.UPDATE_VALUE}`:
        case `${PREFIX}@@DATA_HEADER_NODE@@${ReduxTypes.UPDATE_VALUE}`:
        case `${PREFIX}@@CHANGE_DATA_SCHEDULED_THIRD_PARTY${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@DATA_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const {
            isInitSettingBlastCampaignDone,
            isBlastCampaign,
            design,
          } = state;

          if (
            isBlastCampaign &&
            design !== 'create' &&
            !isInitSettingBlastCampaignDone
          ) {
            return;
          }

          draft.disabled = false;
          draft.actionDisabled = true;
          return;
        }

        case `${PREFIX}@@VALIDATION_BUTTON_ACTION@@${ReduxTypes.UPDATE_DONE}`: {
          draft.actionDisabled = true;
          // console.log('FLATTEN_NODE', action.payload.length);
          if (action.payload.length > 0) {
            draft.disabled = false;
          } else {
            draft.disabled = true;
          }

          return;
        }

        case `${PREFIX}@@TREE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.treeNodes = action.payload;
          return;
        }
        case `${PREFIX}@@CREATE_COPY_DONE@@${ReduxTypes.UPDATE_DONE}`: {
          draft.disabled = false;
          draft.actionDisabled = true;
          return;
        }
        case `${PREFIX}@@DISABLED_HEADER_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const isDisableActive = action.payload;
          draft.disabled = true;
          draft.actionDisabled = isDisableActive;
          draft.firstActionDisabled = false;
          return;
        }
        case `${PREFIX}@@UPDATE_ACTION_BUTTON@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.firstActionDisabled = false;
          return;
        }
        case `${PREFIX}@@DATA_RUN_TEST@@${ReduxTypes.UPDATE}`: {
          const data = { ...action.payload };
          if (draft.activeRow && draft.activeRow.story_id) {
            data.objectId = draft.activeRow.story_id;
          }
          draft.dataRunTest = data;
          return;
        }
        case `${PREFIX}@@IS_EXPAND@@${ReduxTypes.UPDATE_VALUE}`: {
          const { isExpand } = action.payload;
          draft.isExpand = isExpand;
          return;
        }
        case `${PREFIX}@@IS_CLOSE_DATAFLOW@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isCloseDataflow = action.payload;
          // draft.isExpand = false;
          return;
        }
        case `${PREFIX}@@COPPY_BRANCH_DONE@@${ReduxTypes.UPDATE_DONE}`: {
          // console.log('first')
          draft.disabled = false;
          draft.isLoadingFlowChart = false;
          return;
        }
        case `${PREFIX}@@IS_LOADING_COPPY_BRANCH_DONE@@${
          ReduxTypes.UPDATE_DONE
        }`: {
          // console.log('first')
          draft.isLoadingFlowChart = true;
          return;
        }
        case `${PREFIX}@@DISABLED_SAVE_CHANGE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.disabled = action.payload;
          return;
        }
        case `${PREFIX}@@ACTIVE_ROW_CLONE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { activeRowClone } = action.payload;
          draft.activeRowClone = activeRowClone;
          return;
        }
        case `${PREFIX}@@MODAL_CONFIRM_ACTIVE${ReduxTypes.UPDATE_VALUE}`: {
          draft.modalConfirmActive = {
            ...state.modalConfirmActive,
            ...action.payload.value,
          };
          return;
        }
        case `${PREFIX}@@MODAL_CONFIRM_SAVE_CHANGE${ReduxTypes.UPDATE_VALUE}`: {
          draft.modalConfirmSaveChanged = {
            ...state.modalConfirmSaveChanged,
            ...action.payload.value,

            ...(get(action, 'payload.value.show') === false &&
              initialStateMainCreate().modalConfirmSaveChanged),
          };
          return;
        }
        case `${PREFIX}@@MODAL_SAVE_AS_TEMPLATE${ReduxTypes.UPDATE_VALUE}`: {
          draft.modalSaveAsTemplate = {
            ...state.modalSaveAsTemplate,
            ...action.payload.value,
          };

          return;
        }
        case `${PREFIX}@@UPDATE_MAIN${ReduxTypes.UPDATE_VALUE}`: {
          const { updated = {} } = action.payload;

          return { ...state, ...updated };
        }

        case `${PREFIX}@@INIT_SETTING_BLASTCAMPAIGN_DONE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.isInitSettingBlastCampaignDone = action.payload;
          return;
        }
        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default mainReducerFor;

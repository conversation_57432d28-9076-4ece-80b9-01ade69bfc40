/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable consistent-return */
import {
  all,
  call,
  delay,
  put,
  select,
  takeLatest,
  take,
  race,
} from 'redux-saga/effects';
// import { push } from 'react-router-redux';
import { Map } from 'immutable';
import queryString from 'query-string';
// import { delay } from 'redux-saga';
// import delay from '@redux-saga/delay-p';
import { push, replace } from 'react-router-redux';

import cloneDeep from 'lodash/cloneDeep';
import flatten from 'lodash/flatten';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import mapValues from 'lodash/mapValues';
import uniqBy from 'lodash/uniqBy';
import values from 'lodash/values';
import { isEqual } from 'lodash';

import JourneyServices from 'services/Journey';
import MetaDataServices from 'services/MetaData';
import ObjectServices from 'services/Object';
import LinkManagementServices from 'services/LinkManagement';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../Detail/config';

import {
  addNotification,
  dashboardSetOwnerIdFromData,
  getDetail,
  getList,
  getListDone,
  init,
  update,
  updateDone,
  updateValue,
} from '../../../../../redux/actions';
import {
  makeSelectDashboardOwnerId,
  makeSelectSettingPersonalizations,
} from '../../../selector';
import {
  MAP_TYPE_RESUME,
  NodeModifiedHelper,
  getInitValidatedFieldFromNodes,
  getNodeId,
  mapWorkFlowSettingWithChannelId,
  setCampaignVariantInfo,
  toAPI,
  handleUploadWorkflowThumbs,
  MAP_SELECTOR,
} from './utils';

import APP from '../../../../../appConfig';
import { SEGMENT_STATUS } from '../../../../../components/common/constant';
import { toConditionAPI } from '../../../../../containers/Filters/utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { JOURNEY_STATUS } from '../../../../../services/Abstract.data';
import { safeParse } from '../../../../../utils/common';
import { PortalDate } from '../../../../../utils/date';
import {
  getCurrentAccessUserId,
  getCurrentOwnerIds,
  getPortalId,
} from '../../../../../utils/web/cookie';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  MENU_CODE,
  checkingRoleScope,
  getOwnerIdCreateRole,
} from '../../../../../utils/web/permission';
import { getUntitledCopyName } from '../../../../../utils/web/properties';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import {
  handleStoryFetchListNode,
  handleFetchJourneyDetail,
  getAndUpdateAvailabelStatusAndpermission,
  generateFeConfigWithAPI,
} from './saga.fetch';
import {
  handleReplaceDuplicateVariantKeys,
  mergeIsErrorNodes,
  validateAllJourney,
  validateMessage,
  validateNodeActive,
  validateNodesDuplicateCampaignId,
  validateSegmentsInNodes,
} from './utils.validate';
import {
  handleValidateRuleActionDeleteNode,
  resetTriggetPerformEvent,
  resetTriggetTargetAudience,
  handleUpdateDataSplitNodeParent,
  rebuildBranchSplitNodeName,
  handleUpdatePercentSplitBranchNode,
} from './saga.flow';
import {
  removeDataUnUseForAPI,
  serializeFlattenNodesUI,
  updateDataVariantWithFeConfigID,
} from './utils.map';
import { safeParseArray } from '../../../../../utils/web/utils';
import { getPrefixDetail } from '../Detail/VersionHistory2/Detail/utils';
import selectStoryDetailDomain from '../Detail/selectors';
import { makeSelectJourneyChannelActive } from '../selectors';
import { NODE_TYPE } from './Content/Nodes/constant';
import { actionTypes } from './_reducer/configure.flow';

import {
  selectDomainMainCreateWorkflow,
  selectFlattenNodes,
  selectIsExistNodeData,
} from './selectors';
import { initCreateRolesAction } from './utils.story.rules';
import { getChangedFromWorkflowSetting } from './utils.version';
import { handleGetJTSettings, handleSaveAsTemplate } from './saga.template';
import { getChannelCodeById, isDetailDrawer } from '../utils';
import { CHANNEL, UI_DETAIL_DRAWER } from '../constant';
import { CATALOG_CODES } from '../../Destination/CreateV2/Design/Templates/constants';
import { getPermissionTreeNodes } from './utils.checkPermission';

import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../config';
import { KEY_PARAMS } from '../Main/constants';
import {
  PROMOTION_CODE,
  SHORT_LINK_V2,
} from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';
import { translate, translations } from '@antscorp/antsomi-locales';
import {
  dtoPromotionCode,
  dtoShortLinkShortener,
} from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';
import {
  toUIDataPromotionCode,
  toUIDataShortener,
} from '../../../../../components/common/UIEditorPersonalization/utils';
import {
  startPersonalizations,
  updateSettingPersonalizations,
} from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';
import { shouldResetWFRFiltersAfterCreateNode } from './utils.flow';
import { DEFAULT_RULES } from '../../../../../components/common/BaseCondition/BaseCondition';
import { selectWFRNodesByNodeDestId } from './Content/Nodes/WFRNode/selectors';
import { updateNodeData } from './actions';

const PATH = 'app/modules/Dashboard/MarketingHub/Journey/Create/saga';
const PREFIX_DETAIL = MODULE_CONFIG_DETAIL.key;
const INVALID_SEG_S = [SEGMENT_STATUS.ARCHIVE, SEGMENT_STATUS.REMOVE];
const INVALID_ATTR_S = INVALID_SEG_S;
const INVALID_EVENT_ATTR_S = INVALID_SEG_S;

export default function* workerCustomerSaga(args) {
  const { moduleConfig } = args;
  const { key: prefix } = moduleConfig;

  yield takeLatest(`${prefix}${ReduxTypes.INIT}`, handleInit, args);
  yield takeLatest(`${prefix}${ReduxTypes.UPDATE}`, handleSave, args);
  yield takeLatest(
    `${prefix}@@NODE_STATUS@@${ReduxTypes.UPDATE}`,
    handleSaveChangeStatus,
    args,
  );
  yield takeLatest(
    `${prefix}@@VALIDATION_BUTTON_ACTION@@${ReduxTypes.UPDATE}`,
    handleValidateButtonAction,
    args,
  );

  yield takeLatest(
    `${prefix}@@NODE_VALIDATION@@${ReduxTypes.UPDATE_VALUE}`,
    handleOnValidate,
    args,
  );
  yield takeLatest(
    `${prefix}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
    args,
  );
  yield takeLatest(
    `${prefix}@@BEFORE_ACTIVE_NODE@@${ReduxTypes.UPDATE_VALUE}`,
    handleBeforeActiveNode,
    args,
  );
  yield takeLatest(
    `${prefix}@@RESET_TRIGGER_TARGET_AUDIENCE@@${ReduxTypes.UPDATE_VALUE}`,
    resetTriggetTargetAudience,
    args,
  );
  yield takeLatest(
    `${prefix}@@RESET_TRIGGER_PERFORM_EVENT@@${ReduxTypes.UPDATE_VALUE}`,
    resetTriggetPerformEvent,
    args,
  );
  yield takeLatest(
    `${prefix}@@SPLIT_NODE_PARENT@@${ReduxTypes.UPDATE_VALUE}`,
    handleUpdateDataSplitNodeParent,
    args,
  );

  yield takeLatest(
    `${prefix}@@SPLIT_NODE_DATA@@${ReduxTypes.UPDATE_VALUE}`,
    handleUpdatePercentSplitBranchNode,
    args,
  );

  yield takeLatest(
    `${prefix}@@FLOW_UPDATE@@${ReduxTypes.INIT}`,
    handleUpdatePercentSplitBranchNode,
    args,
  );

  // push dispatch for refresh campaignId + variantId in node destination
  yield takeLatest(
    `GET_DETAIL_JOURNEY_AFTER_SAVE_CHANGE${ReduxTypes.GET_DETAIL_DONE}`,
    handleRefreshDataAfterSaveChangeSuccess,
    args,
  );
  yield takeLatest(
    `${prefix}@@GET_CAMPAIGN_VARIANT_COPPY_BRANCH@@${ReduxTypes.GET_DETAIL}`,
    handleGetByCampainVariant,
    args,
  );
  yield takeLatest(
    `${prefix}@@ACTIVE_NODE@@${ReduxTypes.UPDATE_VALUE}`,
    handleUrlOnActiveNode,
  );
  yield takeLatest(
    `${prefix}@@RESUME_TRIGGER${ReduxTypes.UPDATE}`,
    handleResumeTrigger,
    args,
  );

  yield takeLatest(
    `${prefix}@@DATA_NODE@@${ReduxTypes.UPDATE_VALUE}`,
    handleUpdateFlattenNodes,
    args,
  );

  yield takeLatest(
    `${prefix}@@FLOW_UPDATE@@${ReduxTypes.INIT}`,
    handleMapErrorAttributesTreeNodes,
    { ...args, isInit: true },
  );

  yield takeLatest(
    `${prefix}@@MAP_ERROR_ATTRIBUTES@@${ReduxTypes.INIT}`,
    handleMapErrorAttributesTreeNodes,
    args,
  );
}

function* handleInit(args, action) {
  // console.log(action.payload);
  const prefix = args.moduleConfig.key;

  let listNode = yield call(handleStoryFetchListNode);

  yield put(getListDone(`${prefix}@@NODE@@`, listNode));

  const {
    design,
    activeRow,
    channelActive = {},
    isDisplayDiagramBlastCampaign = false,
    isShowDrawer = false,
  } = action.payload;
  const channelCode = getChannelCodeById(channelActive.value);

  // NOTE: in normal of the journey of the Line channel or Journey orchestration, need to remove line rich menu (not support yet)
  if (
    [CHANNEL.LINE.code, CHANNEL.JOURNEY_ORCHESTRATION.code].includes(
      channelCode,
    ) &&
    Array.isArray(listNode) &&
    !isDisplayDiagramBlastCampaign
  ) {
    listNode = listNode.map(node => {
      const { groupId = '', nodes = [] } = node;

      if (groupId === 'destination' && Array.isArray(nodes)) {
        return {
          ...node,
          nodes: nodes.filter(
            nodeItem => nodeItem.catalogCode !== CATALOG_CODES.LINE_RICH_MENU,
          ),
        };
      }

      return node;
    });
  }

  yield put(getListDone(`${prefix}@@NODE@@`, listNode));

  if (design !== 'create') {
    const {
      configure: { main: reducer },
    } = yield select(state => selectDomainMainCreateWorkflow(state, args));

    const data = serializeFlattenNodesUI({
      branchs: activeRow.workflow_setting,
      mapNode: reducer.node.map,
      role: 'INIT',
      customInput: activeRow.custom_inputs,
      isDisplayDiagramBlastCampaign,
    });

    yield put(init(`${prefix}@@FLOW_UPDATE@@`, data));
    yield put(init(`${prefix}@@DATA_TRIGGER@@`, data.nodeTrigger));

    if (!isDisplayDiagramBlastCampaign) {
      yield call(handleValidateCatchedNodesBeforeFlowchartShow, args);
    }

    // call and validate lại node có được xóa không theo status
    // Không cho xóa node đầu tiên với single channel
    yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);
  } else {
    const ownerId = yield select(makeSelectDashboardOwnerId());
    // console.log('ownerId', ownerId, getCurrentAccessUserId());

    if (Number.isNaN(Number(ownerId.value))) {
      yield put(dashboardSetOwnerIdFromData(getCurrentAccessUserId()));
    }

    const { copyId } = action.payload;

    const { settings: templateSettings } = yield call(handleGetJTSettings, {
      moduleConfig: args.moduleConfig,
    });

    if (copyId !== undefined || templateSettings) {
      yield call(
        handleCreateCopy,
        {
          copyId,
          journeyDetail: templateSettings,
          isShowDrawer,
        },
        args,
      );

      yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);
    } else if (channelActive.value !== 8) {
      // console.log('channelActive', channelActive.value);
      yield call(handelInitSingleChannel, channelActive.value, args);
      // call and validate lại node có được xóa không theo status
      // Không cho xóa node đầu tiên với single channel
      yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);
    }
  }

  yield put(updateValue(`${prefix}@@IS_LOADING@@`, false));

  const queryParams = queryString.parse(window.location.search);
  const activeNodeId = queryParams['node-id'];

  if (activeNodeId) {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );

    const { configure } = reducer;
    const { flattenNodes } = configure.main;

    const activeNode = flattenNodes.find(node => node.nodeId === activeNodeId);

    if (activeNode) {
      yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, activeNode));
    }
  }
}

function* handleUrlOnActiveNode(action) {
  if (!action.payload) {
    return;
  }

  const searchParams = new URLSearchParams(window.location.search);

  if (isDetailDrawer() && searchParams.get('tab') === 'settings') {
    const { nodeId } = action.payload;

    if (nodeId) {
      searchParams.set('node-id', nodeId);

      // prettier-ignore
      // const newUrl = `${window.location.href.split('?')[0]}?${searchParams.toString()}`;

      yield put(push({ search: searchParams.toString() }));

      // window.history.pushState({ path: newUrl }, '', newUrl);
    }
  }

  if (
    /\/marketing-hub\/journeys\/\d+\/detail\/\d+\/settings\?/g.test(
      window.location.href,
    )
  ) {
    const { nodeId } = action.payload;

    if (nodeId) {
      const queryParams = queryString.parse(window.location.search);
      queryParams['node-id'] = nodeId;

      // prettier-ignore
      const newUrl = `${window.location.href.split('?')[0]}?${queryString.stringify(queryParams)}`;

      window.history.pushState({ path: newUrl }, '', newUrl);
    }
  }
}

function* handleValidateButtonAction(args) {
  const prefix = args.moduleConfig.key;
  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { flattenNodes } = reducer.configure.flow;
    yield put(init(`${prefix}@@VALIDATION_BUTTON_ACTION@@`, flattenNodes));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleValidateButtonAction',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log(err);
  }
}

function* handleOnValidate(args, action) {
  const { moduleConfig } = args;

  const prefix = moduleConfig.key;

  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );

  const {
    activeNode,
    nodes,
    isValidateFormatDateTime,
  } = reducer.configure.main;

  const infoNode = safeParse(nodes.get(activeNode.nodeId), Map({}));

  const reducerDetail = yield select(state =>
    selectStoryDetailDomain(state, {
      moduleConfig: { key: getPrefixDetail(prefix) },
    }),
  );

  const { main } = reducerDetail;

  const { isViewMode } = main;

  let nodeActiveError = {};

  if (!isViewMode) {
    nodeActiveError = validateNodeActive(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );

    // validate node schedule trigger
    const errorsMessage = validateMessage(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );

    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: nodeActiveError,
        errorsMessage,
      }),
    );
  }

  if (!nodeActiveError[activeNode.nodeId]) {
    const { type, payload } = action.payload;

    if (type === 'ON_ACTIVE_NODE') {
      yield put(updateValue(`${prefix}@@BEFORE_ACTIVE_NODE@@`, payload));
      return;
    }

    yield put(updateValue(`${prefix}@@FLOW@@${type}`, payload));

    if (type === actionTypes.CREATE_NODE || type === 'CONFIRM_CREATE_NODE') {
      yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, payload.node));

      yield call(handleAfterCreateNode, {
        moduleConfig,
        createInfo: { payload, type },
      });
    }
  }
}

function* handleAfterCreateNode(args) {
  const { createInfo, moduleConfig } = args;

  try {
    const pattern = ({ type }) => {
      const actionType = `${moduleConfig.key}@@FLATTEN_NODE@@${
        ReduxTypes.UPDATE_VALUE
      }`;

      return type === actionType;
    };

    // Wait callback set updated flattenNodes from flow chart component after create new nodes.
    const [timeout] = yield race([delay(5_000), take(pattern)]);

    if (timeout) return;

    const createdNodeId = createInfo.payload?.node.nodeId;

    const flattenNodes = yield select(selectFlattenNodes(args));

    switch (true) {
      case shouldResetWFRFiltersAfterCreateNode({
        flattenNodes,
        createdNodeId,
      }): {
        const wfrNodes = yield select(
          selectWFRNodesByNodeDestId(
            {
              nodeId: createdNodeId,
            },
            args,
          ),
        );

        for (const { nodeId } of wfrNodes) {
          const isNodeDataInitialized = yield select(
            selectIsExistNodeData(nodeId, args),
          );

          if (isNodeDataInitialized) {
            yield put(
              updateNodeData({
                nodeId,
                name: 'filters',
                data: DEFAULT_RULES,
                prefix: moduleConfig.key,
              }),
            );
          }
        }

        break;
      }
      default:
        break;
    }
  } catch (error) {
    addMessageToQueue({
      args,
      func: 'handleAfterCreateNode',
      path: PATH,
    });
  }
}

function* handleBeforeActiveNode(args, action) {
  const prefix = args.moduleConfig.key;
  // const reducer = yield select(makeSelectConfigureCreateWorkflow());
  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const {
    activeNode,
    nodes,
    isValidateFormatDateTime,
  } = reducer.configure.main;

  const infoNode = safeParse(nodes.get(activeNode.nodeId), Map({}));
  const reducerDetail = yield select(state =>
    selectStoryDetailDomain(state, {
      moduleConfig: { key: getPrefixDetail(prefix) },
    }),
  );
  const { main } = reducerDetail;
  const { isViewMode } = main;

  let nodeActiveError = {};
  if (!isViewMode) {
    nodeActiveError = validateNodeActive(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );
    // validate node schedule trigger
    const errorsMessage = validateMessage(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );
    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: nodeActiveError,
        errorsMessage,
      }),
    );
  }

  if (action.payload.type === NODE_TYPE.SPLIT_BRANCH) {
    yield call(rebuildBranchSplitNodeName, action.payload, args);
  }

  if (!nodeActiveError[activeNode.nodeId]) {
    yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, action.payload));
  }
}

function* handleGoToList() {
  try {
    const channelActive = yield select(makeSelectJourneyChannelActive());

    yield put(
      push(
        `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
          channelActive.value
        }`,
      ),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleGoToList',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}

function* handleConfirmSaveChanged(args) {
  const prefix = args.moduleConfig.key;

  const { originalWorkflowSetting, workflowSetting } = args;

  const changedInfos = [];

  const { getUpdatedInfoInHtmlContent } = NodeModifiedHelper.getInstance();

  const onNodeChangedCb = ({ node, rootNode }) => {
    const { html } = getUpdatedInfoInHtmlContent({
      node,
      rootNode,
    });

    if (html.length) {
      changedInfos.push({
        nodeId: node.actionId,
        label: node.label,
        htmlRenderdContents: html,
      });
    }
  };

  getChangedFromWorkflowSetting({
    originalWorkflowSetting,
    workflowSetting,
    callbackFns: { onNodeChanged: onNodeChangedCb },
  });

  if (changedInfos.length) {
    yield put(
      updateValue(`${prefix}@@MODAL_CONFIRM_SAVE_CHANGE`, {
        value: {
          show: true,
          changedInfos,
        },
      }),
    );

    const actConfirmPattern = action => {
      const type = `${prefix}@@MODAL_CONFIRM_SAVE_CHANGE${
        ReduxTypes.UPDATE_VALUE
      }`;
      const show = get(action, 'payload.value.show');

      return action.type === type && typeof show === 'boolean';
    };

    const action = yield take(actConfirmPattern);

    return action.payload.confirmSaved;
  }

  return true;
}

function* handleSave(args, action) {
  const prefix = args.moduleConfig.key;

  const {
    copyId = '',
    saveAsTemplate = false,
    isShowDrawer = false,
    isJourneyV2 = false,
  } = action.payload;
  try {
    yield delay(500);

    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerDetail = yield select(state =>
      selectStoryDetailDomain(state, {
        moduleConfig: { key: getPrefixDetail(prefix) },
      }),
    );
    const channelActive = yield select(makeSelectJourneyChannelActive());
    const reducerDetailVariant = reducerDetail.main;

    const { main, configure } = reducer;
    const { design } = main;
    const {
      feKeyVariantId,
      versionId,
      rootJourneyDetail,
    } = reducerDetailVariant;

    if (design === 'create') {
      // Turn off drawer icon closable
      yield put(
        updateValue(
          `${MODULE_CONFIG_COMMON.key}@@DRAWER_JOURNEY_INFO_CLOSABLE_STT`,
          false,
        ),
      );
    }

    const {
      activeNode,
      nodes,
      isValidateFormatDateTime,
      flattenNodes,
    } = configure.main;

    const infoNode = safeParse(nodes.get(activeNode.nodeId), Map({}));

    const activeNodeError = validateNodeActive(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );

    const errorsMessage = validateMessage(
      activeNode,
      infoNode,
      isValidateFormatDateTime,
    );

    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: activeNodeError,
        errorsMessage,
      }),
    );

    if (activeNodeError[activeNode.nodeId]) {
      /** Update MapErrorAttributes */
      yield put(init(`${prefix}@@MAP_ERROR_ATTRIBUTES@@`));

      yield put(updateDone(prefix, false));
      return true;
    }

    /** Update MapErrorAttributes */
    yield put(
      init(`${prefix}@@MAP_ERROR_ATTRIBUTES@@`, { showNotification: true }),
    );
    yield take(updateSettingPersonalizations().type);
    const updatedReducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const {
      errors: updatedErrors,
      errorsNotification,
    } = updatedReducer?.configure?.main;

    /** Error Promotion Pool tags */
    if (Object.keys(updatedErrors).length > 0 && errorsNotification) {
      const notification = {
        id: 'error',
        // translateCode: translations._JOURNEY_SAVE_VALIDATE_POOL,
        title: translate(
          translations._JOURNEY_DESTINATION_ERR_LINK_UNVAIL_TITLE,
          'Failed to action',
        ),
        message: errorsNotification,
        timeout: 5000,
        type: 'danger',
      };
      yield put(addNotification(notification));

      yield put(updateDone(prefix, false));
      return true;
    }

    if (saveAsTemplate) {
      yield put(updateValue(`${prefix}@@CAPTURING_THUMBNAIL@@`, true));
    }

    /* eslint-disable camelcase */
    const _owner_id =
      design === 'update'
        ? safeParse(main.activeRow, {}).c_user_id
        : getOwnerIdCreateRole(MENU_CODE.JOURNEY);

    let versionMT;
    if (
      +channelActive.value === 2 ||
      channelActive.code === 'web_personalization'
    ) {
      const resVersionMT = yield call(JourneyServices.mediaTemplate.getVersion);
      versionMT = get(resVersionMT, 'data.version', '');
    }

    // console.log({ rootJourneyDetail, reducerDetailVariant });
    const toAPIInfo = toAPI({
      main,
      design,
      configure,
      channelActive,
      feKeyVariantId,
      versionMT,
      versionId,
      rootJourneyDetail,
      copyId,
      withWorkflowInfo: true,
    });

    const { originalWorkflowSetting, workflowSetting, ...restInfo } = toAPIInfo;

    let data = restInfo;
    const objectId = main.activeRow.activeId;
    const logicDesgin = parseInt(objectId) > 0 ? 'update' : 'create';
    const isCreate = design === 'create' && logicDesgin === 'create';

    if (!saveAsTemplate) {
      const confirmSaveChanged = yield call(handleConfirmSaveChanged, {
        originalWorkflowSetting,
        workflowSetting,
        ...args,
      });

      if (!confirmSaveChanged) {
        yield put(updateDone(prefix, false));
        return true;
      }
    }

    const { isValidate, error } = yield call(validateAllJourney, data);

    if (!isValidate) {
      const notification = {
        id: 'error',
        message:
          error?.message ||
          getTranslateMessage(
            TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
            'Fail to action the journey, please try again',
          ),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      if (error && error.actionId) {
        yield put(
          updateValue(`${prefix}@@STORY_ERRORS@@`, {
            errors: { [error.actionId]: true },
          }),
        );
      }

      if (error && error.detail) {
        yield put(
          updateValue(`${prefix}@@STORY_DETAILS_ERRORS@@`, error.detail),
        );
      }

      return true;
    }

    const isDuplicateCampaignId = validateNodesDuplicateCampaignId(data);

    if (isDuplicateCampaignId) {
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      addMessageToQueue({
        path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
        func: 'isDuplicateCampaignId',
        data: `JOURNEY isDuplicateCampaignId ${JSON.stringify(data)}`,
      });
      return true;
    }

    const newWorkflowSetting = handleReplaceDuplicateVariantKeys(
      data.workflow_setting,
    );

    if (!newWorkflowSetting) {
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._004,
          'Fail to action the journey, please try again (Duplicate Variant Keys)',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      addMessageToQueue({
        path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
        func: 'handleReplaceDuplicateVariantKeys',
        data: `JOURNEY newWorkflowSetting ${JSON.stringify(data)}`,
      });
      return true;
    }
    data.workflow_setting = newWorkflowSetting;

    const dataVariantFeConfigId = yield call(generateFeConfigWithAPI, {
      ...data,
      copyId,
    });

    if (dataVariantFeConfigId.length === 0) {
      addMessageToQueue({
        path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
        func: 'generateFeConfigWithAPI',
        data: `JOURNEY generateFeConfigWithAPI invalid params`,
      });
      const notification = {
        id: 'error',
        message: getTranslateMessage(
          TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
          'Fail to action the journey, please try again',
        ),
        translateCode: TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      return true;
    }

    // call and update fe_config_id data
    // tại hàm này cần reset lại fe_config_id cho variantExtraData,
    updateDataVariantWithFeConfigID(data, dataVariantFeConfigId);

    // dùng thêm 1 hàm cho việc remove data không cần thiết
    data = removeDataUnUseForAPI(data, flattenNodes, { saveAsTemplate });

    let res = {};

    yield put(
      updateValue(`${prefix}@@STORY_START_TIME@@`, {
        startTime: data.workflow_setting.metadata.startTime,
      }),
    );

    if (saveAsTemplate) {
      let isCaptureDone = false;
      let capturedThumbnails = null;

      while (!isCaptureDone) {
        const curReducer = yield select(state =>
          selectDomainMainCreateWorkflow(state, args),
        );
        const { isCapturing, thumbnails } = curReducer.configure.main;
        isCaptureDone = !isCapturing;
        capturedThumbnails = thumbnails;

        yield delay(500);
      }
      const fullThumbnails = yield call(handleUploadWorkflowThumbs, {
        variantThumbnails: capturedThumbnails,
        captureWorkspaceOnly: true,
        createCopy: false,
      });

      yield call(handleSaveAsTemplate, {
        design,
        moduleConfig: args.moduleConfig,
        savedData: {
          ...data,
          design,
          story_id: objectId || null,
          portal_id: getPortalId(),
          thumbnails: fullThumbnails || [],
        },
      });

      yield put(updateDone(prefix, false));
      return true;
    }

    data.properties = JSON.stringify(data.properties);

    if (isCreate) {
      res = yield call(JourneyServices.createV2_1, { data, _owner_id });
    } else {
      res = yield call(JourneyServices.updateV2_1, {
        objectId,
        data,
        _owner_id,
      });
    }

    if (res.code === 200) {
      const notification = {
        message: logicDesgin === 'create' ? 'Created' : 'Updates saved!',
        translateCode:
          logicDesgin === 'create'
            ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
            : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
        timeout: 1000,
        type: 'success',
      };
      yield put(addNotification(notification));

      yield call(handleUpdateValueLastTesting, args, res.data[0]);
      // update activeId
      if (logicDesgin === 'create') {
        yield put(
          updateValue(`${prefix}@@STORY_ACTIVE_ID@@`, {
            activeId: res.data[0],
            accepted_actions: initCreateRolesAction(),
          }),
        );

        if (isJourneyV2) {
          const prefixCommon = MODULE_CONFIG_COMMON.key;
          yield put(
            updateValue(`${prefixCommon}@@DRAWER_JOURNEY_INFO`, {
              design: 'update',
              storyId: res.data[0],
              isSaveDone: true,
            }),
          );

          const url = `${window.location.href}`.split('?')[0];
          // link to detail
          const newUrl = `${url}?ui=detail-drawer&journeyId=${
            res.data[0]
          }&channelId=${channelActive.value}&tab=settings&design=update`;

          window.history.pushState({ path: newUrl }, '', newUrl);
        } else if (isShowDrawer) {
          yield put(
            push(
              `${
                APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
                channelActive.value
              }/list?${KEY_PARAMS.UI}=${UI_DETAIL_DRAWER}&journeyId=${
                res.data[0]
              }&tab=settings&design=update`,
            ),
          );
        } else {
          yield put(
            push(
              `${
                APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
                channelActive.value
              }/detail/${res.data[0]}/settings?design=update`,
            ),
          );
        }
        // http://localhost:5000/gen2/marketing-hub/33167/journeys/7/detail/167898/settings?design=update
        // call api get detail for update action_ace
      } else if (!versionId) {
        yield put(
          getDetail(`${PREFIX_DETAIL}`, {
            activeId: objectId,
            tab: 'settings',
            channelId: channelActive.value,
            isAfterSave: true,
          }),
        );
      }
      yield put(updateDone(prefix, true));
      // call and validate lại node có được xóa không theo status
      yield call(handleValidateRuleActionDeleteNode, channelActive.value, args);

      if (typeof updatedErrors === 'object' && updatedErrors !== null) {
        const existedErrors = Object.entries(updatedErrors).reduce(
          (accErrors, [nodeErrorId, errorStatus]) => {
            if (errorStatus) {
              accErrors.push(nodeErrorId);
            }
            return accErrors;
          },
          [],
        );

        // If has any error nodes remaining after action success,need to reset them
        if (existedErrors.length > 0) {
          const actionType = `${prefix}@@RESET_CAMPAIGN_NAME_STORY_DETAILS_ERRORS@@`;

          yield all(
            existedErrors.map(nodeErrorId => {
              const resetErrAction = updateValue(actionType, {
                actionId: nodeErrorId,
              });

              return put(resetErrAction);
            }),
          );
        }
      }

      if (versionId) {
        if (isShowDrawer) {
          yield put(
            push(
              `${
                APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
                channelActive.value
              }/list?${KEY_PARAMS.UI}=${UI_DETAIL_DRAWER}&${
                KEY_PARAMS.JOURNEY_ID
              }=${objectId}&${KEY_PARAMS.TAB}=settings&design=update`,
            ),
          );
        } else {
          yield put(
            push(
              `${
                APP.PREFIX
              }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
                channelActive.value
              }/detail/${objectId}/settings?design=update`,
            ),
          );
        }
      }
    } else if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      yield put(updateValue(`${prefix}@@MAIN_ERROR@@`, res.codeMessage));
    } else if (res.codeMessage === '_NOTI_SAME_JOURNEY_CAMPAIGN_NAME') {
      const errorsDetails = res.data[0];
      errorsDetails.errors = getTranslateMessage(
        TRANSLATE_KEY._NOTIFICATION_NAMESAKE,
        'This name already existed',
      );
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));

      const errorsDetails1 = { [errorsDetails.actionId]: true };
      yield put(
        updateValue(`${prefix}@@STORY_ERRORS@@`, { errors: errorsDetails1 }),
      );
      yield put(
        updateValue(`${prefix}@@STORY_DETAILS_ERRORS@@`, errorsDetails),
      );
      yield put(updateValue(`${prefix}@@API_ERRORS@@`, res.data));
    } else {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield put(updateDone(prefix, false));
      // dung res.data for loop and render error
      yield put(updateValue(`${prefix}@@API_ERRORS@@`, res.data));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleSave',
      data: err.stack,
    });
    const notification = {
      id: 'error',
      message: getTranslateMessage(
        TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        'Fail to action the journey, please try again',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 2000,
      timestamp: new PortalDate().getTime(),
      type: 'danger',
    };
    yield put(addNotification(notification));
    yield put(updateDone(prefix, false));
    // eslint-disable-next-line no-console
    console.log(err);
  } finally {
    yield put(
      updateValue(
        `${MODULE_CONFIG_COMMON.key}@@DRAWER_JOURNEY_INFO_CLOSABLE_STT`,
        true,
      ),
    );
  }
}

function* handleUpdateValueLastTesting(args, action) {
  // console.log('action', action);
  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const reducerMain = reducer.main;
  const data = { ...reducerMain.dataRunTest, objectId: action };

  // console.log('reducerMain.dataRunTest', reducerMain.dataRunTest);
  if (action !== 1 && reducerMain.dataRunTest.isOpenModal) {
    // console.log('reducerMain', reducerMain)
    const res = yield call(
      JourneyServices.quicktest.getLastResultExperiment,
      data,
    );

    if (get(data, 'data.experimentId')) {
      data.data.experimentId = res.data[0].experimentId;
      yield call(JourneyServices.quicktest.updateExperiment, data);
    }
  }
}

function* handleConfirmActive(args) {
  const prefix = args.moduleConfig.key;

  yield put(
    updateValue(`${prefix}@@MODAL_CONFIRM_ACTIVE`, {
      value: { show: true },
    }),
  );

  const actConfirmPattern = action => {
    const type = `${prefix}@@MODAL_CONFIRM_ACTIVE${ReduxTypes.UPDATE_VALUE}`;
    const show = get(action, 'payload.value.show');

    return action.type === type && typeof show === 'boolean';
  };

  const action = yield take(actConfirmPattern);

  return get(action, 'payload.activated');
}

function* handleSaveChangeStatus(args, action) {
  const prefix = args.moduleConfig.key;

  const { value: status, nextStatus } = action.payload;

  let prevStatus;

  function* resetPreviousStatus() {
    yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, prevStatus));
    yield put(updateDone(`${prefix}@@NODE_STATUS@@`, { status: false }));
  }

  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { main } = reducer;
    const { design } = main;

    prevStatus = main.activeRow.status;

    const objectId = main.activeRow.activeId;

    if (
      prevStatus !== JOURNEY_STATUS.ACTIVE &&
      [JOURNEY_STATUS.REACTIVED, JOURNEY_STATUS.ACTIVE].includes(nextStatus)
    ) {
      const activated = yield call(handleConfirmActive, args);

      if (!activated) {
        yield call(resetPreviousStatus);

        return;
      }
    }

    yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, nextStatus));

    const data = {
      story_ids: [objectId],
      status,
      owner_id: parseInt(getCurrentOwnerIds()),
    };

    // check quyền VIEW EVERYTHING và truyền param _owner_id tương ứng
    const hasRoleEditEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.UPDATE,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const res = yield call(JourneyServices.data.updateStatusOwner, {
      data,
      _owner_id: hasRoleEditEverything ? null : getCurrentAccessUserId(),
    });

    if (res.code === 200) {
      const notification = {
        id: 'success',
        message: design === 'create' ? 'Created' : 'Updates saved!',
        translateCode: TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
        timeout: 1000,
        timestamp: new PortalDate().getTime(),
        type: 'success',
      };

      yield put(addNotification(notification));

      // mỗi status có mỗi AvailabelStatus khác nhau, cần phải fetch và update lại khi thay đổi status
      yield call(
        getAndUpdateAvailabelStatusAndpermission,
        main.activeRow.activeId,
        args,
      );
      yield put(
        updateDone(`${prefix}@@NODE_STATUS@@`, {
          status: true,
          data: nextStatus,
        }),
      );
      yield call(
        handleValidateRuleActionDeleteNode,
        main.activeRow.channel_id,
        args,
      );

      if (design === 'create') {
        yield call(handleGoToList);
      }
    } else {
      const notification = {
        id: 'error',
        ...getErrorMessageV2Translate(res.codeMessage),
        timeout: 2000,
        timestamp: new PortalDate().getTime(),
        type: 'danger',
      };
      yield put(addNotification(notification));
      yield call(resetPreviousStatus);
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleSaveChangeStatus',
      data: err.stack,
    });
    const notification = {
      id: 'error',
      message: getTranslateMessage(
        TRANSLATE_KEY._NOTI_FAIL_ACTION_JOURNEY,
        'Fail to action the journey, please try again',
      ),
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      timeout: 2000,
      timestamp: new PortalDate().getTime(),
      type: 'danger',
    };

    yield put(addNotification(notification));
    yield call(resetPreviousStatus);

    // eslint-disable-next-line no-console
    console.log(err);
  }
}

function* handelInitSingleChannel(channelId, args) {
  try {
    const prefix = args.moduleConfig.key;
    // const reducer = yield select(makeSelectConfigureMainCreateWorkflow());
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    // init workflowSetting theo channelId
    const workflowSetting = mapWorkFlowSettingWithChannelId(
      channelId,
      reducer.configure.main.node.map,
    );
    // console.log('workflowSetting', workflowSetting);
    const data = serializeFlattenNodesUI({
      branchs: workflowSetting,
      mapNode: reducer.configure.main.node.map,
      role: 'RESET',
    });

    const action = {
      payload: {
        icon: `icon-antsomi-clock-o`,
        iconUrl: '',
        label: workflowSetting.label,
        nodeId: workflowSetting.actionId,
        parentId: null,
        type: workflowSetting.actionType,
      },
    };
    // console.log('handleInitSingleChannel action', action);
    yield put(init(`${prefix}@@FLOW_UPDATE@@`, data));
    yield put(init(`${prefix}@@DATA_TRIGGER@@`, data.nodeTrigger));
    yield put(updateDone(`${prefix}@@CREATE_COPY_DONE@@`));
    yield call(handleOnValidate, args, action);
    yield call(handleBeforeActiveNode, args, action);
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handelCreateCopy',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}

function* handleCreateCopy({ copyId, journeyDetail }, args) {
  try {
    const prefix = args.moduleConfig.key;
    const oid = new URLSearchParams(window.location.search).get('oid');

    if (copyId) {
      journeyDetail = yield call(handleFetchJourneyDetail, copyId);
    }

    if (!journeyDetail) return;

    const storyName = getUntitledCopyName(journeyDetail.story_name);

    if (Object.keys(journeyDetail).length > 0) {
      const reducer = yield select(state =>
        selectDomainMainCreateWorkflow(state, args),
      );

      const data = serializeFlattenNodesUI({
        branchs: journeyDetail.workflow_setting,
        mapNode: reducer.configure.main.node.map,
        role: 'RESET',
        customInput: journeyDetail.custom_inputs,
      });

      // console.log('data', JSON.stringify(data));
      yield put(init(`${prefix}@@FLOW_UPDATE@@`, data));
      yield put(init(`${prefix}@@DATA_TRIGGER@@`, data.nodeTrigger));

      // const thumbnails = getObjectPropSafely(
      //   () => JSON.parse(journeyDetail.properties).thumbnails,
      // );

      // yield put(updateValue(`${prefix}@@STORY_THUMBNAIL@@`, thumbnails));
      yield put(updateDone(`${prefix}@@CREATE_COPY_DONE@@`));

      if (copyId) {
        yield put(updateValue(`${prefix}@@STORY_NAME@@`, storyName));
        yield put(updateValue(`${prefix}@@PAGE_TITLE@@`, storyName));
      }

      yield put(
        updateValue(`${prefix}@@ACTIVE_ROW_CLONE@@`, {
          activeRowClone: journeyDetail,
        }),
      );

      const triggerNode = data.nodeTrigger;

      if (triggerNode) {
        // active to get customInputs
        yield put(updateValue(`${prefix}@@ACTIVE_NODE@@`, triggerNode));
      }

      // check exist oid in url

      let search = `?channelId=${journeyDetail.channel_id}`;

      if (oid) {
        search += `&oid=${oid}`;
      }

      yield put(
        replace({
          search,
        }),
      );

      // updateUrl(
      //   makeUrlPermisison(
      //     `${
      //       APP.PREFIX
      //     }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
      //       journeyDetail.channel_id
      //     }?ui=create-drawer&storyId=${journeyDetail.story_id}&copyId=${
      //       journeyDetail.story_id
      //     }`,
      //   ),
      // );
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handelCreateCopy',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}

function* handleRefreshDataAfterSaveChangeSuccess(args, action) {
  try {
    // console.log('Saga create: ', { action, args });
    const prefix = args.moduleConfig.key;
    const activeRow = action.payload;
    // tạm chấp nhận spam, chưa giải quyết dc
    const updateNodes = [];
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    // console.log({ reducer });
    const { node } = reducer.configure.main;
    const { configure } = reducer;
    const { nodes, flattenNodes } = configure.main;

    // serialize data with campaignId and variantId
    const data = serializeFlattenNodesUI({
      branchs: activeRow.workflow_setting,
      mapNode: node.map,
    });

    nodes.forEach((value, key) => {
      // const currentNode = nodes.get(key);
      const findInfoNode = flattenNodes.find(item => item.nodeId === key);
      if (findInfoNode && findInfoNode.type === NODE_TYPE.DESTINATION) {
        const tmpNode = value;
        const freshNode = data.cacheNodes.get(key);
        // console.log({ tmpNode, freshNode });

        // why fresh node undefined ?
        if (freshNode) {
          // Handle Destination node
          const currentDestination = tmpNode.get('destination');
          const freshDestination = freshNode.get('destination');

          const { campaignId, variantIds } = freshDestination;
          const newDestination = cloneDeep(currentDestination);

          // console.log({ newDestination, campaignId, variantIds });

          // Những node destination nào đang có isFetchInfoData = true, thì to API sẽ lấy full info để truyền lên cho API
          // set isFetchInfoData = false để vào lại node load lại data theo fe_config_settings để lấy email config
          // set isToAPIFullData = true để làm điều kiện toAPI full data, vì với isFetchInfoData = false thì toAPI sẽ lấy preview data (không đúng)
          newDestination.isFetchInfoData = false;
          newDestination.campaignId = campaignId;
          newDestination.variantIds = variantIds;
          // newDestination.variants.list = variants;
          if (typeof newDestination.data === 'object') {
            // có data là isOpened = true
            newDestination.data.campaignId = campaignId;
            newDestination.data.variantIds = variantIds;
          }
          updateNodes.push({
            nodeId: key,
            name: 'destination',
            data: newDestination,
          });
        }
      }
    });
    yield put(updateValue(`${prefix}@@REFRESH_NODES@@`, updateNodes));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleRefreshDataAfterSaveChangeSuccess',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}
function* handleGetByCampainVariant(args, action) {
  const prefix = args.moduleConfig.key;
  const { newNode, arrCampaignId, arrVariantId, nodeAB } = action.payload;
  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { flattenNodes, activeNode } = reducer?.configure?.main || {};

    yield put(updateDone(`${prefix}@@IS_LOADING_COPPY_BRANCH_DONE@@`));
    if (arrCampaignId.length > 0) {
      const resCampaign = yield call(JourneyServices.campaigns.getByIds, {
        data: {
          campaign_ids: arrCampaignId,
          columns: [
            'campaign_id',
            'campaign_name',
            'status',
            'campaign_setting',
            'custom_inputs',
          ],
        },
      });
      let campaignInfo = [];
      if (resCampaign.data.length > 0) {
        campaignInfo = resCampaign.data;
      }
      const resVariant = yield call(JourneyServices.variant.getByIds, {
        data: {
          variant_ids: arrVariantId,
          columns: [
            'variant_id',
            'variant_name',
            'content_setting',
            'status',
            'custom_inputs',
          ],
        },
      });
      let variantInfo = [];
      if (resVariant.data.length > 0) {
        variantInfo = resVariant.data;
      }
      // const actions = {
      //   newNode,
      //   variantInfo,
      //   campaignInfo,
      // };
      setCampaignVariantInfo(newNode, variantInfo, campaignInfo);
      // yield call(setCampaignVariantInfo, actions);
      yield put(updateDone(`${prefix}@@COPPY_BRANCH_DONE@@`));
    } else {
      yield delay(500);
      yield put(updateDone(`${prefix}@@COPPY_BRANCH_DONE@@`));
    }
    yield put(
      updateValue(`${prefix}@@FLATTEN_NODE_COPPY_BRANDS@@`, {
        newNode,
        nodeAB,
      }),
    );

    if (Array.isArray(flattenNodes) && flattenNodes.length > 0) {
      const splitNode = flattenNodes.find(
        item => item.nodeId === newNode.parentId,
      );

      const isABSplit = splitNode?.type === NODE_TYPE.SPLIT_BRANCH;
      const notCurrentActive =
        splitNode && splitNode.nodeId !== activeNode.nodeId;
      const isRecalculatePercent = isABSplit && notCurrentActive;

      if (isRecalculatePercent) {
        yield put(
          updateValue(`${prefix}@@SPLIT_NODE_DATA@@`, {
            activeNode: splitNode,
            branches: nodeAB,
          }),
        );
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleGetByCampainVariant',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}

function* handleValidateCatchedNodesBeforeFlowchartShow(args) {
  const prefix = args.moduleConfig.key;

  try {
    const {
      configure: { main: reducer },
    } = yield select(state => selectDomainMainCreateWorkflow(state, args));

    const { nodes, flattenNodes, triggerEvent } = reducer;
    const {
      segmentsByNodes,
      attributesByNodes,
      eventAttributesByNodes,
    } = getInitValidatedFieldFromNodes(nodes, flattenNodes);

    const [nodesErrorSegment, nodesErrorAttr, nodesErrorEventAttr] = yield all([
      call(handleValidateCatchedSegmentUsed, {
        segmentsByNodes,
        flattenNodes,
      }),
      call(handleValidateCatchedAttribute, {
        attributesByNodes,
      }),
      call(handleValidateCatchedEventAttribute, {
        eventAttributesByNodes,
        triggerEvent,
      }),
    ]);

    // console.log(nodesErrorSegment, nodesErrorAttr, nodesErrorAttr);

    const errors = mergeIsErrorNodes(
      nodesErrorSegment,
      nodesErrorAttr,
      nodesErrorEventAttr,
    );

    // console.log(errors);

    if (!isEmpty(errors)) {
      yield put(
        updateValue(`${prefix}@@STORY_ERRORS@@`, {
          errors,
          errorsMessage: [],
        }),
      );
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleGetByCampainVariant',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', error);
  }
}

function* handleValidateCatchedSegmentUsed(args) {
  try {
    const { segmentsByNodes, flattenNodes } = args;
    // console.log(segmentsByNodes, flattenNodes);
    const errors = {};

    const flattenSegmentIds = flatten(safeParseArray(values(segmentsByNodes)));

    if (flattenSegmentIds.length) {
      const validateResultByNodes = yield call(validateSegmentsInNodes, {
        segmentIds: flattenSegmentIds,
        segmentsByNodes,
        flattenNodes,
      });

      mapValues(
        validateResultByNodes,
        ({ nonPermissions = [], archived = [], removed = [] }, nodeId) => {
          if ([nonPermissions, archived, removed].some(i => !isEmpty(i))) {
            errors[nodeId] = true;
          }
        },
      );
    }

    return errors;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleValidateCatchedSegmentUsed',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', error);
  }
}

function* handleValidateCatchedAttribute(args) {
  try {
    const { attributesByNodes } = args;
    const errors = {};

    let flattenAttributes = flatten(safeParseArray(values(attributesByNodes)));

    flattenAttributes = uniqBy(flattenAttributes, e => JSON.stringify(e));

    if (flattenAttributes.length > 0) {
      const { list: attrListResponse } = yield call(
        MetaDataServices.item.properties.lookupByIdsV2,
        {
          dataPost: { attributes: flattenAttributes },
        },
      );

      // console.log(attrListResponse);

      const inValidAttrs = attrListResponse
        .filter(
          ({ status, type }) => INVALID_ATTR_S.includes(status) && type === 3,
        )
        .map(attr => `${attr.itemTypeId}${attr.name}`);

      mapValues(attributesByNodes, (attrs, nodeId) => {
        const nodeExistInvalidAttr = attrs.some(attr => {
          const attrStringInfo = `${attr.itemTypeId}${attr.itemPropertyName}`;

          return inValidAttrs.includes(attrStringInfo);
        });

        if (nodeExistInvalidAttr) {
          errors[nodeId] = true;
        }
      });
    }

    return errors;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleValidateCatchedAttribute',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', error);
  }
}

function* handleValidateCatchedEventAttribute(args) {
  try {
    const { eventAttributesByNodes, triggerEvent } = args;
    const errors = {};

    let flattenEventAttrs = flatten(
      safeParseArray(values(eventAttributesByNodes)),
    );

    flattenEventAttrs = uniqBy(flattenEventAttrs, e => JSON.stringify(e));

    if (flattenEventAttrs.length > 0) {
      const dataSourceID = '-1';

      const { list: listEventAttrsResponse } = yield call(
        MetaDataServices.eventProperty.lookupByIds,
        {
          inputUrl: `${dataSourceID}/${triggerEvent.eventCategoryId}/${
            triggerEvent.eventActionId
          }`,
          dataPost: {
            eventIds: flattenEventAttrs,
          },
        },
      );

      // console.log(listEventAttrsResponse);

      const inValidEventAtts = listEventAttrsResponse
        .filter(
          ({ status, type }) =>
            INVALID_EVENT_ATTR_S.includes(status) && type === 3,
        )
        .map(attr => `${attr.itemTypeId}${attr.name}`);

      mapValues(eventAttributesByNodes, (eventAttrs, nodeId) => {
        const nodeExistInvalidAttr = eventAttrs.some(attr => {
          const attrString = `${attr.itemTypeId}${attr.eventPropertyName}`;

          return inValidEventAtts.includes(attrString);
        });

        if (nodeExistInvalidAttr) {
          errors[nodeId] = true;
        }
      });
    }

    return errors;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.js',
      func: 'handleValidateCatchedEventAttribute',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', error);
  }
}

function* handleResumeTrigger(args, action) {
  try {
    const reducers = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const { storyType, keyResume } = action.payload;
    const reducer = yield select(MAP_SELECTOR[keyResume].reducerMain);
    const reducerDateRange = yield select(
      MAP_SELECTOR[keyResume].reducerDateRange,
    );
    const table = yield select(MAP_SELECTOR[keyResume].reducerTable);
    const reducerFilter = yield select(MAP_SELECTOR[keyResume].reducerFilter);
    const { rules } = reducerFilter;
    const apiRules = toConditionAPI(rules);
    if (storyType === 'schedule') {
      apiRules.OR[0].AND.push({
        column: 'schedule_id',
        data_type: 'string',
        operator: 'contains',
        type: 1,
        value: reducer.processId,
      });
    }

    const processId = [];
    let version;
    reducer.dataSelect.forEach(each => {
      processId.push(each.process_id);
      version = each.journey_version;
    });
    const nodeMulti = getNodeId(reducers.configure.main.activeNodeMulti);
    const hasRoleViewEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.VIEW,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const params = {
      storyId: parseInt(reducer.storyId),
      _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
      data: {
        processIds: table.isSelectedAll ? [] : processId,
        version,
        filters: apiRules,
        resumeType: MAP_TYPE_RESUME[reducer.typeResume],
        durations: {
          fromDate: reducerDateRange.value.fromDate,
          toDate: reducerDateRange.value.toDate,
          // timeRange: dateRange.selectionType,
        },
        actionType: reducer.actionType,
        allowedActionIds:
          reducer.typeResume === 'whole_process'
            ? []
            : reducer.typeResume === 'from_node'
            ? reducers.configure.main.nodeActiveBehind
            : nodeMulti,
      },
    };
    if (storyType === 'schedule') {
      params.data.scheduleInfo = {
        scheduleId: reducer.processId,
        scheduleVersion: version,
      };
    }

    const res = yield call(JourneyServices.data.resumeTrigger, params);
    if (res && res.code === 200) {
      const notification = NOTI.updateStatus.success(res);
      yield put(addNotification(notification));
      yield put(getList(keyResume));
      yield put(update(`${keyResume}@@TOGGLE_DIAGRAM`));
    } else {
      const notification = NOTI.updateStatus.fail(res);
      yield put(addNotification(notification));
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/SchedulesHistory/saga.js',
      func: 'handleUpdateStatus',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
}

const NOTI = {
  updateStatus: {
    fail: res => ({
      id: 'update-status-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: res => ({
      id: 'success',
      message: res.design === 'create' ? 'Created' : 'Updates saved!',
      translateCode:
        res.design === 'create'
          ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
          : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
      timeout: 1500,
      timestamp: new Date().getTime(),
      type: 'success',
    }),
  },
};

export function* handleUpdateFlattenNodes(args, action) {
  const prefix = args.moduleConfig.key;
  const { nodeId, name, data } = action.payload;

  if (name === 'branchName') {
    yield put(
      updateValue(`${prefix}@@FLOW@@${actionTypes.SET_FLATTEN_NODE}`, {
        nodeId,
        data: {
          label: data,
        },
      }),
    );
  }
}

function* handleMapErrorAttributesTreeNodes(args, action) {
  const { design, showNotification = false } = action.payload || {};
  if (design === 'create') return;

  const prefix = args.moduleConfig.key;
  const { isInit = false } = args;

  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const { configure } = reducer;
  const { nodes, treeNodes, flattenNodes, node } = configure.main;

  const settings = yield select(makeSelectSettingPersonalizations());
  const { personalizationDataError } = settings || {};

  const { mapErrorAttributes, mapNodeError } = yield call(
    getPermissionTreeNodes,
    {
      treeNodes: isInit ? flattenNodes : treeNodes,
      nodes,
      nodeMap: node?.map,
    },
  );

  switch (true) {
    case Object.keys(mapErrorAttributes?.[PROMOTION_CODE]).length > 0: {
      yield put(
        updateValue(
          `${prefix}@@ERRORS_NOTIFICATION@@`,
          translate(
            translations?._JOURNEY_SAVE_VALIDATE_POOL_MESS,
            'Promotion Pools has been removed, please change to another available one.',
          ),
        ),
      );
      break;
    }

    case Object.keys(mapErrorAttributes?.[SHORT_LINK_V2]).length > 0: {
      yield put(
        updateValue(
          `${prefix}@@ERRORS_NOTIFICATION@@`,
          translate(
            translations._JOURNEY_DESTINATION_ERR_LINK_UNVAIL,
            'The associated link shortener is not available',
          ),
        ),
      );
      break;
    }

    default: {
      yield put(updateValue(`${prefix}@@ERRORS_NOTIFICATION@@`, ''));
      break;
    }
  }

  if (!isEqual(personalizationDataError, mapErrorAttributes)) {
    const payloads = {
      [PROMOTION_CODE]: {
        savedKey: PROMOTION_CODE,
        serviceFn: ObjectServices.suggestionMultilang.getList,
        dtoFn: dtoPromotionCode,
        payload: {
          data: {
            filters: { OR: [{ AND: [{}] }] },
            limit: 2000,
            page: 0,
            search: '',
            sort: 'asc',
            objectType: 'PROMOTION_POOL',
          },
        },
        normalizeData: toUIDataPromotionCode,
        isForceUpdateNew: true,
      },
      [SHORT_LINK_V2]: {
        savedKey: SHORT_LINK_V2,
        serviceFn: LinkManagementServices.shortener.getPersonalizations,
        dtoFn: dtoShortLinkShortener,
        payload: {
          data: {
            filters: {
              OR: [
                {
                  AND: [
                    {
                      column: 'status',
                      data_type: 'number',
                      operator: 'matches',
                      value: [53],
                    },
                  ],
                },
              ],
            },
            properties: ['link_shortener_id', 'shortener_name', 'properties'],
            limit: 2000,
            page: 1,
            sort: 'ctime',
            sd: 'desc',
            search: '',
          },
        },
        normalizeData: toUIDataShortener,
        isForceUpdateNew: true,
      },
    };
    yield put(startPersonalizations(payloads));
  }

  /** Update MapNodeError Done */
  if (showNotification) {
    yield put(
      updateValue(`${prefix}@@STORY_ERRORS@@`, {
        errors: mapNodeError,
        errorsMessage: mapNodeError,
      }),
    );
  }

  /** Update MapErrorAttributes Done */
  yield put(
    updateSettingPersonalizations({
      metadata: {},
      entries: {
        personalizationDataError: mapErrorAttributes,
      },
    }),
  );
}

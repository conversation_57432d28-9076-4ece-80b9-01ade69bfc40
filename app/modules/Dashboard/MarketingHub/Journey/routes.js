import React from 'react';

import APP from 'appConfig';
import ComponentRendering from 'components/common/ComponentRendering';

import NotFoundPage from '../../Notfound/Loadable';
import StoriesListModule from './List/Loadable';
import StoriesMainModule from './Main/Loadable';
import StoriesCreateModule from './Create/Loadable';
import StoriesCreateTemplateModule from './CreateTemplate/Loadable';
import StoriesCreateBlastCampaign from './Create/BlastCampaign/Loadable';
import StoriesDetailModule from './Detail/Loadable';
import VersionHistoryDetail from './Detail/VersionHistory2/Detail/Loadable';
import VersionRestore from './Detail/VersionHistory2/Restore/Loadable';
import VersionCompare from './Detail/VersionHistory2/Compare/Loadable';
import ScheduleHistoryDetail from './Detail/ScheduleHistory2/Detail/Loadable';
import ActionHistoryDetail from './Detail/ActionHistory2/Detail/Loadable';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from './Detail/config';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE } from './Create/config';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE_BLAST_CAMPAIGN } from './Create/BlastCampaign/config';
import { generatePath, Redirect } from 'react-router-dom';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../utils/web/cookie';
// import Automation from './Marketing/Automation/Loadable';
// import InboxPage2 from './Conversations/Inbox2/Loadable';

const PARENT_PREFIX = MODULE_CONFIG.parentPrefix;
const getPathName = props =>
  generatePath(`${PARENT_PREFIX}/:channelId/list`, {
    portalId: props?.match?.params?.portalId || getPortalId(),
    userId: props?.match?.params?.userId || getCurrentAccessUserId(),
    channelId: props?.match?.params?.channelId || 0,
  });
const routes = [
  // {
  //   path: `${PARENT_PREFIX}/:channelId/create?copyId=:copyId:`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <StoriesCreateModule design="copy" />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/create/template`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesCreateTemplateModule />
      </ComponentRendering>
    ),
  },
  {
    path: `${PARENT_PREFIX}/:channelId/create`,
    exact: false,
    main: () => (
      <ComponentRendering>
        <StoriesCreateModule
          design="create"
          moduleConfig={MODULE_CONFIG_CREATE}
        />
      </ComponentRendering>
    ),
  },

  {
    path: `${PARENT_PREFIX}/:channelId/blast-campaign/create`,
    exact: false,
    main: () => (
      <ComponentRendering>
        <StoriesCreateBlastCampaign
          design="create"
          moduleConfig={MODULE_CONFIG_CREATE_BLAST_CAMPAIGN}
        />
      </ComponentRendering>
    ),
  },

  {
    path: `${PARENT_PREFIX}/:channelId`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesMainModule />
      </ComponentRendering>
    ),
  },
  {
    path: `${PARENT_PREFIX}/:channelId/:tab`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesMainModule />
      </ComponentRendering>
    ),
  },
  {
    path: `${PARENT_PREFIX}/:channelId/:tab/:view`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesMainModule />
      </ComponentRendering>
    ),
  },

  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesDetailModule moduleConfig={MODULE_CONFIG_DETAIL} />
      </ComponentRendering>
    ),
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/:tab`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <StoriesDetailModule moduleConfig={MODULE_CONFIG_DETAIL} />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/:tab`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&tab=${props?.match?.params?.tab}&channelId=${
              props?.match?.params?.channelId
            }`,
          }}
        />
      );
    },
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/version-history/:versionId`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <VersionHistoryDetail />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/version-history/:versionId`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&tab=version-history&channelId=${
              props?.match?.params?.channelId
            }&versionId=${props?.match?.params?.versionId}`,
          }}
        />
      );
    },
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/compare/:versionLeft/:versionRight?`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <VersionCompare />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/compare/:versionLeft/:versionRight?`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&tab=version-history&channelId=${
              props?.match?.params?.channelId
            }&compare=true&versionLeft=${
              props?.match?.params?.versionLeft
            }&versionRight=${props?.match?.params?.versionRight}`,
          }}
        />
      );
    },
  },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/settings/:versionId`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <VersionRestore moduleConfig={MODULE_CONFIG_DETAIL} />
      </ComponentRendering>
    ),
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/schedule-history/:scheduleId/:versionId/:processId`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <ActionHistoryDetail />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/schedule-history/:scheduleId/:versionId/:processId`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&channelId=${
              props?.match?.params?.channelId
            }&tab=schedule-history&scheduleId=${
              props?.match?.params?.scheduleId
            }&versionId=${props?.match?.params?.versionId}&processId${
              props?.match?.params?.processId
            }`,
          }}
        />
      );
    },
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/schedule-history/:scheduleId/:versionId`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <ScheduleHistoryDetail />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/schedule-history/:scheduleId/:versionId`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&channelId=${
              props?.match?.params?.channelId
            }&tab=schedule-history&scheduleId=${
              props?.match?.params?.scheduleId
            }&versionId=${props?.match?.params?.versionId}`,
          }}
        />
      );
    },
  },
  // {
  //   path: `${PARENT_PREFIX}/:channelId/detail/:activeId/action-history/:processId/:versionId`,
  //   exact: true,
  //   main: () => (
  //     <ComponentRendering>
  //       <ActionHistoryDetail />
  //     </ComponentRendering>
  //   ),
  // },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/action-history/:processId/:versionId`,
    exact: true,
    render: props => {
      return (
        <Redirect
          to={{
            pathname: getPathName(props),
            search: `?ui=detail-drawer&journeyId=${
              props?.match?.params?.activeId
            }&tab=action-history&processId=${
              props?.match?.params?.processId
            }&versionId=${props?.match?.params?.versionId}`,
          }}
        />
      );
    },
  },
  {
    path: `${PARENT_PREFIX}/:channelId/detail/:activeId/:tab`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesDetailModule moduleConfig={MODULE_CONFIG_DETAIL} />
      </ComponentRendering>
    ),
  },
  {
    path: `${PARENT_PREFIX}`,
    exact: true,
    main: () => (
      <ComponentRendering>
        <StoriesMainModule />
      </ComponentRendering>
    ),
  },
  {
    path: '',
    exact: false,
    main: () => (
      <ComponentRendering>
        <NotFoundPage />
      </ComponentRendering>
    ),
  },
];

export default routes;

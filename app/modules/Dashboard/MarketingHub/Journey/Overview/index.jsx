/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import ViewQuiltIcon from '@material-ui/icons/ViewQuilt';
import { UIButton as Button, UILoading } from '@xlab-team/ui-components';

import CalendarSelection from 'components/Templates/CalendarSelection';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';

import { useImmer } from 'use-immer';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectOverviewDomainMain,
  makeSelectListOverviewDateRange,
} from './selectors';
import { MODULE_CONFIG } from './config';
import {
  init,
  updateValue,
  update,
  reset,
  addNotification,
} from '../../../../../redux/actions';
import { IconSetup, SetupWrapper, StyleWrapper } from './styles';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../components/Templates/LayoutContent';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { makeSelectJourneyChannelActive } from '../selectors';
import { getBreadcrums } from './utils';
import { makeSelectStoryDetailDomainActiveTab } from '../Main/selectors';
import Iframe from '../Detail/Dashdoard/_UI/Iframe';
import Settings from './settings/index';
import { getPortalId } from '../../../../../utils/web/cookie';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import WrapperDisable from '../../../../../components/common/WrapperDisable';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
} from '../../../../../utils/web/permission';

const MAP_TRANSLATE = {
  title: getTranslateMessage(
    TRANSLATE_KEY._TITL_DONT_HAVE_REPORT,
    "You don't have any report for this overview",
  ),
  subtitle: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_CLICK_TO_SETUP,
    'Click here to setup',
  ),
  setup: getTranslateMessage(TRANSLATE_KEY._, 'Setup'),
};

function OverviewPage(props) {
  const {
    main,
    dateRange,
    channelActive,
    activeTab,
    onChangeLayout,
    useDesign,
    onUpdateLoading,
    objectId,
    menuCode,
  } = props;

  const {
    isInitDone,
    componentRenderKey: componentKey,
    layout,
    src,
    width,
    height,
    title,
    activeRow,
    isLoading,
  } = main;

  const hasEditRole = checkingRoleScope(
    menuCode,
    APP_ACTION.UPDATE,
    APP_ROLE_SCOPE.EVERYTHING,
  );

  useEffect(() => {
    if (useDesign === 'settings') {
      onChangeLayout({ layout: 'settings' });
    }
  }, [useDesign]);

  useEffect(() => {
    props.callback('SHOW_SETTING_SETUP', layout);
  }, [layout]);

  useEffect(() => {
    if (activeTab === 'overview') {
      props.init({
        objectType: MODULE_CONFIG.objectType,
        objectId,
      });
    }
    return () => {
      props.reset();
      props.callback('SHOW_SETTING_SETUP', 'setup');
    };
  }, [activeTab]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'ON_CHANGE_DATERANGE': {
        if (layout === 'list') {
          props.onChangeDateRange(dataIn);
          props.onSaveDateRange(dataIn);
        }
        break;
      }
      case 'CANCEL':
        onChangeLayout({ layout: dataIn });
        break;
      case 'UPDATE_VALUE_DATE_RANGE':
        props.onChangeDateRange(dataIn);

        break;
      default:
        break;
    }
  };

  const onLoad = () => {
    onUpdateLoading({ isLoading: false });
  };

  const initDateRangeData = useMemo(() => dateRange, [dateRange]);

  // check cái isInitDone = false => tiến hành render

  const breadcrums = useMemo(() => {
    return getBreadcrums(channelActive);
  }, [channelActive.label]);

  const handleChangeLayout = () => {
    props.init({
      objectType: MODULE_CONFIG.objectType,
      objectId,
    });
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Overview/index.jsx">
      {isInitDone ? (
        <>
          {' '}
          <NavBarRightContent right="14px" className="p-right-4">
            {isInitDone && (
              <CalendarSelection
                initData={initDateRangeData}
                callback={callback}
                maxWidth={channelActive.value === 2 && '135px'}
                hiddenLabel
              />
            )}
          </NavBarRightContent>
          <StyleWrapper>
            {isLoading && <UILoading isLoading />}
            <CustomHeader breadcrums={breadcrums} />
            <LayoutContent
              padding="8px 16px 16px 16px"
              margin="-16px"
              height="calc(100vh - 90px)"
            >
              <LayoutContentLoading isLoading={!isInitDone}>
                {isInitDone && (
                  <div
                    id={`div-sca-${componentKey}-0`}
                    style={{
                      position: 'position',
                      width: '100%',
                      height: '100%',
                      paddingTop: '0.4rem',
                    }}
                  >
                    {layout === 'list' && (
                      <Iframe
                        componentKey={componentKey}
                        title={title}
                        src={src}
                        width={width}
                        height={height}
                        onLoad={onLoad}
                      />
                    )}
                    {layout === 'settings' && (
                      <div style={{ height: '100%', background: '#fff' }}>
                        <Settings
                          handleChangeLayout={handleChangeLayout}
                          activeRow={activeRow}
                          objectType={MODULE_CONFIG.objectType}
                          objectId={objectId}
                          moduleConfig={MODULE_CONFIG}
                          layout={layout}
                          callback={callback}
                        />
                      </div>
                    )}
                    {layout === 'setup' && (
                      <>
                        <SetupWrapper>
                          <IconSetup>
                            <ViewQuiltIcon className="icon--setup" />
                          </IconSetup>
                          <div>
                            {MAP_TRANSLATE.title} <br />{' '}
                            {MAP_TRANSLATE.subtitle}
                          </div>
                          <WrapperDisable noPermission={!hasEditRole}>
                            <Button
                              theme="primary"
                              onClick={
                                hasEditRole
                                  ? () =>
                                      onChangeLayout({
                                        layout: 'settings',
                                      })
                                  : () => {}
                              }
                            >
                              {MAP_TRANSLATE.setup}
                            </Button>
                          </WrapperDisable>
                        </SetupWrapper>
                      </>
                    )}
                  </div>
                )}
              </LayoutContentLoading>
            </LayoutContent>
          </StyleWrapper>
        </>
      ) : (
        <div style={{ position: 'relative', height: 'calc(100vh - 120px)' }}>
          <UILoading isLoading={!isInitDone} />
        </div>
      )}
    </ErrorBoundary>
  );
}
const mapStateToProps = createStructuredSelector({
  main: makeSelectOverviewDomainMain(),
  dateRange: makeSelectListOverviewDateRange(),
  channelActive: makeSelectJourneyChannelActive(),
  activeTab: makeSelectStoryDetailDomainActiveTab(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeLayout: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ON_CHANGE_LAYOUT`, params));
    },
    onUpdateLoading: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_LOADING`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(OverviewPage);

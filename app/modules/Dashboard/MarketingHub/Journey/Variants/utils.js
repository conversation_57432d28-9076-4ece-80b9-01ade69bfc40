// import { getLabelStoryStatus } from '../../../../../utils/web/processStatus';
import {
  getLabelCampaignStatus,
  getLabelStoryStatus,
} from '../../../../../utils/web/processStatus';
import {
  CellArray,
  CellCampaingStatus,
  CellDate,
  CellJourneyStatus,
  CellMainVariant,
  CellNumber,
  CellStoryStatus,
  CellText,
  CellToggle,
  CellToggleAPI,
  CellToggleWithJourney,
  CellToggleWithStyle,
  CellArrayObject,
} from '../../../../../containers/Table/Cell';
// import { getSegmentTypeLabel } from '../../../../../services/Abstract.data';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
import { initNumberId } from '../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
// import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../appConfig';
import { MENU_CODE } from '../../../../../utils/web/permission';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  // console.log(list);
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.variant_id,
      original_status: tmp.status,
      campaign_status: getLabelCampaignStatus(tmp.campaign_status),
      story_status: getLabelStoryStatus(parseInt(tmp.story_status)),
      story_type: tmp.story_type_name,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  // console.log(data);
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      Footer: '',
      use: 'PERMISSION',
      menuCode: MENU_CODE.JOURNEY,
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellMainVariant,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    // {
    //   Header: columnStatus.label,
    //   id: columnStatus.value,
    //   name: columnStatus.value,
    //   accessor: columnStatus.value,
    //   disableSortBy,
    //   width: 150,
    //   minWidth: 150,
    //   // maxWidth: 150,
    //   // disableResizing: true,
    //   sticky: 'left',
    //   Cell: CellStoryStatus,
    //   getIsDisabledToggle: () => true,
    //   placement: 'status',
    //   // className: 'tbl-border-left padding-left-right-10',
    //   className: `${columnStatus.value}`,
    //   mapParamsFn: ({ oldData, newData }) => ({
    //     objectId: oldData.segment_id,
    //     itemTypeId: oldData.item_type_id,
    //     data: {
    //       columns: ['status'],
    //       status: newData._newStatus ? 1 : 2,
    //     },
    //   }),
    //   ServiceToggleFn: null,
    //   Footer: '',
    // },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (['c_user_id', 'u_user_id'].includes(property.value)) {
      column.placement = 'left';
    }
    if (['story_status', 'campaign_status'].includes(property.value)) {
      column.className = `${dataType} ${property.value} txt-status`;
    }
    ['campaign_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['story_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['variant_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['end_date'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {
  // campaign_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
  campaign_status: CellCampaingStatus,
  story_status: CellJourneyStatus,
};

export function getModuleConfig(moduleConfig, storyId, objectType, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = storyId;
  newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};
export const mapDataToConfigFilter = (
  channelActive,
  isMapConfigSuggestionForDetails,
  isUseApiSelector,
) => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: isUseApiSelector ? null : 100,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '6-variant_name',
  };

  if (
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.itemTypeId = null;
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: `${
                isMapConfigSuggestionForDetails ? 'story_id' : 'story_type'
              }`,
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_type',
              data_type: 'number',
              operator: 'equals',
              value: channelActive.value,
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_VARIANTS,
      'Listing Variants',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}

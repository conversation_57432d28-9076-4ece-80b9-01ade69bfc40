import styled, { css } from 'styled-components';
import { UITabs as Tabs } from '@xlab-team/ui-components';
import TabScrollButton from '@material-ui/core/TabScrollButton';
import { breakdownMd } from 'utils/variables';
import { Icon, Tabs as TabsUI } from '@antscorp/antsomi-ui';

// import TableContainer from 'containers/Table';

// export const StyleTableCampaign = styled(TableContainer)`
//   &::-webkit-scrollbar-track {
//     margin-left: ${props =>
//       props.firstColWidth
//         ? `${props.firstColWidth + 148}px`
//         : '291px'} !important;
//     ${breakdownMd(
//       css`
//         margin-left: ${props =>
//           props.firstColWidth
//             ? `${props.firstColWidth + 148}px`
//             : '218px'} !important;
//       `,
//     )}
//   }
// `;

export const StyleWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const StyleTabs = styled(Tabs)``;

export const WrapperDetail = styled.div`
  position: relative;
  height: calc(100vh - 76px);
  ${({ isLoading }) =>
    isLoading && `overflow: hidden; box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);`}
  /* overflow-x: hidden; */
  /* ::-webkit-scrollbar {
    width: 0.5rem;
  }
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0.125rem #c1c1c1;
    border-radius: 10px;
    background: #fafafa;
  }
  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
  } */
  .private-overlay {
    padding: 0 !important;
    z-index: 902;
  }

  .navbar-name {
    height: 120px;
    padding-left: 24px;
    align-items: start;
    padding-top: 16px;
  }
`;

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  margin-top: 8px;
  padding: 3.25rem 0.75rem 0.2rem 0.75rem;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  /* ${breakdownMd(
    css`
      height: calc(100vh - 108px);
      top: 108px;
      left: 0.75rem;
      right: 0.75rem;
    `,
  )} */
`;

export const TabsWrapper = styled.div`
  .MuiTab-wrapper {
    text-transform: capitalize;
    font-size: 1em;
    /* color: rgb(127, 127, 127); */
    font-family: Roboto-Medium;
    margin-top: 3px;
  }

  .MuiTabs-indicator {
    height: 3px;
  }

  .MuiTabs-root {
    width: 68% !important;

    @media screen and (max-width: 1200px) {
      width: 60% !important;
    }

    @media screen and (max-width: 1024px) {
      width: 55% !important;
    }

    @media screen and (max-width: 767px) {
      width: 50% !important;
    }

    /* &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -10px;
      width: 10px;
      height: 48px;
      box-shadow: -10px 0px 10px -20px rgb(44 44 44 / 66%) inset;
    }

    &:after {
      content: '';
      position: absolute;
      width: 10px;
      height: 48px;
      top: 0;
      right: 32%;
      box-shadow: 10px 0px 17px -20px rgb(44 44 44 / 66%) inset;
    } */
  }
  .MuiTab-root {
    min-width: 155px !important;
  }

  .MuiTabScrollButton-root {
    width: 30px;
    opacity: 1;
  }

  .MuiTabs-scrollButtons {
    z-index: 100;
    /* box-shadow: rgb(0 0 0 / 20%) 0px 0px 0.375rem 0px; */
    padding-top: 2px;
    background-color: #fff;
    border-top: 1px solid rgb(230, 230, 230);
  }

  .MuiTabScrollButton-root.Mui-disabled {
    opacity: 0;
  }

  .tabs--header {
    display: flex;
    background-color: rgb(255, 255, 255);
    border-bottom: 1px solid rgb(230, 230, 230);
    height: 48px;
    //box-shadow: rgb(0 0 0 / 20%) 0px 1px 0.375rem 0px;
    align-items: flex-end;
    /* box-shadow: 3px 2px 5px 0 rgb(15 19 23 / 10%); */
    z-index: 5;
    position: relative;
    .MuiButtonBase-root {
      &:hover {
        background-color: rgb(242, 249, 255);
      }
    }
  }
`;

export const MyTabScrollButton = styled(TabScrollButton)`
  &.Mui-disabled {
    width: 0;
  }
  overflow: hidden;
  /* transition: width 0.7s; */
  width: 28;

  &:first-child {
    border-right: 1px solid #e0e0e0;
    box-shadow: rgb(255 255 255) 10px 15px 10px 5px;
  }

  &:last-child {
    border-left: 1px solid #e0e0e0;
    border-right: 3px solid rgb(231 231 231 / 47%);
    box-shadow: rgb(255 255 255) -10px 15px 10px 5px;
  }
`;

export const DrawerHeader = styled.div`
  position: sticky;
  top: 0;
  background: #ffffff;
  z-index: 99999;

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 15px;
  min-height: 50px;
  border-bottom: 1px solid rgb(230, 230, 230);

  .right-content {
    display: flex;
    align-items: center;
    gap: 10px;

    .header-info-drawer {
      display: flex;
      flex-direction: column;
      gap: 5px;
      padding: 4.5px 0;

      > div {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: end;
      }

      p {
        margin: 0;
      }

      button {
        white-space: nowrap;
      }

      .schedule-id {
        white-space: nowrap;
        color: #595959;
      }

      .process-id {
        white-space: nowrap;
        color: #595959;
      }
    }
  }
`;

export const IconSpeaker = styled(Icon)`
  font-size: 20px !important;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const WrapperHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  width: ${props =>
    `calc(100% - ${
      props.widthRightEle > 0 ? props.widthRightEle + 100 : 0
    }px)`};

  .header-journey-name {
    width: 100%;
    overflow: hidden;
  }
`;

export const AntsomiTabsUI = styled(TabsUI)`
  .antsomi-tabs-content {
    position: unset !important;
  }
`;

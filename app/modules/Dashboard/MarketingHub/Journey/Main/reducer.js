/* eslint-disable consistent-return */
/* eslint-disable camelcase */
import produce from 'immer';
import { combineReducers } from 'redux';
import tableReducerFor from 'containers/Table/reducer';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../config';
import { mapSingleItemFE } from './utils';
import ReduxTypes from '../../../../../redux/constants';
import filtersRulesReducerFor from '../../../../../containers/Filters/reducer.rules';
import filtersConfigReducerFor from '../../../../../containers/Filters/reducer.config';
import columnConfigReducerFor from '../../../../../containers/ModifyColumn/reducer.config';
import APP from '../../../../../appConfig';
import { setLocalStorage } from '../../../../../utils/web/cookie';
import { safeParse } from '../../../../../utils/common';

// import { DEFAULT_ACTION } from './constants';
const PREFIX = MODULE_CONFIG.key;

// const DEFAULT_TAB = 'list';
// const DEFAULT_TAB = 'campaigns';

export function initialState() {
  return {
    isLoading: true,
    activeTab: '',
    name: '',
    status: 1,
    processStatus: 3,
    activeId: null,
    itemTypeId: null,
    activeRow: {},
  };
}

/* eslint-disable default-case, no-param-reassign */
const mainReducerFor = () => {
  const mainReducer = (state = initialState(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT}`: {
          const { activeId, tab } = action.payload;
          draft.activeId = activeId;
          draft.isLoading = true;
          // draft.activeTab = DEFAULT_TAB;
          draft.activeTab = tab;
          return;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          draft.isLoading = true;
          return;
        }
        case `${PREFIX}${ReduxTypes.GET_LIST}`: {
          draft.isLoading = true;
          return;
        }
        case `${PREFIX}${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.activeRow = mapSingleItemFE(action.payload);
          draft.itemTypeId = draft.activeRow.itemTypeId;
          draft.name = draft.activeRow.segmentDisplay;
          draft.status = parseInt(draft.activeRow.status);
          draft.processStatus = draft.activeRow.processStatus;
          draft.isLoading = false;
          return;
        }
        case `${MODULE_CONFIG.key}@@CHANGE_TAB@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.activeTab = action.payload;
          return;
        }
        case `${MODULE_CONFIG_COMMON.key}@@CHANNEL_ACTIVE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // check change active channel from web to other
          // if active overview => update to list
          const channel = safeParse(action.payload, {});
          if (channel.value != 2 && state.activeTab === 'overview') {
            draft.activeTab = 'list';
          }
          return;
        }
        default:
          return state;
      }
    });
  return mainReducer;
};

const filter = combineReducers({
  rules: filtersRulesReducerFor(PREFIX),
  config: filtersConfigReducerFor(PREFIX),
});

// export default customerReducer;
export default combineReducers({
  main: mainReducerFor(),
  table: tableReducerFor(PREFIX),
  filter,
  column: columnConfigReducerFor(PREFIX),
});

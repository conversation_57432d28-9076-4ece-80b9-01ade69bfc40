import { createSelector } from 'reselect';

import { initialState } from './reducer';
import { MODULE_CONFIG } from './config';

/**
 * Direct selector to the customer state domain
 */

const selectStoryDetailDomain = state =>
  state.get(MODULE_CONFIG.key) || {
    main: initialState(),
  };

/**
 * Other specific selectors
 */

/**
 * Default selector used by Customer
 */

const makeSelectActiveRow = () =>
  createSelector(
    selectStoryDetailDomain,
    substate => substate.main.activeRow,
  );

const makeSelectStoryDetailDomainMain = () =>
  createSelector(
    selectStoryDetailDomain,
    substate => substate.main,
  );
const makeSelectStoryDetailDomainActiveTab = () =>
  createSelector(
    selectStoryDetailDomain,
    substate => substate.main.activeTab,
  );

export default selectStoryDetailDomain;
export {
  makeSelectStoryDetailDomainMain,
  makeSelectActiveRow,
  makeSelectStoryDetailDomainActiveTab,
};

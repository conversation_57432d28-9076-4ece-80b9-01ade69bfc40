/* eslint-disable react/react-in-jsx-scope */
import {
  CellText,
  CellDate,
  CellArray,
  CellDestinationWithFilterById,
  CellNumberString,
} from 'containers/Table/Cell';
import { safeParse } from 'utils/common';
import { COLUMNS_WIDTH } from '../../../../../../../../containers/Table/constants';
import { state } from '../../../ActionsHistory/NodesList/utils';

export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.node_id,
      node_id: safeParse(tmp.node_id, '--'),
      node_name: safeParse(tmp.node_name, '--'),
      node_state: safeParse(state[tmp.node_state], '--'),
      duration: safeParse(tmp.duration, '--'),
      variant_id: safeParse(tmp.variant_id, '--'),
      campaign_id: safeParse(tmp.campaign_id, '--'),
      destination_name: safeParse(tmp.destination_name, '--'),
      catalog_name: safeParse(tmp.catalog_name, '--'),
      error_message: safeParse(tmp.error_message, '--'),
      receiver_address: safeParse(tmp.input && tmp.input.receiverAddress, '--'),
      receiver_channel: safeParse(tmp.input && tmp.input.channelName, '--'),
      start_time: tmp.start_date,
      end_time: tmp.end_date,

      // Test
      // variant_id: 12341,
      // campaign_id: 12434,
      // destination_id: 341234,
    };
    if (
      (tmp.node_name === 'Scheduled Trigger' ||
        tmp.node_name === 'Action-based Trigger') &&
      tmp.node_state === 'SKIP'
    ) {
      tempt.node_state = state.PASSED;
    }
    data.list.push(tempt);
    data.map[tempt.actionId] = tempt;
  });
  return data;
}

export function buildTableColumns(columnsActive) {
  const columns = [];

  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };

    if (['campaign_id', 'variant_id'].includes(property.value)) {
      column.Cell = CellNumberString;
    } else if (property.value === 'destination_id') {
      column.Cell = CellDestinationWithFilterById;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }

    columns.push(column);
  });
  return columns;
}

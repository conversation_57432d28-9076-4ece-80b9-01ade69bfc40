/* eslint-disable no-unused-expressions */
/* eslint-disable jsx-a11y/no-access-key */
/* eslint-disable react/prop-types */
/* eslint-disable import/order */
// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { get, keyBy, set } from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Components
import { Header } from './Header';
import { Diagram } from './Diagram';
import ActionHistoryDetailList from './List';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Styles
import { AntsomiTabs } from './styled';
import { WrapperDetail } from '../../VersionHistory2/Detail/styled';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { init, updateValue } from 'redux/actions';
import reducer from './reducer';
import saga from './saga';
import { makeSelectActiveRow } from '../../selectors';
import { makeSelectActionDetail } from './selectors';
import { makeSelectConfigureMainCreateFlattenNodes } from '../../../Create/selectors';
import { makeSelectJourneyChannelActive } from '../../../selectors';

// Constants
import { PREFIX, PREFIX_DETAIL } from './config';
import { MAP_NODE_STATE } from './constants';

// Utils
import { getBreadcrums } from '../../utils';
import { getPrefixCreate } from '../../VersionHistory2/Detail/utils';

const MAP_TITLE = {
  diagram: getTranslateMessage(TRANSLATE_KEY._TAB_DIAGRAM, 'Diagram'),
  nodes: getTranslateMessage(TRANSLATE_KEY._TAB_NODES, 'Nodes'),
  tabTitle: `${getTranslateMessage(
    TRANSLATE_KEY._TAB_JOURNEY,
    'Journeys',
  )} - ${getTranslateMessage(
    TRANSLATE_KEY._TITL_TAG_ACTION_HISTORY_DETAIL,
    'Action History Detail',
  )}`,
};

const ActionHistory = props => {
  const {
    initActionDetail,
    actionDetail,
    journeyDetail,
    flattenNodes,
    updateFlattenNodes,
    channelActive,
    isShowDrawer,
    isScheduleHistory,
  } = props;
  // const { processId, versionId = 1, scheduleId, activeId } = useParams();
  const params = useParams();

  const processId = props.processId || params.processId;

  const versionId = props.versionId || params.versionId;

  const scheduleId = props.scheduleId || params.scheduleId;

  const activeId = props.activeId || params.activeId;

  const { is_blast_campaign } = journeyDetail;

  const isBlastCampaign = !!is_blast_campaign;

  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });

  const [activeTab, setActiveTab] = useState('diagram');

  useEffect(() => {
    if (journeyDetail.story_id && processId) {
      initActionDetail({
        storyId: journeyDetail.story_id,
        processId,
      });
    }
  }, [processId, journeyDetail.story_id]);

  useDeepCompareEffect(() => {
    const mapActionDetail = keyBy(actionDetail.data, 'node_id');

    const newFlattenNodes = flattenNodes.map(node => {
      const newNode = {
        ...node,
        moreInfo: { ...node.moreInfo, backgroundColor: '' },
      };

      const nodeProcessInfo = mapActionDetail[node.nodeId];
      if (nodeProcessInfo) {
        newNode.nodeProcessInfo = nodeProcessInfo;

        const nodeState = get(
          MAP_NODE_STATE,
          `${
            (nodeProcessInfo.node_name === 'Scheduled Trigger' ||
              nodeProcessInfo.node_name === 'Action-based Trigger') &&
            nodeProcessInfo.node_state === 'SKIP'
              ? 'PASSED'
              : nodeProcessInfo.node_state
          }`,
          null,
        );

        if (nodeState) {
          const {
            backgroundColor,
            color,
            hoverColor,
            hoverBorder,
            activeColor,
            activeBorder,
          } = nodeState;

          backgroundColor &&
            set(newNode, 'moreInfo.backgroundColor', backgroundColor);
          color && set(newNode, 'moreInfo.color', color);
          hoverColor && set(newNode, 'moreInfo.hoverColor', hoverColor);
          hoverBorder && set(newNode, 'moreInfo.hoverBorder', hoverBorder);
          activeColor && set(newNode, 'moreInfo.activeColor', activeColor);
          activeBorder && set(newNode, 'moreInfo.activeBorder', activeBorder);
        }
      }

      return newNode;
    });

    updateFlattenNodes({
      flattenNodes: newFlattenNodes,
    });
  }, [actionDetail.data, flattenNodes]);

  const onChangeActiveTab = tab => {
    setActiveTab(tab);
  };

  const breadcrumbs = useMemo(
    () => getBreadcrums(channelActive, journeyDetail.story_name),
    [journeyDetail.story_name],
  );

  return (
    <>
      <UIHelmet
        title={
          isScheduleHistory
            ? 'Journey - Schedule History Detail'
            : MAP_TITLE.tabTitle
        }
      />
      {!isShowDrawer && <CustomHeader breadcrums={breadcrumbs} />}
      <WrapperDetail
        isShowDrawer={isShowDrawer}
        data-test="page-detail-process"
      >
        <Header
          versionId={versionId}
          journeyInfo={journeyDetail}
          versionInfo={journeyDetail.story_version}
          processId={processId}
          scheduleId={scheduleId}
          isShowDrawer={isShowDrawer}
        />
        <AntsomiTabs
          items={[
            {
              label: MAP_TITLE.diagram,
              key: 'diagram',
              children: (
                <Diagram
                  prefix={PREFIX_DETAIL}
                  versionId={+versionId}
                  isBlastCampaign={isBlastCampaign}
                  isShowDrawer={isShowDrawer}
                />
              ),
            },
            {
              label: MAP_TITLE.nodes,
              key: 'nodes',
              children: (
                <ActionHistoryDetailList
                  activeId={activeId}
                  processId={processId}
                  journeyDetail={journeyDetail}
                />
              ),
            },
          ]}
          onTabClick={onChangeActiveTab}
          accessKey={activeTab}
        />
        {/* <StyledTabs
          // noBorderBottom
          isRenderChildren
          activeTab={activeTab}
          onChange={onChangeActiveTab}
          className="tab-box-shadow"
        >
          <TabPanel marginTop label={MAP_TITLE.diagram} eventKey="diagram">
            <Diagram
              prefix={PREFIX_DETAIL}
              versionId={+versionId}
              isBlastCampaign={isBlastCampaign}
              isShowDrawer={isShowDrawer}
            />
          </TabPanel>
          <TabPanel marginTop label={MAP_TITLE.detail} eventKey="detail">
            <ActionHistoryDetailList
              activeId={activeId}
              processId={processId}
              journeyDetail={journeyDetail}
            />
          </TabPanel>
        </StyledTabs> */}
      </WrapperDetail>
    </>
  );
};

ActionHistory.propTypes = {
  journeyDetail: PropTypes.object,
  actionDetail: PropTypes.object,
  initActionDetail: PropTypes.func,
  flattenNodes: PropTypes.array,
  updateFlattenNodes: PropTypes.func,
  channelActive: PropTypes.object,
};

const mapStateToProps = createStructuredSelector({
  journeyDetail: state =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: PREFIX_DETAIL },
    }),
  actionDetail: makeSelectActionDetail(),
  flattenNodes: state =>
    makeSelectConfigureMainCreateFlattenNodes()(state, {
      moduleConfig: { key: getPrefixCreate(PREFIX_DETAIL) },
    }),
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => {
  return {
    initActionDetail: params => {
      dispatch(init(PREFIX, params));
    },
    updateFlattenNodes: payload => {
      dispatch(
        updateValue(
          `${getPrefixCreate(PREFIX_DETAIL)}@@FLATTEN_NODES@@`,
          payload,
        ),
      );
    },
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ActionHistory);

/* eslint-disable import/no-cycle */
// Libraries
import React from 'react';
import PropTypes from 'prop-types';

// Constants
import { VERSION_HISTORY } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/constants';

// Components
import JourneyDetail from '../../..';
import JourneyDetailV2 from '../../../indexV2';

export const Diagram = props => {
  const { prefix, versionId, isBlastCampaign = false, isShowDrawer } = props;

  if (isShowDrawer) {
    return (
      <JourneyDetailV2
        page={VERSION_HISTORY}
        versionId={versionId}
        moduleConfig={{
          key: prefix,
        }}
        isDisplayDiagramBlastCampaign={isBlastCampaign}
      />
    );
  }

  return (
    <JourneyDetail
      page={VERSION_HISTORY}
      versionId={versionId}
      moduleConfig={{
        key: prefix,
      }}
      isDisplayDiagramBlastCampaign={isBlastCampaign}
    />
  );
};

Diagram.propTypes = {
  prefix: PropTypes.string,
  versionId: PropTypes.number,
  isShowDrawer: PropTypes.bool,
  isBlastCampaign: PropTypes.bool,
};

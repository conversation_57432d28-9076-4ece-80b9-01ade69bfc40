/* eslint-disable import/order */
// Libraries
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';

// Components
import { TabPanel } from '@xlab-team/ui-components';
import { Header } from './Header';
import JourneyDetail from '../..';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Utils
import { makeUrlPermisison } from 'utils/web/permission';
import { getCurrentAccessUserId } from 'utils/web/cookie';

// Constants
import APP from 'appConfig';
import { VERSION_HISTORY } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/constants';

// Styles
import { StyledTabs } from './styled';
import { WrapperDetail } from '../../VersionHistory2/Detail/styled';

const MAP_TITLE = {
  diagram: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Diagram'),
  detail: getTranslateMessage(TRANSLATE_KEY._TAB_DETAIL, 'Details'),
};

const ScheduleHistory = props => {
  const { processId } = useParams();
  const { moduleConfig } = props;
  const [activeTab, setActiveTab] = useState('diagram');

  const onChangeActiveTab = tab => {
    setActiveTab(tab);
  };

  return (
    <WrapperDetail>
      <Header processId={processId} />
      <StyledTabs
        // noBorderBottom
        isRenderChildren
        activeTab={activeTab}
        onChange={onChangeActiveTab}
        className="tab-box-shadow"
      >
        <TabPanel marginTop label={MAP_TITLE.diagram} eventKey="diagram">
          <JourneyDetail
            page={VERSION_HISTORY}
            versionId={13}
            moduleConfig={{
              key: moduleConfig.key,
            }}
          />
        </TabPanel>
        <TabPanel marginTop label={MAP_TITLE.detail} eventKey="detail">
          Detail
        </TabPanel>
      </StyledTabs>
    </WrapperDetail>
  );
};

ScheduleHistory.propTypes = {
  moduleConfig: PropTypes.object,
};

export default ScheduleHistory;

import styled from 'styled-components';

export const StyleWrapper = styled.div`
  /* flex-grow: 1; */
  /* margin-top: 8px; */
  position: relative;
  display: flex;
  /* height: 100%; */
  /* height: 100vh; */
  height: ${props => (props.isShowDrawer ? '100%' : 'calc(100vh - 130px)')};
  width: cacl(100% + 12px);
  font-size: 13px;
  overflow: hidden;
  flex-wrap: no-wrap;
  box-shadow: -1px 0 3px 0 rgb(0 0 0 / 10%);

  #automation {
    background-color: inherit !important;
    width: 100%;
  }
`;

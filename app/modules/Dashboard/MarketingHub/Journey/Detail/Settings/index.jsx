/* eslint-disable react/prop-types */
import React, { memo, useState } from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { createStructuredSelector } from 'reselect';

import CreateDesgin from '../../Create/Desgin';
import { StyleWrapper } from './styles';
import { makeSelectStoryDetailDomainMain } from '../selectors';
import StoriesCreateBlastCampaign from '../../Create/BlastCampaign/Design';
import { getModuleConfigBlast } from '../utils';

function SettingPage(props) {
  const {
    main,
    versionId,
    page,
    isViewMode,
    typeResume,
    isBlastCampaign = false,
    isDisplayDiagramBlastCampaign,
    moduleConfig,
    channelId,
    activeId,
    isShowDrawer,
    keyResume,
  } = props;

  const { activeRow, rootJourneyDetail } = main;
  const [design, setDesign] = useState(props.design || 'preview');

  const moduleConfigCreate = getModuleConfigBlast({
    moduleConfig,
    isBlastCampaign,
    isDisplayDiagramBlastCampaign,
  });

  // khi isDisplayDiagramBlastCampaign = true, thì BlastCampaign sử dụng luồng của JourneyCreate để lấy hiển thị (do cần FlowChart);
  const onChangeDesign = value => {
    setDesign(value);
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/Settings/index.jsx">
      {isBlastCampaign && !isDisplayDiagramBlastCampaign ? (
        <StoriesCreateBlastCampaign
          design={design}
          moduleConfig={moduleConfigCreate}
          rootJourneyDetail={rootJourneyDetail}
          activeRow={activeRow}
          onChangeDesign={onChangeDesign}
          versionId={versionId}
          page={page}
          typeResume={typeResume}
          isViewMode={isViewMode}
          blastCampaign={main.blastCampaign}
          channelId={channelId}
          activeId={activeId}
          isShowDrawer={isShowDrawer}
          isJourneyV2={props.isJourneyV2}
        />
      ) : (
        <StyleWrapper className="style-wrapper" isShowDrawer={isShowDrawer}>
          <CreateDesgin
            design={design}
            rootJourneyDetail={rootJourneyDetail}
            activeRow={activeRow}
            onChangeDesign={onChangeDesign}
            moduleConfig={moduleConfigCreate}
            versionId={versionId}
            page={page}
            typeResume={typeResume}
            isViewMode={isViewMode}
            isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
            activeId={activeId}
            isShowDrawer={isShowDrawer}
            isJourneyV2={props.isJourneyV2}
            keyResume={keyResume}
          />
        </StyleWrapper>
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectStoryDetailDomainMain(),
});

const withConnect = connect(
  mapStateToProps,
  memo,
);

export default compose(withConnect)(SettingPage);

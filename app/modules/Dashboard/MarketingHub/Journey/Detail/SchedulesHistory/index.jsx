/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import CalendarSelection from 'components/Templates/CalendarSelection';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import JourneyServices from 'services/Journey';
import ModalDownload from 'containers/modals/ModalDownload';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListStoryMapDataFooter,
  makeSelectListSchedulesHistoryColumn,
  makeSelectListSchedulesHistoryDomainMain,
  makeSelectListSchedulesHistoryDomainMainData,
  makeSelectListSchedulesHistoryFilter,
  makeSelectListSchedulesHistoryTable,
  makeSelectListVariantDateRange,
  makeSelectListSchedulesHistoryDefaultMetrics,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import { MODULE_CONFIG } from './config';
import Filters from '../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import {
  addNotification,
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import {
  PerformanceChartVariant,
  TableRelative,
  TableWrapper,
} from '../../Variants/styles';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import Search from '../../../../../../containers/Search';
import ControlTable from './ControlTable';
import ModalDeleteMulti from '../../../../../../containers/modals/ModalDeleteMulti';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import AddComponent from './AddComponent';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
import { safeParse } from '../../../../../../utils/common';
import { handleProcessType } from './utils';
import useToggle from '../../../../../../hooks/useToggle';
import ModalExport from '../../../../../../containers/modals/ModalExport';

const MAP_TITLE = {
  actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  scheduleId: getTranslateMessage(TRANSLATE_KEY._SCHEDULE_ID, 'Schedule ID'),
};
const layoutStyle = {
  overflow: 'hidden',
  height: 'calc(100vh - 60px)',
};

// const defaultMetric = ['impression', 'click'];

export function SchedulesHistory(props) {
  const {
    main,
    table,
    filter,
    column,
    data,
    channelId,
    moduleConfig,
    moduleConfigColumn,
    dateRange,
    activeRow,
  } = props;
  const storyId = safeParse(props.storyId, '');
  const [isOpenModalResume, toggleModalResume] = useToggle(false);
  const { isInitDone, isFistLoadTable } = main;
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);

  const dataHandle = handleProcessType(data);

  useEffect(() => {
    if (storyId !== '') props.init({ storyId, channelId });

    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      props.resetIsLoadingModify();
      // setDateValue(state.value);
    };
  }, [storyId]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(dataIn);
        break;
      }
      // case 'ACTION_TABLE_CHANGE_STATUS': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      case 'ON_CHANGE_DATERANGE': {
        // console.log('ON_CHANGE_DATERANGE', dataIn, dateRange.value);
        props.onChangeDateRange(dataIn);
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dateRange);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics(dataIn);
        props.onSaveDefaultMetrics(dataIn);
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'RESUME_TRIGGER': {
        toggleModalResume();
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);
  const columnsExport = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);
  const mapParamsDeleteFn = useCallback(
    (oldData, newData) => ({
      data: {
        totalSelected: oldData.isSelectedAll
          ? oldData.totalRecord
          : newData.accepted.length,
        objectIds: newData.accepted.map(each => each.story_id),
        isCheckUsedStatus: 0,
        filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
      },
    }),
    [],
  );

  // console.log('render Campaigns', channelId, moduleConfigColumn);
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/SchedulesHistory/index.jsx">
      {isInitDone ? (
        <>
          <LayoutContent
            padding="0 15px 15px 15px"
            margin="0"
            style={layoutStyle}
          >
            <LayoutContentLoading isLoading={!isInitDone}>
              <TableRelative>
                <TableWrapper>
                  <TableContainer
                    columnActive={column.columnObj}
                    table={table}
                    isLoading={main.isLoading}
                    moduleConfig={moduleConfig}
                    selectedIds={table.selectedIds}
                    selectedRows={table.selectedRows}
                    isSelectedAll={table.isSelectedAll}
                    isSelectedAllPage={table.isSelectedAllPage}
                    columns={tableColumns}
                    data={dataHandle}
                    callback={callback}
                    noDataText="No data"
                    ComponentControlTable={ControlTable}
                    menuCode={MODULE_CONFIG.menuCode}
                    widthFirstColumns={198} // 150 + 48
                    initialWidthColumns={478} // 150 + 280 + 48
                  >
                    <>
                      <Filters
                        use="list"
                        rules={filter.rules}
                        moduleConfig={moduleConfig}
                        filterActive={filter.config.filterObj}
                        filterCustom={filter.config.library.filterCustom}
                        libraryFilters={filter.config.library.filters}
                        groups={main.groupAttributes.groupsFilter}
                        isFilter={filter.config.design.isFilter}
                        isLoading={filter.config.isLoading}
                        // AddComponent={AddComponent}
                      />
                      <WrapActionTable
                        show={!filter.config.design.isFilter}
                        className="p-x-4"
                      >
                        <div className="actionTable__inner">
                          <Search
                            moduleConfig={moduleConfig}
                            config={{
                              objectType: moduleConfig.objectType,
                              limit: 10,
                              page: 1,
                              search: '',
                              columns: ['schedule_id'],
                              storyId,
                              propertyCode: 'suggestion_with_process_id',
                              filters: {
                                OR: [
                                  {
                                    AND: [
                                      {
                                        type: 1,
                                        column: 'story_id',
                                        data_type: 'number',
                                        operator: 'matches',
                                        value: [storyId],
                                      },
                                    ],
                                  },
                                ],
                              },
                            }}
                            suggestionType="suggestionMultilang"
                            moduleLabel={MAP_TITLE.scheduleId}
                            isAddFilter
                          />
                          <ModifyColumn
                            sort={table.sort}
                            moduleConfig={moduleConfigColumn}
                            columnActive={column.columnObj}
                            columnCustom={column.library.columnCustom}
                            libraryColumns={column.library.columns}
                            defaults={moduleConfigColumn.columnsDefault}
                            defaultSortColumns={
                              moduleConfigColumn.defaultSortColumns
                            }
                            columns={column.columnObj.columns.columnsAlias}
                            groups={main.groupAttributes.groups}
                            isLoading={column.isLoading}
                            isLoadingInfoProperties={
                              // Using for loading modify column info-properties
                              main.isLoadingModifyColumn
                            }
                          />
                          {/* <IconButton
                            iconName="file_download"
                            size="24px"
                            // disabled={table.paging.totalRecord === 0}
                            disabled
                            isVertical
                            onClick={
                              // table.paging.totalRecord === 0
                              //   ? () => {}
                              //   : toggleModalDownload
                              () => {}
                            }
                          >
                            {MAP_TITLE.actExport.toUpperCase()}
                          </IconButton> */}
                        </div>
                      </WrapActionTable>
                    </>
                  </TableContainer>
                </TableWrapper>
              </TableRelative>
            </LayoutContentLoading>
          </LayoutContent>
        </>
      ) : (
        <div style={{ position: 'relative', height: 'calc(100vh - 120px)' }}>
          <Loading isLoading={!isInitDone} />
        </div>
      )}
      {/* <ModalExport
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        object_type="SCHEDULE_HISTORY"
        exportType="ScheduleHistory"
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        filters={filter}
        storyId={storyId}
        objectName="Journey"
        // itemTypeId={itemTypeId}
        // itemTypeName={activeRow.item_type_name}
        columns={column.columnObj.columns.columnsAlias}
      /> */}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListSchedulesHistoryDomainMain(),
  table: makeSelectListSchedulesHistoryTable(),
  filter: makeSelectListSchedulesHistoryFilter(),
  column: makeSelectListSchedulesHistoryColumn(),
  data: makeSelectListSchedulesHistoryDomainMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  mapDataFooter: makeSelectListStoryMapDataFooter(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    resetIsLoadingModify: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(SchedulesHistory);

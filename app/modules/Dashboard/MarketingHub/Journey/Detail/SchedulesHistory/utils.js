// import { getLabelStoryStatus } from '../../../../../utils/web/processStatus';
import {
  getLabelCampaignStatus,
  getLabelStoryStatus,
} from 'utils/web/processStatus';
import {
  CellArray,
  CellCampaingStatus,
  CellDate,
  CellJourneyStatus,
  CellMainVariant,
  CellNumber,
  CellStoryStatus,
  CellText,
  CellToggle,
  CellToggleAPI,
  CellToggleWithJourney,
  CellToggleWithStyle,
  CellArrayObject,
} from 'containers/Table/Cell';
// import { getSegmentTypeLabel } from '../../../../../services/Abstract.data';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
import { initNumberId } from 'utils/web/portalSetting';
import { getTranslateMessage } from 'containers/Translate/util';
// import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from 'messages/constant';
import APP from 'appConfig';
import { MENU_CODE } from 'utils/web/permission';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import CellMainScheduleHistory from '../../../../../../containers/Table/Cell/CellMainScheduleHistory';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  // console.log(list);
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.schedule_id,
      user_type: userType[tmp.user_type] || tmp.user_type,
      start_time: tmp.start_date,
      end_time: tmp.end_date,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  // console.log(data);
  return data;
}

const userType = {
  user: 'Visitor',
  customer: 'Customer',
};

export const handleProcessType = data => {
  const dataAdjustArray = data.map(element => {
    let adjustProcessType;
    // adjust process_type
    switch (element.process_type) {
      case 'EVENT_BASED':
        adjustProcessType = { process_type: 'Action-based Trigger' };
        break;
      case 'SCHEDULED':
        adjustProcessType = { process_type: 'Scheduled Trigger' };
        break;
    }
    if (adjustProcessType) {
      return { ...element, ...adjustProcessType };
    }
    return element;
  });
  return dataAdjustArray;
};

export function buildTableColumns(columnsActive) {
  const columns = [];

  columnsActive.forEach(property => {
    if (property) {
      const { dataType, displayFormat } = property;
      const disableSortBy = parseInt(property.isSort) !== 1;
      const column = {
        Header: property.label,
        id: property.value,
        accessor: property.value,
        Cell: CellText,
        disableSortBy,
        mapPropertyValues: new Map(property.mapPropertyValues),
        displayFormat,
        minWidth: COLUMNS_WIDTH.DEFAULT,
        className: `${dataType} ${property.value}`,
        Footer: property.type === 2 ? '0' : '',
      };
      column.Cell =
        MAP_CELL_BY_VALUE[property.value] ||
        MAP_CELL_BY_DATATYPE[dataType] ||
        CellText;

      if (['journey_version', 'total_audience'].includes(property.value)) {
        column.displayFormat = initNumberId();
      }
      if (property.value === 'schedule_id') {
        column.Cell = CellMainScheduleHistory;
      }
      columns.push(column);
    }
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {
  // campaign_id: CellText,
  c_user_id: CellText,
  u_user_id: CellText,
  campaign_status: CellCampaingStatus,
  story_status: CellJourneyStatus,
};

export function getModuleConfig(moduleConfig, storyId, objectType, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = storyId;
  newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};

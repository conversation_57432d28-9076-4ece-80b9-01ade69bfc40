/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable import/order */
// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { get, keyBy } from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Components
import NodesList from './NodesList';
import ActionsHistory from './ListProcess';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Styles
import { AntsomiTabs, StyledTabs } from './styled';
import { WrapperDetail } from '../VersionHistory2/Detail/styled';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { init, updateValue, reset } from 'redux/actions';
import reducer from './reducer';
import saga from './saga';

// Utils
import { getPrefixCreate } from '../VersionHistory2/Detail/utils';
import { getBreadcrums } from '../utils';
import queryString from 'query-string';
import { PREFIX } from './config';
import {
  makeSelectDateRangeAction,
  makeSelectEndAction,
  makeSelectStartAction,
} from './selectors';

const MAP_TITLE = {
  diagram: getTranslateMessage(TRANSLATE_KEY._TAB_DIAGRAM, 'Diagram'),
  processes: getTranslateMessage(TRANSLATE_KEY._TAB_DETAIL, 'Processes'),
  nodes: getTranslateMessage(TRANSLATE_KEY._TAB_NODES, 'Nodes'),
  tabTitle: `${getTranslateMessage(
    TRANSLATE_KEY._TAB_JOURNEY,
    'Journeys',
  )} - ${getTranslateMessage(
    TRANSLATE_KEY._TITL_TAG_SCHEDULE_HISTORY_DETAIL,
    'Schedule History Detail',
  )}`,
};

const ActionHistoryPage = props => {
  const {
    storyId,
    channelId,
    versionId,
    activeRow,
    isShowDrawer,
    initScheduleDetail,
    resetScheduleDetail,
    dateRange,
  } = props;
  const params = useParams();
  const { start_date, end_date } = activeRow;

  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });
  useEffect(() => {
    // console.log({ props, story_id: journeyDetail.story_id, scheduleId });
    if (storyId) {
      // console.log('useEffect');
      initScheduleDetail({
        startTime: start_date,
        endTime: end_date,
      });
    }

    return () => {
      resetScheduleDetail();
    };
  }, [storyId]);

  const [activeTab, setActiveTab] = useState('processes');

  const onChangeActiveTab = tab => {
    setActiveTab(tab);
  };
  // console.log({ props });
  return (
    <>
      <WrapperDetail isShowDrawer={isShowDrawer}>
        <AntsomiTabs
          items={[
            {
              children: (
                <ActionsHistory
                  versionId={+versionId}
                  storyId={storyId}
                  channelId={channelId}
                  isShowDrawer={isShowDrawer}
                  isShowButtonAdjust={false}
                  activeRow={activeRow}
                  activeTab={activeTab}
                  startTime={start_date}
                  endTime={end_date}
                  dateRange={dateRange}
                />
              ),
              key: 'processes',
              label: MAP_TITLE.processes,
            },
            {
              children: (
                <NodesList
                  versionId={+versionId}
                  storyId={storyId}
                  channelId={channelId}
                  isShowDrawer={isShowDrawer}
                  activeRow={activeRow}
                  activeTab={activeTab}
                  startTime={start_date}
                  endTime={end_date}
                  dateRange={dateRange}
                />
              ),
              key: 'nodes',
              label: MAP_TITLE.nodes,
            },
          ]}
          onTabClick={onChangeActiveTab}
        />
      </WrapperDetail>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  endTime: makeSelectEndAction(),
  startTime: makeSelectStartAction(),
  dateRange: makeSelectDateRangeAction(),
});

const mapDispatchToProps = dispatch => {
  return {
    initScheduleDetail: params => {
      dispatch(init(PREFIX, params));
    },
    resetScheduleDetail: params => {
      dispatch(reset(PREFIX, params));
    },
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ActionHistoryPage);

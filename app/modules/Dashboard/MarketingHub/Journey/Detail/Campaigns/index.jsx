/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import CalendarSelection from 'components/Templates/CalendarSelection';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import { Divider } from 'components/Atoms/Divider';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import JourneyServices from 'services/Journey';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListCampaignDateRange,
  makeSelectListCampaignDefaultMetrics,
  makeSelectListStoryCampaignColumn,
  makeSelectListStoryCampaignDomainMain,
  makeSelectListStoryCampaignDomainMainData,
  makeSelectListStoryCampaignFilter,
  makeSelectListStoryCampaignTable,
  makeSelectListStoryMapDataFooter,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import { MODULE_CONFIG } from './config';
import Filters from '../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import {
  addNotification,
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import {
  PerformanceChartCampaign,
  TableRelative,
  TableWrapper,
} from './styles';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import Search from '../../../../../../containers/Search';
import ControlTable from './ControlTable';
import ModalDeleteMulti from '../../../../../../containers/modals/ModalDeleteMulti';
import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import AddComponent from './AddComponent';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
import { MAP_STORY_STATUS } from '../../../../../../utils/web/processStatus';
import { useLocalStorage } from '../../../../../../utils/web/useHooks';
import { isProduction, safeParse } from '../../../../../../utils/common';
import ModalDownload from '../../../../../../containers/modals/ModalDownload';
import useToggle from '../../../../../../hooks/useToggle';
import { makeSelectActiveRow } from '../selectors';
import { configToAPISuggestion } from '../../utils';
import ModalExport from '../../../../../../containers/modals/ModalExport';

const MAP_TITLE = {
  titlStories: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_CAMPAIGN,
    'Campaign',
  ),
  // actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  itemNameStories: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'campaign(ies)',
  ),
};
const layoutStyle = {
  overflow: 'hidden',
  height: 'calc(100vh - 105px)',
  zIndex: 1,
  position: 'relative',
};

// const defaultMetric = ['impression', 'click'];

export function Campaign(props) {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isLoadingSelectionTime, setIsLoadingSelectionTime] = useState(true);
  // const [showChart, setShowChart] = useState(false);

  const [configFilter, setConfigFilter] = useState({});

  const [showChart, setShowChart] = useLocalStorage(
    'show-chart-list-story-campaign',
    true,
  );

  const {
    main,
    table,
    filter,
    column,
    data,
    channelId,
    moduleConfig,
    moduleConfigColumn,
    dateRange,
    defaultMetrics,
    activeRow,
  } = props;

  const storyId = safeParse(props.storyId, '');

  const { isInitDone, isFistLoadTable } = main;

  useEffect(() => {
    if (storyId !== '') props.init({ storyId, channelId });
    const channelActive = {
      value: storyId,
    };
    const filtersBody = {
      objectType: MODULE_CONFIG.objectType,
      filtersBody: {
        column: 'story_id',
        data_type: 'number',
        operator: 'equals',
        value: storyId,
      },
      isGetOnlySuggestParams: true,
      isFilters: channelActive.value !== 0 && true,
    };
    setConfigFilter(configToAPISuggestion(filtersBody));
    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      props.resetIsLoadingModify();

      // setDateValue(state.value);
    };
  }, [storyId]);

  useEffect(() => () => setIsLoadingSelectionTime(false), []);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(dataIn);
        break;
      }
      // case 'ACTION_TABLE_CHANGE_STATUS': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      case 'ON_CHANGE_DATERANGE': {
        // console.log('ON_CHANGE_DATERANGE', dataIn, dateRange.value);
        props.onChangeDateRange(dataIn);
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dateRange);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics(dataIn);
        props.onSaveDefaultMetrics(dataIn);
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'SET_LOADING_SELECTION_CHART': {
        setIsLoadingSelectionTime(dataIn);
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const mapParamsDeleteFn = useCallback(
    (oldData, newData) => ({
      data: {
        totalSelected: oldData.isSelectedAll
          ? oldData.totalRecord
          : newData.accepted.length,
        objectIds: newData.accepted.map(each => each.story_id),
        isCheckUsedStatus: 0,
        filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
      },
    }),
    [],
  );

  const initDateRangeData = useMemo(() => dateRange, [isInitDone]);
  // console.log('render Campaigns', channelId, moduleConfigColumn);
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/Campaigns/index.jsx">
      {isInitDone ? (
        <>
          <NavBarRightContent
            right="14px"
            className="p-right-4"
            style={{ width: props.rightTabWidth || 'auto', top: '-50px' }}
          >
            <CalendarSelection
              isLoading={isLoadingSelectionTime}
              initData={initDateRangeData}
              callback={callback}
              maxTotalWidth={props.rightTabWidth ? props.rightTabWidth - 16 : 0}
            />
          </NavBarRightContent>
          <LayoutContent padding="0" margin="0" style={layoutStyle}>
            <LayoutContentLoading isLoading={!isInitDone}>
              <PerformanceChartCampaign
                defaultMetric={defaultMetrics}
                dateRange={dateRange.value}
                showChart={showChart}
                listSelection={main.groupAttributes.metricsPerformanceChart}
                stylesChart={{ width: '100%', height: 200 }}
                ServiceFn={JourneyServices.campaigns.chart.getListV2_1}
                storyId={Number(storyId)}
                storyType={Number(channelId)}
                // style={{ marginTop: `${showChart ? '0.5rem' : '0'}` }}
                callback={callback}
                rules={filter.rules}
                objectType={MODULE_CONFIG.objectType}
              />

              <TableRelative>
                <TableWrapper>
                  <TableContainer
                    columnActive={column.columnObj}
                    table={table}
                    isLoading={main.isLoading}
                    moduleConfig={moduleConfig}
                    selectedIds={table.selectedIds}
                    selectedRows={table.selectedRows}
                    isSelectedAll={table.isSelectedAll}
                    isSelectedAllPage={table.isSelectedAllPage}
                    columns={tableColumns}
                    data={data}
                    callback={callback}
                    noDataText="No data"
                    resizeColName="campaign_name"
                    ComponentControlTable={ControlTable}
                    menuCode={MODULE_CONFIG.menuCode}
                    widthFirstColumns={198} // 150 + 48
                    initialWidthColumns={478} // 150 + 280 + 48
                    // widthFirstColumns={124}
                    // initialWidthColumns={width > 1600 ? 291 : 218}
                    // initialWidthColumns={274}
                    isShowFooter
                    mapDataFooter={props.mapDataFooter}
                  >
                    <>
                      <Filters
                        use="list"
                        rules={filter.rules}
                        moduleConfig={moduleConfig}
                        filterActive={filter.config.filterObj}
                        filterCustom={filter.config.library.filterCustom}
                        libraryFilters={filter.config.library.filters}
                        groups={main.groupAttributes.groupsFilter}
                        isFilter={filter.config.design.isFilter}
                        isLoading={filter.config.isLoading}
                        // AddComponent={AddComponent}
                      />
                      <WrapActionTable
                        show={!filter.config.design.isFilter}
                        className="p-x-4"
                      >
                        <div className="actionTable__inner">
                          <Search
                            moduleConfig={moduleConfig}
                            moduleLabel={MAP_TITLE.titlStories}
                            config={configFilter}
                            suggestionType="suggestionMultilang"
                            isAddFilter
                            isGoTo={false}
                          />
                          <ModifyColumn
                            sort={table.sort}
                            moduleConfig={moduleConfigColumn}
                            columnActive={column.columnObj}
                            columnCustom={column.library.columnCustom}
                            libraryColumns={column.library.columns}
                            defaults={moduleConfigColumn.columnsDefault}
                            defaultSortColumns={
                              moduleConfigColumn.defaultSortColumns
                            }
                            columns={column.columnObj.columns.columnsAlias}
                            groups={main.groupAttributes.groups}
                            isLoading={column.isLoading}
                            isLoadingInfoProperties={
                              // Using for loading modify column info-properties
                              main.isLoadingModifyColumn
                            }
                          />
                          <IconButton
                            iconName="file_download"
                            size="24px"
                            onClick={
                              table.paging.totalRecord === 0
                                ? () => {}
                                : toggleModalDownload
                            }
                            disabled={table.paging.totalRecord === 0}
                            isVertical
                            // disabled={false}
                          >
                            {MAP_TITLE.actExport.toUpperCase()}
                          </IconButton>
                        </div>
                        <Divider className="m-x-1" />
                        <IconButton
                          iconName={showChart ? 'arrow-up' : 'arrow-down'}
                          size="24px"
                          onClick={() => setShowChart(prev => !prev)}
                          // disabled
                        />
                      </WrapActionTable>
                    </>
                  </TableContainer>
                </TableWrapper>
              </TableRelative>
            </LayoutContentLoading>
          </LayoutContent>
          {isOpenModalDelete && (
            <ModalDeleteMulti
              activeRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              totalRecord={table.paging.totalRecord}
              rules={filter.rules}
              label={getTranslateMessage(
                TRANSLATE_KEY._BOX_TITL_DELETE_STORY,
                'Delete stories',
              )}
              placeHolderName=""
              moduleName={MAP_TITLE.titlStories}
              ObjectServicesFn={JourneyServices.data.deleteWithCondition}
              criteriaFn={each =>
                each.original_status === MAP_STORY_STATUS.DESIGNED
              }
              isOpenModal={isOpenModalDelete}
              setOpenModal={setIsOpenModalDelete}
              fetchData={props.fetchData}
              mapParamsFn={mapParamsDeleteFn}
            />
          )}
          {/* <ModalDownload
            isOpen={isOpenModalDownload}
            toggle={toggleModalDownload}
            paging={table.paging}
            sort={table.sort}
            filters={filter}
            itemTypeId={storyId}
            itemTypeName="campaigns"
            columns={column.columnObj.columns.columnsAlias}
            // properties={campaignscolumn.columnObj.columns.columnsAlias}
            customName={`data_campaigns_${activeRow.story_name &&
              activeRow.story_name.toLowerCase()}_${new Date().toLocaleString()}`}
            feService={JourneyServices.data.download}
            otherData={{
              story_type: channelId,
              object_type: 'CAMP_STORIES',
              story_id: storyId,
              // columns: column.columnObj.columns.columnsAlias.filter(each => {
              //   const { map } = main.groupAttributes;
              //   return map[each] && map[each].type !== 2;
              // }),
              columns: (function() {
                const columns = [];
                column.columnObj.columns.columnsAlias.forEach(each => {
                  const { map } = main.groupAttributes;
                  if (map[each] && map[each].type !== 2) {
                    columns.push(each);
                  }
                });
                if (!columns.includes('campaign_id')) {
                  columns.push('campaign_id');
                }
                return columns;
              })(),
              perf_columns: column.columnObj.columns.columnsAlias.filter(
                item => {
                  const { map } = main.groupAttributes;
                  return map[item] && map[item].type === 2;
                },
              ),
              durations: dateRange.value,
            }}
          /> */}
          <ModalExport
            isOpen={isOpenModalDownload}
            toggle={toggleModalDownload}
            paging={table.paging}
            sort={table.sort}
            sortDefault="utime"
            filters={filter}
            itemTypeId={storyId}
            itemTypeName="campaigns"
            perf_columns={{ column, main }}
            storyId={Number(storyId)}
            channelId={Number(channelId)}
            durations={dateRange.value}
            // columns={column.columnObj.columns.columnsAlias}
            // properties={column.columnObj.columns.columnsAlias}
            customName={`data_campaigns_${activeRow.story_name &&
              activeRow.story_name.toLowerCase()}_${new Date().toLocaleString()}`}
            feService={JourneyServices.data.download}
            object_type="CAMP_STORIES"
            objectName="Campaign"
            columns={{ column, main, type: 'campaign_id' }}
          />
        </>
      ) : (
        <div style={{ position: 'relative', height: 'calc(100vh - 120px)' }}>
          <Loading isLoading={!isInitDone} />
        </div>
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListStoryCampaignDomainMain(),
  table: makeSelectListStoryCampaignTable(),
  filter: makeSelectListStoryCampaignFilter(),
  column: makeSelectListStoryCampaignColumn(),
  data: makeSelectListStoryCampaignDomainMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  mapDataFooter: makeSelectListStoryMapDataFooter(),
  dateRange: makeSelectListCampaignDateRange(),
  defaultMetrics: makeSelectListCampaignDefaultMetrics(),
  activeRow: makeSelectActiveRow(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeDefaultMetrics: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    onSaveDefaultMetrics: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    resetIsLoadingModify: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(Campaign);

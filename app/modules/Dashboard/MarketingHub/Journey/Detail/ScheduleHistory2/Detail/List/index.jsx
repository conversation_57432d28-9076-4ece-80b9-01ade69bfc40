/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React, { useEffect, useMemo, useState } from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import { useHistory, useParams, withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import CloseIcon from '@material-ui/icons/Close';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UIDrawer,
} from '@xlab-team/ui-components';

import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import JourneyServices from 'services/Journey';
import { init, getList, reset, update, updateValue } from 'redux/actions';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import Filters from 'containers/Filters';
import TableContainer from 'containers/Table';
import Search from 'containers/Search';
import ModifyColumn from 'containers/ModifyColumn';
import ModalConfirm from 'containers/modals/ModalConfirm';
import { WrapActionTable } from 'containers/Table/styles';
import ErrorBoundary from 'components/common/ErrorBoundary';
import LayoutContent, {
  LayoutContentLoading,
  StyledWrapperLoading,
} from 'components/Templates/LayoutContent';
import { Divider } from 'components/Atoms/Divider';
import { VERSION_HISTORY } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/constants';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import CalendarSelection from 'components/Templates/CalendarSelection';
import queryString from 'query-string';
import DestinationServices from 'services/Destination';
import ControlTable from './ControlTable';
import reducer from './reducer';
import saga from './saga';

import {
  makeSelectListActionHistoryDomainMain,
  makeSelectListActionHistoryTable,
  makeSelectListActionHistoryFilter,
  makeSelectListActionHistoryColumn,
  makeSelectListActionHistoryDomainMainData,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
  makeSelectListActionsHistoryDefaultMetrics,
  makeSelectListActionCalendarView,
} from './selectors';
import JourneyDetail from '../../..';

import { TableWrapper, TableRelative } from './styles';
import { MODULE_CONFIG } from './config';
import useToggle from '../../../../../../../../hooks/useToggle';
import {
  ContainerCustomize,
  WrapperContentCustomize,
} from '../../../../Create/Content/Nodes/Destination/Testing/styles';
import { WapperStyleHeader } from '../../../../../../ApiHub/BusinessObject/Detail/Attributes/List/styles';
import { makeSelectMain, makeSelectScheduleDetail } from '../selectors';
import {
  ARRAY_OPTION_SCORECARD,
  DEFAULT_OPTIN,
  MAP_TITLE_RESUME,
} from '../../../ActionsHistory/ListProcess/utils';
import { PerformanceChartDelivery } from '../../../../../Destination/Detail/DeliveryLog/styles';
import { StyleWrapper } from '../../../../List/styles';
import { useLocalStorage } from '../../../../../../../../utils/web/useHooks';
import { getViewType } from '../../../../utils';
import ModalExport from '../../../../../../../../containers/modals/ModalExport';
import {
  DRAWER_DETAIL_DIMENSION,
  DrawerDetail,
  Tooltip,
} from '@antscorp/antsomi-ui';
import { StyledInfoOutlined } from '../../../ActionsHistory/ListProcess/styled';
import { DRAWER_NAME_CACHE } from '../../../../../../../../utils/constants';
import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../../../../config';
const MAP_TITLE = {
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
    actUpload: getTranslateMessage(TRANSLATE_KEY._ACT_UPLOAD, 'UPLOAD'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  },
};
const layoutStyle = {
  overflow: 'hidden',
  height: 'calc(100vh - 120px)',
};
function ActionPage(props) {
  const {
    main,
    table,
    filter,
    column,
    versionId,
    activeTab,
    dateRange,
    calendarView = {},
    startTime,
    endTime,
    mainDetail,
    // start = 0,
    // end = 0,
  } = props;
  const {
    isInitDone,
    moduleConfig,
    moduleConfigColumn,
    isFistLoadTable,
  } = main;
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalResumeConfirm, toggleModalResumeConfirm] = useToggle(false);
  const [showChart, setShowChart] = useLocalStorage(
    'show-chart-list-delivery-log',
    true,
  );
  const { channelId: channelIdParams } = useParams();

  const searchParams = new URLSearchParams(window.location.search);

  const channelId = searchParams.get('channelId');
  // const [isOpenModalDiagram, toggleModalDiagram] = useToggle(false);
  useEffect(() => {
    props.init({
      storyId: props.activeId,
      processId: props.processId,
      channelId: channelId || channelIdParams,
    });
    return () => {
      props.reset();
    };
  }, []);

  const tableColumns = useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);
  const keyViewType = `${MODULE_CONFIG.objectType}`;
  const [viewType, setViewType] = useState(getViewType(keyViewType));
  useEffect(() => {
    // khong fetch lan dau (do da dung props.init())
    if (!main.isLoading) {
      props.fetchData();
    }
  }, [viewType, calendarView.dateRange, activeTab]);
  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(data);
        break;
      }
      case 'EDIT_CELL_NAME': {
        props.editCellName(data);
        break;
      }
      case 'RESUME_TRIGGER': {
        // toggleModalResume();
        props.onChangeTypeResume({ type: 'whole_process', data });
        props.toggleModalDiagram();

        break;
      }
      case 'RESUME_FROM_NODE_TRIGGER': {
        // toggleModalResume();
        props.onChangeTypeResume({ type: 'from_node', data });
        props.toggleModalDiagram();

        break;
      }
      case 'RESUME_SPECIFIC_NODE': {
        // toggleModalResume();
        props.onChangeTypeResume({ type: 'specific_node', data });
        props.toggleModalDiagram();
        break;
      }
      case 'RESUME_CAMPAIGN': {
        toggleModalResumeConfirm();
        break;
      }
      case 'ACTION_RESUME': {
        props.toggleModalDiagram();
        // props.onResumeTrigger(data);
        break;
      }
      case 'ON_CHANGE_CALENDAR_DATERANGE': {
        props.onChangeCalendarDateRange(data);
        break;
      }
      case 'ON_CHANGE_DATERANGE': {
        // console.log('ON_CHANGE_DATERANGE', dataIn, dateRange.value);
        props.onChangeDateRange({ data, type: 'date_range' });
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dateRange);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics({ data, type: 'metric' });
        props.onSaveDefaultMetrics({ data, type: 'metric' });
        break;
      }
      case 'CHANGE_CHART_TYPE': {
        props.onChangeChartType({ data, type: 'chart_type' });
        break;
      }
      default:
        break;
    }
  };

  const renderTitleTooltip = () => {
    switch (main.typeResume) {
      case 'from_node':
        return 'This process will be resumed from the selected node using the latest version';

      case 'whole_process':
        return 'This process will be resumed using the latest version';

      case 'specific_node':
        return 'Using the latest version, only the selected node is resumed';

      default:
        return null;
    }
  };
  const onCloseDrawer = () => {
    props.updateOpenFullScreen(false);
    props.toggleModalDiagram();
  };
  const onConfirmResume = () => {
    props.onResumeTrigger({
      selectedRows: table.selectedRows || [],
      isBlastCampaign: true,
    });

    toggleModalResumeConfirm();
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/Action/index.jsx">
      {isInitDone ? (
        <>
          <NavBarRightContent
            style={{ zIndex: 998, top: '-50px' }}
            right="14px"
            className="p-right-4"
          >
            <CalendarSelection
              activeRows={{
                start_date: startTime || main.startTime,
                end_date: endTime || main.endTime,
              }}
              initData={dateRange}
              callback={callback}
              isLifeTime
              maxWidth="135px"
              hiddenLabel={props.isShowOverviewTab}
              isRefresh
              isLoading={main.isLoading}
            />
          </NavBarRightContent>
          <StyleWrapper>
            {/* <CustomHeader breadcrums={breadcrums} /> */}

            <LayoutContent
              padding="0 15px 15px 15px"
              margin="0"
              style={layoutStyle}
            >
              <LayoutContentLoading isLoading={!isInitDone}>
                <PerformanceChartDelivery
                  defaultMetric={mainDetail.defaultMetrics}
                  dateRange={dateRange.value}
                  showChart={showChart}
                  listSelection={main.groupAttributes.metricsPerformanceChart}
                  stylesChart={{ width: '100%', height: 200 }}
                  ServiceFn={
                    JourneyServices.schedulesHistory.getChartActionHistory
                  }
                  style={{ marginTop: 0, boxShadow: 'none', overflow: 'auto' }}
                  callback={callback}
                  rules={filter.rules}
                  optionScoreCard={ARRAY_OPTION_SCORECARD}
                  defaultOptionScoreCard={DEFAULT_OPTIN}
                  isScoreCard
                  storyType={channelId}
                  storyId={props.activeId}
                  // destination_id={destinationId}
                  // channelId={null} // Tương ứng story id
                  objectType="SCHEDULE_HISTORY"
                  dataRangeType={dateRange.selectionType}
                  processId={props.processId}
                  chartTypeDefault={mainDetail.chartType}
                  isShowButtonAdjust={false}
                />
                <TableRelative className="m-top-2">
                  <TableWrapper>
                    <TableContainer
                      // noCheckboxAction={props.isBlastCampaign}
                      moduleConfig={moduleConfig}
                      columns={tableColumns}
                      isLoading={main.isLoading}
                      selectedIds={table.selectedIds}
                      table={table}
                      noDataText="No data"
                      ComponentControlTable={ControlTable}
                      callback={callback}
                      selectedRows={table.selectedRows}
                      isSelectedAll={table.isSelectedAll}
                      isSelectedAllPage={table.isSelectedAllPage}
                      data={props.data}
                      isBlastCampaign={props.isBlastCampaign}
                    >
                      <>
                        {/* current not support */}
                        <Filters
                          use="list"
                          rules={filter.rules}
                          moduleConfig={moduleConfig}
                          filterActive={filter.config.filterObj}
                          filterCustom={filter.config.library.filterCustom}
                          libraryFilters={filter.config.library.filters}
                          groups={main.groupAttributes.groupsFilter}
                          isFilter={filter.config.design.isFilter}
                          isLoading={filter.config.isLoading}
                        />
                        <WrapActionTable
                          show={!filter.config.design.isFilter}
                          className="p-x-4"
                        >
                          <div className="actionTable__inner">
                            {/* <WrapperDisable>
                              <Search moduleConfig={moduleConfig} disabled />
                            </WrapperDisable> */}

                            <ModifyColumn
                              sort={table.sort}
                              moduleConfig={moduleConfigColumn}
                              columnActive={column.columnObj}
                              columnCustom={column.library.columnCustom}
                              libraryColumns={column.library.columns}
                              defaults={moduleConfigColumn.columnsDefault}
                              defaultSortColumns={
                                moduleConfigColumn.defaultSortColumns
                              }
                              columns={column.columnObj.columns.columnsAlias}
                              groups={main.groupAttributes.groups}
                              isLoading={column.isLoading}
                              isLoadingInfoProperties={
                                // Using for loading modify column info-properties
                                main.isLoadingModifyColumn
                              }
                            />
                            <IconButton
                              iconName="file_download"
                              size="24px"
                              onClick={
                                table.paging.totalRecord === 0
                                  ? () => {}
                                  : toggleModalDownload
                              }
                              disabled={table.paging.totalRecord === 0}
                              isVertical
                            >
                              {MAP_TITLE.action.actExport.toUpperCase()}
                            </IconButton>
                            <Divider className="m-x-1" />
                            <IconButton
                              iconName={showChart ? 'arrow-up' : 'arrow-down'}
                              size="24px"
                              onClick={() => setShowChart(prev => !prev)}
                              // disabled
                            />
                          </div>
                        </WrapActionTable>
                      </>
                    </TableContainer>
                  </TableWrapper>
                </TableRelative>
              </LayoutContentLoading>
            </LayoutContent>
          </StyleWrapper>
        </>
      ) : (
        <StyledWrapperLoading
          className="m-top-2"
          style={{ height: 'calc(100vh - 120px)' }}
        >
          <Loading isLoading={!isInitDone} />
        </StyledWrapperLoading>
      )}

      <DrawerDetail
        open={main.isOpenModalDiagram}
        onClose={props.toggleModalDiagram}
        menuProps={{
          show: false,
        }}
        maxWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
        minWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.minWidth}
        name={DRAWER_NAME_CACHE.DRAWER_SCHEDULE_RESUME}
        destroyOnClose
      >
        <WapperStyleHeader>
          <div style={{ color: '#000000', fontSize: '20px' }}>
            {MAP_TITLE_RESUME[main.typeResume]}
            <Tooltip title={renderTitleTooltip()} arrow placement="top">
              <StyledInfoOutlined style={{ marginLeft: ' 10px' }} />
            </Tooltip>
          </div>
          <div>
            <IconButton onClick={onCloseDrawer}>
              <CloseIcon style={{ color: '#666666' }} />
            </IconButton>
          </div>
        </WapperStyleHeader>
        <ContainerCustomize style={{ height: 'calc(100vh - 54px)' }}>
          <WrapperContentCustomize style={{ width: '100%' }}>
            <JourneyDetail
              typeResume={main.typeResume}
              page={VERSION_HISTORY}
              versionId={versionId}
              moduleConfig={{
                key: 'story-detail-action-history-detail',
              }}
              keyResume={moduleConfig.key}
            />
          </WrapperContentCustomize>
        </ContainerCustomize>
      </DrawerDetail>

      {/* Blast Campaign confirm modal */}
      <ModalConfirm
        title={getTranslateMessage(
          TRANSLATE_KEY._ACTION_RESUME_BLAST_POP_TITLE,
          'Resume processes',
        )}
        width={400}
        centered
        isOpen={isOpenModalResumeConfirm}
        toggle={toggleModalResumeConfirm}
        labelConfirmBtn={getTranslateMessage(
          TRANSLATE_KEY._ACT_TICKET_CONFIRM,
          'Confirm',
        )}
        onConfirm={onConfirmResume}
      >
        <div>
          <p style={{ margin: '6px 0 20px 0', lineHeight: '15px' }}>
            {getTranslateMessage(
              TRANSLATE_KEY._ACTION_RESUME_BLAST_POP_DES_1,
              'The system will automatically resend marketing messages to affected audiences using the latest content version & the exact version of the journey settings from the original send time.',
            )}
          </p>
          <p style={{ lineHeight: '15px' }}>
            {getTranslateMessage(
              TRANSLATE_KEY._ACTION_RESUME_BLAST_POP_DES_2,
              'Even if you have updated the journey filters, those changes will not affect this action. Are you sure you want to resume sending?',
            )}
          </p>
        </div>
      </ModalConfirm>

      {/* <UIDrawer
        isOpen={main.isOpenModalDiagram}
        // toggle={props.toggleModalDiagram}
      >
        <WrapperDrawer width="95vw">
          <WapperStyleHeader
            style={{ borderBottom: '1px solid #e6e6e6', fontSize: '16px' }}
          >
            <div
              style={{ color: '#000000', fontWeight: '700', fontSize: '20px' }}
              className="text"
            >
              {MAP_TITLE_RESUME[main.typeResume]}
              {main.typeResume === 'from_node' && (
                <div style={{ color: '#595959', fontSize: '13px' }}>
                  Using the latest version, the process is resumed from the
                  selected node
                </div>
              )}
              {main.typeResume === 'whole_process' && (
                <div style={{ color: '#595959', fontSize: '13px' }}>
                  Using the latest version
                </div>
              )}
              {main.typeResume === 'specific_node' && (
                <div style={{ color: '#595959', fontSize: '13px' }}>
                  Using the latest version, only the selected node is resumed
                </div>
              )}
            </div>
            <div>
              <IconButton onClick={() => props.toggleModalDiagram()}>
                <CloseIcon style={{ color: '#666666' }} />
              </IconButton>
            </div>
          </WapperStyleHeader>
          <ContainerCustomize style={{ height: 'calc(100vh - 54px)' }}>
            <WrapperContentCustomize style={{ width: '100%' }}>
              <JourneyDetail
                typeResume={main.typeResume}
                page={VERSION_HISTORY}
                versionId={versionId}
                moduleConfig={{
                  key: 'story-detail-action-history-detail',
                }}
              />
              {/* <ComputaitionList
                activeTab={main.activeTab}
                itemTypeId={itemTypeId}
              /> */}
      {/* </WrapperContentCustomize> */}
      {/* </ContainerCustomize> */}
      {/* </WrapperDrawer> */}
      {/* </UIDrawer> */}
      <ModalExport
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        storyId={parseInt(props.activeId)}
        durations={dateRange.value}
        object_type="SCHEDULE_HISTORY_PROCESS"
        exportType="Schedulehistory_AllProcess"
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        sortDefault="start_date"
        filters={filter}
        processId={props.processId}
        objectName="Journey"
        // itemTypeId={itemTypeId}
        // itemTypeName={activeRow.item_type_name}
        columns={column.columnObj.columns.columnsAlias}
      />
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListActionHistoryDomainMain(),
  table: makeSelectListActionHistoryTable(),
  filter: makeSelectListActionHistoryFilter(),
  column: makeSelectListActionHistoryColumn(),
  data: makeSelectListActionHistoryDomainMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  scheduleDetail: makeSelectScheduleDetail(),
  calendarView: makeSelectListActionCalendarView(),
  mainDetail: makeSelectMain(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },
    onResumeTrigger: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@RESUME_TRIGGER`, params));
    },
    onChangeTypeResume: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@RESUME_TYPE`, params));
    },
    toggleModalDiagram: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@TOGGLE_DIAGRAM`, params));
    },
    onChangeCalendarDateRange: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CALENDAR_DATE_RANGE`, params),
      );
    },
    onSaveDateRange: params => {
      dispatch(update(`story-detail-schedule-history@@DATE_RANGE`, params));
    },
    onChangeDateRange: params => {
      dispatch(
        updateValue(`story-detail-schedule-history@@DATE_RANGE`, params),
      );
    },
    onChangeDefaultMetrics: params => {
      dispatch(
        updateValue(`story-detail-schedule-history@@DEFAULT_METRICS`, params),
      );
    },
    onSaveDefaultMetrics: params => {
      dispatch(
        update(`story-detail-schedule-history@@DEFAULT_METRICS`, params),
      );
    },
    onChangeChartType: params => {
      dispatch(
        update(`story-detail-schedule-history@@UPDATE_CHART_TYPE`, params),
      );
    },
    updateOpenFullScreen: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_COMMON.key}@@TOGGLE_FULL_SCREEN@@`,
          params,
        ),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(ActionPage);

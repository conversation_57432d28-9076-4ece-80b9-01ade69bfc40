/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable import/order */
// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { get, keyBy } from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Components
import { TabPanel, UILoading } from '@xlab-team/ui-components';
import { Header } from './Header';
import ScheduleHistoryDetailList from './List';
import NodesList from './NodesList';
import { Diagram } from './Diagram';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Styles
import { AntsomiTabs, StyledTabs } from './styled';
import { WrapperDetail } from '../../VersionHistory2/Detail/styled';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { init, updateValue, reset } from 'redux/actions';
import reducer from './reducer';
import saga from './saga';
import { makeSelectActiveRow } from '../../selectors';
import {
  makeSelectDateRangeSchedule,
  makeSelectEnd,
  makeSelectScheduleDetail,
  makeSelectScheduleDetailLog,
  makeSelectStart,
} from './selectors';
import { makeSelectConfigureMainCreateFlattenNodes } from '../../../Create/selectors';
import { makeSelectJourneyChannelActive } from '../../../selectors';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Constants
import { PREFIX, PREFIX_DETAIL } from './config';

// Utils
import { getObjectPropSafely } from 'utils/common';
import { getPrefixCreate } from '../../VersionHistory2/Detail/utils';
import { getBreadcrums } from '../../utils';
import queryString from 'query-string';
import { Tabs } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  diagram: getTranslateMessage(TRANSLATE_KEY._TAB_DIAGRAM, 'Diagram'),
  processes: getTranslateMessage(TRANSLATE_KEY._TAB_DETAIL, 'Processes'),
  nodes: getTranslateMessage(TRANSLATE_KEY._TAB_NODES, 'Nodes'),
  tabTitle: `${getTranslateMessage(
    TRANSLATE_KEY._TAB_JOURNEY,
    'Journeys',
  )} - ${getTranslateMessage(
    TRANSLATE_KEY._TITL_TAG_SCHEDULE_HISTORY_DETAIL,
    'Schedule History Detail',
  )}`,
};

const ScheduleHistory = props => {
  const {
    initScheduleDetail,
    resetScheduleDetail,
    scheduleDetail,
    journeyDetail,
    flattenNodes,
    updateFlattenNodes,
    channelActive,
    startTime,
    endTime,
    scheduleDetailLog,
    isShowDrawer,
    dateRange,
  } = props;
  const params = useParams();

  const scheduleId = props.scheduleId || params.scheduleId;

  const versionId = props.versionId || params.versionId || 1;

  const activeId = props.activeId || params.activeId;
  const { start, end } = queryString.parse(window.location.search);

  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });

  const { is_blast_campaign } = journeyDetail;

  const isBlastCampaign = !!is_blast_campaign;

  const [activeTab, setActiveTab] = useState('diagram');

  useEffect(() => {
    // console.log({ props, story_id: journeyDetail.story_id, scheduleId });
    if (journeyDetail.story_id && scheduleId) {
      // console.log('useEffect');
      initScheduleDetail({
        storyId: journeyDetail.story_id,
        scheduleId,
        start,
        end,
        journeyDetail,
      });
    }

    return () => {
      resetScheduleDetail();
    };
  }, [scheduleId, journeyDetail.story_id]);

  useDeepCompareEffect(() => {
    const mapScheduleDetail = keyBy(scheduleDetail.data, 'actionId');
    const newFlattenNodes = flattenNodes.map(node => ({
      ...node,
      moreInfo: {
        ...node.moreInfo,
        audience: getObjectPropSafely(
          () =>
            `${get(mapScheduleDetail, `${node.nodeId}.total`, 0)
              .toString()
              .split('.')
              .map((item, idx) =>
                idx === 0
                  ? item.replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,')
                  : item,
              )
              .join('.')} - ${Math.round(
              (get(mapScheduleDetail, `${node.nodeId}.total`, 0) /
                scheduleDetailLog.totalAudience) *
                100 *
                100,
            ) / 100 || 0}%`,
          '0 - 0%',
        ),
      },
    }));
    updateFlattenNodes({
      flattenNodes: newFlattenNodes,
    });
  }, [scheduleDetail.data, flattenNodes]);

  const onChangeActiveTab = tab => {
    setActiveTab(tab);
  };
  // console.log({ props });

  const breadcrumbs = useMemo(
    () => getBreadcrums(channelActive, journeyDetail.story_name),
    [journeyDetail.story_name],
  );
  return (
    <>
      <UIHelmet title={MAP_TITLE.tabTitle} />
      {!isShowDrawer && <CustomHeader breadcrums={breadcrumbs} />}
      <WrapperDetail isShowDrawer={isShowDrawer}>
        <Header
          versionId={versionId}
          journeyInfo={journeyDetail}
          versionInfo={journeyDetail.story_version}
          processId={scheduleId}
          isShowDrawer={isShowDrawer}
        />
        <AntsomiTabs
          items={[
            {
              children: (
                <Diagram
                  isShowDrawer={isShowDrawer}
                  prefix={PREFIX_DETAIL}
                  versionId={+versionId}
                  isBlastCampaign={isBlastCampaign}
                />
              ),
              key: 'diagram',
              label: MAP_TITLE.diagram,
            },
            {
              children: (
                <ScheduleHistoryDetailList
                  activeTab={activeTab}
                  prefix={PREFIX_DETAIL}
                  versionId={+versionId}
                  isBlastCampaign={isBlastCampaign}
                  activeId={activeId}
                  processId={scheduleId}
                  startTime={start || startTime}
                  endTime={end || endTime}
                  dateRange={dateRange}
                  journeyDetail={journeyDetail}
                />
              ),
              key: 'processes',
              label: MAP_TITLE.processes,
            },
            {
              children: (
                <NodesList
                  activeTab={activeTab}
                  prefix={PREFIX_DETAIL}
                  versionId={+versionId}
                  isBlastCampaign={isBlastCampaign}
                  activeId={activeId}
                  processId={scheduleId}
                  startTime={start || startTime}
                  endTime={end || endTime}
                  dateRange={dateRange}
                  journeyDetail={journeyDetail}
                />
              ),
              key: 'nodes',
              label: MAP_TITLE.nodes,
            },
          ]}
          onTabClick={onChangeActiveTab}
        />
      </WrapperDetail>
    </>
  );
};

ScheduleHistory.propTypes = {
  journeyDetail: PropTypes.object,
  scheduleDetail: PropTypes.object,
  initScheduleDetail: PropTypes.func,
  resetScheduleDetail: PropTypes.func,
  scheduleDetailLog: PropTypes.object,
  flattenNodes: PropTypes.array,
  updateFlattenNodes: PropTypes.func,
  channelActive: PropTypes.object,
  startTime: PropTypes.any,
  endTime: PropTypes.any,
  dateRange: PropTypes.any,
};

const mapStateToProps = createStructuredSelector({
  journeyDetail: state =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: PREFIX_DETAIL },
    }),
  scheduleDetail: makeSelectScheduleDetail(),
  endTime: makeSelectEnd(),
  startTime: makeSelectStart(),
  flattenNodes: state =>
    makeSelectConfigureMainCreateFlattenNodes()(state, {
      moduleConfig: { key: getPrefixCreate(PREFIX_DETAIL) },
    }),
  channelActive: makeSelectJourneyChannelActive(),
  scheduleDetailLog: makeSelectScheduleDetailLog(),
  dateRange: makeSelectDateRangeSchedule(),
});

const mapDispatchToProps = dispatch => {
  return {
    initScheduleDetail: params => {
      dispatch(init(PREFIX, params));
    },
    resetScheduleDetail: params => {
      dispatch(reset(PREFIX, params));
    },
    updateFlattenNodes: payload => {
      dispatch(
        updateValue(
          `${getPrefixCreate(PREFIX_DETAIL)}@@FLATTEN_NODES@@`,
          payload,
        ),
      );
    },
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ScheduleHistory);

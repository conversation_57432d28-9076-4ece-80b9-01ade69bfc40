import { Button } from '@antscorp/antsomi-ui';
import styled from 'styled-components';

export const WrapperHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => !props.isShowDrawer && '12px 20px'};
  // border-bottom: 1px solid #c2cee0;
  background-color: #fff;
  position: relative; /* for box-shadow */
  box-shadow: 0 3px 3px 0 rgb(15 19 23 / 10%);
  p {
    margin: 0 !important;
  }

  .btn-trigger {
    padding-left: 0;
    .MuiSvgIcon-root {
      font-size: 24px;
      margin: 0 8px;
    }
  }
`;

export const LeftHeader = styled.div`
  display: flex;
  align-items: center;
  .header-info {
    display: flex;
    align-items: center;
    p {
      font-size: 12px;
      line-height: 1.33;
      letter-spacing: normal;
      color: #666;
      margin-right: 15px !important;
    }
    .schedule-id {
      max-width: 156px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
`;
export const RightHeader = styled.div``;

export const StyledButton = styled(Button)`
  padding-left: 8px;
`;

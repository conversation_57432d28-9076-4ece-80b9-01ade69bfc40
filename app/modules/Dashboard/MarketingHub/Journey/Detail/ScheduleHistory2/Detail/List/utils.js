import {
  CellText,
  CellDate,
  CellArray,
  CellMainActionHistory,
  CellNumber,
} from 'containers/Table/Cell';

import { safeParse } from 'utils/common';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import {
  DATA_INFO,
  getLabelStateSchedule,
} from '../../../../../../../../services/Abstract.data';
import { COLUMNS_WIDTH } from '../../../../../../../../containers/Table/constants';

const stateMap = { ...DATA_INFO.state.map };

// overide state ERROR to show Exit journey label in table
stateMap.ERROR = getTranslateMessage(
  TRANSLATE_KEY._STATE_EXIT_JOURNEY,
  'Exit journey',
);

const processTypeMap = { ...DATA_INFO.schedule_detail_process_type.map };

export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    // console.log(tmp);

    const tempt = {
      ...tmp,
      id: safeParse(tmp.process_id, Math.random()),
      process_id: safeParse(tmp.process_id, '--'),
      start_time: tmp.start_date,
      end_time: tmp.end_date,

      // temp, has new option process_type in feature
      process_type: safeParse(
        processTypeMap[tmp.process_type].label,
        tmp.process_type || '--',
      ),
      visitor_id: safeParse(tmp.visitor_id, '--'),
      customer_id: safeParse(tmp.customer_id, '--'),
      resumed_process_id: safeParse(tmp.resumed_process_id, '--'),
      journey_version: safeParse(tmp.journey_version, '--'),
      duration: safeParse(tmp.duration, '--'),
      state: safeParse(getLabelStateSchedule(tmp.state), tmp.state || '--'),
    };
    data.list.push(tempt);
    data.map[tempt.actionId] = tempt;
  });

  return data;
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };

    if (property.value === 'process_id') {
      column.Cell = CellMainActionHistory;
    } else if (property.value === 'journey_version') {
      column.displayFormat = {
        type: 'NUMBER',
        config: {
          decimal: '.',
          decimalPlace: 0,
          group: ',',
        },
      };
      column.Cell = CellNumber;
    } else if (property.value === 'resumed_process_id') {
      column.placement = 'left';
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }

    columns.push(column);
  });

  return columns;
}
export const TYPE_RESUME_WHOLE_PROCESS = [
  'EVENT_BASED',
  'SCHEDULED',
  'CLASSIC_LIST_BRANCH',
  'FILTER',
  'AB_SPLIT',
  'WAIT_EVENT',
];
export const ARRAY_STATE_LINE_CHART = [
  {
    value: 'WAITING',
    label: 'Waiting',
    propertyCode: 'WAITING',
  },
  {
    value: 'COMPLETED',
    label: 'Completed',
    propertyCode: 'COMPLETED',
  },
  {
    value: 'END',
    label: 'End',
    propertyCode: 'END',
  },
  // {
  //   value: 'PASSED',
  //   label: 'Passed',
  //   propertyCode: 'PASSED',
  // },
  {
    value: 'EXIT',
    label: 'Exit journey',
    propertyCode: 'EXIT',
  },
  {
    value: 'BUILD_CONTENT_ERROR',
    label: 'Build Content Error',
    propertyCode: 'BUILD_CONTENT_ERROR',
  },
  {
    value: 'MISSING_RECEIVER',
    label: 'Missing receiver',
    propertyCode: 'MISSING_RECEIVER',
  },
  {
    value: 'TRIGGER_FREQUENCY_CAPPING',
    label: 'Violate trigger frequency capping',
    propertyCode: 'TRIGGER_FREQUENCY_CAPPING',
  },
  {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    label: 'Violate destination frequency capping',
    propertyCode: 'DESTINATION_FREQUENCY_CAPPING',
  },
  {
    value: 'DELIVERED',
    label: 'Delivered',
    propertyCode: 'DELIVERED',
  },
  {
    value: 'BOUNCED_N_ERROR',
    label: 'Bounce & Error',
    propertyCode: 'BOUNCED_N_ERROR',
  },
  {
    value: 'PAUSED',
    label: 'Paused',
    propertyCode: 'PAUSED',
  },
  {
    value: 'INVALID_TIME_FRAME',
    label: 'Invalid time frame',
    propertyCode: 'INVALID_TIME_FRAME',
  },
  {
    value: 'HOLD_UNTIL_VALID_TIME',
    label: 'Hold until valid time',
    propertyCode: 'HOLD_UNTIL_VALID_TIME',
  },
  {
    value: 'ERROR',
    label: 'Error',
    propertyCode: 'ERROR',
  },
  {
    value: 'SKIP',
    label: 'Resume skip',
    propertyCode: 'SKIP',
  },
  {
    value: 'HOLD_UNTIL_SEND_BROADCAST',
    label: 'Hold until send broadcast',
    propertyCode: 'HOLD_UNTIL_SEND_BROADCAST',
  },
];

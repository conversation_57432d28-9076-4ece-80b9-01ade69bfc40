import styled from 'styled-components';
import { UITabs } from '@xlab-team/ui-components';
import { Tabs } from '@antscorp/antsomi-ui';

export const StyledTabs = styled(UITabs)`
  font-size: 0.75rem;
  box-shadow: none !important;
  nav {
    & > div {
      & > div:first-child {
        padding: 0 1.5rem 0.25rem;
      }
    }
  }

  :not(nav) {
    height: 100%;
    & > div {
      margin-top: -5px;
      height: calc(100% - 50px);
      & > div {
        height: 100%;
        & > div {
          /* TabPanel */
          height: 100%;
          display: grid;
          .wrapper-journey-detail {
            overflow: hidden auto;
          }
          .dataflow-tree {
            height: 100% important;
          }
        }
      }
    }
  }
`;

export const AntsomiTabs = styled(Tabs)`
  height: 100%;
  .antsomi-tabs-content {
    height: 100%;
  }
  .antsomi-tabs-tabpane {
    height: 100%;
  }
`;

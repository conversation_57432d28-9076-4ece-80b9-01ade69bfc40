// Libraries
import React from 'react';
import PropTypes from 'prop-types';
import { useHistory, useParams } from 'react-router-dom';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import { get } from 'lodash';

// Components
import { UIButton } from '@xlab-team/ui-components';
import UIIconXlab from 'components/common/UIIconXlab';
import MoreInfo from 'components/common/MoreInfo';
import { VersionCard } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Detail/VersionList/VersionCard';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Utils
import { makeUrlPermisison } from 'utils/web/permission';
import { getCurrentAccessUserId } from 'utils/web/cookie';

// Constants
import APP from 'appConfig';

// Styles
import { LeftHeader, RightHeader, Styled<PERSON><PERSON>on, WrapperHeader } from './styled';
import { createPortal } from 'react-dom';
import { UI_DETAIL_DRAWER } from '../../../../constant';
import { KEY_PARAMS } from '../../../../Main/constants';
import { Icon } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  backToScheduleHistory: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Back'),
  resumeFromTriggerNode: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Resume from trigger node',
  ),
};

export const Header = props => {
  const {
    journeyInfo,
    versionId,
    processId,
    versionInfo,
    isShowDrawer,
  } = props;
  const history = useHistory();
  const { channelId = 0 } = useParams();

  const onBack = () => {
    if (isShowDrawer) {
      history.push(
        `${APP.PREFIX}/${
          journeyInfo.portal_id
        }/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}/list?${
          KEY_PARAMS.UI
        }=${UI_DETAIL_DRAWER}&journeyId=${journeyInfo.story_id}&channelId=${
          journeyInfo.channel_id
        }&tab=schedule-history`,
      );
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            journeyInfo.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            journeyInfo.channel_id
          }/detail/${journeyInfo.story_id}/schedule-history`,
        ),
      );
    }
  };

  const onTrigger = () => {
    // history.push(
    //   makeUrlPermisison(
    //     `${APP.PREFIX}/${
    //       versionInfo.portal_id
    //     }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
    //       versionInfo.channel_id
    //     }/detail/${versionInfo.story_id}/settings/${versionId}?design=update`,
    //   ),
    // );
  };

  const leftEle = document.getElementById('header-journey-left');
  const rightEle = document.getElementById('header-journey-right');

  return (
    <WrapperHeader isShowDrawer={isShowDrawer}>
      <LeftHeader>
        {isShowDrawer && leftEle ? (
          createPortal(
            <StyledButton
              theme="outline"
              onClick={onBack}
              disabled={!journeyInfo || !journeyInfo.story_id}
              style={{ textWrap: 'nowrap' }}
            >
              <Icon
                type="icon-ants-angle-left"
                size={12}
                style={{ marginRight: '5px', marginLeft: '5px' }}
              />
              {MAP_TITLE.backToScheduleHistory}
            </StyledButton>,
            leftEle,
          )
        ) : (
          <StyledButton
            theme="outline"
            onClick={onBack}
            disabled={!journeyInfo || !journeyInfo.story_id}
          >
            <UIIconXlab name="arrow-left" />
            {MAP_TITLE.backToScheduleHistory}
          </StyledButton>
        )}

        {!isShowDrawer && (
          <UIIconXlab name="line-vertical" className="icon-line-vertical" />
        )}

        {isShowDrawer && rightEle ? (
          createPortal(
            <div className="header-info-drawer">
              <div>
                <p>Version {versionId}</p>
                <MoreInfo>
                  <VersionCard
                    versionId={versionInfo.version}
                    userInfo={versionInfo.user_info}
                    utime={versionInfo.utime}
                    versionInfo={versionInfo.version_info}
                    isActive={false}
                    isCurrentVersion={get(
                      versionInfo,
                      'version_info.current_version',
                    )}
                    isMoreInfo
                  />
                </MoreInfo>
              </div>
              <p className="schedule-id">Schedule ID: {processId}</p>
            </div>,
            rightEle,
          )
        ) : (
          <div className="header-info">
            <p className="schedule-id">Schedule ID: {processId}</p>
            <p>Version {versionId}</p>
            <MoreInfo>
              <VersionCard
                versionId={versionInfo.version}
                userInfo={versionInfo.user_info}
                utime={versionInfo.utime}
                versionInfo={versionInfo.version_info}
                isActive={false}
                isCurrentVersion={get(
                  versionInfo,
                  'version_info.current_version',
                )}
                isMoreInfo
              />
            </MoreInfo>
          </div>
        )}
      </LeftHeader>
      <RightHeader>
        <UIButton
          theme="primary"
          onClick={onTrigger}
          className="btn-trigger"
          style={{ display: 'none' }}
        >
          <PlayArrowIcon />
          {MAP_TITLE.resumeFromTriggerNode}
        </UIButton>
      </RightHeader>
    </WrapperHeader>
  );
};

Header.propTypes = {
  journeyInfo: PropTypes.object,
  versionInfo: PropTypes.object,
  versionId: PropTypes.number,
  processId: PropTypes.number,
  isShowDrawer: PropTypes.bool,
};

Header.defaultProps = {
  journeyInfo: {},
  versionInfo: {},
};

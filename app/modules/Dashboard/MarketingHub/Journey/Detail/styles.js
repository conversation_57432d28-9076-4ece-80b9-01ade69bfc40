import styled, { css } from 'styled-components';

import { breakdownMd } from 'utils/variables';
import { TabScrollButton } from '@material-ui/core';
import { isShowFlowChartOnly } from './utils';
import { Tabs } from '@antscorp/antsomi-ui';
// import TableContainer from 'containers/Table';

// export const StyleTableCampaign = styled(TableContainer)`
//   &::-webkit-scrollbar-track {
//     margin-left: ${props =>
//       props.firstColWidth
//         ? `${props.firstColWidth + 148}px`
//         : '291px'} !important;
//     ${breakdownMd(
//       css`
//         margin-left: ${props =>
//           props.firstColWidth
//             ? `${props.firstColWidth + 148}px`
//             : '218px'} !important;
//       `,
//     )}
//   }
// `;

export const StyleWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const StyleTabs = styled(Tabs)`
  nav {
    box-shadow: 3px 2px 5px 0 rgb(15 19 23 / 10%);
    z-index: 5;
    position: relative;
  }
`;

export const WrapperDetail = styled.div`
  position: relative;
  height: calc(100vh - 76px);
  padding-right: ${props => !props.isShowDrawer && '15px'};
  flex: 1;

  ${({ isLoading }) =>
    isLoading && `overflow: hidden; box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);`}
  /* overflow-x: hidden; */
  /* ::-webkit-scrollbar {
    width: 0.5rem;
  }
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0.125rem #c1c1c1;
    border-radius: 10px;
    background: #fafafa;
  }
  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
  } */
  .private-overlay {
    padding: 0 !important;
    z-index: 902;
  }

  .navbar-name {
    height: 120px;
    padding-left: 24px;
    align-items: start;
    padding-top: 16px;
  }

  .behind-shadow {
    position: relative;
    z-index: 1;
  }
`;

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  //margin-top: 8px;
  padding: 3.25rem 0.75rem 0.2rem 0.75rem;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  /* ${breakdownMd(
    css`
      height: calc(100vh - 108px);
      top: 108px;
      left: 0.75rem;
      right: 0.75rem;
    `,
  )} */
`;

export const Wrapper = styled.div`
  height: ${props => props.height};
`;

export const WrapperJourneyDetail = styled.div`
  position: relative;
  height: 100%;
  & > div:first-child {
    height: 100% !important;
  }
  #automation {
    height: 100%;
    #wrapper-automation {
      padding-bottom: 0;
    }
  }
  .dataflow-tree {
    height: 100% !important;
    padding-bottom: 0 !important;
  }
  .antsomi-custom-drawer {
    bottom: 0 !important;
    box-shadow: 0px -5px 0px -4px rgb(0 0 0 / 10%) !important;
    z-index: 9 !important;
  }
  .popup-main {
    height: 100% !important;
  }
  ${props =>
    isShowFlowChartOnly(props.page)
      ? css`
          // border-right: 1px solid rgba(15, 19, 23, 0.1);
        `
      : css`
          /* width: calc(100% - 32px);
          margin: 0 16px; */
          // #automation {
          //   margin: 0 16px 16px 16px;
          // }
        `}
`;

export const TabsWrapper = styled.div`
  height: ${props => props.isShowDrawer && '100%'};
  .MuiTab-wrapper {
    text-transform: capitalize;
    font-size: 1em;
    /* color: rgb(127, 127, 127); */
    font-family: Roboto-Medium;
    margin-top: 3px;
  }

  .MuiTabs-indicator {
    height: 3px;
  }

  .MuiTabs-root {
    width: 68% !important;

    @media screen and (max-width: 1200px) {
      width: 60% !important;
    }

    @media screen and (max-width: 1024px) {
      width: 55% !important;
    }

    @media screen and (max-width: 767px) {
      width: 50% !important;
    }

    /* &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -10px;
      width: 10px;
      height: 48px;
      box-shadow: -10px 0px 10px -20px rgb(44 44 44 / 66%) inset;
    }

    &:after {
      content: '';
      position: absolute;
      width: 10px;
      height: 48px;
      top: 0;
      right: 32%;
      box-shadow: 10px 0px 17px -20px rgb(44 44 44 / 66%) inset;
    } */
  }
  .MuiTab-root {
    min-width: 155px !important;
  }

  .MuiTabScrollButton-root {
    width: 30px;
    opacity: 1;
  }

  .MuiTabs-scrollButtons {
    z-index: 100;
    /* box-shadow: rgb(0 0 0 / 20%) 0px 0px 0.375rem 0px; */
    padding-top: 2px;
    background-color: #fff;
    border-top: 1px solid rgb(230, 230, 230);
  }

  .MuiTabScrollButton-root.Mui-disabled {
    opacity: 0;
  }

  .tabs--header {
    display: flex;
    background-color: rgb(255, 255, 255);
    //border-bottom: 1px solid rgb(230, 230, 230);
    height: 48px;
    //box-shadow: rgb(0 0 0 / 20%) 0px 1px 0.375rem 0px;
    align-items: flex-end;
    box-shadow: 3px 2px 5px 0 rgb(15 19 23 / 10%);
    z-index: 997;
    position: sticky;
    /* .MuiTabs-root {
      box-shadow: rgb(215 215 215) 3px 0px 4px -1px;
    } */
  }
`;

export const MyTabScrollButton = styled(TabScrollButton)`
  &.Mui-disabled {
    width: 0;
  }
  overflow: hidden;
  /* transition: width 0.7s; */
  width: 28;

  &:first-child {
    border-right: 1px solid #e0e0e0;
    box-shadow: rgb(255 255 255) 10px 15px 10px 5px;
  }

  &:last-child {
    border-left: 1px solid #e0e0e0;
    border-right: 3px solid rgb(231 231 231 / 47%);
    box-shadow: rgb(255 255 255) -10px 15px 10px 5px;
  }
`;

export const StyledTabs = styled(Tabs)`
  height: 100%;

  .antsomi-tabs-content {
    height: 100%;
  }

  .antsomi-tabs-tabpane {
    height: 100%;
  }
`;

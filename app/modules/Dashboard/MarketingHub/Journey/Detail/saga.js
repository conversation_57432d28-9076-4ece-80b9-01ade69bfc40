/* eslint-disable camelcase */
import { takeLatest, select, call, put, fork } from 'redux-saga/effects';
import { get, has } from 'lodash';
import { Map } from 'immutable';
import LinkManagementServices from 'services/LinkManagement';
import JourneyServices from 'services/Journey';
import { VERSION_RESTORE } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Restore/constants';
import { getPrefixCreate } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Detail/utils';
import ReduxTypes from '../../../../../redux/constants';
import selectStoryDetailDomain from './selectors';
import {
  dashboardSetOwnerIdFromData,
  getDetailDone,
  initDone,
  updateValue,
} from '../../../../../redux/actions';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  MENU_CODE,
} from '../../../../../utils/web/permission';
import { getCurrentAccessUserId } from '../../../../../utils/web/cookie';
import { mapVariantIdwithFeKey } from './utils';
import {
  convertJourneyToBlast,
  convertThirdPartyCampaignToBlast,
} from '../Create/BlastCampaign/utils';
import {
  toNodeTriggerScheduleUI,
  wrapperToUITargetAudience,
} from '../Create/Content/Nodes/TriggerScheduled/utils';
import { toNodeDestinationUI } from '../Create/Content/Nodes/Destination/utils';
import { getObjectPropSafely } from '../../../../../utils/common';
import { NODE_TYPE } from '../Create/Content/Nodes/constant';
import { toNodeTriggerEventBasedUI } from '../Create/Content/Nodes/TriggerEventBased/utils';
import SegmentServices from 'services/Segment';
import DestinationServices from 'services/Destination';
import { makeSelectSettingPersonalizations } from '../../../selector';
import { SHORT_LINK_V2 } from '@antscorp/antsomi-ui';
import { fetchPersonalizations } from '../../../saga';
import { dtoShortLinkShortener } from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';
import { toUIDataShortener } from '../../../../../components/common/UIEditorPersonalization/utils';

const PATH = 'app/modules/Dashboard/MarketingHub/Journey/Detail/saga.js';

export default function* workerSegmentDetailSaga(args) {
  const prefix = args.moduleConfig.key;

  yield takeLatest(`${prefix}${ReduxTypes.INIT}`, handleInit, args);
  yield takeLatest(
    `${prefix}${ReduxTypes.GET_DETAIL}`,
    handleFetchObjectDetail,
    args,
  );
}

function* handleInit(args, action) {
  // console.log('activeId', action);
  yield call(handleFetchObjectDetail, args, action);
  yield call(handleGetDataListing, args, action);
}

function* handleFetchShortLinkShortenerInfo() {
  try {
    const searchParams = new URLSearchParams(window.location.search);
    const design = searchParams.get('design');

    const settings = yield select(makeSelectSettingPersonalizations());
    if (
      design === 'preview' &&
      !has(settings, ['personalizationData', SHORT_LINK_V2])
    ) {
      yield call(fetchPersonalizations, {
        [SHORT_LINK_V2]: {
          savedKey: SHORT_LINK_V2,
          serviceFn: LinkManagementServices.shortener.getPersonalizations,
          dtoFn: dtoShortLinkShortener,
          payload: {
            data: {
              filters: {
                OR: [
                  {
                    AND: [
                      {
                        column: 'status',
                        data_type: 'number',
                        operator: 'matches',
                        value: [53],
                      },
                    ],
                  },
                ],
              },
              properties: ['link_shortener_id', 'shortener_name', 'properties'],
              limit: 2000,
              page: 1,
              sort: 'ctime',
              sd: 'desc',
              search: '',
            },
          },
          normalizeData: toUIDataShortener,
        },
      });
    }
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'handleFetchShortLinkShortenerInfo',
      data: error.stack,
    });
    console.log(error);
  }
}

function* handleFetchObjectDetail(args, action) {
  // console.log('handleFetchObjectDetail', { args, action });
  const prefix = args.moduleConfig.key;
  // console.log('handleFetchObjectDetail', action);
  try {
    const { channelId, isAfterSave, page } = action.payload;
    const { main } = yield select(state =>
      selectStoryDetailDomain(state, args),
    );
    const { activeId, versionId, typeResume } = main;
    if (activeId) {
      yield fork(handleFetchShortLinkShortenerInfo);

      // check quyền VIEW EVERYTHING và truyền param _owner_id tương ứng
      const hasRoleViewEverything = checkingRoleScope(
        MENU_CODE.JOURNEY,
        APP_ACTION.VIEW,
        APP_ROLE_SCOPE.EVERYTHING,
      );
      const params = {
        objectId: activeId,
        versionId: !typeResume && versionId,
        _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
      };

      const res = yield call(JourneyServices.versionHistory.getDetail, params);
      // res.data.isBlastCampaign = true; // HARD
      let rootJourneyDetail;

      if (page === VERSION_RESTORE) {
        const paramsRootVersion = {
          objectId: activeId,
          _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
        };

        const resCurrentVersion = yield call(
          JourneyServices.versionHistory.getDetail,
          paramsRootVersion,
        );

        rootJourneyDetail = get(resCurrentVersion, 'data', {});
      } else {
        rootJourneyDetail = get(res, 'data', {});
      }
      const { is_blast_campaign: isBlastCampaign } = res.data || {};
      const isThirdParty = get(
        res,
        'data.workflow_setting.metadata.isThirdParty',
        false,
      );

      if (isBlastCampaign) {
        const actionType = getObjectPropSafely(
          () => res.data.workflow_setting.actionType,
          null,
        );

        let newMapMain = Map({});

        if (actionType === NODE_TYPE.EVENT_BASED) {
          const mapTrigger = toNodeTriggerEventBasedUI(
            res.data.workflow_setting.metadata,
            res.data.custom_inputs,
          );

          const selectTime = mapTrigger.get('selectTime');
          const peformEvent = mapTrigger.get('peformEvent');
          const frequencyCapping = mapTrigger.get('frequencyCapping');
          const frequencyCappingV2 = mapTrigger.get('frequencyCappingV2');
          const selectDayOfWeek = mapTrigger.get('selectDayOfWeek');
          const customInput = mapTrigger.get('customInput');
          const journeyGoals = mapTrigger.get('journeyGoals');
          newMapMain = newMapMain
            .set('selectTime', selectTime)
            .set('peformEvent', peformEvent)
            .set('frequencyCapping', frequencyCapping)
            .set('frequencyCappingV2', frequencyCappingV2)
            .set('selectDayOfWeek', selectDayOfWeek)
            .set('customInput', customInput)
            .set('journeyGoals', journeyGoals);
        } else if (actionType === NODE_TYPE.SCHEDULED) {
          const mapTrigger = toNodeTriggerScheduleUI(
            res.data.workflow_setting.metadata,
            res.data.custom_inputs,
          );
          const scheduled = mapTrigger.get('scheduled');
          const frequencyCapping = mapTrigger.get('frequencyCapping');
          const customInput = mapTrigger.get('customInput');
          const journeyGoals = mapTrigger.get('journeyGoals');
          newMapMain = newMapMain
            .set('scheduled', scheduled)
            .set('frequencyCapping', frequencyCapping)
            .set('customInput', customInput)
            .set('journeyGoals', journeyGoals);
        }

        let blastCampaignData = {};
        if (isThirdParty) {
          blastCampaignData = convertThirdPartyCampaignToBlast(res.data);
        } else {
          blastCampaignData = convertJourneyToBlast(res.data);
        }

        let newMap = Map({});
        blastCampaignData.campaigns.forEach(campaign => {
          const newMapDestination = toNodeDestinationUI(campaign, '');
          const destination = newMapDestination.get('destination');

          newMap = newMap.setIn(
            [campaign.actionId, 'destination'],
            destination,
          );

          if (actionType === NODE_TYPE.EVENT_BASED) {
            newMap = newMap.setIn(
              [campaign.actionId, 'filter'],
              campaign.filter,
            );
          } else if (actionType === NODE_TYPE.SCHEDULED) {
            const targetAudience = wrapperToUITargetAudience(
              campaign.audiences,
            );
            const targetAudienceTmp = {
              ...targetAudience,
              viewObject: campaign.viewObject,
            };
            newMap = newMap.setIn(
              [campaign.actionId, 'targetAudience'],
              targetAudienceTmp,
            );
          }
        });
        yield put(
          updateValue(`${prefix}@@UPDATE_BLAST_CAMPAIGN@@`, {
            blastCampaignNodeMain: newMapMain,
            blastCampaignNode: newMap,
            blastCampaignData,
          }),
        );

        if (isAfterSave) {
          // update campaigns to get new campaignIds & variantIds after save
          yield put(
            updateValue(
              `${getPrefixCreate(prefix)}@@UPDATE_CAMPAIGNS_LIST_DATA@@`,
              blastCampaignData.campaigns,
            ),
          );
        }
      }

      yield put(
        updateValue(`${prefix}@@ROOT_JOURNEY_DETAIL@@`, rootJourneyDetail),
      );

      yield put(getDetailDone(prefix, { data: res.data, channelId }));
      if (res?.code === 200) {
        yield put(dashboardSetOwnerIdFromData(res?.data?.owner_id));
      }
      // push dispatch for refresh campaignId + variantId in node destination
      if (isAfterSave) {
        yield put(
          getDetailDone('GET_DETAIL_JOURNEY_AFTER_SAVE_CHANGE', res.data),
        );
        yield call(handelgetFeKeyAfterSave, args);
      }

      yield put(initDone(`${prefix}@@INIT_DETAIL_DONE`));
    }
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
  }
}

function* handelgetFeKeyAfterSave(args) {
  try {
    const prefix = args.moduleConfig.key;
    const { main } = yield select(state =>
      selectStoryDetailDomain(state, args),
    );
    const params = {
      data: {
        version_id: main.versionId,
        variant_ids: main.variantId.list,
        columns: [
          'variant_id',
          'variant_name',
          'content_setting',
          'status',
          'custom_inputs',
          'api_temp_id',
        ],
      },
    };
    const res = yield call(JourneyServices.variant.getByIds, params);
    const feKeyVariantId = mapVariantIdwithFeKey(res.data, main.variantId);
    yield put(updateValue(`${prefix}@@FEKEY_VARIANTID@@`, { feKeyVariantId }));
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handelgetFeKeyAfterSave',
      data: err.stack,
    });
    console.log('error', err);
  }
}

function* handleGetDataListing(args, action) {
  try {
    yield call(handleGetFullEventTracking, args, action);
    yield call(handleGetListDestination, args, action);
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetDataListing',
      data: err.stack,
    });
  }
}

function* handleGetFullEventTracking(args) {
  try {
    const prefix = args.moduleConfig.key;
    const res = yield call(SegmentServices.fetch.getListEvent, {
      objectType: 'STORIES',
    });

    const events = get(res, 'data', null);

    if (events) {
      yield put(updateValue(`${prefix}@@DATA_LISTING@@`, { events }));
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetFullEventTracking',
      data: err.stack,
    });
  }
}

function* handleGetListDestination(args) {
  try {
    const prefix = args.moduleConfig.key;
    const res = yield call(DestinationServices.data.getList, {
      data: {
        page: 1,
        limit: 1000,
        search: '',
        sort: 'ctime',
        sd: 'desc',
        columns: [
          'destination_id',
          'destination_name',
          'status',
          'catalog_id',
          'utime',
          'ctime',
          'c_user_id',
          'u_user_id',
          'destination_status',
          'channel_id',
          'logo_url',
          'owner_id',
        ],
        perf_columns: [],
        filters: { OR: [{ AND: [] }] },
        getListType: 1,
      },
    });

    const destinations = get(res, 'data', null);

    if (destinations) {
      yield put(updateValue(`${prefix}@@DATA_LISTING@@`, { destinations }));
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetListDestination',
      data: err.stack,
    });
  }
}

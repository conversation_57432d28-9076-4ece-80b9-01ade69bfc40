/* eslint-disable indent */
/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { <PERSON>, withRouter, useHistory } from 'react-router-dom';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';
import TableContainer from 'containers/Table';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { compose } from 'redux';
import {
  addNotification,
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
import { TableRelative } from '../styles';
import { TableWrapper } from '../Campaigns/styles';
import Filters from '../../../../../../containers/Filters';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import Search from '../../../../../../containers/Search';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import ControlTable from './ControlTable';

import saga from './saga';
import reducer from './reducer';
import { MODULE_CONFIG } from './config';

// Utilities
import { safeParse } from '../../../../../../utils/common';

// Translate
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { IconWrapper, TextInnerIcon } from './styled';
import {
  makeselectListingVersionHistoryMainData,
  makeSelectListVersionHistoryColumn,
  makeSelectListVersionHistoryDateRange,
  makeSelectListVersionHistoryDefaultMetrics,
  makeSelectListVersionHistoryDomainMain,
  makeSelectListVersionHistoryFilter,
  makeSelectListVersionHistoryMapDataFooter,
  makeSelectListVersionHistoryTable,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import { makeSelectActiveRow } from '../selectors';
import AddComponent from '../Variants/AddComponent';
import { getCurrentAccessUserId } from '../../../../../../utils/web/cookie';
import { makeUrlPermisison } from '../../../../../../utils/web/permission';
import APP from '../../../../../../appConfig';
import { isDetailDrawer } from '../../utils';
import { KEY_PARAMS } from '../../Main/constants';
import { Button, Flex } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  compare: getTranslateMessage(TRANSLATE_KEY._ACT_COMPARE, 'COMPARE'),
};

const layoutStyle = {
  overflow: 'hidden',
  height: 'calc(100vh - 54px)',
};

const VersionHistory = props => {
  const {
    main,
    table,
    channelId,
    column,
    data,
    filter,
    moduleConfig,
    moduleConfigColumn,
  } = props;

  const { isInitDone, isFirstLoadTable } = main;

  const storyId = safeParse(props.storyId, '');

  const history = useHistory();
  const href = React.useMemo(() => {
    let to;
    if (data.length > 0) {
      if (data.length === 1) {
        to = makeUrlPermisison(
          `${APP.PREFIX}/${
            data[0].portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            props.channelId
          }/detail/${props.storyId}/compare/${data[0].version}`,
        );
      } else {
        const temp = [...data];
        const twoLastItems = temp
          .sort(
            (firstItemVersion, lastItemVersion) =>
              lastItemVersion.version - firstItemVersion.version,
          )
          .splice(0, 2);

        to = makeUrlPermisison(
          `${APP.PREFIX}/${
            twoLastItems[0].portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            props.channelId
          }/detail/${props.storyId}/compare/${twoLastItems[0].version}/${
            twoLastItems[1].version
          }`,
        );
      }
    }
    return to;
  }, [data]);

  const searchParams = new URLSearchParams(window.location.search);
  const onCompareVersion = () => {
    if (!searchParams.has(KEY_PARAMS.JOURNEY_ID)) {
      searchParams.set(KEY_PARAMS.JOURNEY_ID, props.storyId);
    }

    searchParams.set(KEY_PARAMS.COMPARE, 'true');

    if (data.length > 0) {
      if (data.length === 1) {
        searchParams.set('versionLeft', data[0].version);
      } else {
        const temp = [...data];
        const twoLastItems = temp
          .sort(
            (firstItemVersion, lastItemVersion) =>
              lastItemVersion.version - firstItemVersion.version,
          )
          .splice(0, 2);
        searchParams.set('versionLeft', twoLastItems[0].version);
        searchParams.set('versionRight', twoLastItems[1].version);
      }

      history.push({ search: searchParams.toString() });
    }
  };

  useEffect(() => {
    localStorage.setItem('journey-prev-state', 'version-history');
  }, []);

  useEffect(() => {
    if (storyId !== '') props.init({ storyId, channelId });

    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      // props.resetIsLoadingModify();
      // setDateValue(state.value);
    };
  }, [storyId]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      // case 'ACTION_TABLE_DELETE': {
      //   setIsOpenModalDelete(true);
      //   break;
      // }
      // case 'EDIT_CELL_NAME': {
      //   props.editCellName(dataIn);
      //   break;
      // }
      // case 'ACTION_TABLE_CHANGE_STATUS': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      // case 'ON_CHANGE_DATERANGE': {
      //   // console.log('ON_CHANGE_DATERANGE', dataIn, dateRange.value);
      //   props.onChangeDateRange(dataIn);
      //   if (isInitDone && isFistLoadTable) {
      //     props.fetchData();
      //     props.onSaveDateRange(dateRange);
      //   }
      //   break;
      // }
      // case 'UPDATE_METRICS': {
      //   props.onChangeDefaultMetrics(dataIn);
      //   props.onSaveDefaultMetrics(dataIn);
      //   break;
      // }
      // case 'UPDATE_TOGGLE_CELL': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory/index.jsx">
      <LayoutContent padding="0 15px 15px 15px" margin="0" style={layoutStyle}>
        <LayoutContentLoading isLoading={!isInitDone}>
          <TableRelative data-test="list-versions">
            <TableWrapper>
              <TableContainer
                isLoading={main.isLoading}
                data={data}
                moduleConfig={moduleConfig}
                columns={tableColumns}
                table={table}
                noDataText="No data"
                callback={callback}
                selectedRows={new Map()}
                // isShowFooter
                // mapDataFooter={props.mapDataFooter}
                noCheckboxAction
                isShowPagination
              >
                <>
                  {/* <Filters
                    use="list"
                    rules={filter.rules}
                    moduleConfig={moduleConfig}
                    filterActive={filter.config.filterObj}
                    filterCustom={filter.config.library.filterCustom}
                    libraryFilters={filter.config.library.filters}
                    groups={main.groupAttributes.groupsFilter}
                    isFilter={filter.config.design.isFilter}
                    isLoading={filter.config.isLoading}
                    // AddComponent={AddComponent}
                  /> */}
                  <WrapActionTable
                    show={!filter.config.design.isFilter}
                    className="p-x-4"
                    style={{ padding: '12px 0', marginLeft: 'auto' }}
                  >
                    <div className="actionTable__inner">
                      {/* <Search
                        // moduleConfig={moduleConfig}
                        moduleConfig={moduleConfig}
                        // moduleLabel={MAP_TITLE.titlStories}
                        // config={configFilter}
                        suggestionType="suggestionMultilang"
                        isAddFilter
                        isGoTo={false}
                      />
                      <ModifyColumn
                        sort={table.sort}
                        moduleConfig={moduleConfigColumn}
                        // moduleConfig={moduleConfig}
                        columnActive={column.columnObj}
                        columnCustom={column.library.columnCustom}
                        libraryColumns={column.library.columns}
                        // libraryColumns={tableColumns}
                        defaults={moduleConfigColumn.columnsDefault}
                        defaultSortColumns={
                          moduleConfigColumn.defaultSortColumns
                        }
                        columns={tableColumns}
                        groups={main.groupAttributes.groups}
                        isLoading={column.isLoading}
                        isLoadingInfoProperties={
                          // Using for loading modify column info-properties
                          main.isLoadingModifyColumn
                        }
                      /> */}
                      {/* <Link to={href} className="item-link"> */}
                      <Button
                        type="text"
                        style={{ height: '43px' }}
                        onClick={onCompareVersion}
                        data-test="btn-compare-version"
                      >
                        <Flex vertical align="center">
                          <CompareArrowsIcon
                            style={{ height: '24px', color: '#595959' }}
                          />
                          <TextInnerIcon>
                            {MAP_TITLE.compare.toUpperCase()}
                          </TextInnerIcon>
                        </Flex>
                      </Button>
                      {/* </Link> */}
                      {!isDetailDrawer() && (
                        <IconButton
                          iconName="more-vertical"
                          size="20px"
                          onClick={() => {}}
                          isVertical
                          disabled
                        >
                          {MAP_TITLE.actMore.toUpperCase()}
                        </IconButton>
                      )}
                    </div>
                  </WrapActionTable>
                </>
              </TableContainer>
            </TableWrapper>
          </TableRelative>
        </LayoutContentLoading>
      </LayoutContent>
    </ErrorBoundary>
  );
};

const mapStateToProps = createStructuredSelector({
  main: makeSelectListVersionHistoryDomainMain(),
  table: makeSelectListVersionHistoryTable(),
  filter: makeSelectListVersionHistoryFilter(),
  column: makeSelectListVersionHistoryColumn(),
  data: makeselectListingVersionHistoryMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  // mapDataFooter: makeSelectListVersionHistoryMapDataFooter(),
  // dateRange: makeSelectListVersionHistoryDateRange(),
  // defaultMetrics: makeSelectListVersionHistoryDefaultMetrics(),
  activeRow: makeSelectActiveRow(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    // onChangeDateRange: params => {
    //   dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    // },
    // onSaveDateRange: params => {
    //   dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    // },
    // onChangeDefaultMetrics: params => {
    //   dispatch(updateValue(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    // },
    // onSaveDefaultMetrics: params => {
    //   dispatch(update(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    // },
    // resetIsLoadingModify: params => {
    //   dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    // },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });

const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(VersionHistory);

// export default VersionHistory;

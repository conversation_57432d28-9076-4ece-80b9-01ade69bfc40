/* eslint-disable import/order */
// Libraries
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';

// Components
import JourneyDetail from '../..';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Utils
import { getBreadcrums } from '../../utils';
import { createStructuredSelector } from 'reselect';
import { makeSelectActiveRow } from '../../selectors';
import { makeSelectJourneyChannelActive } from '../../../selectors';

// Constants
import { VERSION_RESTORE } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Restore/constants';

// Styles
import { WrapperJourneyDetail } from './styled';

const VersionRestore = props => {
  const { moduleConfig, journeyDetail, channelActive, isShowDrawer } = props;
  const params = useParams();

  const versionId = props.versionId || params.versionId;

  const breadcrumbs = useMemo(
    () => getBreadcrums(channelActive, journeyDetail.story_name),
    [journeyDetail.story_name],
  );

  return (
    <>
      <UIHelmet title="Restore Version" />
      <CustomHeader
        breadcrums={breadcrumbs}
        nameEditConfig={{
          moduleConfigKey: moduleConfig.key,
        }}
        isShowDrawer={isShowDrawer}
      />
      <WrapperJourneyDetail>
        <JourneyDetail
          page={VERSION_RESTORE}
          versionId={versionId}
          moduleConfig={{
            key: moduleConfig.key,
          }}
        />
      </WrapperJourneyDetail>
    </>
  );
};

VersionRestore.propTypes = {
  moduleConfig: PropTypes.object,
  journeyDetail: PropTypes.object,
  channelActive: PropTypes.object,
  versionId: PropTypes.number,
  isShowDrawer: PropTypes.bool,
};

const mapStateToProps = createStructuredSelector({
  journeyDetail: (state, props) =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: props.moduleConfig.key },
    }),
  channelActive: makeSelectJourneyChannelActive(),
});

export default connect(
  mapStateToProps,
  null,
)(VersionRestore);

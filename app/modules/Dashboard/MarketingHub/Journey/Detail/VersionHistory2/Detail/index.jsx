/* eslint-disable import/order */
// Libraries
import React, { useEffect, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Constants
import { VERSION_HISTORY } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/constants';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import { init } from 'redux/actions';
import reducer from './reducer';
import saga from './saga';
import { makeSelectVersionDetail } from './selectors';
import { makeSelectActiveRow } from '../../selectors';

// Components
import { Header } from './Header';
import { VersionList } from './VersionList';
import JourneyDetail from '../../index';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Configs
import { PREFIX, PREFIX_DETAIL } from './config';

// Styles
import {
  StyledGrid,
  WrapperDetail,
  WrapperJourneyDetail,
  WrapperVersionList,
} from './styled';

// Utils
import { getBreadcrums } from '../../utils';
import { makeSelectJourneyChannelActive } from '../../../selectors';

const VersionHistoryDetail = props => {
  const params = useParams();
  const {
    versionDetail,
    journeyDetail = {},
    channelActive,
    activeId,
    isShowDrawer,
  } = props;
  const prevStateRef = useRef(localStorage.getItem('journey-prev-state'));

  const versionId = props.versionId || params.versionId;

  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });

  const {
    dataAPIS: { versionHistoryListing },
  } = versionDetail;

  useEffect(() => {
    props.initVersionDetail(activeId);
    if (prevStateRef.current === 'journey-editing') {
      document.body.classList.add(prevStateRef.current);
    }
  }, []);

  const breadcrumbs = useMemo(
    () => getBreadcrums(channelActive, journeyDetail.story_name),
    [journeyDetail.story_name],
  );

  return (
    <>
      <UIHelmet title="Version History" />
      {!isShowDrawer && (
        <CustomHeader
          breadcrums={breadcrumbs}
          nameEditConfig={{
            moduleConfigKey: PREFIX_DETAIL,
          }}
        />
      )}
      <WrapperDetail
        isShowDrawer={isShowDrawer}
        data-test="page-version-history"
      >
        <Header
          versionId={+versionId}
          journeyInfo={journeyDetail}
          prevState={prevStateRef.current}
          isShowDrawer={isShowDrawer}
          currentVersionData={versionHistoryListing.data[0]}
        />
        <StyledGrid isShowDrawer={isShowDrawer}>
          <WrapperJourneyDetail isShowDrawer={isShowDrawer}>
            <JourneyDetail
              page={VERSION_HISTORY}
              versionId={versionId}
              moduleConfig={{
                key: PREFIX_DETAIL,
              }}
            />
          </WrapperJourneyDetail>
          <WrapperVersionList>
            <VersionList
              journeyInfo={journeyDetail}
              versionId={+versionId}
              versionHistoryListing={versionHistoryListing}
              isShowDrawer={isShowDrawer}
            />
          </WrapperVersionList>
        </StyledGrid>
      </WrapperDetail>
    </>
  );
};

VersionHistoryDetail.propTypes = {
  initVersionDetail: PropTypes.func,
  versionDetail: PropTypes.object,
  journeyDetail: PropTypes.object,
  channelActive: PropTypes.object,
  activeId: PropTypes.number,
  versionId: PropTypes.number,
  isShowDrawer: PropTypes.bool,
};

const mapStateToProps = createStructuredSelector({
  versionDetail: makeSelectVersionDetail(),
  journeyDetail: state =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: PREFIX_DETAIL },
    }),
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => ({
  initVersionDetail: activeId => {
    dispatch(init(PREFIX, { activeId }));
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(VersionHistoryDetail);

// Libraries
import React from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';

// Components
import { UIButton } from '@xlab-team/ui-components';
import UIIconXlab from 'components/common/UIIconXlab';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Utils
import { makeUrlPermisison } from 'utils/web/permission';
import { getCurrentAccessUserId } from 'utils/web/cookie';

// Constants
import APP from 'appConfig';

// Styles
import { StyledButton, Wrapper, WrapperHeader } from './styled';
import { createPortal } from 'react-dom';
import { KEY_PARAMS, TAB_DRAWER_JOURNEY } from '../../../../Main/constants';
import { Button, Icon } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  back: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Back'),
  versionHistory: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Version history'),
  restoreThisVersion: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Restore this version',
  ),
};

export const Header = props => {
  const {
    currentVersionData,
    journeyInfo,
    versionId,
    prevState,
    isShowDrawer,
  } = props;
  const history = useHistory();

  const searchParams = new URLSearchParams(history.location.search);
  const onBack = () => {
    const activeNode = localStorage.getItem('journey-active-node');

    if (isShowDrawer) {
      searchParams.set('tab', TAB_DRAWER_JOURNEY.VERSION_HISTORY.key);

      searchParams.delete('versionId');

      if (prevState === 'journey-editing') {
        if (activeNode) {
          searchParams.set('node-id', activeNode);
        }
      }
      history.push({ search: searchParams.toString() });
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            journeyInfo.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            journeyInfo.channel_id
          }/detail/${journeyInfo.story_id}/${
            prevState === 'journey-editing'
              ? `settings?design=update${
                  activeNode ? `&node-id=${activeNode}` : ''
                }`
              : 'version-history'
          }`,
        ),
      );
    }
  };

  const onRestore = () => {
    if (isShowDrawer) {
      searchParams.set(KEY_PARAMS.RESTORE, 'true');
      history.push({ search: searchParams.toString() });
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            journeyInfo.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            journeyInfo.channel_id
          }/detail/${journeyInfo.story_id}/settings/${versionId}?design=update`,
        ),
      );
    }
  };

  const leftEle = document.getElementById('header-journey-left');
  const rightEle = document.getElementById('header-journey-right');

  return (
    <WrapperHeader isShowDrawer={isShowDrawer}>
      <div className="d-flex align-items-center">
        {isShowDrawer ? (
          <>
            {leftEle &&
              createPortal(
                <Button
                  theme="outline"
                  onClick={onBack}
                  style={{ textWrap: 'nowrap' }}
                >
                  <Icon
                    type="icon-ants-angle-left"
                    size={12}
                    style={{ marginRight: '5px', marginLeft: '5px' }}
                  />
                  {MAP_TITLE.back}
                </Button>,
                leftEle,
              )}
          </>
        ) : (
          <div>
            <StyledButton theme="outline" onClick={onBack}>
              <UIIconXlab name="arrow-left" />
              {MAP_TITLE.back}
            </StyledButton>
          </div>
        )}

        {!isShowDrawer && (
          <>
            <UIIconXlab name="line-vertical" className="icon-line-vertical" />
            <p>{MAP_TITLE.versionHistory}</p>
          </>
        )}
      </div>
      {isShowDrawer ? (
        <>
          {rightEle &&
            createPortal(
              <Button
                onClick={onRestore}
                style={{ textWrap: 'nowrap' }}
                disabled={currentVersionData?.version === versionId}
                data-test="btn-restore-version"
              >
                {MAP_TITLE.restoreThisVersion}
              </Button>,
              rightEle,
            )}
        </>
      ) : (
        <div>
          <UIButton theme="outline" onClick={onRestore}>
            {MAP_TITLE.restoreThisVersion}
          </UIButton>
        </div>
      )}
    </WrapperHeader>
  );
};

Header.propTypes = {
  journeyInfo: PropTypes.object,
  versionId: PropTypes.number,
  prevState: PropTypes.string,
  isShowDrawer: PropTypes.bool,
  currentVersionData: PropTypes.object,
};

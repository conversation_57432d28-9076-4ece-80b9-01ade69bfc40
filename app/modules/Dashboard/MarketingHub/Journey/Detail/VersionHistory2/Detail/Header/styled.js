import { UIButton } from '@xlab-team/ui-components';
import styled from 'styled-components';

export const WrapperHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: ${props => !props.isShowDrawer && '12px 20px'};
  border-bottom: 1px solid #c2cee0;
  p {
    font-family: Roboto;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.88;
    letter-spacing: normal;
    color: #333;
    margin: 0 !important;
  }
`;

export const StyledButton = styled(UIButton)`
  padding-left: 8px;
`;

export const Wrapper = styled.div`
  position: ${props => (props.isShowDrawer ? 'fixed' : 'relative')};
  top: ${props => props.isShowDrawer && '0'};
  display: ${props => props.isShowDrawer && 'flex'};
  align-items: ${props => props.isShowDrawer && 'center'};
  height: ${props => props.isShowDrawer && '47px'};
  z-index: ${props => props.isShowDrawer && '999'};
  right: ${props =>
    props.isShowDrawer && props.right ? `${props.right}px` : 'unset'};
  left: ${props =>
    props.isShowDrawer && props.left ? `${props.left}px` : 'unset'};
  border-bottom: ${props =>
    props.isShowDrawer && props.left && '1px solid #e6e6e6'};
  padding-left: ${props => props.isShowDrawer && props.left && '15px'};
`;

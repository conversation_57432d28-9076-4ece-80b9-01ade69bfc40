import styled from 'styled-components';

export const WrapperDetail = styled.div`
  width: 100%;
  margin: ${props => !props.isShowDrawer && ' 0 15px 10px 15px'};
  background-color: #fff;
  box-shadow: -1px 0 3px 0 rgb(0 0 0 / 10%);
  .icon-xlab {
    font-size: 20px;
  }
  .list-campaign-sticky {
    top: 0px !important;
  }
  height: ${props => props.isShowDrawer && '100%'};
`;

export const StyledGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 370px;
  height: ${props => (props.isShowDrawer ? '100%' : 'calc(100% - 57px)')};
  & > div {
    height: 100%;
    overflow: hidden auto;
  }
`;

export const WrapperJourneyDetail = styled.div`
  .dataflow-tree {
    height: 100% !important;
    padding-right: 5px !important;
  }
  .blast-grid-container {
    overflow-x: auto;

    & > div:first-child {
      min-width: 1400px;
    }
  }
`;

export const WrapperVersionList = styled.div`
  border-left: 1px solid #e3eef1;
`;

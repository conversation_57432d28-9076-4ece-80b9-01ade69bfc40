// Libraries
import React from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';

// Utils
import { makeUrlPermisison } from 'utils/web/permission';
import { getCurrentAccessUserId } from 'utils/web/cookie';

// Constants
import APP from 'appConfig';

// Components
import { UILoading } from '@xlab-team/ui-components';
import { VersionCard } from './VersionCard';
import { WrapperVersionList } from './styled';
import { KEY_PARAMS } from '../../../../Main/constants';

export const VersionList = props => {
  const { versionHistoryListing, journeyInfo, versionId, isShowDrawer } = props;
  const history = useHistory();

  const searchParams = new URLSearchParams(history.location.search);
  const onChangeVersion = id => {
    if (isShowDrawer) {
      searchParams.set(KEY_PARAMS.VERSION_ID, id);
      history.push({ search: searchParams.toString() });
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            journeyInfo.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            journeyInfo.channel_id
          }/detail/${journeyInfo.story_id}/version-history/${id}`,
        ),
      );
    }
  };

  return (
    <WrapperVersionList>
      <UILoading isLoading={versionHistoryListing.isLoading} />
      {versionHistoryListing.data.map((version, index) => (
        <VersionCard
          isActive={+version.version === +versionId}
          isCurrentVersion={index === 0}
          versionId={+version.version}
          utime={version.utime}
          userInfo={version.user_info}
          versionInfo={version.version_info}
          onClick={onChangeVersion}
        />
      ))}
    </WrapperVersionList>
  );
};

VersionList.propTypes = {
  journeyInfo: PropTypes.object,
  versionHistoryListing: PropTypes.array,
  versionId: PropTypes.number,
  isShowDrawer: PropTypes.bool,
};

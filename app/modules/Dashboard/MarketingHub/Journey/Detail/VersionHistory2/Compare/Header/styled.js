import { Button } from '@antscorp/antsomi-ui';
import styled from 'styled-components';

export const WrapperHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: ${props => (props.isShowDrawer ? '0' : '8px 16px')};
  // box-shadow: 0 2px 3px 0 rgba(15, 19, 23, 0.1);
  box-shadow: 0 0 6px rgb(0 0 0 / 10%);
  p {
    margin: 0 !important;
  }
  .compare-title {
    font-size: 16px;
  }
`;

export const StyledButton = styled(Button)`
  padding-left: 8px;
`;

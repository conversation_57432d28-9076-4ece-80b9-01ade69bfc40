// Libraries
import React from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import classnames from 'classnames';
// Components
import UIIconXlab from 'components/common/UIIconXlab';

// Translates
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

// Utils
import { makeUrlPermisison } from 'utils/web/permission';
import { getCurrentAccessUserId } from 'utils/web/cookie';

// Constants
import APP from 'appConfig';

// Styles
import { StyledButton, WrapperHeader } from './styled';
import { createPortal } from 'react-dom';
import { Icon } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  back: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Back'),
  versionHistory: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Compare Version History',
  ),
};

export const Header = props => {
  const { journeyInfo, isShowDrawer } = props;
  const history = useHistory();
  const leftEle = document.getElementById('header-journey-left');

  const onBack = () => {
    if (isShowDrawer) {
      const searchParams = new URLSearchParams(history.location.search);

      searchParams.delete('compare');
      searchParams.delete('versionLeft');
      searchParams.delete('versionRight');

      history.push({
        search: searchParams.toString(),
      });
    } else {
      history.push(
        makeUrlPermisison(
          `${APP.PREFIX}/${
            journeyInfo.portal_id
          }/${getCurrentAccessUserId()}/marketing-hub/journeys/${
            journeyInfo.channel_id
          }/detail/${journeyInfo.story_id}/version-history`,
        ),
      );
    }
  };

  return (
    <WrapperHeader
      className={classnames({ 'm-bottom-2': !isShowDrawer })}
      isShowDrawer={isShowDrawer}
    >
      <div className="d-flex align-items-center">
        {isShowDrawer && leftEle ? (
          createPortal(
            <StyledButton
              theme="outline"
              onClick={onBack}
              style={{ textWrap: 'nowrap' }}
            >
              <Icon
                type="icon-ants-angle-left"
                size={12}
                style={{ marginRight: '5px', marginLeft: '5px' }}
              />
              {MAP_TITLE.back}
            </StyledButton>,
            leftEle,
          )
        ) : (
          <StyledButton theme="outline" onClick={onBack}>
            <UIIconXlab name="arrow-left" />
            {MAP_TITLE.back}
          </StyledButton>
        )}

        {!isShowDrawer && (
          <>
            <UIIconXlab name="line-vertical" className="icon-line-vertical" />
            <p className="compare-title">{MAP_TITLE.versionHistory}</p>
          </>
        )}
      </div>
      {/* <div>
        <UIButton theme="outline" onClick={onRestore}>
          {MAP_TITLE.restoreThisVersion}
        </UIButton>
      </div> */}
    </WrapperHeader>
  );
};

Header.propTypes = {
  journeyInfo: PropTypes.object,
  isShowDrawer: PropTypes.bool,
};

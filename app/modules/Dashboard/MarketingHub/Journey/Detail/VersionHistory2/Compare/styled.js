/* eslint-disable indent */
import styled, { css } from 'styled-components';
import COLOR from '../../../../../../../utils/colors';

export const WrapperCompareVersion = styled.div`
  display: block;
  width: 100%;
  height: calc(100% - 5px);
  .icon-xlab {
    font-size: 20px;
  }
`;

export const WrapperVersions = styled.div`
  /* display: grid;
    grid-template-columns: 50% 50%;
    gap: 8px;
    width: calc(100% - 8px); */

  height: ${props => (!props.isShowDrawer ? 'calc(100% - 54px)' : '100%')};
  display: flex;
  flex: 1;
  width: 100%;

  .resizable {
    position: relative;
    flex-shrink: 1 !important;
    border-right: 1px solid #e5e5e5;
    /* z-index === #wrapper-drawer for zoom drawer */
    z-index: 1000;
    min-width: 425px;
    // overflow: auto hidden;

    :hover {
      border-right: 1px solid ${COLOR.primary60};
      &::before {
        content: '';
        width: 8px;
        height: 8px;
        background-color: ${COLOR.white};
        border: 1px solid ${COLOR.primary60};
        border-radius: 8px;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(55%, -50%);
        z-index: 4;
        pointer-events: none;
      }
    }

    & > span > div {
      right: -10px !important;
    }
  }
`;

export const WrapperVersion = styled.div`
  flex: 1;
  min-width: 420px;
  height: 100%;
  .wrapper-journey-detail {
    height: calc(100% - 54px) !important;
  }
  .dataflow-tree {
    height: 100% !important;
  }
  .blast-grid-container {
    overflow-y: auto;

    & > div:first-child {
      min-width: 1400px;
    }
  }
  .list-campaign-sticky {
    top: 0px !important;
  }

  ${props =>
    props.compareCss
      ? css`
          ${props.compareCss}
        `
      : ``};
`;

/* eslint-disable import/no-duplicates */
/* eslint-disable no-restricted-syntax */
/* eslint-disable import/order */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable camelcase */
// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { Resizable } from 're-resizable';
import MoreVertIcon from '@material-ui/icons/MoreVert';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Redux
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import reducer from './reducer';
import saga from './saga';
import { makeSelectListingVersions } from './selectors';
import { makeSelectActiveRow } from '../../selectors';
import { init, updateValue } from '../../../../../../../redux/actions';
import { makeSelectConfigureMainCreateFlattenNodes } from '../../../Create/selectors';
import { makeSelectJourneyChannelActive } from '../../../selectors';

// Constants
import APP from '../../../../../../../appConfig';
import { PREFIX, PREFIX_LEFT_DETAIL, PREFIX_RIGHT_DETAIL } from './config';
import { VERSION_COMPARE } from 'modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Compare/constants';

// Components
import JourneyDetail from '../..';
import JourneyDetailV2 from '../../indexV2';
import { Header } from './Header';
import { VersionHeader } from './VersionHeader';
import { NoVersion } from './NoVersion';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import CustomHeader from 'components/Organisms/CustomHeader';

// Utils
import { compareJourney, compareJourneyBlast } from './utils';
import { getPrefixCreate } from '../Detail/utils';
import { makeUrlPermisison } from '../../../../../../../utils/web/permission';
import { getCurrentAccessUserId } from '../../../../../../../utils/web/cookie';
import {
  getMapFlattenNodes,
  getChangedNodes,
} from '../../../Create/utils.version';

// Styles
import {
  WrapperVersions,
  WrapperVersion,
  WrapperCompareVersion,
} from './styled';
import { getBreadcrums } from '../../utils';
import { UI_DETAIL_DRAWER } from '../../../constant';
import { KEY_PARAMS } from '../../../Main/constants';

const CompareVersion = props => {
  const {
    listingVersions,
    initVersionCompare,
    detailVersionLeft,
    detailVersionRight,
    updateFlattenNodesLeft,
    updateFlattenNodesRight,
    rootFlattenNodesLeft,
    rootFlattenNodesRight,
    channelActive,
    isShowDrawer,
  } = props;

  const params = useParams();
  const storyId = props.activeId || params.activeId;
  const versionLeft = +props.versionLeft || +params.versionLeft;
  const versionRight = +props.versionRight || +params.versionRight;
  const [compareCssLeft, setCompareCssLeft] = useState('');
  const [compareCssRight, setCompareCssRight] = useState('');
  const isBlastCampaign =
    detailVersionLeft.is_blast_campaign || detailVersionRight.is_blast_campaign;

  const history = useHistory();

  const searchParams = new URLSearchParams(window.location.search);

  useInjectReducer({
    key: PREFIX,
    reducer,
  });
  useInjectSaga({
    key: PREFIX,
    saga,
  });

  useEffect(() => {
    initVersionCompare({ storyId });
  }, []);

  // Compare versions
  useDeepCompareEffect(() => {
    if (!versionLeft || !versionRight) {
      return;
    }

    let newVersion;
    let oldVersion;

    if (versionLeft > versionRight) {
      newVersion = {
        nodes: detailVersionLeft.workflow_setting || {},
        flattenNodes: rootFlattenNodesLeft || [],
      };
      oldVersion = {
        nodes: detailVersionRight.workflow_setting || {},
        flattenNodes: rootFlattenNodesRight || [],
      };
    } else {
      newVersion = {
        nodes: detailVersionRight.workflow_setting || {},
        flattenNodes: rootFlattenNodesRight || [],
      };
      oldVersion = {
        nodes: detailVersionLeft.workflow_setting || {},
        flattenNodes: rootFlattenNodesLeft || [],
      };
    }

    const oldMapFlattenNodes = getMapFlattenNodes({
      node: oldVersion.nodes,
    });
    const newMapFlattenNodes = getMapFlattenNodes({
      node: newVersion.nodes,
      mapFlattenRootNodes: oldMapFlattenNodes,
    });

    const nodesChanged = getChangedNodes(
      newMapFlattenNodes,
      oldMapFlattenNodes,
    );

    if (isBlastCampaign) {
      const { cssNewBlastCampaign, cssOldBlastCampaign } = compareJourneyBlast({
        nodesChanged,
        oldMapFlattenNodes,
        newMapFlattenNodes,
      });

      const cssLeft =
        versionLeft > versionRight ? cssNewBlastCampaign : cssOldBlastCampaign;
      const cssRight =
        versionRight > versionLeft ? cssNewBlastCampaign : cssOldBlastCampaign;

      setCompareCssLeft(cssLeft);
      setCompareCssRight(cssRight);
    } else {
      const { oldFlattenNodes, newFlattenNodes } = compareJourney({
        nodesChanged,
        oldFlattenNodes: oldVersion.flattenNodes,
        newFlattenNodes: newVersion.flattenNodes,
      });

      const flattenNodesLeft =
        versionLeft > versionRight ? newFlattenNodes : oldFlattenNodes;
      const flattenNodesRight =
        versionRight > versionLeft ? newFlattenNodes : oldFlattenNodes;

      updateFlattenNodesLeft({ flattenNodes: flattenNodesLeft });
      updateFlattenNodesRight({ flattenNodes: flattenNodesRight });
    }
  }, [
    detailVersionLeft,
    detailVersionRight,
    rootFlattenNodesLeft,
    rootFlattenNodesRight,
  ]);

  const versionOptions = useMemo(
    () =>
      listingVersions.data.map(version => ({
        value: version.version,
        label: version.version,
      })),
    [listingVersions],
  );

  const onChangeVersionLeft = version => {
    const { portal_id, channel_id, story_id } = detailVersionLeft;

    if (isShowDrawer) {
      searchParams.set('versionLeft', version.value);

      history.push({
        search: searchParams.toString(),
      });
    } else {
      history.push(
        makeUrlPermisison(
          `${
            APP.PREFIX
          }/${portal_id}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channel_id}/detail/${story_id}/compare/${
            version.value
          }/${versionRight}`,
        ),
      );
    }
  };

  const onChangeVersionRight = version => {
    // always get info of versionLeft
    const { portal_id, channel_id, story_id } = detailVersionLeft;
    if (isShowDrawer) {
      searchParams.set('versionRight', version.value);

      history.push({
        search: searchParams.toString(),
      });
    } else {
      history.push(
        makeUrlPermisison(
          `${
            APP.PREFIX
          }/${portal_id}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channel_id}/detail/${story_id}/compare/${versionLeft}/${
            version.value
          }`,
        ),
      );
    }
  };

  const onRestoreVersionLeft = version => {
    const { portal_id, channel_id, story_id } = detailVersionLeft;

    if (isShowDrawer) {
      searchParams.set(KEY_PARAMS.VERSION_ID, version);
      searchParams.set(KEY_PARAMS.RESTORE, true);

      history.push({
        search: searchParams.toString(),
      });
    } else {
      history.push(
        makeUrlPermisison(
          `${
            APP.PREFIX
          }/${portal_id}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channel_id}/detail/${story_id}/settings/${version}`,
        ),
      );
    }
  };

  const onRestoreVersionRight = version => {
    const { portal_id, channel_id, story_id } = detailVersionRight;

    if (isShowDrawer) {
      searchParams.set(KEY_PARAMS.VERSION_ID, version);
      searchParams.set(KEY_PARAMS.RESTORE, true);

      history.push({
        search: searchParams.toString(),
      });
    } else {
      history.push(
        makeUrlPermisison(
          `${
            APP.PREFIX
          }/${portal_id}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channel_id}/detail/${story_id}/settings/${version}`,
        ),
      );
    }
  };

  const breadcrumbs = useMemo(() => {
    let listBreadcrums = getBreadcrums(
      channelActive,
      detailVersionLeft.story_name,
    );

    if (isShowDrawer) {
      listBreadcrums = listBreadcrums.filter(item => !item.urlPath);
    }
    return listBreadcrums;
  }, [detailVersionLeft.story_name]);

  return (
    <>
      <UIHelmet title="Compare Version" />
      {!isShowDrawer && (
        <CustomHeader
          breadcrums={breadcrumbs}
          nameEditConfig={{
            moduleConfigKey: PREFIX_LEFT_DETAIL,
          }}
          isShowDrawer={isShowDrawer}
        />
      )}
      <WrapperCompareVersion
        isShowDrawer={isShowDrawer}
        data-test="page-compare-version"
      >
        <Header journeyInfo={detailVersionLeft} isShowDrawer={isShowDrawer} />
        <WrapperVersions isShowDrawer={isShowDrawer}>
          <Resizable
            className="resizable"
            enable={{
              // right: !isBlastCampaign,
              right: true,
              bottom: false,
            }}
            defaultSize={{
              width: '50%',
              height: '100%',
            }}
          >
            {/* {isBlastCampaign ? null : (
              <div className="drag-resize-vert">
                <MoreVertIcon htmlColor="#666" />
              </div>
            )} */}
            {/* tach WrapperVersion ra sau */}
            <WrapperVersion compareCss={compareCssLeft}>
              <VersionHeader
                options={versionOptions}
                versionId={versionLeft}
                onChangeVersion={onChangeVersionLeft}
                onRestoreVersion={onRestoreVersionLeft}
                version={detailVersionLeft.story_version}
              />
              {versionLeft ? (
                <JourneyDetail
                  page={VERSION_COMPARE}
                  versionId={versionLeft}
                  moduleConfig={{
                    key: PREFIX_LEFT_DETAIL,
                    isBlastCampaign,
                  }}
                />
              ) : (
                <NoVersion />
              )}
            </WrapperVersion>
          </Resizable>
          <WrapperVersion compareCss={compareCssRight}>
            <VersionHeader
              options={versionOptions}
              versionId={versionRight}
              onChangeVersion={onChangeVersionRight}
              onRestoreVersion={onRestoreVersionRight}
              version={detailVersionRight.story_version}
            />
            {versionRight ? (
              <JourneyDetail
                page={VERSION_COMPARE}
                versionId={versionRight}
                moduleConfig={{
                  key: PREFIX_RIGHT_DETAIL,
                  isBlastCampaign,
                }}
              />
            ) : (
              <NoVersion />
            )}
          </WrapperVersion>
        </WrapperVersions>
      </WrapperCompareVersion>
    </>
  );
};

CompareVersion.propTypes = {
  initVersionCompare: PropTypes.func,
  listingVersions: PropTypes.array,
  detailVersionLeft: PropTypes.object,
  detailVersionRight: PropTypes.object,
  rootFlattenNodesLeft: PropTypes.object,
  rootFlattenNodesRight: PropTypes.object,
  updateFlattenNodesLeft: PropTypes.func,
  updateFlattenNodesRight: PropTypes.func,
  channelActive: PropTypes.object,
  versionLeft: PropTypes.string,
  versionRight: PropTypes.string,
  activeId: PropTypes.string,
  isShowDrawer: PropTypes.bool,
};

const mapStateToProps = createStructuredSelector({
  listingVersions: makeSelectListingVersions(),
  detailVersionLeft: state =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: PREFIX_LEFT_DETAIL },
    }),
  detailVersionRight: state =>
    makeSelectActiveRow()(state, {
      moduleConfig: { key: PREFIX_RIGHT_DETAIL },
    }),
  rootFlattenNodesLeft: state =>
    makeSelectConfigureMainCreateFlattenNodes()(state, {
      moduleConfig: { key: getPrefixCreate(PREFIX_LEFT_DETAIL) },
    }),
  rootFlattenNodesRight: state =>
    makeSelectConfigureMainCreateFlattenNodes()(state, {
      moduleConfig: { key: getPrefixCreate(PREFIX_RIGHT_DETAIL) },
    }),
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => ({
  initVersionCompare: payload => {
    dispatch(init(PREFIX, payload));
  },
  updateFlattenNodesLeft: payload => {
    dispatch(
      updateValue(
        `${getPrefixCreate(PREFIX_LEFT_DETAIL)}@@FLATTEN_NODES@@`,
        payload,
      ),
    );
  },
  updateFlattenNodesRight: payload => {
    dispatch(
      updateValue(
        `${getPrefixCreate(PREFIX_RIGHT_DETAIL)}@@FLATTEN_NODES@@`,
        payload,
      ),
    );
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CompareVersion);

/* eslint-disable arrow-body-style */
/* eslint-disable react/prop-types */
import React from 'react';

const Iframe = props => {
  if (props.src === '') {
    return null;
  }
  return (
    <iframe
      id={`iframe-${props.componentKey}`}
      key={props.src}
      title={props.title}
      src={props.src}
      height={props.height}
      width={props.width}
      frameBorder="0"
      onLoad={props.onLoad}
      // style={{

      //   border: '0',
      // }}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
      }}
      allowFullScreen
      // className="datastudio-google"
    />
  );
};

export default Iframe;

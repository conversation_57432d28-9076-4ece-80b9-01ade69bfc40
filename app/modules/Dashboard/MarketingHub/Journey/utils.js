/* eslint-disable indent */
import { mapKeys } from 'lodash';
import AMServices from 'services/AnalyticsModel';
import { isProduction, safeParse } from '../../../../utils/common';
import { CHANNEL, UI_DETAIL_DRAWER } from './constant';
import queryString from 'query-string';
import { TAB_DRAWER_JOURNEY } from './Main/constants';

export const configToAPISuggestion = ({
  objectType,
  limit = 100,
  page = 1,
  sort = 'asc',
  search,
  // filtersBody = {},
  isGetOnlySuggestParams = false,
  propertyCode,
  feServices = 'suggestionMultilang',
  feKey = '',
  isFilters = false,
  isPk,
  filtersBody,
}) => {
  const dataOut = {
    isPk,
    objectType,
    limit,
    page,
    sort,
    search,
    filters: isFilters && {
      OR: [
        {
          AND: [filtersBody],
        },
      ],
    },
    isGetOnlySuggestParams: isGetOnlySuggestParams && 0,
    propertyCode,
    feServices,
    feKey,
  };
  // console.log('dataOut', dataOut);
  return dataOut;
};
export const JOURNEY_CHART_METRIC = 'story-list-default-metrics';
export function getColumnsExport(column, main, type) {
  const columns = [];
  column.columnObj.columns.columnsAlias.forEach(each => {
    const { map } = main.groupAttributes;
    if (map[each] && map[each].type !== 2 && map[each].type !== 3) {
      columns.push(each);
    }
  });
  if (!columns.includes(type)) {
    columns.push(type);
  }
  // console.log(columns);
  return columns;
}

export const getViewType = key =>
  safeParse(JSON.parse(localStorage.getItem(`view-type-${key}`)), 'table');

export const setViewType = (key, value) =>
  localStorage.setItem(`view-type-${key}`, JSON.stringify(value));

export const getDomainInsight = () => {
  const envProduction = isProduction();
  const domain = envProduction
    ? 'https://insights.antsomi.com'
    : 'https://sandbox-antalyser.antsomi.com';
  return domain;
};

export const getBuildTimeWithModelId = async modelId => {
  const data = {
    scope: 'analytic-model',
    filters: {
      OR: [
        {
          AND: [],
        },
      ],
    },
    columns: [
      'compute_id',
      'trigger_type',
      'compute_status',
      'compute_start_time',
      'compute_end_time',
      'compute_durations',
      'response_code',
    ],
    model_id: [modelId],
    limit: 1,
    page: 1,
    sort: 'utime',
    sd: 'desc',
  };
  let result = '';
  const res = await AMServices.computation.getList({ data });
  if (res.code === 200 && res.data.length) {
    result = res.data[0].compute_end_time;
  }
  return result;
};

export const getChannelIdByCode = channelCode => {
  try {
    const objects = mapKeys(CHANNEL, v => v.code);

    return objects[channelCode].id || null;
  } catch (err) {
    return null;
  }
};

export const getChannelCodeById = channelId => {
  const map = Object.values(CHANNEL).reduce((acc, cur) => {
    acc[cur.id] = cur;
    return acc;
  }, {});

  if (map[channelId]) {
    return map[channelId].code;
  }

  return null;
};

export const isDetailDrawer = (isSaveDone = false) => {
  const { ui, tab } = queryString.parse(window.location.search);

  const existingTab =
    tab && Object.values(TAB_DRAWER_JOURNEY).some(t => t.key === tab);

  return isSaveDone ? false : ui === UI_DETAIL_DRAWER && existingTab;
};

export const getObjectTypeByChannelId = channelId => {
  let id = '';

  switch (channelId) {
    case CHANNEL.WEB_PERSONALIZATION.id:
      id = 8;
      break;

    case CHANNEL.EMAIL.id:
      id = 9;
      break;

    default:
      break;
  }
  return id;
};

import {
  CellArray,
  CellDate,
  CellNumber,
  CellText,
} from '../../../../../containers/Table/Cell';
import CellMainAccount from '../../../../../containers/Table/Cell/CellMainAccount';
import CellStatusVendor from '../../../../../containers/Table/Cell/CellStatusVendor';
import CellToggleVendor from '../../../../../containers/Table/Cell/CellToggleVendor';
import CellVendor from '../../../../../containers/Table/Cell/CellVendor';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';
import { initNumberId } from '../../../../../utils/web/portalSetting';

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.account_id,
    disabledCheckbox: true,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

const MAP_ACCESSOR = {
  status: 'status_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellDate,
  status: CellStatusVendor,
  account_name: CellMainAccount,
  is_primary: CellToggleVendor,
  vendor: CellVendor,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };

    if (
      property.value === 'status' ||
      property.value === 'is_primary' ||
      property.value === 'vendor'
    ) {
      column.placement = 'left';
    }

    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    if (['user_id', 'u_user_id'].includes(property.value)) {
      column.displayFormat = initNumberId();
    }

    columns.push(column);
  });
  return columns;
}

export const mapChannels = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.vendor_id,
    label: item.vendor_name,
  }));

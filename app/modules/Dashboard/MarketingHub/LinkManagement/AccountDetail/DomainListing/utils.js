/* eslint-disable react/prop-types */
/* eslint-disable react/react-in-jsx-scope */
import { CellDate, CellText } from '../../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import COLOR from '../../../../../../utils/colors';
import { safeParse, safeParseInt } from '../../../../../../utils/common';
import { safeParseDisplayFormat } from '../../../../../../utils/web/portalSetting';

import CellAction from './Cell/CellAction';
import CellDomain from './Cell/CellDomain';
import { COLUMNS, PRIMARY_STATUS, VERIFY_STATUS } from './config';
import CellPrimary from './Cell/CellPrimary';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export const TRANSLATE_MAP = {
  navBar: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'RFM'),
  action: {
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'Download'),
    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'More'),
  },
};

export const getPrimaryStatusLabel = primaryStatus => {
  if (Object.values(PRIMARY_STATUS).includes(primaryStatus)) {
    return primaryStatus === PRIMARY_STATUS.paused ? 'Paused' : 'Active';
  }

  return '--';
};

export const getVerifyStatusLabel = verifyStatus => {
  if (Object.values(VERIFY_STATUS).includes(verifyStatus)) {
    return verifyStatus === VERIFY_STATUS.valid ? 'Valid' : 'Invalid';
  }

  return '--';
};

export function serializeData(list) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp[COLUMNS.domainId],

      // support accessor
      primary_status_label: getPrimaryStatusLabel(tmp[COLUMNS.primaryStatus]),

      verify_status_label: getVerifyStatusLabel(tmp[COLUMNS.verifyStatus]),
    };

    data.list.push(tempt);
    data.map[tempt[COLUMNS.domainId]] = tempt;
  });

  return data;
}

export const serializeGroupAttrs = groups => {
  const data = {
    groups: [],
    mapGroups: {},
    groupsFilter: [],
    map: {},
  };

  groups.forEach(group => {
    const temptGroup = {
      groupId: group.groupId,
      groupName: safeParse(group.translateLabel, group.groupName),
      value: group.groupId,
      label: safeParse(group.translateLabel, group.groupName),
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };

    const temptGroupFilter = { ...temptGroup, options: [] };

    group.properties.forEach((item, _indexGroup) => {
      if (item.status === 0) return;

      const tmp = item;
      tmp.value = item.propertyCode;
      tmp.label = item.translateLabel;
      tmp.isSort = parseInt(item.isSort);
      tmp.isFilter = parseInt(item.isFilter);
      tmp.itemDataType = item.dataType;
      tmp.displayFormat = safeParseDisplayFormat(
        safeParse(item.displayFormat, null),
        {
          dataType: item.dataType,
        },
      );

      temptGroup.options.push(tmp);

      if (tmp.isFilter === 1) {
        temptGroupFilter.options.push(tmp);
      }

      data.map[tmp.value] = tmp;
    });

    data.groups.push(temptGroup);

    if (temptGroupFilter.options.length > 0)
      data.groupsFilter.push(temptGroupFilter);

    data.mapGroups[temptGroup.groupId] = temptGroup;
  });

  return data;
};

export function buildColumnPrimaryStatus() {
  return {
    Header: 'Primary Status',
    id: COLUMNS.primaryStatus,
    accessor: 'primary_status_label',
    disableSortBy: true,
    Cell: CellText,
    width: COLUMNS_WIDTH.SWITCH,
    minWidth: COLUMNS_WIDTH.SWITCH,
  };
}

export function buildColumnAction() {
  return {
    Header: 'Action',
    id: 'action',
    accessor: 'action',
    disableSortBy: true,
    width: COLUMNS_WIDTH.SWITCH,
    minWidth: COLUMNS_WIDTH.SWITCH,
    Cell: CellAction,
    placement: 'center',
  };
}

export function buildTableColumns({ colProperties }) {
  const columns = [];

  colProperties.forEach(property => {
    const { dataType, displayFormat } = property;

    const disableSortBy = parseInt(property.isSort) !== 1;

    const column = {
      Header: property.label,
      id: property.value,
      disableSortBy,
      className: `${dataType} ${property.value}`,
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
    };

    column.accessor = safeParse(MAP_ACCESSOR[column.id], property.value);

    column.Cell = MAP_CELL_BY_VALUE[column.id] || CellText;

    if (
      column.id === COLUMNS.isPrimary ||
      column.id === COLUMNS.primaryStatus
    ) {
      column.placement = 'left';
    }

    if (column.id === COLUMNS.domainUrl) {
      column.displayFormat.type = 'LINK';
      column.displayFormat.config.style = {
        color: COLOR.primary,
      };
    }

    columns.push(column);
  });

  const columnAction = buildColumnAction();
  const columnPrimaryStatus = buildColumnPrimaryStatus();

  columns.push(columnAction);
  columns.splice(2, 0, columnPrimaryStatus);

  return columns;
}

export const MAP_ACCESSOR = {
  [COLUMNS.primaryStatus]: 'status_label',
  [COLUMNS.verifyStatus]: 'verify_status_label',
};

const MAP_CELL_BY_VALUE = {
  [COLUMNS.createdOn]: CellDate,
  [COLUMNS.modifiedOn]: CellDate,
  [COLUMNS.isPrimary]: CellPrimary,
  [COLUMNS.domainUrl]: CellDomain,
};

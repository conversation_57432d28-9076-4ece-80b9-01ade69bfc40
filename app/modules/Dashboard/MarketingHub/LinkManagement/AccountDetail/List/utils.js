import {
  CellArray,
  CellDate,
  CellNumber,
  CellText,
} from '../../../../../../containers/Table/Cell';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { initNumberId } from '../../../../../../utils/web/portalSetting';

const MAP_ACCESSOR = {
  status: 'status_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellDate,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    // console.log('columnsActive', property);
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;
    if (['user_id', 'story_id', 'variant_id', 'campaign_id'].includes(property.value)) {
      column.displayFormat = initNumberId();
    }

    columns.push(column);
  });
  return columns;
}

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.link_id,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export const addFilterAccountId = (rules, accountId) => {
  let newRules = rules;
  const ruleAccountId = {
    type: 1,
    column: 'account_id',
    data_type: 'number',
    operator: 'matches',
    value: [+accountId],
  };

  newRules.OR[0].AND.push(ruleAccountId);

  return newRules;
};

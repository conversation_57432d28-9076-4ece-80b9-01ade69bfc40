/* eslint-disable indent */
import React, { useEffect, useMemo, memo } from 'react';
import { getList, init } from '../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import { connect } from 'react-redux';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import reducer from './reducer';
import saga from './saga';
import { compose } from 'redux';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { UILoading } from '@xlab-team/ui-components';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectListLinkColumn,
  makeSelectListLinkDomainMain,
  makeSelectListLinkDomainMainData,
  makeSelectListLinkFilter,
  makeSelectListLinkTable,
} from './selectors';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
import TableContainer from 'containers/Table';
import { MAP_TITLE } from './constant';
import Filters from '../../../../../../containers/Filters';
import Search from '../../../../../../containers/Search';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import { TableRelative, TableWrapper } from './styles';
import useNotificationBar from 'hooks/useNotificationBar';
import PropTypes from 'prop-types';
import { Box } from '@material-ui/core';
import { useAccountContext } from '../context';
import HeaderDrawer from '../../../../../../components/common/HeaderDrawer';
import { EditableName } from '@antscorp/antsomi-ui';

function LinkManagementListing(props) {
  const { accountId, main, table, filter, column } = props;
  const { isInitDone } = main;

  const { account } = useAccountContext();
  console.log('🚀 ~ LinkManagementListing ~ account:', account);

  const tableColumns = useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const { isShow } = useNotificationBar();

  const callback = (type, data) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData({ accountId });
        break;
      }

      default:
        break;
    }
  };

  useEffect(() => {
    props.init({ accountId });
  }, [accountId]);

  if (!account || !account.vendor) return null;

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/LinkManagement/AccountDetail/List/index.jsx">
      <HeaderDrawer>
        <EditableName value={account.name} readonly />
      </HeaderDrawer>
      <Box height="100%" padding="15px">
        <LayoutContent
          padding="0px 4px 4px 4px"
          margin="0px -4px -4px -4px"
          overflow="hidden"
          height="100%"
          // height={isShow ? 'calc(100vh - 100px - 54px)' : 'calc(100vh - 100px)'}
        >
          <UILoading isLoading={!isInitDone} />
          <LayoutContentLoading classNameLoading="m-y-2">
            <TableRelative>
              <TableWrapper>
                <TableContainer
                  columnActive={column.columnObj}
                  table={table}
                  isLoading={main.isLoading}
                  moduleConfig={MODULE_CONFIG}
                  global={{ modulePrefix: 'marketing-hub' }}
                  selectedIds={table.selectedIds}
                  selectedRows={table.selectedRows}
                  isSelectedAll={table.isSelectedAll}
                  isSelectedAllPage={table.isSelectedAllPage}
                  columns={tableColumns}
                  data={props.data || []}
                  callback={callback}
                  noDataText="No data"
                  resizeColName="destination_name"
                  widthFirstColumns={124} // 76 + 48
                  initialWidthColumns={404} // 76 + 280 + 48
                  noCheckboxAction
                >
                  <>
                    <Filters
                      use="list"
                      rules={filter.rules}
                      moduleConfig={MODULE_CONFIG}
                      filterActive={filter.config.filterObj}
                      filterCustom={filter.config.library.filterCustom}
                      libraryFilters={filter.config.library.filters}
                      groups={main.groupAttributes.groupsFilter}
                      isFilter={filter.config.design.isFilter}
                      isLoading={filter.config.isLoading}
                    />
                    <WrapActionTable
                      show={!filter.config.design.isFilter}
                      className="p-x-4"
                    >
                      <div className="actionTable__inner">
                        <Search
                          moduleConfig={MODULE_CONFIG}
                          config={{
                            objectType: MODULE_CONFIG.objectType,
                            limit: 20,
                            page: 1,
                            sort: 'asc',
                            filters: {},
                            decryptFields: ['original_url'],
                            accountId,
                          }}
                          suggestionType="suggestionLinkManagement"
                          moduleLabel={MAP_TITLE.title}
                        />
                        <ModifyColumn
                          sort={table.sort}
                          moduleConfig={MODULE_CONFIG}
                          columnActive={column.columnObj}
                          columnCustom={column.library.columnCustom}
                          libraryColumns={column.library.columns}
                          requires={main.groupAttributes.requires}
                          defaults={main.groupAttributes.defaults}
                          defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                          columns={column.columnObj.columns.columnsAlias}
                          groups={main.groupAttributes.groups}
                          isLoading={column.isLoading}
                        />
                        {/* <IconButton
                          iconName="file_download"
                          size="24px"
                          onClick={() => {}}
                          isVertical
                          disabled
                        >
                          {MAP_TITLE.actExport.toUpperCase()}
                        </IconButton> */}
                      </div>
                      {/* <Divider className="m-x-1" />
                      <IconButton
                        iconName="arrow-down"
                        size="24px"
                        handleClick={() => {}}
                      /> */}
                    </WrapActionTable>
                  </>
                </TableContainer>
              </TableWrapper>
            </TableRelative>
          </LayoutContentLoading>
        </LayoutContent>
      </Box>
    </ErrorBoundary>
  );
}

LinkManagementListing.propTypes = {
  accountId: PropTypes.string.isRequired,

  main: PropTypes.object,
  table: PropTypes.object,
  filter: PropTypes.object,
  column: PropTypes.object,
  data: PropTypes.array,

  init: PropTypes.func,
  fetchData: PropTypes.func,
};

const mapStateToProps = createStructuredSelector({
  main: makeSelectListLinkDomainMain(),
  table: makeSelectListLinkTable(),
  filter: makeSelectListLinkFilter(),
  column: makeSelectListLinkColumn(),
  data: makeSelectListLinkDomainMainData(),
});

const mapDispatchToProps = dispatch => {
  const PREFIX = MODULE_CONFIG.key;
  return {
    init: params => {
      dispatch(init(PREFIX, params));
    },
    fetchData: params => {
      dispatch(getList(PREFIX, params));
    },
  };
};

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withConnect,
  withReducer,
  withSaga,
  memo,
)(LinkManagementListing);

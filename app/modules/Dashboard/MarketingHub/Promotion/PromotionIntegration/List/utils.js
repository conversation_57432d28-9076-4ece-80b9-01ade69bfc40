import _isEmpty from 'lodash/isEmpty';
import {
  getLabelStatus,
  getLabelStatusWithType,
  getLabelStatusPromotion,
} from '../../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellMainPromotionSource,
  CellProcessStatus,
  CellDate,
  CellCurrency,
  CellNumber,
  CellArray,
  CellToggleAPI,
  CellToggleWithStyle,
  CellPoolId,
} from '../../../../../../containers/Table/Cell';
import {
  getstorageTypeLabel,
  getUpdateMethodLabel,
} from '../../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../../utils/web/permission';
import PromotionServices from '../../../../../../services/BusinessObject';
import {
  initNumberId,
  initNumberWithoutDecimal,
} from '../../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId, isEdit) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.source_id,
      portal_id: {
        value: portalId,
      },
      connector_id: tmp.connector_name,
      process_status: getLabelStatusPromotion(
        parseInt(tmp.status),
        parseInt(tmp.process_status),
      ),
      original_process_status: tmp.process_status,
      c_user_id: tmp.c_user_name,
      u_user_id: tmp.u_user_name,
      disabled_status: tmp.status === 1 && tmp.process_status === 1,
      isEdit,
      disabledCheckbox: !isEdit,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
    // console.log('tmp', tmp)
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  // console.log(columnName, columnStatus, columnsActive);
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggleWithStyle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainPromotionSource,
      getIsDisabledEditName: () => false,
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat, propertyCode } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'process_status') {
      column.Cell = CellProcessStatus;
      column.placement = 'status';
      // column.className = `${dataType} ${property.value} ${'status'}`;
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      if (property.value === 'source_id') {
        column.displayFormat = initNumberId();
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    ['etime'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}

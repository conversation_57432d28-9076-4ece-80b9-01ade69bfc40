import _isEmpty from 'lodash/isEmpty';
import { getLabelSatusPromotionCode } from '../../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellDate,
  CellCurrency,
  CellNumber,
  CellArray,
  CellStatusProcess,
  CellCodeStatus,
  CellAllocatedJourney,
} from '../../../../../../containers/Table/Cell';
import {
  getstorageTypeLabel,
  getUpdateMethodLabel,
  getSegmentTypeLabel,
} from '../../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../../utils/web/permission';
import PromotionServices from '../../../../../../services/BusinessObject';
import { initNumberWithoutDecimal } from '../../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.id,
      portal_id: {
        value: portalId,
      },
      code_status: getLabelSatusPromotionCode(parseInt(tmp.code_status)),
      original_process_status: tmp.code_status,
      storage_type: getstorageTypeLabel(tmp.storage_type),
      c_user_id: tmp.c_user_name,
      u_user_id: tmp.u_user_name,
      update_method: getUpdateMethodLabel(tmp.update_method),
      disabled_status: tmp.status === 1 && tmp.process_status === 1,
      segmentTypeDisplay: getSegmentTypeLabel(`${tmp.audience_type}`),
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
    // console.log('tmp', tmp)
  });
  return data;
}

export function buildTableGroupColumns(columnID, columnsActive) {
  // console.log(columnName, columnStatus, columnsActive);
  const columns = [
    {
      Header: columnID.label,
      id: columnID.value,
      accessor: columnID.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellText,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
  ];
  columns.push({
    Header: 'Info Remaining',
    columns: buildTableColumns(columnsActive),
  });
  // console.log("123",columns)
  return columns;
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    if (property.value === 'status') {
      column.Cell = CellStatusProcess;
      column.placement = 'status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
      column.width = COLUMNS_WIDTH.DEFAULT;
      column.minWidth = COLUMNS_WIDTH.DEFAULT;
    } else if (property.value === 'audience_type') {
      column.accessor = `segmentTypeDisplay`;
      column.placement = 'left';
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'story_id') {
      column.Cell = CellAllocatedJourney;
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      if (property.value === 'duration') {
        column.displayFormat = initNumberWithoutDecimal();
        column.width = COLUMNS_WIDTH.DEFAULT;
        column.minWidth = COLUMNS_WIDTH.DEFAULT;
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    columns.push(column);
  });
  return columns;
}

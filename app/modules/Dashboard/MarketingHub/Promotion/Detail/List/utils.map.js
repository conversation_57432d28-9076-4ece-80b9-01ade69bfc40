import {
  OPERATORS_OPTION,
  CODE,
} from '../../../../../../containers/Filters/_UI/operators';
import { safeParseDisplayFormat } from '../../../../../../utils/web/portalSetting';
import { safeParse, safeParseInt } from '../../../../../../utils/common';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getConfigAutoSuggestion } from '../../../../../../utils/web/map';

const labelInformation = getTranslateMessage(
  TRANSLATE_KEY._TAB_INFORMATION_FIELD,
  'Information Field',
);

export const serializeGroupAttributesPromotion = (groups, poolId) => {
  const data = {
    groups: [],
    mapGroups: {},
    groupsFilter: [],
    map: {},
    requires: [],
    defaults: [],
  };

  // const configFilterStoryName = configToAPISuggestion({});

  groups.forEach(group => {
    const temptGroup = {
      groupId: group.groupName,
      groupName: safeParse(
        group.translateLabel,
        group.itemPropertyDisplay || group.groupName,
      ),
      value: group.groupName,
      translateCode: group.translateCode,
      label: safeParse(
        group.translateLabel,
        group.itemPropertyDisplay || group.groupName,
      ),
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };

    const temptGroupFilter = { ...temptGroup, options: [] };
    group.properties
      .filter(item => item.status)
      .forEach((item, indexGroup) => {
        if (
          item.isFixed ||
          item.itemPropertyName === 'code_status' ||
          item.propertyCode === 'code_status'
        ) {
          data.requires.push(item.itemPropertyName || item.propertyCode);
        }
        // if (item.isDefault) {
        //   console.log(item);
        //   data.defaults.push(item.itemPropertyName);
        // }

        const tmp = item;
        tmp.groupId = group.groupCode;
        tmp.value = item.itemPropertyName || item.propertyCode;
        tmp.label = item.translateLabel;
        tmp.isSort = parseInt(item.isSort);
        tmp.isFilter = parseInt(item.isFilter);
        tmp.itemDataType = item.dataType;
        tmp.displayFormat = safeParseDisplayFormat(
          safeParse(item.displayFormat, null),
          {
            dataType: item.dataType,
          },
        );
        tmp.configSuggestion = getConfigAutoSuggestion(item);
        if (tmp.value === 'code_status') {
          tmp.operatorInitValue = OPERATORS_OPTION.MATCHES;
          tmp.autoSuggestion = 1;
          tmp.configSuggestion = {
            // feServices: 'suggestionMultilang',
            feKey: '-100-promotion_code-code_status',
            sort: 'asc',
            limit: 20,
            page: 1,
            propertyCode: 'code_status',
            search: '',
            itemTypeId: -100,
            itemTypeName: 'promotion_code',
            itemPropertyName: 'code_status',
            scope: 1,
            // tmp.uiType = 'lookup-info';
            // tmp.config = {
            //   objectName: 'promotion_code_status',
            //   operators: {
            //     list: [OPERATORS_OPTION.MATCHES],
            //     set: new Set([CODE.MATCHES]),
            //   },
            //   operatorInitValue: OPERATORS_OPTION.MATCHES,
            // };
          };
        } else if (
          // tmp.value === 'story_id' ||
          tmp.value === 'c_user_id'
        ) {
          tmp.uiType = 'lookup-info';
          tmp.config = {
            objectName: item.itemPropertyName,
            operators: {
              list: [OPERATORS_OPTION.MATCHES],
              set: new Set([CODE.MATCHES]),
            },
            operatorInitValue: OPERATORS_OPTION.MATCHES,
          };
        } else if (tmp.value === 'story_id') {
          tmp.uiType = 'lookup-info';
          tmp.config = {
            objectName: 'allocated_to_journey',
            operators: {
              list: [OPERATORS_OPTION.MATCHES],
              set: new Set([CODE.MATCHES]),
            },
            operatorInitValue: OPERATORS_OPTION.MATCHES,
          };
        } else if (tmp.value === 'audience_type') {
          tmp.uiType = 'lookup-info';
          tmp.config = {
            objectName: 'audience_type',
            operators: {
              list: [OPERATORS_OPTION.MATCHES],
              set: new Set([CODE.MATCHES]),
            },
            operatorInitValue: OPERATORS_OPTION.MATCHES,
          };
        } else if (tmp.value === 'u_user_id') {
          tmp.uiType = 'lookup-info';
          tmp.config = {
            objectName: item.itemPropertyName,
            operators: {
              list: [OPERATORS_OPTION.MATCHES],
              set: new Set([CODE.MATCHES]),
            },
            operatorInitValue: OPERATORS_OPTION.MATCHES,
          };
        }

        if (item.value === 'id') {
          tmp.operatorInitValue = OPERATORS_OPTION.CONTAINS;
          tmp.autoSuggestion = 1;
          tmp.configSuggestion = {
            feKey: `11-promotion_code-id`,
            sort: 'asc',
            limit: 20,
            page: 1,
            search: '',
            itemTypeId: -100,
            poolId: Number(poolId),
            itemTypeName: 'promotion_code',
            itemPropertyName: 'name',
            scope: 1,
          };
        }

        if (item.value === 'allocated_audience') {
          tmp.operatorInitValue = OPERATORS_OPTION.CONTAINS;
          tmp.autoSuggestion = 1;
          tmp.configSuggestion = {
            feKey: `11-promotion_code-allocated_audience`,
            sort: 'asc',
            limit: 20,
            page: 1,
            search: '',
            poolId: Number(poolId),
            itemTypeId: -100,
            itemTypeName: 'promotion_code',
            itemPropertyName: 'allocated_audience',
            scope: 1,
          };
        }

        if (item.value === 'last_used_source') {
          tmp.operatorInitValue = OPERATORS_OPTION.CONTAINS;
          tmp.autoSuggestion = 1;
          tmp.configSuggestion = {
            feKey: `11-promotion_code-last_used_source`,
            sort: 'asc',
            limit: 20,
            page: 1,
            search: '',
            itemTypeId: -100,
            poolId: Number(poolId),
            itemTypeName: 'promotion_code',
            itemPropertyName: 'last_used_source',
            scope: 1,
          };
        }

        if (item.dataType === 'datetime') {
          tmp.operators = {
            list: [
              OPERATORS_OPTION.EQUALS,
              OPERATORS_OPTION.NOT_EQUALS,
              OPERATORS_OPTION.BEFORE_DATE,
              OPERATORS_OPTION.AFTER_DATE,
              OPERATORS_OPTION.BETWEEN,
              OPERATORS_OPTION.EQUAL_TIME_AGO,
              OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
              OPERATORS_OPTION.BEFORE_TIME_AGO,
              OPERATORS_OPTION.AFTER_TIME_AGO,
              OPERATORS_OPTION.BETWEEN_TIME_AGO,
              OPERATORS_OPTION.EXISTS,
              OPERATORS_OPTION.NOT_EXISTS,
            ],
            set: new Set([
              CODE.EQUALS,
              CODE.NOT_EQUALS,
              CODE.BEFORE_DATE,
              CODE.AFTER_DATE,
              CODE.BETWEEN,
              CODE.EQUAL_TIME_AGO,
              CODE.NOT_EQUAL_TIME_AGO,
              CODE.BEFORE_TIME_AGO,
              CODE.AFTER_TIME_AGO,
              CODE.BETWEEN_TIME_AGO,
              CODE.EXISTS,
              CODE.NOT_EXISTS,
            ]),
          };
          tmp.operatorInitValue = OPERATORS_OPTION.EQUALS;
        }

        temptGroup.options.push(tmp);
        if (tmp.isFilter === 1) {
          temptGroupFilter.options.push(tmp);
        }
        data.map[tmp.value] = tmp;
      });

    data.groups.push(temptGroup);
    if (temptGroupFilter.options.length > 0) {
      data.groupsFilter.push(temptGroupFilter);
    }
    data.mapGroups[temptGroup.groupId] = temptGroup;
  });
  return data;
};

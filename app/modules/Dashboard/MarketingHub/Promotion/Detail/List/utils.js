/* eslint-disable no-unused-expressions */
import _isEmpty from 'lodash/isEmpty';
import { getLabelSatusPromotionCode } from '../../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellDate,
  CellCurrency,
  CellNumber,
  CellArray,
  CellPromotionCodeId,
  CellCodeStatus,
  CellAllocatedJourney,
} from '../../../../../../containers/Table/Cell';
import {
  getstorageTypeLabel,
  getUpdateMethodLabel,
  getSegmentTypeLabel,
} from '../../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../../utils/web/permission';
import PromotionServices from '../../../../../../services/BusinessObject';
import { initNumberWithoutDecimal } from '../../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { safeParse } from '../../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

export function serializeData(list, portalId, isEdit) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.id,
      portal_id: {
        value: portalId,
      },
      code_status: getLabelSatusPromotionCode(parseInt(tmp.code_status)),
      original_process_status: tmp.code_status,
      storage_type: getstorageTypeLabel(tmp.storage_type),
      c_user_id: tmp.c_user_name,
      u_user_id: tmp.u_user_name,
      update_method: getUpdateMethodLabel(tmp.update_method),
      disabled_status: tmp.status === 1 && tmp.process_status === 1,
      segmentTypeDisplay: getSegmentTypeLabel(`${tmp.audience_type}`),
      disabledCheckbox: !isEdit,
      isEdit,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
    // console.log('tmp', tmp)
  });
  return data;
}

export function buildTableGroupColumns(columnID, columnsActive) {
  // console.log(columnName, columnStatus, columnsActive);
  const columns = [
    {
      Header: columnID.label,
      id: columnID.value,
      accessor: columnID.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellPromotionCodeId,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
  ];
  columns.push({
    Header: 'Info Remaining',
    columns: buildTableColumns(columnsActive),
  });
  // console.log("123",columns)
  return columns;
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    if (property.value === 'code_status') {
      column.Cell = CellCodeStatus;
      column.placement = 'code_status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
    } else if (property.value === 'audience_type') {
      column.accessor = `segmentTypeDisplay`;
      column.placement = 'left';
    } else if (property.value === 'name') {
      column.Cell = CellText;
    } else if (property.value === 'story_id') {
      column.Cell = props =>
        CellAllocatedJourney({ ...props, isOpenNewTab: true });
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      if (property.value === 'allocated_audience') {
        column.displayFormat = initNumberWithoutDecimal();
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      // column.placement = 'left';
      column.right = false;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    columns.push(column);
  });
  return columns;
}
export const getLabelModalDeleteCodes = (selectRow, type, isSelectAll) => {
  let label = 'content';
  if (isSelectAll) {
    label = `Are you sure you want to delete all ${type} permanently ?`;
  } else if (selectRow.size === 1) {
    label = `Are you sure you want to delete this ${type} permanently ?`;
  } else if (type === 'code') {
    const arr = [];
    selectRow.forEach(each => {
      if (Number(each.code_status.id) === 1) {
        arr.push(each);
      }
    });
    label = `Are you sure you want to delete this ${arr.length} permanently ?`;
  }
  return label;
};
export const getInfoPromotion = data => {
  const dataOut = [];
  data[0]?.properties?.forEach(each => {
    if (
      [
        'story_name',
        'campaign_name',
        'variant_name',
        'code_status',
        'campaign_id',
        'story_id',
        'variant_id',
        'audience_type',
        'allocated_audience',
        'allocated_time',
        'last_used_source',
        'last_used_time',
        'last_updated',
      ].includes(each.propertyCode)
    ) {
      dataOut.push(each);
    }
  });
  return dataOut;
};
export function updateProperties(data, newProperties) {
  const removeCodes = [
    'pool_id',
    'is_deleted',
    'code_status',
    'campaign_id',
    'story_id',
    'variant_id',
    'audience_type',
    'allocated_audience',
    'allocated_time',
    'last_used_source',
    'last_used_time',
    'last_updated'
  ];
  const updatedGroup = {
    ...data[0],
    properties: [
      ...data[0].properties.filter(
        prop => !removeCodes.includes(prop.itemPropertyName),
      ),
      ...newProperties,
    ],
  };
  return [updatedGroup, ...data.slice(1)];
}

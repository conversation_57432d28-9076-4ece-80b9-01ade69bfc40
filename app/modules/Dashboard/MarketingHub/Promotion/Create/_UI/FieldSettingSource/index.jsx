/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable indent */
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Grid from '@material-ui/core/Grid';
import { UILoading as Loading, UITextField } from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';
import PropTypes from 'prop-types';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import { RadioGroup } from '@material-ui/core';
import DateFnsUtils from '@date-io/date-fns';
import UISelectWithAPI from 'components/form/UISelectWithAPI';
// import Box from '../../../../../../../components/Templates/LayoutContent/Box';
import { init, reset, updateValue } from '../../../../../../../redux/actions';
import {
  // makeSelectCreateSettingsDataConfig,
  makeSelectCreateSettings,
} from './selectors';
import WrapperCreateSource from '../../../../../../../containers/modals/WrapperCreateSource';
import {
  WrapperContent,
  StyleContent,
  WrapperStyle,
  StyledText,
  Item,
  CirclePoint,
  Wrapper,
  UIInputCalendarStyled,
  KeyboardTimePickerStyled,
} from './styles';
import {
  ButtonRemove,
  ButtonTag,
  FormControlLabelStyled,
  Title,
} from '../../styled';
import { LableSelect, Span } from '../FieldExpiration/styles';
import {
  MAP_REPEAT_TYPE,
  PERIOD_LIST_REPEAT,
  PERIOD_MAP_CODE,
  PERIOD_MAP_REPEAT,
  mapDataListSource,
} from './utils';
import UISelectCondition from '../../../../../../../components/form/UISelectCondition';
import { UINumberStyled } from '../../../../../../../components/common/UIFrequencyCapping/styled';
import { DisplayTimeZone } from '../../../../../Profile/Segment/Create/styles';
import { getLabelPortalTimeZone } from '../../../../../utils';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { WrapperDays } from '../../../../../../../components/common/UISchedulerTrigger/styled';
import WrapperDisable from '../../../../../../../components/common/WrapperDisable';
import LabeledSelection from '../../../../../../../components/Molecules/LabeledSelection';
import { DayOfMonths } from '../../../../../../../components/Organisms/ComputationSchedule/styled';
import {
  DAY_OF_MONTH,
  END_OF_MONTH,
} from '../../../../../../../components/Organisms/ComputationSchedule/constant';
import { Flex, Icon } from '@antscorp/antsomi-ui';
import Radio from '../../../../../../../components/Molecules/Radio';
import IntegrationCreateDrawer from '../../../PromotionIntegration/Create/Drawer';
import { translate, translations } from '@antscorp/antsomi-locales';

const MAP_TITLE = {
  TITLE_INTEGRATION: translate(
    translations._TAB_PROMOTION_INTE,
    'Promotion Integration',
  ),
  TITLE_SOURCE: translate(translations._COLUMN_SOURCE, 'Source'),
  TITLE_INTERVAL: translate(translations._ACT_ADJUST, 'Interval'),
};

function ContentSettings(props) {
  const {
    main,
    onInit,
    design,
    sourceId,
    isViewMode,
    onAfterChangeSource,
  } = props;
  const {
    isLoading,
    settingDateTime,
    repeatBy,
    andRemain,
    codeQuantity,
    settings,
  } = main;

  // States
  const [newSourceId, setNewSourceId] = useState();
  const [depsRefetchDataApi, setDepsRefetchDataApi] = useState(0);

  const handleChange = (name, valueChange) => {
    props.onChange({ name, value: valueChange });
  };
  const onChangeSource = value => {
    if (newSourceId) {
      setNewSourceId();
    }

    props.onChange({ name: 'sourceId', value });
    if (typeof onAfterChangeSource === 'function') {
      onAfterChangeSource(value?.source_id);
    }
  };

  const handleAfterSaveSource = data => {
    setDepsRefetchDataApi(depsRefetchDataApi + 1);
    setNewSourceId(data);
  };

  useEffect(() => {
    onInit({ design, sourceId });
    return () => props.onReset();
  }, []);
  const renderContent = (data, key) => {
    switch (data.dataType) {
      case 'string': {
        return (
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={3}>
              <Span>
                {data.label} <span style={{ color: '#ff0000' }}> *</span>
              </Span>
            </Grid>
            <Grid item xs={9}>
              {isViewMode ? (
                <StyledText>{main.inputs[key] || ''}</StyledText>
              ) : (
                <WrapperStyle>
                  <UITextField
                    fullWidth
                    value={main.inputs[key] || ''}
                    onChange={e =>
                      props?.onChangeInputs({ name: key, value: e })
                    }
                    firstText={
                      main.inputsError[key] && main.inputsError[key].label
                    }
                    textFieldProps={{
                      // disabled,
                      size: 'small',
                      multiline: false,
                      rowsMax: 1,
                      error:
                        main.inputsError[key] && main.inputsError[key].error,
                      fullWidth: true,
                    }}
                    // inputProps={props.inputProps || {}}
                  />
                </WrapperStyle>
              )}
            </Grid>
          </Grid>
        );
      }

      default: {
        return null;
      }
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Create/FieldSetting/index.jsx">
      <WrapperContent>
        <StyleContent>
          <Loading isLoading={isLoading} />
          <Flex vertical gap={15}>
            <Title>{MAP_TITLE.TITLE_INTEGRATION}</Title>
            <Grid container>
              <Grid item xs={12} md={7}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={3}>
                        <Span>
                          {MAP_TITLE.TITLE_SOURCE}{' '}
                          <span style={{ color: '#ff0000' }}> *</span>
                        </Span>
                      </Grid>
                      <Grid item xs={9}>
                        <WrapperStyle>
                          <UISelectWithAPI
                            placeholder="Select an item"
                            errors={[]}
                            // placeholderTranslateCode={
                            //   TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM
                            // }
                            // {...props}
                            // options={props.options}
                            initData={newSourceId || sourceId}
                            extraDepsFetchDataAPI={depsRefetchDataApi}
                            // disabled={props.disabled}
                            onChange={onChangeSource}
                            isGetFullValue
                            keyAPI="promotionSource"
                            FooterComponent={WrapperCreateSource}
                            isViewMode={isViewMode}
                            mapBluePrintFn={mapDataListSource}
                          />
                        </WrapperStyle>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    {settings &&
                      settings.inputs &&
                      settings.inputs.map(item =>
                        renderContent(settings.settings[item], item),
                      )}
                  </Grid>
                  <Grid item xs={12}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Grid container spacing={2} alignContent="center">
                          <Grid item xs={3}>
                            <Span>
                              {MAP_TITLE.TITLE_INTERVAL}{' '}
                              <span style={{ color: '#ff0000' }}> *</span>
                            </Span>
                          </Grid>
                          <Grid item xs={9}>
                            <Flex gap={10} vertical>
                              <Span>
                                How to get the code to the promotion pool?
                              </Span>
                              {isViewMode ? (
                                <div>
                                  {main.optionInterval === 'manually' ? (
                                    <Item>
                                      <CirclePoint />
                                      <LableSelect>Manually</LableSelect>
                                    </Item>
                                  ) : (
                                    <Item>
                                      <CirclePoint />
                                      <LableSelect>Repeat by time</LableSelect>
                                    </Item>
                                  )}
                                </div>
                              ) : (
                                <RadioGroup
                                  aria-label="interval"
                                  name="interval"
                                  value={main.optionInterval}
                                  onChange={e =>
                                    handleChange(
                                      'optionInterval',
                                      e.target.value,
                                    )
                                  }
                                  style={{
                                    gap: 10,
                                  }}
                                >
                                  <FormControlLabelStyled
                                    value="manually"
                                    control={
                                      <Radio color="primary" size="small" />
                                    }
                                    label={<LableSelect>Manually</LableSelect>}
                                  />
                                  <FormControlLabelStyled
                                    value="repeat_time"
                                    control={
                                      <Radio color="primary" size="small" />
                                    }
                                    label={
                                      <LableSelect>Repeat by time</LableSelect>
                                    }
                                  />
                                  {main.optionInterval === 'repeat_time' && (
                                    <Flex
                                      vertical
                                      gap={15}
                                      style={{ marginLeft: 26 }}
                                    >
                                      <Flex gap={20}>
                                        <Flex gap={5} align="center">
                                          <Span>Repeat by</Span>
                                          <div
                                            style={{
                                              minWidth: '100px',
                                              width: '100%',
                                              flex: 1,
                                            }}
                                          >
                                            <UISelectCondition
                                              use="tree"
                                              isSearchable={false}
                                              options={PERIOD_LIST_REPEAT}
                                              value={
                                                PERIOD_MAP_REPEAT[repeatBy.type]
                                              }
                                              onChange={value =>
                                                handleChange(
                                                  'repeatBy',
                                                  value.value,
                                                )
                                              }
                                              // fullWidthPopover={false}
                                              labelWidth="100%"
                                              minWidth="100%"
                                              // width={100}
                                              // placeholderTranslateCode={props.placeholderTranslateCode}
                                            />
                                          </div>
                                        </Flex>
                                        <Flex gap={10} align="center">
                                          <Span>every</Span>
                                          <UINumberStyled
                                            name="times"
                                            onChange={e =>
                                              handleChange('every', e)
                                            }
                                            value={repeatBy.value}
                                            min={1}
                                            defaultValue={1}
                                            width={70}
                                          />
                                          <Span>
                                            {MAP_REPEAT_TYPE[repeatBy.type]}
                                          </Span>
                                        </Flex>
                                      </Flex>
                                      {repeatBy.type === 'months' && (
                                        <div>
                                          <Span>
                                            Repeat on day of the month
                                          </Span>
                                          <div
                                            className="container-field"
                                            style={{ maxWidth: '475px' }}
                                          >
                                            <DayOfMonths>
                                              {DAY_OF_MONTH.map(item => {
                                                return (
                                                  <span
                                                    key={item}
                                                    onClick={() =>
                                                      handleChange(
                                                        'optionWeek',
                                                        item,
                                                      )
                                                    }
                                                    className={`day${
                                                      (
                                                        main.optionWeek
                                                          .optionsSelected || []
                                                      ).includes(item)
                                                        ? ' active'
                                                        : ''
                                                    }`}
                                                  >
                                                    {item}
                                                  </span>
                                                );
                                              })}
                                              <span
                                                className={`day${
                                                  (
                                                    main.optionWeek
                                                      .optionsSelected || []
                                                  ).includes(END_OF_MONTH)
                                                    ? ' active'
                                                    : ''
                                                }`}
                                                onClick={() =>
                                                  handleChange(
                                                    'optionWeek',
                                                    END_OF_MONTH,
                                                  )
                                                }
                                              >
                                                End of month
                                              </span>
                                            </DayOfMonths>
                                          </div>
                                        </div>
                                      )}
                                      {repeatBy.type === 'weeks' && (
                                        <div>
                                          <Span>Repeat on day of the week</Span>
                                          <WrapperDays>
                                            <WrapperDisable>
                                              {main.optionWeek.options.map(
                                                option => (
                                                  <LabeledSelection
                                                    item={option}
                                                    name={option.value}
                                                    checked={(
                                                      main.optionWeek
                                                        .optionsSelected || []
                                                    ).includes(option.value)}
                                                    onClick={value =>
                                                      handleChange(
                                                        'optionWeek',
                                                        value.value,
                                                      )
                                                    }
                                                    handleChange={() => {}}
                                                    modifiers="checkbox"
                                                    label={option.label}
                                                    key={option.value}
                                                  />
                                                ),
                                              )}
                                            </WrapperDisable>
                                          </WrapperDays>
                                        </div>
                                      )}
                                      <Flex gap={10} align="center">
                                        <Span
                                          style={{
                                            fontSize: 11,
                                            flexShrink: 0,
                                            color: '#595959',
                                          }}
                                        >
                                          Start date
                                        </Span>

                                        <MuiPickersUtilsProvider
                                          utils={DateFnsUtils}
                                        >
                                          <UIInputCalendarStyled
                                            // className="form-control private-form__control"
                                            // type={item.get('dataType')}
                                            placeholder="MM/DD/YYYY"
                                            value={settingDateTime.value.getTime()}
                                            onChange={e =>
                                              handleChange('date', e)
                                            }
                                            isShowLabel={false}
                                            error={
                                              settingDateTime.date.isValidate
                                            }
                                            helperText={
                                              settingDateTime.date.isValidate
                                                ? getTranslateMessage(
                                                    TRANSLATE_KEY._NOTI_INVALID_DATE_FORMAT,
                                                    'Invalid Date Format',
                                                  )
                                                : ''
                                            }
                                            disablePast
                                            style={{
                                              height: 30,
                                              minWidth: 125,
                                            }}
                                            otherProps={{
                                              style: { padding: 0 },
                                            }}
                                          />
                                        </MuiPickersUtilsProvider>
                                        <Span
                                          style={{
                                            fontSize: 11,
                                            flexShrink: 0,
                                            color: '#595959',
                                          }}
                                        >
                                          at
                                        </Span>

                                        <MuiPickersUtilsProvider
                                          utils={DateFnsUtils}
                                        >
                                          <KeyboardTimePickerStyled
                                            style={{ width: 100 }}
                                            // label={getTranslateMessage(
                                            //   TRANSLATE_KEY._,
                                            //   'Start date',
                                            // )}
                                            keyboardIcon={
                                              <Icon
                                                type="icon-ants-clock"
                                                size={24}
                                                color="#595959"
                                              />
                                            }
                                            value={settingDateTime.value}
                                            onChange={e =>
                                              handleChange('time', e)
                                            }
                                            // onAccept={e => handleChange('time', e)}
                                            KeyboardButtonProps={{
                                              'aria-label': 'change time',
                                            }}
                                            mask="__:__ _M"
                                            helperText={
                                              settingDateTime.time.isValidate
                                                ? getTranslateMessage(
                                                    TRANSLATE_KEY._,
                                                    'Invalid Date',
                                                  )
                                                : ''
                                            }
                                            error={
                                              settingDateTime.time.isValidate
                                            }
                                            placeholder="HH:mm"
                                          />
                                        </MuiPickersUtilsProvider>
                                        <DisplayTimeZone>
                                          {getLabelPortalTimeZone()}
                                        </DisplayTimeZone>
                                      </Flex>
                                      <div>
                                        {!andRemain.status ? (
                                          <ButtonTag
                                            onClick={e =>
                                              handleChange('and', e)
                                            }
                                          >
                                            <Icon
                                              type="icon-ants-plus-square"
                                              size={10}
                                              style={{ marginRight: 8 }}
                                            />
                                            AND
                                          </ButtonTag>
                                        ) : (
                                          <></>
                                        )}
                                        {andRemain.status && (
                                          <Flex align="center" gap={20}>
                                            <Flex align="center" gap={5}>
                                              <Span>Remaining less than</Span>
                                              <UINumberStyled
                                                name="times"
                                                onChange={e =>
                                                  handleChange('andValue', e)
                                                }
                                                value={andRemain.value}
                                                min={1}
                                                defaultValue={1}
                                                width={70}
                                              />
                                            </Flex>
                                            <Flex align="center" gap={10}>
                                              <UISelectCondition
                                                use="tree"
                                                isSearchable={false}
                                                options={PERIOD_MAP_CODE}
                                                value={andRemain.type}
                                                onChange={e =>
                                                  handleChange('andType', e)
                                                }
                                                labelWidth="70px"
                                                // width={100}
                                                // placeholderTranslateCode={props.placeholderTranslateCode}
                                              />
                                              <Span>code(s)</Span>
                                            </Flex>
                                            <ButtonRemove
                                              onClick={e =>
                                                handleChange('and', e)
                                              }
                                            >
                                              <Icon
                                                type="icon-ants-remove-slim"
                                                size={12}
                                                color="#005EB8"
                                              />
                                            </ButtonRemove>
                                          </Flex>
                                        )}
                                      </div>
                                    </Flex>
                                  )}
                                </RadioGroup>
                              )}
                            </Flex>
                          </Grid>
                        </Grid>
                      </Grid>
                      <Grid item xs={12}>
                        <Grid container spacing={2} alignContent="center">
                          <Grid item xs={3}>
                            <Span>
                              Code quantity{' '}
                              <span style={{ color: '#ff0000' }}> *</span>
                            </Span>
                          </Grid>
                          <Grid item xs={9}>
                            <Flex vertical gap={10}>
                              <Span>How many codes can be pulled at once?</Span>
                              {isViewMode ? (
                                <Wrapper>
                                  <span>{codeQuantity}</span>
                                  <span>code per times</span>
                                </Wrapper>
                              ) : (
                                <Flex gap={10} align="center">
                                  <UINumberStyled
                                    name="times"
                                    onChange={e =>
                                      handleChange('codeQuantity', e)
                                    }
                                    value={codeQuantity}
                                    min={1}
                                    defaultValue={1000}
                                    width={100}
                                  />
                                  <Span>code (s) per times</Span>
                                </Flex>
                              )}
                            </Flex>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Flex>
        </StyleContent>
      </WrapperContent>

      {/* Create Integration */}
      <IntegrationCreateDrawer
        onAfterSave={data => handleAfterSaveSource(data)}
      />
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  // dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
});

export function mapDispatchToProps(dispatch, props) {
  const { moduleConfig } = props;
  return {
    onChange: value =>
      dispatch(
        updateValue(
          `${moduleConfig.key}@@SETTING_SOURCE_POOL@@DATA_CONFIG`,
          value,
        ),
      ),
    onChangeInputs: value =>
      dispatch(
        updateValue(
          `${moduleConfig.key}@@SETTING_SOURCE_POOL_INPUTS@@DATA_CONFIG`,
          value,
        ),
      ),
    onReset: value =>
      dispatch(reset(`${moduleConfig.key}@@SETTING_SOURCE`, value)),
    onInit: value =>
      dispatch(init(`${moduleConfig.key}@@SETTING_SOURCE`, value)),
  };
}

ContentSettings.propTypes = {
  sourceId: PropTypes.string,
  main: PropTypes.any,
  // dataConfig: PropTypes.object,
  onChange: PropTypes.func.isRequired,
  onReset: PropTypes.func,
  onInit: PropTypes.func,
  design: PropTypes.string,
  isViewMode: PropTypes.bool,
  onAfterChangeSource: PropTypes.func,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ContentSettings);

/* ---------------------------------- NOTE ---------------------------------- */
// init: value => dispatch(init(`${PREFIX}@@SETTING_POOL`, value)),
// onSave: value => dispatch(update(`${PREFIX}@@SETTING_POOL`, value)),

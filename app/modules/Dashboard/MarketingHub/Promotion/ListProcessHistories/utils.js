import _isEmpty from 'lodash/isEmpty';
import {
  getLabelStatus,
  getLabelStatusWithType,
  getLabelStatusPromotion,
} from '../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellMainPromotion,
  CellProcessStatus,
  CellDate,
  CellCurrency,
  CellNumber,
  CellArray,
  CellToggleAPI,
  CellStatusProcess,
  CellPoolId,
} from '../../../../../containers/Table/Cell';
import {
  getstorageTypeLabel,
  getUpdateMethodLabel,
} from '../../../../../services/Abstract.data';
import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
import PromotionServices from '../../../../../services/BusinessObject';
import { initNumberWithoutDecimal } from '../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';
import CellResultMessage from '../../../../../containers/Table/Cell/_UI/CellResultMessage';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      id: tmp.pool_id,
      portal_id: {
        value: portalId,
      },
      process_status: getLabelStatusPromotion(
        parseInt(tmp.status),
        parseInt(tmp.process_status),
      ),
      original_process_status: tmp.process_status,
      c_user_id: tmp.c_user_name,
      u_user_id: tmp.u_user_name,
      disabled_status: tmp.status === 1 && tmp.process_status === 1,
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
    // console.log('tmp', tmp)
  });
  return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainPromotion,
      getIsDisabledEditName: () => false,
    },
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellText,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      Footer: '',
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}
export function buildTableColumns(columnsActive) {
  console.log('🚀 ~ buildTableColumns ~ columnsActive:', columnsActive);
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat, propertyCode } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'status') {
      column.Cell = CellStatusProcess;
      column.placement = 'status';
      column.className = `${dataType} ${property.value} ${'txt-status'}`;
      // column.className = `${dataType} ${property.value} ${'status'}`;
      column.width = COLUMNS_WIDTH.DEFAULT;
      column.minWidth = COLUMNS_WIDTH.DEFAULT;
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'pool_id') {
      column.Cell = CellPoolId;
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      if (property.value === 'duration') {
        column.displayFormat = initNumberWithoutDecimal();
        column.width = COLUMNS_WIDTH.DEFAULT;
        column.minWidth = COLUMNS_WIDTH.DEFAULT;
      }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    } else if (dataType === 'string') {
      column.Cell = CellResultMessage;
    }
    ['etime'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}
export const mapDataLisSource = data => {
  const dataOut = [{ value: 'manual', label: 'Manual Upload' }];
  data.map(each => {
    dataOut.push({
      ...each,
      value: each.source_id,
      label: each.source_name,
    });
  });
  return dataOut;
};

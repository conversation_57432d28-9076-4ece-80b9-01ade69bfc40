/* eslint-disable no-nested-ternary */
/* eslint-disable camelcase */
import _isEmpty from 'lodash/isEmpty';
import {
  getLabelStatus,
  getLabelStatusWithType,
  getLabelStatusPromotion,
} from '../../../../../utils/web/processStatus';
import {
  CellText,
  CellCustomerName,
  CellMainPromotion,
  CellProcessStatus,
  CellDate,
  CellCurrency,
  CellNumber,
  CellArray,
  CellToggleAPI,
  // CellToggleWithStyle,
  CellToggle,
  CellPoolId,
} from '../../../../../containers/Table/Cell';
import {
  getstorageTypeLabel,
  getUpdateMethodLabel,
} from '../../../../../services/Abstract.data';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  MAP_JOURNEY_ACCEPTED_ACTIONS as MAA,
  MENU_CODE,
} from '../../../../../utils/web/permission';
import PromotionServices from '../../../../../services/BusinessObject';
import { initNumberWithoutDecimal } from '../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';
import { getCurrentOwnerIds } from '../../../../../utils/web/cookie';
import { TABS } from '../Main/config';
import { mapBluePrint } from '../../../../../utils/web/renderForm';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(data, portalId, activeTab) {
  return mapBluePrint(data, item => {
    const isHavePermission = isShowAction(
      activeTab,
      item,
      MENU_CODE.PROMOTION_CENTER,
    );

    return {
      ...item,
      id: item.pool_id,
      source_name:
        !item.type || Number(item.type) === 1
          ? 'File Upload'
          : Number(item.type) === 3
          ? 'Rule-based Generation'
          : item.source_name,
      portal_id: {
        value: portalId,
      },
      process_status: getLabelStatusPromotion(
        parseInt(item.status),
        parseInt(item.process_status),
      ),
      original_process_status: item.process_status,
      c_user_id: item.c_user_name,
      u_user_id: item.u_user_name,
      disabled_status:
        (item.status === 1 && item.process_status === 1) ||
        item.process_status.name === 'computing' ||
        !isHavePermission,
      disabledCheckbox: !isHavePermission,
    };
  });

  // list.forEach(tmp => {
  //   const tempt = {
  //     ...tmp,
  //     id: tmp.pool_id,
  //     source_name:
  //       !tmp.type || Number(tmp.type) === 1 ? 'File Upload' : tmp.source_name,
  //     portal_id: {
  //       value: portalId,
  //     },
  //     process_status: getLabelStatusPromotion(
  //       parseInt(tmp.status),
  //       parseInt(tmp.process_status),
  //     ),
  //     original_process_status: tmp.process_status,
  //     c_user_id: tmp.c_user_name,
  //     u_user_id: tmp.u_user_name,
  //     disabled_status:
  //       (tmp.status === 1 && tmp.process_status === 1) || !isEdit,
  //     disabledCheckbox: !isEdit,
  //     isEdit,
  //   };
  //   data.list.push(tempt);
  //   data.map[tempt.id] = tempt;
  //   // console.log('tmp', tmp)
  // });
  // return data;
}

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  // console.log(columnName, columnStatus, columnsActive);
  if (_isEmpty(columnName)) return [];
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: COLUMNS_WIDTH.SWITCH,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      disableSortBy: parseInt(columnStatus.isSort) !== 1,
      sticky: 'left',
      Cell: CellToggle,
      className: 'tbl-border-left padding-left-right-10',
      placement: 'center',
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      disableSortBy: false,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      sticky: 'left',
      Cell: CellMainPromotion,
      getIsDisabledEditName: () => false,
    },
    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, codeType, displayFormat, propertyCode } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
    };
    if (property.value === 'process_status') {
      column.Cell = CellProcessStatus;
      column.placement = 'status';
      // column.className = `${dataType} ${property.value} ${'status'}`;
    } else if (property.value === 'name') {
      column.Cell = CellCustomerName;
    } else if (property.value === 'pool_id') {
      column.Cell = CellPoolId;
    } else if (property.value === 'c_user_id') {
      column.accessor = `c_user_id`;
      column.Cell = CellText;
    } else if (dataType === 'number') {
      // if (property.value === 'segment_size') {
      //   column.displayFormat = initNumberWithoutDecimal();
      // }
      const CellTempt =
        parseInt(property.isCurrency) === 1 ? CellCurrency : CellNumber;
      column.Cell = CellTempt;
      column.placement = 'right';
      // column.right = true;
    } else if (dataType === 'datetime') {
      column.Cell = CellDate;
    } else if (dataType.startsWith('array')) {
      column.Cell = CellArray;
    } else if (dataType === 'object') {
      column.Cell = CellText;
    }
    ['etime'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}
export const mapDataLisSource = data => {
  // const dataOut = [{ value: 'manual', label: 'Manual Upload' }];
  const dataOut = [];
  data.map(each => {
    if (Number(each.status) === 1) {
      dataOut.push({
        ...each,
        value: each.source_id,
        label: each.source_name,
      });
    }
  });
  return dataOut;
};

/**
 * Determines if an action should be displayed based on user permissions and pool type.
 * @param {string} tabType - Tab type, can be 'owner' or 'share_with_me'.
 * @param {Object} activePool - Active pool data containing share access details.
 * @param {string} menuCode - Menu code to check permission.
 * @return {boolean} True if the action should be displayed based on conditions.
 */
export const isShowAction = (tabType, activePool, menuCode) => {
  let isCheck = false;

  // Get the current user's ID
  const userId = getCurrentOwnerIds();

  // Check if the user has edit permissions for items they created
  const isEditByUser = checkingRoleScope(
    menuCode,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.CREATED_BY_USER,
    true,
  );

  // Check if the user has permissions to edit everything
  const isEditEverything = checkingRoleScope(
    menuCode,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.EVERYTHING,
    true,
  );

  // Extract share access information from the active pool
  const { share_access = {} } = activePool;
  const { list_access = [] } = share_access;

  const userHasAccess = list_access.find(
    item => Number(item.user_id) === Number(userId),
  );

  if (tabType === TABS.OWNER) {
    if (isEditByUser && Number(userId) === Number(activePool.object_owner_id)) {
      isCheck = isEditByUser;
    } else {
      isCheck = isEditEverything;
    }

    // share public role with edit
    if (
      ((Number(share_access.public_role) === 2 &&
        Number(share_access.is_public) === 1) ||
        Number(userHasAccess?.role) === 2) &&
      isEditByUser
    ) {
      isCheck = true;
    }
  }

  if (tabType === TABS.SHARE_WITH_ME) {
    if (userHasAccess) {
      isCheck =
        Number(userHasAccess?.allow_edit) === 1 &&
        (isEditByUser || isEditEverything);
    }
    if (isEditEverything) {
      isCheck = isEditEverything;
    }

    if (
      (Number(share_access.public_role) === 2 ||
        Number(userHasAccess?.role) === 2) &&
      isEditByUser
    ) {
      isCheck = true;
    }
  }

  return isCheck;
};

/* eslint-disable react/prop-types */
/* eslint-disable indent */
/* eslint-disable import/order */
// Libraries
import React, { Fragment, memo, useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import PropTypes from 'prop-types';

// Translations
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { MAP_TRANSLATE } from '../../../../../../../../containers/Drawer/DrawerIntegration/constants.translate';

// Styles
import {
  BlockContent,
  Divider,
  WrapperStyle,
  WrapperCenterFlexEnd,
  useStyles,
  WrapperLabelOption,
  WrapperFixWidth,
  UINumberStyled,
  WrapperTitleItem,
} from './styles';
import {
  BlockContainer,
  BlockLeft,
  BlockRight,
  BlockTitle,
  Title,
} from 'containers/Drawer/DrawerIntegration/components/Content/styled';

// Components
import { Alert } from '@antscorp/antsomi-ui';
import {
  FormControlLabel,
  FormHelperText,
  Grid,
  Radio,
  RadioGroup,
} from '@material-ui/core';
import UIFrequencyCapping from 'components/common/UIFrequencyCappingV2';
import { UIWrapperDisable } from '@xlab-team/ui-components';

// Utils
import {
  generateKey,
  getObjectPropSafely,
} from '../../../../../../../../utils/common';
import { checkHideInfoFields } from '../../../../../../../../containers/Drawer/DrawerIntegration/utils';
import { CATALOG_WITH_RESPONSE } from '../../../../../../../../containers/Drawer/DrawerIntegration/constants';
import { CircleInfoIcon } from '@antscorp/antsomi-ui/es/components/icons';

const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ALERT_EDIT_DESTINATION,
    'If you edit this destination will affect the input of other features.',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titleGeneralInformation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titleAdvancedSetting: getTranslateMessage(
    TRANSLATE_KEY._DES_ADV_PUSH_SETTING,
    'Advanced Push Settings (Optional)',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_SETTING,
    'General Setting',
  ),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titlDeliveredRate: getTranslateMessage(
    TRANSLATE_KEY._TITLE_DES_DELIVERED_RATE,
    'Delivered rate',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  limitSentRate: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_LIMIT_SEND_RATE,
    'Limit send rate',
  ),
  timePerson: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_TIME_PER_SECOND,
    'time(s) per second',
  ),
};

const InputComponent = memo(props => (
  <BlockContainer container className={props.classes.item}>
    {props.componentEl(props)}
  </BlockContainer>
));

function GeneralInformation(props) {
  const {
    design,
    dataConfig,
    isHideDivider,
    isShowDelivered,
    channelId,
    isHiddenCatalogSettings,
    isInitDone,
    isValidate,
    activeStep,
    labelSettings,
    labelCapping,
    onChangeData,
    isHasPermissionEdit,
    isOwner,
  } = props;

  const catalogCodeSelected =
    dataConfig?.destinationCatalog?.value?.catalogCode;

  // Hooks
  const classes = useStyles();

  const infoFields = useMemo(() => {
    if (_isEmpty(dataConfig)) return [];
    const infoTemp = getObjectPropSafely(() => dataConfig.infoFields, []);

    if (isHiddenCatalogSettings && !_isEmpty(infoTemp)) {
      return infoTemp.filter(each => each !== 'destinationCatalog');
    }

    return infoTemp;
  }, [dataConfig, isHiddenCatalogSettings]);

  const isHideMethod = useMemo(
    () => _get(dataConfig, 'method.options.length', 0) === 1,
    [dataConfig?.method?.options],
  );

  const renderDeliveredRate = isShow => {
    if (!isShow || _isEmpty(getObjectPropSafely(() => dataConfig.deliveryRate)))
      return null;

    return (
      <BlockContainer container>
        <BlockLeft item xs={2}>
          <WrapperCenterFlexEnd>
            <Title>{MAP_TITLE.titlDeliveredRate}</Title>
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item xs={9}>
          <UIWrapperDisable disabled={dataConfig.deliveryRate.disabled}>
            <RadioGroup
              aria-label="segmentMember"
              name="segmentMember"
              defaultValue={dataConfig.deliveryRate.type}
              value={dataConfig.deliveryRate.type}
              onChange={onChangeData('deliveryRate')}
              style={{ gap: '5px', marginLeft: '10px' }}
            >
              <FormControlLabel
                value="normal"
                control={
                  <Radio
                    color="primary"
                    style={{ width: '20px', height: '20px' }}
                  />
                }
                label={
                  <WrapperLabelOption style={{ marginLeft: '4px' }}>
                    <UIWrapperDisable>{MAP_TITLE.possible}</UIWrapperDisable>
                  </WrapperLabelOption>
                }
                className={classes.customSizeInput}
              />
              <div className="d-flex align-items-center">
                <FormControlLabel
                  value="limit"
                  control={
                    <Radio
                      color="primary"
                      style={{ width: '20px', height: '20px' }}
                    />
                  }
                  label={
                    <WrapperLabelOption style={{ marginLeft: '4px' }}>
                      {/* {MAP_TRANSLATE.labelLimit} */}
                      {MAP_TITLE.limitSentRate}
                    </WrapperLabelOption>
                  }
                  className={classes.customSizeInput}
                />
                <WrapperFixWidth>
                  <UINumberStyled
                    name="times"
                    onChange={onChangeData('deliveryRate')}
                    value={dataConfig.deliveryRate.limit}
                    min={1}
                    // max={100}
                    defaultValue={1}
                    defaultEmptyValue={1}
                    width={60}
                  />
                </WrapperFixWidth>

                <WrapperTitleItem style={{ marginLeft: '16px' }}>
                  {MAP_TITLE.timePerson}
                </WrapperTitleItem>
              </div>
              {isValidate && (
                <div style={{ color: '#EF3340', fontSize: '12px' }}>
                  Please input limit send rate values
                </div>
              )}
            </RadioGroup>
          </UIWrapperDisable>
        </BlockRight>
      </BlockContainer>
    );
  };

  return (
    <Fragment>
      <BlockContent container>
        {design === 'update' && (
          <>
            <Alert
              showIcon
              type="warning"
              variant="outline"
              message={MAP_TRANSLATE.notiAlertEdit}
              style={{ marginBottom: 15, width: '100%' }}
            />

            {CATALOG_WITH_RESPONSE.includes(catalogCodeSelected) && (
              <Alert
                showIcon
                closable
                type="info"
                variant="outline"
                message={MAP_TRANSLATE.notiInfoRetry}
                style={{ marginBottom: 15, width: '100%' }}
                icon={<CircleInfoIcon size={24} />}
              />
            )}
          </>
        )}
        <Grid item xs={12}>
          <BlockTitle>{MAP_TITLE.titleGeneralInformation}</BlockTitle>
        </Grid>
        <Grid item xs={12}>
          {infoFields.map(
            item =>
              !checkHideInfoFields(item) && (
                <InputComponent
                  key={item}
                  {...dataConfig[item]}
                  onChange={onChangeData}
                  classes={classes}
                  isHasPermissionEdit={isHasPermissionEdit}
                  isOwner={isOwner}
                />
              ),
          )}
          {!isHiddenCatalogSettings &&
            dataConfig &&
            dataConfig.extraInfoFields.map(item => {
              if (item === 'method' && isHideMethod) return null;

              return (
                <InputComponent
                  key={item}
                  {...dataConfig[item]}
                  onChange={onChangeData}
                  classes={classes}
                  isHasPermissionEdit={isHasPermissionEdit}
                  isOwner={isOwner}
                />
              );
            })}
          {dataConfig.destinationCatalog.options.length === 0 && (
            <FormHelperText error>{MAP_TITLE.notiNotCreateDest}</FormHelperText>
          )}
        </Grid>
      </BlockContent>
      {!isHideDivider && <Divider style={{ width: '65%' }} />}
      {dataConfig.configFields.length > 0 && (
        <>
          <BlockContent
            container
            style={{ marginBottom: isHideDivider ? '20px' : '0px' }}
          >
            <Grid item xs={12}>
              <BlockTitle>{MAP_TITLE.titlConfigFields}</BlockTitle>
            </Grid>
            <Grid item xs={12}>
              {dataConfig &&
                dataConfig.configFields.map(item => (
                  <InputComponent
                    key={item}
                    {...dataConfig[item]}
                    onChange={onChangeData}
                    classes={classes}
                    isHasPermissionEdit={isHasPermissionEdit}
                    isOwner={isOwner}
                  />
                ))}
            </Grid>
          </BlockContent>
          {!isHideDivider && <Divider style={{ width: '65%' }} />}
        </>
      )}
      <BlockContent container>
        <Grid item xs={12}>
          <BlockTitle>
            {labelSettings || MAP_TITLE.titleAdvancedSetting}
          </BlockTitle>
        </Grid>
        <Grid item xs={12}>
          <BlockContainer container>
            <BlockLeft item sm={2}>
              <WrapperCenterFlexEnd>
                <Title>{labelCapping || MAP_TITLE.titlGeneralSetting}</Title>
              </WrapperCenterFlexEnd>
            </BlockLeft>
            <BlockRight item sm={10}>
              <WrapperStyle className="custom-style-deep">
                <UIFrequencyCapping
                  isNoPadding
                  channelId={channelId}
                  isShowLabel={false}
                  onChange={onChangeData('frequencyCapping')}
                  initData={useMemo(() => dataConfig.frequencyCapping.value, [
                    isInitDone,
                    activeStep,
                  ])}
                  sizeCheckbox="large"
                  isNoPaddingBottom
                  componentId={useMemo(() => generateKey(), [isInitDone])}
                  disabled={dataConfig.frequencyCapping.disabled}
                />
              </WrapperStyle>
            </BlockRight>
          </BlockContainer>
        </Grid>
        {renderDeliveredRate(isShowDelivered)}
      </BlockContent>
    </Fragment>
  );
}

GeneralInformation.propTypes = {
  isInitDone: PropTypes.bool,
  isHideDivider: PropTypes.bool,
  isValidate: PropTypes.bool,
  isShowDelivered: PropTypes.bool,
  isHiddenCatalogSettings: PropTypes.bool,
  labelSettings: PropTypes.string,
  labelCapping: PropTypes.string,
  activeStep: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  dataConfig: PropTypes.any,
  channelId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  design: PropTypes.string,
  onChangeData: PropTypes.func,
};
GeneralInformation.defaultProps = {
  isInitDone: false,
  isHideDivider: false,
  isValidate: false,
  isShowDelivered: false,
  labelSettings: '',
  labelCapping: '',
  activeStep: '',
  isHiddenCatalogSettings: false,
  dataConfig: {},
  design: '',
  channelId: '',
  onChangeData: () => {},
};
export default GeneralInformation;

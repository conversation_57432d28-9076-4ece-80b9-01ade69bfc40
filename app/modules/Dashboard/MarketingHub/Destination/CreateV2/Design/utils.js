/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
import { MAP_VALIDATE, MAP_INPUT_TYPE, getTypeRender } from './utils.map';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';

export const initInputElement = (
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: !isRequired,
  validate: validate || (() => ({ errors: [], isValidate: false })),
  initValue: value || null,
});

export const mapMethods = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.name,
    value: item.name,
    label: item.label,
  }));

export const mapDestCatalog = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.catalogId,
    value: item.catalogId,
    label: item.translateLabel || item.catalogName,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};
export const mapConfigureField = (fields, settings) => {
  // console.log('fields, settings: ', fields, settings);
  const data = {};

  fields.forEach(each => {
    const item = settings[each];
    // const initValue = (initData || {})[each] || '';
    const value = '';
    const typeRender = getTypeRender(item);
    const label = getTranslateMessage(item.labelCode, item.label);
    const temp = {
      ...item,
      ...initInputElement(
        each,
        value,
        item.maxLength,
        item.isRequired,
        label,
        null,
        MAP_VALIDATE[typeRender],
      ),
      isValidate: !item.isRequired,
      componentEl: MAP_INPUT_TYPE[typeRender],
    };
    data[each] = temp;
  });
  return data;
};

/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { combineReducers } from 'redux';
import ReduxTypes from '../../../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
// import { PROMPT_TYPES } from './Content/_UI/Permission/utils';
import { mapValueToFE, initDefaultDataConfig, mapBehavior } from './utils';
import { mapDestCatalog, mapMethods, mapConfigureField } from '../utils';
import { createTooltip } from './utils.map';

export const initStateCreateDestionation = () => ({
  isInitDone: false,
  dataConfig: initDefaultDataConfig(),
  destCatalogs: { map: {}, list: [] },
  methods: { map: {}, list: [] },
  activeDestCatalog: {},
  activeRow: {},
  channelId: null,
  catalogId: null,
  cachePromptId: null,
  cachePromptType: null,
  design: 'create',
  allowSave: true,
  isShowModalPrompt: false,
  isLoadingConfigFields: false,
  isLoading: true,
  isDoing: false,
  isLimitedDestination: false,
  isLimitDestinationCreate: false,
  accessInfo: {
    general: {},
    listAccess: [],
  },
});

const validateAll = draft => {
  const {
    dataConfig: {
      configFields,
      infoFields,
      extraInfoFields,
      infoSiteSetupFields,
    },
  } = draft;
  return [
    ...configFields,
    ...infoFields,
    ...extraInfoFields,
    ...infoSiteSetupFields,
  ].every(each => {
    return draft.dataConfig[each].isValidate;
  });
};

const mainReducerFor = moduleConfig => {
  const PREFIX = MODULE_CONFIG.key;
  const mainReducer = (state = initStateCreateDestionation(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initStateCreateDestionation();
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          draft.isInitDone = false;
          draft.allowSave = true;
          const { channelId, activeRow, design, queryDesign } = action.payload;
          draft.channelId = channelId;
          draft.design = design;
          if (design === 'create') {
            draft.activeRow = {};
          } else if (design === 'preview' || design === 'update') {
            draft.activeRow = activeRow;
            draft.catalogId = activeRow.catalogId;
          }
          return draft;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          const {
            dataConfig: {
              configFields,
              infoFields,
              extraInfoFields,
              infoSiteSetupFields,
              infoAdvancedSettingFields,
              infoNotificationFields,
            },
            design,
            activeRow,
            dataConfig,
          } = state;
          if (design === 'preview' || design === 'update') {
            draft.allowSave = false;

            const mapValue = mapValueToFE({ activeRow, dataConfig });
            [
              ...configFields,
              ...infoFields,
              ...extraInfoFields,
              ...infoSiteSetupFields,
              ...infoAdvancedSettingFields,
              ...infoNotificationFields,
            ].forEach(each => {
              const isToggleEl = [
                'autoReSub',
                'notHTTPS',
                'link',
                // 'webhooks', // CURRENTLY, THESE OPTIONS WEREN'T USED
                // 'serviceWorkers',
                // 'persistence',
                // 'safariCertificate',
              ].includes(each);

              if (isToggleEl) {
                draft.dataConfig[each].toggleProps.value = mapValue[each];
                // draft.dataConfig[each].toggleProps.disabled = true;

                if (each === 'link') {
                  draft.dataConfig.url.isHidden = !mapValue[each];
                }

                if (each === 'notHTTPS') {
                  draft.dataConfig.proxyOrigin.isHidden = !mapValue[each];
                }
              } else if (each === 'clickBehavior') {
                draft.dataConfig.clickBehavior.settings.matchingStrategy.value = mapBehavior(
                  'matching',
                  mapValue.clickBehavior.match,
                );
                draft.dataConfig.clickBehavior.settings.actionStrategy.value = mapBehavior(
                  'action',
                  mapValue.clickBehavior.action,
                );
              } else {
                draft.dataConfig[each].value = mapValue[each]; // string only
                draft.dataConfig[each].initValue = mapValue[each]; // string only
              }

              const { isValidate, errors } = draft.dataConfig[each].validate(
                draft.dataConfig[each],
              );
              draft.dataConfig[each].errors = errors;
              draft.dataConfig[each].isValidate = isValidate;
              if (design === 'preview') {
                draft.dataConfig[each].disabled = true;
              }
            });

            if (design === 'update') {
              draft.dataConfig.destinationCatalog.disabled = true;
              draft.dataConfig.method.disabled = true;
            }

            // CURRENTLY, THESE OPTIONS BELOW WEREN'T USED
            // const { types } = state.dataConfig.permissionPrompt.mapPrompts;
            // const options = state.dataConfig.permissionPrompt.promptTypeOptions;
            // const isEmptyTable = mapValue.prompts.promptsTable.length === 0;
            // // Enable Dropdown Permission & active prompt type when empty table & prompt
            // if (isEmptyTable && types.every(type => type.isHidden)) {
            //   draft.dataConfig.permissionPrompt.promptTypeOptions = options.map(
            //     option => {
            //       if (option.value === 'PUSH') {
            //         return {
            //           ...option,
            //           disabled: false,
            //         };
            //       }

            //       return option;
            //     },
            //   );

            //   draft.dataConfig.permissionPrompt.mapPrompts.currentType =
            //     PROMPT_TYPES.PUSH_SLIDE;
            //   draft.dataConfig.permissionPrompt.mapPrompts.types = types.map(
            //     type => {
            //       if (type === PROMPT_TYPES.PUSH_SLIDE) {
            //         return {
            //           ...type,
            //           isHidden: false,
            //         };
            //       }

            //       return type;
            //     },
            //   );
            // }

            draft.dataConfig.appId = mapValue.appId;
            draft.dataConfig.permissionPrompt.data =
              mapValue.prompts.promptsTable;
            draft.dataConfig.permissionPrompt.prompts =
              mapValue.prompts.prompts;
            draft.dataConfig.frequencyCapping.value = mapValue.frequencyCapping;
            draft.dataConfig.frequencyCapping.disabled = design === 'preview';
            draft.dataConfig.isValidate = validateAll(draft);
          }

          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.isDoing = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.isDoing = false;
          draft.isLimitedDestination = false;
          return draft;
        }
        case `${PREFIX}@@MODAL_PROMPT@@${ReduxTypes.UPDATE_VALUE}`: {
          const { isOpen } = action.payload;

          draft.isShowModalPrompt = isOpen;
          return draft;
        }
        case `${PREFIX}@@TOGGLE_LIMIT_DESTINATION_CREATE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isOpen } = action.payload;

          draft.isLimitDestinationCreate = isOpen;
          return draft;
        }
        case `${PREFIX}@@VALIDATE_PERMISSION_ACTIONS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // CURRENTLY, HIDE THEM BECAUSE HIDDEN DROPDOWN IN PROMPT PERMISSION
          // const { types } = state.dataConfig.permissionPrompt.mapPrompts;
          // const options = state.dataConfig.permissionPrompt.promptTypeOptions;

          // draft.dataConfig.permissionPrompt.promptTypeOptions = options.map(
          //   option => ({
          //     ...option,
          //     disabled:
          //       option.value === 'PUSH'
          //         ? types.every(item => item.isHidden)
          //         : true,
          //   }),
          // );
          return draft;
        }
        case `${PREFIX}@@UPDATE_PROMPT_ID@@${ReduxTypes.UPDATE_VALUE}`: {
          const { promptId, promptType } = action.payload;

          draft.cachePromptId = promptId;
          draft.cachePromptType = promptType;
          return draft;
        }
        case `${PREFIX}@@UPDATE_TABLE_PERMISSION@@${ReduxTypes.UPDATE_VALUE}`: {
          const { data } = action.payload;

          draft.dataConfig.permissionPrompt.data = data;
          return draft;
        }
        case `${PREFIX}@@REMOVE_PROMPT@@${ReduxTypes.UPDATE_VALUE}`: {
          const { promptId } = action.payload;

          const newPrompts = state.dataConfig.permissionPrompt.prompts.slidedown.prompts.filter(
            promptItem => promptItem.promptId !== promptId,
          );

          draft.dataConfig.permissionPrompt.prompts.slidedown.prompts = newPrompts;
          return draft;
        }
        case `${PREFIX}@@UPDATE_PERMISSION_PROMPTS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const {
            // promptTypeAction = '',
            // type = '',
            data = {},
          } = action.payload;
          // console.log({ promptTypeAction, type, data });
          const promptsSnap =
            state.dataConfig.permissionPrompt.prompts.slidedown.prompts;

          const index = promptsSnap.findIndex(
            item => item.promptId === data.promptId,
          );

          if (index === -1) {
            draft.dataConfig.permissionPrompt.prompts.slidedown.prompts.push(
              data,
            );
          } else {
            draft.dataConfig.permissionPrompt.prompts.slidedown.prompts = promptsSnap.map(
              item => {
                if (item.promptId === data.promptId) {
                  return data;
                }

                return item;
              },
            );
          }

          // Allow save when user change value
          draft.allowSave = true;
          return draft;
        }
        case `${PREFIX}@@HIDDEN_PROMPT_TYPE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { promptType, mode = '' } = action.payload;
          const { types } = state.dataConfig.permissionPrompt.mapPrompts;

          // console.log(promptType, mode);
          draft.dataConfig.permissionPrompt.mapPrompts.types = types.map(
            item => {
              if (item.type === promptType) {
                return {
                  ...item,
                  isHidden: mode !== 'DELETE',
                };
              }

              return item;
            },
          );
          return draft;
        }
        case `${PREFIX}@@UPDATE_PROMPT_TYPE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { type = '' } = action.payload;

          draft.dataConfig.permissionPrompt.mapPrompts.currentType = type;
          return draft;
        }
        case `${PREFIX}@@UPDATE_PREVIEW_STATE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name = '', value = {} } = action.payload;

          draft.dataConfig.permissionPrompt.mapPrompts[
            name
          ].mapPreviewState = value;
          return draft;
        }
        case `${PREFIX}@@DATA_CONFIG@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name, value } = action.payload;

          // Allow save when user change value
          draft.allowSave = true;

          // console.log({ name, value });
          const isToggleEl = [
            'autoReSub',
            'notHTTPS',
            'link',
            // 'webhooks',
            // 'serviceWorkers',
            // 'persistence',
            // 'safariCertificate',
          ].includes(name);

          if (isToggleEl && state.dataConfig[name].toggleProps.name === name) {
            draft.dataConfig[name].toggleProps = {
              ...state.dataConfig[name].toggleProps,
              value: !value,
            };

            if (name === 'notHTTPS') {
              draft.dataConfig.autoReSub.toggleProps = {
                ...state.dataConfig.autoReSub.toggleProps,
                disabled: !value,
              };
              draft.dataConfig.proxyOrigin.isHidden = value;
            }

            if (name === 'link') {
              draft.dataConfig.url.isHidden = value;
            }
          } else if (name === 'clickBehavior') {
            const { type, ...newValue } = value;

            draft.dataConfig[name].settings[type].value = newValue;
          } else {
            draft.dataConfig[name].value = value;
          }

          if (name === 'frequencyCapping') {
            //
          } else {
            const { errors, isValidate } = draft.dataConfig[name].validate(
              draft.dataConfig[name],
            );
            draft.dataConfig[name].errors = errors;
            draft.dataConfig[name].isValidate = isValidate;
          }
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_LIST_DONE}`: {
          const data = action.payload;
          const map = mapDestCatalog(data);
          draft.dataConfig.destinationCatalog.options = map.list;
          draft.dataConfig.destinationCatalog.mapOptions = map.map;
          return draft;
        }
        case `${PREFIX}@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
          const { name, errors } = action.payload;
          draft.dataConfig[name].errors = errors;
          draft.dataConfig[name].isValidate = false;
          draft.dataConfig.isValidate = false;
          return draft;
        }
        case `${PREFIX}@@DEST_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          const destinationName = action.payload;

          draft.dataConfig.destinationName.value = destinationName;
          const {
            errors,
            isValidate,
          } = draft.dataConfig.destinationName.validate(
            draft.dataConfig.destinationName,
          );
          draft.dataConfig.destinationName.errors = errors;
          draft.dataConfig.destinationName.isValidate = isValidate;
          draft.dataConfig.isValidate = validateAll(draft);

          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL}`: {
          const { value } = action.payload;
          draft.catalogId = value.id;
          draft.isLoadingConfigFields = true;
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.isLoadingConfigFields = false;
          const data = action.payload;
          const {
            catalogInput: { methods, settings, basicSettings, inputConfig },
          } = data;

          // update method
          if (!state.dataConfig.extraInfoFields.includes('method'))
            draft.dataConfig.extraInfoFields.push('method');
          const mapMethod = mapMethods(methods);
          draft.methods = mapMethod;
          draft.dataConfig.method.options = mapMethod.list;
          draft.dataConfig.method.mapOptions = mapMethod.map;
          if (mapMethod.list.length === 1) {
            [draft.dataConfig.method.value] = mapMethod.list;
            draft.dataConfig.method.isValidate = true;
          }
          // update tooltip

          const { basicInputs = [], inputs = {} } =
            inputConfig[draft.dataConfig.method.value.name] || {};
          draft.dataConfig.destinationCatalog.tooltip = createTooltip({
            basicSettings,
            settings,
            basicInputs,
            inputs,
          });

          // update config form
          const mapConfigFields = mapConfigureField(basicSettings, settings);
          draft.dataConfig.configFields = basicSettings;
          basicSettings.forEach(each => {
            draft.dataConfig[each] = mapConfigFields[each];
          });
          draft.activeDestCatalog = data;
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DESIGN${ReduxTypes.UPDATE_VALUE}`: {
          const { design } = action.payload;
          const {
            dataConfig: {
              configFields,
              infoFields,
              extraInfoFields,
              infoSiteSetupFields,
              infoAdvancedSettingFields,
              infoNotificationFields,
            },
          } = state;
          draft.design = design;
          // handle disable here

          if (design === 'update') {
            [
              ...configFields,
              ...infoFields,
              ...extraInfoFields,
              ...infoSiteSetupFields,
              ...infoAdvancedSettingFields,
              ...infoNotificationFields,
            ].forEach(each => {
              const isToggleEl = [
                'autoReSub',
                'notHTTPS',
                'link',
                // 'webhooks',
                // 'serviceWorkers',
                // 'persistence',
                // 'safariCertificate',
              ].includes(each);
              if (isToggleEl) {
                draft.dataConfig[each].toggleProps.disabled = false;
              } else {
                draft.dataConfig[each].disabled = false;
              }
            });
            draft.dataConfig.frequencyCapping.disabled = false;
            draft.dataConfig.destinationCatalog.disabled = true;
            draft.dataConfig.method.disabled = false;
          } else if (design === 'preview') {
            [...configFields, ...infoFields, ...extraInfoFields].forEach(
              each => {
                draft.dataConfig[each].disabled = true;
              },
            );
            draft.dataConfig.frequencyCapping.disabled = true;
          }
          return draft;
        }
        case `${PREFIX}@@WARNING_LIMIT_DESTINATION${ReduxTypes.UPDATE_VALUE}`: {
          const { isLimited } = action.payload;
          draft.isLimitedDestination = isLimited;
          return draft;
        }
        case `${PREFIX}@@CHANGE_STATUS_ALLOW_SAVE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newStatus = action.payload;
          draft.allowSave = newStatus;
          return draft;
        }
        case `${PREFIX}@@UPDATE_SHARE_ACCESS${ReduxTypes.UPDATE_VALUE}`: {
          const { accessInfo } = action.payload;
          draft.accessInfo = accessInfo;
          return draft;
        }
        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

/* eslint-disable arrow-body-style */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { combineReducers } from 'redux';
import ReduxTypes from '../../../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import { mapValueToFE, initDefaultDataConfig } from './utils';
import { mapDestCatalog, mapMethods, mapConfigureField } from '../utils';
import { createTooltip } from './utils.map';

export const initStateCreateDestionation = () => ({
  isInitDone: false,
  dataConfig: initDefaultDataConfig(),
  destCatalogs: { map: {}, list: [] },
  methods: { map: {}, list: [] },
  activeDestCatalog: {},
  activeRow: {},
  channelId: null,
  catalogId: null,
  design: 'create',
  allowSave: true,
  isLoadingConfigFields: false,
  isLoading: true,
  isDoing: false,
  isLimitDestinationCreate: false,
  accessInfo: {
    general: {},
    listAccess: [],
  },
});

const validateAll = draft => {
  const {
    dataConfig: {
      configFields,
      infoFields,
      extraInfoFields,
      APNFields,
      FCMFields,
      authFields,
      templateSettingFields,
    },
  } = draft;
  return [
    ...configFields,
    ...infoFields,
    ...extraInfoFields,
    ...APNFields,
    ...FCMFields,
    ...authFields,
    ...templateSettingFields,
  ].every(each => {
    return draft.dataConfig[each].isValidate;
  });
};

const mainReducerFor = moduleConfig => {
  const PREFIX = MODULE_CONFIG.key;
  const mainReducer = (state = initStateCreateDestionation(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initStateCreateDestionation();
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          draft.isInitDone = false;
          draft.allowSave = true;
          const { channelId, activeRow, design, queryDesign } = action.payload;
          draft.channelId = channelId;
          draft.design = design;
          if (design === 'create') {
            draft.activeRow = {};
          } else if (design === 'preview' || design === 'update') {
            draft.activeRow = activeRow;
            draft.catalogId = activeRow.catalogId;
          }
          return draft;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          const {
            dataConfig: {
              configFields,
              infoFields,
              extraInfoFields,
              APNFields = [],
              FCMFields = [],
              authFields = [],
              templateSettingFields = [],
            },
            design,
            activeRow,
            dataConfig,
          } = state;
          if (design === 'preview' || design === 'update') {
            const mapValue = mapValueToFE({ activeRow, dataConfig });
            [
              ...configFields,
              ...infoFields,
              ...extraInfoFields,
              ...APNFields,
              ...FCMFields,
              ...authFields,
              ...templateSettingFields,
            ].forEach(each => {
              draft.dataConfig[each].value = mapValue[each]; // string only
              draft.dataConfig[each].initValue = mapValue[each]; // string only
              const { isValidate, errors } = draft.dataConfig[each].validate(
                draft.dataConfig[each],
              );
              draft.dataConfig[each].errors = errors;
              draft.dataConfig[each].isValidate = isValidate;
              if (design === 'preview') {
                draft.dataConfig[each].disabled = true;
              }
            });

            if (design === 'update') {
              draft.dataConfig.destinationCatalog.disabled = true;
              draft.dataConfig.method.disabled = true;
              draft.allowSave = false;
            }

            draft.dataConfig.frequencyCapping.value = mapValue.frequencyCapping;
            draft.dataConfig.frequencyCapping.disabled = design === 'preview';
            draft.dataConfig.appId = mapValue.appId || '';
            draft.dataConfig.isValidate = validateAll(draft);
          }

          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.isDoing = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.isDoing = false;
          return draft;
        }
        case `${PREFIX}@@TOGGLE_LIMIT_DESTINATION_CREATE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isOpen } = action.payload;

          draft.isLimitDestinationCreate = isOpen;
          return draft;
        }
        case `${PREFIX}@@UPDATE_VALUE_TO_INIT_VALUE_DATA_CONFIG@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.dataConfig.destinationName.initValue =
            state.dataConfig.destinationName.value;
          draft.dataConfig.description.initValue =
            state.dataConfig.description.value;
          return draft;
        }
        case `${PREFIX}@@DEST_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          const destinationName = action.payload;

          draft.dataConfig.destinationName.value = destinationName;
          const {
            errors,
            isValidate,
          } = draft.dataConfig.destinationName.validate(
            draft.dataConfig.destinationName,
          );
          draft.dataConfig.destinationName.errors = errors;
          draft.dataConfig.destinationName.isValidate = isValidate;
          draft.dataConfig.isValidate = validateAll(draft);

          return draft;
        }
        case `${PREFIX}@@DATA_CONFIG@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name, value } = action.payload;

          // Allow save when user change value
          draft.allowSave = true;

          draft.dataConfig[name].value = value;

          if (name === 'frequencyCapping') {
            //
          } else {
            const { errors, isValidate } = draft.dataConfig[name].validate(
              draft.dataConfig[name],
            );
            draft.dataConfig[name].errors = errors;
            draft.dataConfig[name].isValidate = isValidate;
          }
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_LIST_DONE}`: {
          const data = action.payload;
          const map = mapDestCatalog(data);
          draft.dataConfig.destinationCatalog.options = map.list;
          draft.dataConfig.destinationCatalog.mapOptions = map.map;
          return draft;
        }
        case `${PREFIX}@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
          const { name, errors } = action.payload;
          draft.dataConfig[name].errors = errors;
          draft.dataConfig[name].isValidate = false;
          draft.dataConfig.isValidate = false;
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL}`: {
          const { value } = action.payload;
          draft.catalogId = value.id;
          draft.isLoadingConfigFields = true;
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.isLoadingConfigFields = false;
          const data = action.payload;
          const {
            catalogInput: { methods, settings, basicSettings, inputConfig },
          } = data;

          // update method
          if (!state.dataConfig.extraInfoFields.includes('method'))
            draft.dataConfig.extraInfoFields.push('method');
          const mapMethod = mapMethods(methods);
          draft.methods = mapMethod;
          draft.dataConfig.method.options = mapMethod.list;
          draft.dataConfig.method.mapOptions = mapMethod.map;
          if (mapMethod.list.length === 1) {
            [draft.dataConfig.method.value] = mapMethod.list;
            draft.dataConfig.method.isValidate = true;
          }
          // update tooltip

          const { basicInputs = [], inputs = {} } =
            inputConfig[draft.dataConfig.method.value.name] || {};
          draft.dataConfig.destinationCatalog.tooltip = createTooltip({
            basicSettings,
            settings,
            basicInputs,
            inputs,
          });

          // update config form
          const mapConfigFields = mapConfigureField(basicSettings, settings);
          draft.dataConfig.configFields = basicSettings;
          basicSettings.forEach(each => {
            draft.dataConfig[each] = mapConfigFields[each];
          });
          draft.activeDestCatalog = data;
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DESIGN${ReduxTypes.UPDATE_VALUE}`: {
          const { design } = action.payload;
          const {
            dataConfig: {
              configFields,
              infoFields,
              extraInfoFields,
              APNFields,
              FCMFields,
              authFields,
              templateSettingFields,
            },
          } = state;
          draft.design = design;
          // handle disable here

          if (design === 'update') {
            [
              ...configFields,
              ...infoFields,
              ...extraInfoFields,
              ...APNFields,
              ...FCMFields,
              ...authFields,
              ...templateSettingFields,
            ].forEach(each => {
              draft.dataConfig[each].disabled = false;
            });
            draft.dataConfig.frequencyCapping.disabled = false;
            draft.dataConfig.destinationCatalog.disabled = true;
            draft.dataConfig.method.disabled = false;
          } else if (design === 'preview') {
            [
              ...configFields,
              ...infoFields,
              ...extraInfoFields,
              ...APNFields,
              ...FCMFields,
              ...authFields,
              ...templateSettingFields,
            ].forEach(each => {
              draft.dataConfig[each].disabled = true;
            });
            draft.dataConfig.frequencyCapping.disabled = true;
          }
          return draft;
        }
        case `${PREFIX}@@CHANGE_STATUS_ALLOW_SAVE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newStatus = action.payload;
          draft.allowSave = newStatus;
          return draft;
        }
        case `${PREFIX}@@UPDATE_SHARE_ACCESS${ReduxTypes.UPDATE_VALUE}`: {
          const { accessInfo } = action.payload;
          draft.accessInfo = accessInfo;
          return draft;
        }
        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

/* eslint-disable dot-notation */
/* eslint-disable arrow-body-style */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { combineReducers } from 'redux';
import _different from 'lodash/difference';
import _union from 'lodash/union';
import ReduxTypes from '../../../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import {
  mapValueToFE,
  initDefaultDataConfig,
  mapDestCatalog,
  mapMethods,
  uneditableFields,
  getAdvancedDkimFields,
  mapConfigureField,
} from './utils';
import { createTooltip } from './utils.map';
import { EXTRA_EMAIL_FIELDS } from '../../../Create/utils';

export const initStateCreateDestionation = () => ({
  isInitDone: false,
  dataConfig: initDefaultDataConfig(),
  destCatalogs: { map: {}, list: [] },
  methods: { map: {}, list: [] },
  activeDestCatalog: {},
  activeRow: {},
  channelId: null,
  catalogId: null,
  design: 'create',
  allowSave: true,
  isLoadingConfigFields: false,
  isLoading: true,
  isDoing: false,
  isLimitedDestination: false,
  isLimitDestinationCreate: false,
  extraEmailBtn: [],
  infoCreateDomain: {
    isValid: true,
    isShowWarning: false,
    changeNameField: '',
    cachedDataField: '',
    errorIdentity: '',
    destInfoExisted: null,
    isIdentityDone: false,
    updateDkim: false,
    selectorNameCache: '',
  },
  accessInfo: {
    general: {},
    listAccess: [],
  },
});

const validateAll = draft => {
  const {
    dataConfig: { configFields, infoFields, advancedDkimFields },
  } = draft;
  return [...configFields, ...infoFields, ...advancedDkimFields].every(each => {
    return draft.dataConfig[each].isValidate;
  });
};

const mainReducerFor = () => {
  const PREFIX = MODULE_CONFIG.key;
  const mainReducer = (state = initStateCreateDestionation(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initStateCreateDestionation();
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          draft.isInitDone = false;
          draft.allowSave = true;
          const { channelId, activeRow, design } = action.payload;
          draft.channelId = channelId;
          draft.design = design;
          if (design === 'create') {
            draft.activeRow = {};
          } else if (design === 'preview' || design === 'update') {
            draft.activeRow = activeRow;
            draft.catalogId = activeRow.catalogId;
          }
          return draft;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          const { design, activeRow, dataConfig } = state;
          const extraEmailBtn = [];
          if (design === 'preview' || design === 'update') {
            draft.allowSave = false;

            const mapValue = mapValueToFE({ activeRow, dataConfig });

            if (mapValue.dkimType) {
              draft.dataConfig['advancedDkimFields'] = getAdvancedDkimFields(
                mapValue.dkimType,
              );
            }

            [
              ...draft.dataConfig.configFields,
              ...draft.dataConfig.authFields,
              ...draft.dataConfig.infoFields,
              ...draft.dataConfig.advancedDkimFields,
            ].forEach(each => {
              const value = mapValue[each];
              draft.dataConfig[each].value = value;
              draft.dataConfig[each].initValue = value;

              const { isValidate, errors } = draft.dataConfig[each].validate(
                draft.dataConfig[each],
              );
              draft.dataConfig[each].errors = errors;
              draft.dataConfig[each].isValidate = isValidate;
              if (design === 'preview') {
                draft.dataConfig[each].disabled = true;
              } else if (design === 'update') {
                if (uneditableFields.includes(each)) {
                  draft.dataConfig[each].disabled = true;
                } else {
                  draft.dataConfig[each].disabled = false;
                }
              }

              if (
                design !== 'create' &&
                Object.values(EXTRA_EMAIL_FIELDS).includes(each) &&
                !mapValue[each]
              ) {
                extraEmailBtn.push(each);
              }

              if (each === 'selectorName') {
                draft.infoCreateDomain.selectorNameCache = value;
              }
            });

            draft.dataConfig.deliveryRate =
              Object.keys(mapValue.deliveryRate).length > 1
                ? mapValue.deliveryRate
                : state.dataConfig.deliveryRate;
            draft.dataConfig.deliveryRate.disabled = design === 'preview';
            draft.extraEmailBtn = extraEmailBtn;
            draft.dataConfig.frequencyCapping.value = mapValue.frequencyCapping;
            draft.dataConfig.frequencyCapping.disabled = design === 'preview';
            draft.dataConfig.isValidate = validateAll(draft);
          }

          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.isDoing = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.isDoing = false;
          draft.isLimitedDestination = false;
          return draft;
        }
        case `${PREFIX}@@DATA_CONFIG@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name, value } = action.payload;

          // Allow save when user change value
          draft.allowSave = true;

          if (name !== 'deliveryRate') {
            draft.dataConfig[name].value = value;
          }

          switch (name) {
            case 'dkimType': {
              draft.dataConfig['advancedDkimFields'] = getAdvancedDkimFields(
                value,
              );

              break;
            }
            default:
          }

          if (name === 'deliveryRate') {
            if (typeof value === 'number') {
              draft.dataConfig[name].limit = value;
            } else {
              draft.dataConfig[name].type = value.target.value;
              if (value.target.value === 'normal') {
                draft.isValidate = false;
                // delete draft.dataConfig[name].limit;
              }
            }
          } else if (name === 'frequencyCapping') {
            // SKIPPED
          } else {
            const { errors, isValidate } = draft.dataConfig[name].validate(
              draft.dataConfig[name],
            );
            draft.dataConfig[name].errors = errors;
            draft.dataConfig[name].isValidate = isValidate;
          }

          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DEST_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          const destinationName = action.payload;

          draft.dataConfig.destinationName.value = destinationName;
          const {
            errors,
            isValidate,
          } = draft.dataConfig.destinationName.validate(
            draft.dataConfig.destinationName,
          );
          draft.dataConfig.destinationName.errors = errors;
          draft.dataConfig.destinationName.isValidate = isValidate;
          draft.dataConfig.isValidate = validateAll(draft);

          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_LIST_DONE}`: {
          const data = action.payload;
          const map = mapDestCatalog(data);
          draft.dataConfig.destinationCatalog.options = map.list;
          draft.dataConfig.destinationCatalog.mapOptions = map.map;
          return draft;
        }
        case `${PREFIX}@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
          const { name, errors } = action.payload;
          draft.dataConfig[name].errors = errors;
          draft.dataConfig[name].isValidate = false;
          draft.dataConfig.isValidate = false;
          return draft;
        }
        case `${PREFIX}@@UPDATE_REQUIRED_FIELD@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name = '', isRequired = false, errors = [] } = action.payload;

          draft.dataConfig[name].isRequired = isRequired;
          draft.dataConfig[name].errors = !isRequired ? [] : errors;
          draft.dataConfig[name].isValidate = !isRequired;
          return draft;
        }
        case `${PREFIX}@@UPDATE_LOADING_PAGE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = action.payload;
          return draft;
        }
        case `${PREFIX}@@UPDATE_INFO_EXIST_CREATED_DOMAIN@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.infoCreateDomain = action.payload;

          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL}`: {
          const { value } = action.payload;
          draft.catalogId = value.id;
          draft.isLoadingConfigFields = true;
          return draft;
        }
        case `${PREFIX}@@UPDATE_EXTRA_EMAIL_BTN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { btnName = '', type = '' } = action.payload;
          if (!btnName) return draft;

          if (type === 'ADD') {
            draft.extraEmailBtn = _different(state.extraEmailBtn, [btnName]);
          } else if (type === 'REMOVE') {
            draft.extraEmailBtn = _union(state.extraEmailBtn, [btnName]);

            draft.dataConfig[btnName].value = '';
            draft.dataConfig[btnName].initValue = '';
          }
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.isLoadingConfigFields = false;
          const data = action.payload;
          const {
            catalogInput: { methods, settings, basicSettings, inputConfig },
          } = data;

          if (!state.dataConfig.infoFields.includes('method')) {
            draft.dataConfig.infoFields.push('method');
          }

          const mapMethod = mapMethods(methods);
          draft.methods = mapMethod;
          draft.dataConfig.method.options = mapMethod.list;
          draft.dataConfig.method.mapOptions = mapMethod.map;
          if (mapMethod.list.length === 1) {
            [draft.dataConfig.method.value] = mapMethod.list;
            draft.dataConfig.method.isValidate = true;
          }

          // update config form
          const mapConfigFields = mapConfigureField(basicSettings, settings);

          // Map extra btn for channel email
          if (
            Number(data.channelId) === 1 &&
            Array.isArray(basicSettings) &&
            state.design === 'create'
          ) {
            const extraEmailBtn = [];

            basicSettings.forEach(each => {
              if (Object.values(EXTRA_EMAIL_FIELDS).includes(each)) {
                extraEmailBtn.push(each);
              }
            });

            draft.extraEmailBtn = extraEmailBtn;
          }

          const { basicInputs = [], inputs = {} } =
            inputConfig[draft.dataConfig.method.value.name] || {};

          basicSettings.forEach(each => {
            if (!state.dataConfig.configFields.includes(each)) {
              draft.dataConfig.configFields.push(each);
            }
            draft.dataConfig[each] = mapConfigFields[each];
          });

          draft.dataConfig.destinationCatalog.tooltip = createTooltip({
            basicSettings,
            settings,
            basicInputs,
            inputs,
          });
          draft.activeDestCatalog = data;
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DESIGN${ReduxTypes.UPDATE_VALUE}`: {
          const { design } = action.payload;
          const {
            dataConfig: { configFields, infoFields, advancedDkimFields },
          } = state;
          draft.design = design;
          // handle disable here

          if (design === 'update') {
            [...configFields, ...infoFields, ...advancedDkimFields].forEach(
              each => {
                if (!uneditableFields.includes(each)) {
                  draft.dataConfig[each].disabled = false;
                }
              },
            );

            draft.dataConfig.deliveryRate.disabled = false;
            draft.dataConfig.frequencyCapping.disabled = false;
          } else if (design === 'preview') {
            [...configFields, ...infoFields, ...advancedDkimFields].forEach(
              each => {
                draft.dataConfig[each].disabled = true;
              },
            );

            draft.dataConfig.deliveryRate.disabled = true;
            draft.dataConfig.frequencyCapping.disabled = true;
          }
          return draft;
        }
        case `${PREFIX}@@WARNING_LIMIT_DESTINATION${ReduxTypes.UPDATE_VALUE}`: {
          const { isLimited } = action.payload;
          draft.isLimitedDestination = isLimited;
          return draft;
        }
        case `${PREFIX}@@CHANGE_STATUS_ALLOW_SAVE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newStatus = action.payload;
          draft.allowSave = newStatus;
          return draft;
        }
        case `${PREFIX}@@UPDATE_SHARE_ACCESS${ReduxTypes.UPDATE_VALUE}`: {
          const { accessInfo } = action.payload;
          draft.accessInfo = accessInfo;
          return draft;
        }
        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

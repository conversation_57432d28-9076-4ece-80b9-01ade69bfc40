import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';

export const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ALERT_EDIT_DESTINATION,
    'If you edit this destination will affect the input of other features.',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titleSiteSetup: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Site Setup'),
  titlePermissionPrompt: getTranslateMessage(
    TRANSLATE_KEY._DES_PER_PROMPT_SETUP,
    'Permission Prompt Setup',
  ),
  titleNoti: getTranslateMessage(
    TRANSLATE_KEY._DES_WELCOME_NOTI,
    'Welcome Notification',
  ),
  titleAdvancedSetting: getTranslateMessage(
    TRANSLATE_KEY._DES_ADV_PUSH_SETTING,
    'Advanced Push Settings (Optional)',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(TRANSLATE_KEY._, 'General Settings'),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titlDeliveredRate: getTranslateMessage(
    TRANSLATE_KEY._TITLE_DES_DELIVERED_RATE,
    'Delivered rate',
  ),
  titleVerifyYourDomain: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Verifying your domain',
  ),
  titleDkimBased: getTranslateMessage(
    TRANSLATE_KEY._004,
    'DKIM-based domain verification',
  ),
  descDkimBased: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Messages you send through SES use a subdomain of amazonses.com as the default MAIL FROM domain. Setting the MAIL FROM to a domain you enables you to comply with Domain-based Message Authentication, Reporting and Conformance (DMARC)',
  ),
  titlConfigDkim: getTranslateMessage(TRANSLATE_KEY._004, 'Configuring DKIM'),
  descConfigDkim: getTranslateMessage(
    TRANSLATE_KEY._004,
    `Following identity creation, Amazon SES will provide a set of DNS records. These records must be published to your domain's DNS server in order to successfully configure DKIM and verify ownership of your domain. For more information, see Verifying a domain with Amazon SES`,
  ),
  noteVerifyDomain: getTranslateMessage(
    TRANSLATE_KEY._004,
    `Following identity creation, Amazon SES will provide a set of DNS records. These records must be published to your domain's DNS server in order to successfully configure DKIM and verify ownership of your domain. \nFor more information, see Verifying a domain with Amazon SES`,
  ),
  titleAdvanceDkim: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Advanced DKIM Settings',
  ),
  titleAuthentication: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Authentication',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  limitSentRate: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_LIMIT_SEND_RATE,
    'Limit send rate',
  ),
  timePerson: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_TIME_PER_SECOND,
    'time(s) per second',
  ),
};

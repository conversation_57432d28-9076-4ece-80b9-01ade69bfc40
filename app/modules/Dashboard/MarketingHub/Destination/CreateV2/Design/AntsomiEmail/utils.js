/* eslint-disable dot-notation */
/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
// Utils
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import _isEmpty from 'lodash/isEmpty';
import {
  MAP_VALIDATE,
  MAP_INPUT_TYPE,
  MAP_COMPONENT_ALIAS,
  getTypeRender,
} from './utils.map';
import {
  getObjectPropSafely,
  safeParseInt,
} from '../../../../../../../utils/common';
import { checkIsHideField } from '../../../Create/utils';

// Translate
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';

const DEFAULT_FREQUENCY_CAPPING = {
  version: '2.0',
  onScheduleId: false,
  onJourneyId: false,
  onDestination: false,
};

export const PROMPT_OPTIONS = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._, 'Push Prompt'),
    value: 'PUSH',
    iconSub: 'settings',
    disabled: false,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._, 'Email/Phone Prompt'),
    value: 'EMAIL/PHONE',
    iconSub: 'settings',
    disabled: true,
  },
];

const IDENTITY_TYPE_OPTIONS = [
  {
    value: 'domain',
    label: 'Domain',
    description:
      'To verify ownership of a domain, you must have access to its DNS settings to add the necessary records.',
    disabled: true,
  },
  // {
  //   value: 'email',
  //   label: 'Email address',
  //   description:
  //     'To verify ownership of an email address, you must have access to its inbox to open the verification email.',
  //   disabled: true,
  // },
];

export const DKIM_TYPE_OPTIONS = [
  {
    value: 'easy',
    label: 'Easy DKIM',
    description:
      'To set up Easy DKIM, you have to modify the DNS settings for your domain.',
  },
  {
    value: 'byodkim',
    label: 'Provide DKIM authentication token (BYODKIM)',
    description:
      'Configure DKIM for this domain by providing your own private key.',
  },
];

export const DKIM_KEY_LENGTH_OPTIONS = [
  {
    value: 'RSA_2048_BIT',
    label: 'RSA_2048_BIT',
  },
  {
    value: 'RSA_1024_BIT',
    label: 'RSA_1024_BIT',
  },
];

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  titlEmail: getTranslateMessage(TRANSLATE_KEY._004, 'Email'),
  titlDomain: getTranslateMessage(TRANSLATE_KEY._004, 'Domain'),
  titlDomainPlaceholder: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Enter a domain or subdomain',
  ),
  titlIdentityType: getTranslateMessage(TRANSLATE_KEY._004, 'Identity type'),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
  titlDkimKeyLength: getTranslateMessage(
    TRANSLATE_KEY._004,
    'DKIM signing key length',
  ),
  titlDkimKeyLengthDesc: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Signing key length is bits required in sign-in algorithm. DKIM 2048 is the recommended way to enhance security',
  ),
  titlDkimSignatures: getTranslateMessage(
    TRANSLATE_KEY._004,
    'DKIM signatures',
  ),
  titlDkimSignaturesDesc: getTranslateMessage(
    TRANSLATE_KEY._004,
    'DKIM signatures help validate that a message was not forged or altered in transit. Disabling this feature is not recommended.',
  ),
  titlPrivateKey: getTranslateMessage(TRANSLATE_KEY._004, 'Private key'),
  placeholderPrivateKey: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Paste your private key',
  ),
  titlSelectorName: getTranslateMessage(TRANSLATE_KEY._004, 'Selector name'),
  placeholderSelectorName: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Enter a unique name',
  ),
  titleIdentityStatus: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Identity status',
  ),
  titleDnsRecords: getTranslateMessage(
    TRANSLATE_KEY._004,
    'Publish DNS records',
  ),
};

export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  configFields: ['identityType', 'emailIdentity'],
  advancedDkimFields: ['dkimType', 'dkimSigningKeyLength', 'dkimSignatures'],
  authFields: ['identityStatus', 'dnsRecords'],
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: 'sdnnsjdfg',
  },
  identityType: {
    ...initInputElement(
      'identityType',
      'domain',
      null,
      false,
      MAP_TITLE.titlIdentityType,
      null,
      MAP_VALIDATE.default,
    ),
    options: IDENTITY_TYPE_OPTIONS,
    componentEl: MAP_INPUT_TYPE.selectRadioBox,
  },
  emailIdentity: {
    ...initInputElement(
      'emailIdentity',
      '',
      255,
      true,
      MAP_TITLE.titlDomain,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    placeholder: MAP_TITLE.titlDomainPlaceholder,
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  // domain: {
  //   ...initInputElement(
  //     'domain',
  //     '',
  //     255,
  //     true,
  //     MAP_TITLE.titlDomain,
  //     null,
  //     MAP_VALIDATE.singleLineText,
  //   ),
  //   placeholder: MAP_TITLE.titlDomainPlaceholder,
  //   componentEl: MAP_INPUT_TYPE.singleLineText,
  // },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  dkimType: {
    ...initInputElement(
      'dkimType',
      DKIM_TYPE_OPTIONS[0].value,
      null,
      false,
      MAP_TITLE.titlIdentityType,
      null,
      MAP_VALIDATE.default,
    ),
    options: DKIM_TYPE_OPTIONS,
    componentEl: MAP_INPUT_TYPE.selectRadioBox,
  },
  dkimSigningKeyLength: {
    ...initInputElement(
      'dkimSigningKeyLength',
      DKIM_KEY_LENGTH_OPTIONS[0].value,
      null,
      false,
      MAP_TITLE.titlDkimKeyLength,
      null,
      MAP_VALIDATE.default,
    ),
    options: DKIM_KEY_LENGTH_OPTIONS,
    description: MAP_TITLE.titlDkimKeyLengthDesc,
    componentEl: MAP_INPUT_TYPE.selectRadio,
  },
  dkimSignatures: {
    ...initInputElement(
      'dkimSignatures',
      true,
      null,
      false,
      MAP_TITLE.titlDkimSignatures,
      null,
      MAP_VALIDATE.default,
    ),
    disabled: true,
    title: 'Enabled',
    description: MAP_TITLE.titlDkimSignaturesDesc,
    componentEl: MAP_INPUT_TYPE.checkbox,
  },
  privateKey: {
    ...initInputElement(
      'privateKey',
      '',
      null,
      true,
      MAP_TITLE.titlPrivateKey,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    placeholder: MAP_TITLE.placeholderPrivateKey,
    componentEl: MAP_INPUT_TYPE.textarea,
  },
  selectorName: {
    ...initInputElement(
      'selectorName',
      '',
      63,
      true,
      MAP_TITLE.titlSelectorName,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    placeholder: MAP_TITLE.placeholderSelectorName,
    componentEl: MAP_INPUT_TYPE.selectorName,
  },
  identityStatus: {
    ...initInputElement(
      'identityStatus',
      '',
      null,
      false,
      MAP_TITLE.titleIdentityStatus,
      null,
      MAP_VALIDATE.default,
    ),
    componentEl: MAP_INPUT_TYPE.text,
  },
  dnsRecords: {
    ...initInputElement(
      'dnsRecords',
      [],
      null,
      false,
      MAP_TITLE.titleDnsRecords,
      null,
      MAP_VALIDATE.default,
    ),
    componentEl: MAP_INPUT_TYPE.tableState,
  },
  frequencyCapping: { value: {}, disabled: false },
  deliveryRate: { type: 'normal', disabled: false },
});
const initInputElement = (
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: !isRequired,
  validate: validate || (() => ({ errors: [], isValidate: false })),
  initValue: value || null,
});

/**
 * Render a list of components using the provided rendering component and configuration options.
 *
 * @function
 * @param {Object} classes - The css classes to apply to the rendered components.
 * @param {Object} configurations - An object containing configuration options for rendered components.
 * @param {Object} ComponentRendering - The component to use rendering each item in the array.
 * @param {Object} configurations.dataConfig - An object containing data configuration options for the rendered components.
 * @param {Array} configurations.infoFields - An array of strings representing the data fields to be rendered.
 * @param {Array} configurations.nameToggleList - An array of strings representing the data fields that should be rendered as toggle Elements.
 * @param {String} configurations.design - A string to decide type in view.
 * @param {Object} configurations.infoCreateDomain - A object containing data to settings domain in case check domain create existed.
 * @param {Function} configurations.onChange - A callback function to be executed when the value of any non-toggle element changes.
 * @param {Function} configurations.onToggle - A callback function to executed when the value of any toggle elements changes.
 *
 * @returns {Array} An array of rendered components.
 */
export const renderBlock = (classes, configurations, ComponentRendering) => {
  const {
    dataConfig = {},
    infoFields = [],
    extraData = {},
    nameToggleList = [],
    design = '',
    infoCreateDomain = {},
    onChange = () => { },
    onToggle = () => { },
  } = configurations;

  return infoFields.map(each => {
    if (each !== 'destinationCatalog') {
      const isToggleElType = nameToggleList.includes(each);

      let configs = {
        ...dataConfig[each],
        ...extraData,
        isHideField: checkIsHideField({
          channelId: extraData.channelId,
          extraEmailBtn:
            safeParseInt(extraData.channelId) === 1
              ? extraData.extraEmailBtn
              : [],
          name: each,
        }),
        design,
        infoCreateDomain,
      };

      if (isToggleElType) {
        configs = {
          ...configs,
          toggleProps: {
            ...(dataConfig[each].toggleProps || {}),
            handleClick: onToggle,
          },
        };
      }

      const handleChange = isToggleElType ? () => { } : onChange;

      return (
        // eslint-disable-next-line react/react-in-jsx-scope
        <ComponentRendering
          key={each}
          {...configs}
          dataConfig={dataConfig}
          onChange={handleChange}
          classes={classes}
        />
      );
    }
  });
};

export const mapMethods = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.name,
    value: item.name,
    label: item.label,
  }));

export const mapDestCatalog = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.catalogId,
    value: item.catalogId,
    label: item.translateLabel || item.catalogName,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export const toEntryAPI = data => {
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    email,
    fromEmailAddress,
    fromEmailName,
    identityType,
    frequencyCapping,
    deliveryRate,
    emailIdentity,
    dkimType,
    dkimSigningKeyLength,
    catalogCode,
    dkimSignatures,
    privateKey,
    selectorName,
    infoCreateDomain = {},
    ccEmail = {},
    bccEmail = {},
  } = data;

  let destinationSetting = {};

  switch (catalogCode) {
    case 'antsomi_email': {
      let dkimSigningAttributes = {
        identityType: dkimType.value,
        dkimSignatures: dkimSignatures.value,
      };

      if (dkimType.value === 'easy') {
        dkimSigningAttributes = {
          ...dkimSigningAttributes,
          nextSigningKeyLength: dkimSigningKeyLength.value,
        };
      } else if (dkimType.value === 'byodkim') {
        dkimSigningAttributes = {
          ...dkimSigningAttributes,
          domainSigningPrivateKey: privateKey.value,
          domainSigningSelector: selectorName.value,
        };
      }

      destinationSetting = {
        updateDkim:
          typeof infoCreateDomain === 'object'
            ? Boolean(infoCreateDomain.updateDkim)
            : false,
        identityFields: {
          type: identityType.value,
          emailIdentity: emailIdentity.value,
          dkimSigningAttributes,
        },
        frequencyCapping:
          typeof frequencyCapping === 'object' &&
            !_isEmpty(frequencyCapping.value)
            ? frequencyCapping.value
            : DEFAULT_FREQUENCY_CAPPING,
        deliveryRate:
          deliveryRate.type === 'normal'
            ? {
              type: deliveryRate.type,
            }
            : {
              type: deliveryRate.type,
              limit: deliveryRate.limit,
            },
        method: getObjectPropSafely(() => method.value.value, ''),
        email: email.value,
        fromEmailAddress: fromEmailAddress.value,
        fromEmailName: fromEmailName.value,
        ccEmail: ccEmail.value,
        bccEmail: bccEmail.value,
      };
      break;
    }
    default:
  }

  const result = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };

  return result;
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  const {
    catalogCode,
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;

  let result = {};
  switch (catalogCode) {
    case 'antsomi_email': {
      const {
        method,
        email,
        fromEmailAddress,
        fromEmailName,
        frequencyCapping = {},
        deliveryRate = {},
        identityFields = {},
        dkimTokens = [],
        dkimTokensBackup = [],
        status: identityStatus,
        bccEmail = '',
        ccEmail = '',
        mailFromDomain = "",
      } = destinationSetting;
      const {
        type: identityType,
        emailIdentity,
        dkimSigningAttributes = {},
      } = identityFields;
      const {
        identityType: dkimType,
        nextSigningKeyLength = 'RSA_2048_BIT',
        domainSigningPrivateKey = '',
        domainSigningSelector = '',
        dkimSignatures,
      } = dkimSigningAttributes;

      const dnsRecords = [
        {
          type: 'TXT',
          name: `_dmarc.${emailIdentity}`,
          value: `v=DMARC1;p=reject;rua=mailto:dmarcreports@${emailIdentity}`,
        },
        {
          type: 'TXT',
          name: `${emailIdentity}`,
          value: `v=spf1 a include:amazonses.com ~all`,
        },
      ];

      if (dkimType === 'easy') {
        const records = [...dkimTokens, ...dkimTokensBackup].map(token => ({
          type: 'CNAME',
          name: `${token}._domainkey.${emailIdentity}`,
          value: `${token}.dkim.amazonses.com`,
        }));
        dnsRecords.push(...records);
      } else if (dkimType === 'byodkim') {
        const records = [...dkimTokens, ...dkimTokensBackup].map(token => ({
          type: 'TXT',
          name: `${token}._domainkey.${emailIdentity}`,
          value: `p=customerProvidedPublicKey`,
        }));
        dnsRecords.push(...records);
      }
      if (mailFromDomain) {
        const records = [
          {
            type: 'MX',
            name: `${mailFromDomain}`,
            value: `feedback-smtp.ap-southeast-1.amazonses.com`,
            priority: '10',
          },
          {
            type: 'TXT',
            name: `${mailFromDomain}`,
            value: `v=spf1 include:amazonses.com ~all`,
          },
        ];
        dnsRecords.push(...records);
      }
      result = {
        frequencyCapping,
        deliveryRate: { ...deliveryRate, disabled: false },
        destinationName: destinationNameMultilang,
        description: descriptionMultilang,
        destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
        method: dataConfig.method.mapOptions[method],
        ccEmail,
        bccEmail,
        email,
        fromEmailAddress,
        fromEmailName,
        emailIdentity,
        identityType,
        dkimType,
        dkimSignatures,
        dkimSigningKeyLength: nextSigningKeyLength,
        privateKey: domainSigningPrivateKey,
        selectorName: domainSigningSelector,
        dnsRecords,
        identityStatus,
      };
      break;
    }
    default:
  }

  return result;
};

export const uneditableFields = [
  'emailIdentity',
  'destinationCatalog',
  'method',
  'dkimSignatures',
];

export const getAdvancedDkimFields = dkimType => {
  let fields = [];
  if (dkimType === 'easy') {
    fields = ['dkimType', 'dkimSigningKeyLength', 'dkimSignatures'];
  } else if (dkimType === 'byodkim') {
    fields = ['dkimType', 'privateKey', 'selectorName', 'dkimSignatures'];
  }

  return fields;
};

export const mapConfigureField = (fields, settings) => {
  const data = {};

  fields.forEach(each => {
    const item = settings[each];
    const value = '';
    const typeRender = getTypeRender(item);
    const label = getTranslateMessage(item.labelCode, item.label);
    const temp = {
      ...item,
      ...initInputElement(
        each,
        value,
        item.maxLength,
        item.isRequired,
        label,
        null,
        MAP_VALIDATE[typeRender],
      ),
      isValidate: !item.isRequired,
      componentEl: MAP_INPUT_TYPE[typeRender],
    };
    data[each] = temp;
  });
  return data;
};

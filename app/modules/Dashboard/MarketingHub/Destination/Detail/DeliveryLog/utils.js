// import { getLabelStoryStatus } from '../../../../../utils/web/processStatus';
import {
  getLabelCampaignStatus,
  getLabelStoryStatus,
} from '../../../../../../utils/web/processStatus';
import {
  CellArray,
  CellCampaingStatus,
  CellDate,
  CellJourneyStatus,
  CellMainVariant,
  CellNumber,
  CellStoryStatus,
  CellText,
  CellToggle,
  CellToggleAPI,
  CellToggleWithJourney,
  CellToggleWithStyle,
  CellArrayObject,
  CellNumberString,
  CellSourceDelivery,
  CellProcessIdDelivery,
} from '../../../../../../containers/Table/Cell';
// import { getSegmentTypeLabel } from '../../../../../services/Abstract.data';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../../../utils/web/permission';
import {
  getPortalTimeZone,
  initNumberId,
} from '../../../../../../utils/web/portalSetting';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
// import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { BREADCRUMDATA, MODULE_CONFIG } from './config';
import APP from '../../../../../../appConfig';
import { MENU_CODE } from '../../../../../../utils/web/permission';
import {
  DATA_INFO,
  getLabelDeliveryStatus,
} from '../../../../../../services/Abstract.data';
import moment from 'moment';
import { format, utcToZonedTime } from 'date-fns-tz';
import { COLUMNS_WIDTH } from '../../../../../../containers/Table/constants';

const MAP_TITLE = {
  storyEndNever: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
};

export function serializeData(list, portalId) {
  const data = { list: [], map: {} };
  list.forEach(tmp => {
    const tempt = {
      ...tmp,
      status: getLabelDeliveryStatus(tmp.status),
      story_id: tmp.journey_id,
      story_name: tmp.journey_name,
      start_time: tmp.delivery_start_date,
      end_time: tmp.delivery_end_date,
      // campaign_status: getLabelCampaignStatus(tmp.campaign_status),
      // story_status: getLabelStoryStatus(parseInt(tmp.story_status)),
      story_type: tmp.story_type_name,
      trigger_type: tmp.trigger_type
        ? DATA_INFO.trigger_type.map[tmp.trigger_type].label
        : '',
      // story_id
    };
    data.list.push(tempt);
    data.map[tempt.id] = tempt;
  });
  return data;
}

export function buildTableGroupColumns(columnName, columnsActive) {
  return [
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // maxWidth: 280,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellProcessIdDelivery,
      // getIsDisabledEditName: row => !row.accepted_actions[MAA.EDIT_NAME],
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },
    // {
    //   Header: columnStatus.label,
    //   id: columnStatus.value,
    //   name: columnStatus.value,
    //   accessor: columnStatus.value,
    //   disableSortBy,
    //   width: 150,
    //   minWidth: 150,
    //   // maxWidth: 150,
    //   // disableResizing: true,
    //   sticky: 'left',
    //   Cell: CellStoryStatus,
    //   getIsDisabledToggle: () => true,
    //   placement: 'status',
    //   // className: 'tbl-border-left padding-left-right-10',
    //   className: `${columnStatus.value}`,
    //   mapParamsFn: ({ oldData, newData }) => ({
    //     objectId: oldData.segment_id,
    //     itemTypeId: oldData.item_type_id,
    //     data: {
    //       columns: ['status'],
    //       status: newData._newStatus ? 1 : 2,
    //     },
    //   }),
    //   ServiceToggleFn: null,
    //   Footer: '',
    // },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      className: `${dataType} ${property.value}`,
      Footer: property.type === 2 ? '0' : '',
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (['c_user_id', 'u_user_id'].includes(property.value)) {
      column.placement = 'left';
    }

    if (
      [
        'story_id',
        'campaign_id',
        'variant_id',
        'journey_version',
        'source_id',
      ].includes(property.value)
    ) {
      column.placement = 'right';
      column.className = `txt-right-format`;
    }

    if (['schedule_id', 'resumed_process_id'].includes(property.value)) {
      column.placement = 'left';
    }

    if (['story_status', 'campaign_status'].includes(property.value)) {
      column.className = `${dataType} ${property.value} txt-status`;
    }
    ['campaign_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['story_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['variant_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    ['end_date'].forEach(each => {
      if (each === property.propertyCode) {
        column.alternativeLabel = MAP_TITLE.storyEndNever;
      }
    });
    columns.push(column);
  });
  return columns;
}

const MAP_CELL_BY_VALUE = {
  // campaign_id: CellText,
  source_id: CellSourceDelivery,
  c_user_id: CellText,
  user_id: CellText,
  campaign_status: CellCampaingStatus,
  story_id: CellText,
  campaign_id: CellText,
  variant_id: CellText,
  journey_version: CellText,
  // source_id: CellNumber,
};

export function getModuleConfig(moduleConfig, destinationId, channelId) {
  const newModuleConfig = { ...moduleConfig };
  newModuleConfig.objectId = destinationId;
  // newModuleConfig.objectType = objectType;
  newModuleConfig.channelId = channelId;

  // case lỗi
  return newModuleConfig;
}

const MAP_CELL_BY_DATATYPE = {
  // for normal column
  string: CellText,
  datetime: CellDate,
  number: CellText,
  boolean: CellText,
  array: CellArray,
  array_number: CellArrayObject,
};
export const mapDataToConfigFilter = (
  channelActive,
  isMapConfigSuggestionForDetails,
  isUseApiSelector,
) => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: isUseApiSelector ? null : 100,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
    feKey: '6-variant_name',
  };

  if (
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.itemTypeId = null;
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: `${
                isMapConfigSuggestionForDetails ? 'story_id' : 'story_type'
              }`,
              data_type: 'number',
              operator: 'equals',
              value: Number(channelActive.value),
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export const mapDataToConfigSusggestion = channelActive => {
  const moduleConfigFilter = {
    objectType: MODULE_CONFIG.objectType,
    limit: 20,
    page: 1,
    sd: 'asc',
    systemDefined: 0,
    feServices: 'suggestionMultilang',
  };
  if (
    channelActive &&
    channelActive.value !== 0 &&
    channelActive.value !== MODULE_CONFIG.objectId
  ) {
    moduleConfigFilter.filters = {
      OR: [
        {
          AND: [
            {
              column: 'story_type',
              data_type: 'number',
              operator: 'equals',
              value: channelActive.value,
            },
          ],
        },
      ],
    };
  }
  return moduleConfigFilter;
};

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '1',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const listing = {
    key: '3',
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_VARIANTS,
      'Listing Variants',
    ),
  };
  return [...BREADCRUMDATA, tmp, listing];
}
export const MAP_STATUS = {
  BUILD_CONTENT_ERROR: {
    value: 'BUILD_CONTENT_ERROR',
    label: 'Build content error',
    propertyCode: 'BUILD_CONTENT_ERROR',
  },
  MISSING_RECEIVER: {
    value: 'MISSING_RECEIVER',
    label: 'Missing receiver',
    propertyCode: 'MISSING_RECEIVER',
  },
  DESTINATION_FREQUENCY_CAPPING: {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    label: 'Violate destination frequency capping',
    propertyCode: 'DESTINATION_FREQUENCY_CAPPING',
  },
  DELIVERED: {
    value: 'DELIVERED',
    label: 'Delivered',
    propertyCode: 'DELIVERED',
  },
  BOUNCED_N_ERROR: {
    value: 'BOUNCED_N_ERROR',
    label: 'Bounce & error',
    propertyCode: 'BOUNCED_N_ERROR',
  },
};
export const ARRAY_STATUS = [
  {
    value: 'BUILD_CONTENT_ERROR',
    label: 'Build content error',
    propertyCode: 'BUILD_CONTENT_ERROR',
  },
  {
    value: 'MISSING_RECEIVER',
    label: 'Missing receiver',
    propertyCode: 'MISSING_RECEIVER',
  },
  {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    label: 'Violate destination frequency capping',
    propertyCode: 'DESTINATION_FREQUENCY_CAPPING',
  },
  {
    value: 'DELIVERED',
    label: 'Delivered',
    propertyCode: 'DELIVERED',
  },
  {
    value: 'BOUNCED_N_ERROR',
    label: 'Bounce & error',
    propertyCode: 'BOUNCED_N_ERROR',
  },
];

export const ARRAY_OPTION_SCORECARD = [
  {
    value: 'TOTAL_DELIVERY',
    label: 'Total Delivery',
    propertyCode: 'TOTAL_DELIVERY',
  },
  {
    value: 'DELIVERED',
    label: 'Delivered',
    propertyCode: 'DELIVERED',
  },
  {
    value: 'BUILD_CONTENT_ERROR',
    label: 'Build Content Error',
    propertyCode: 'BUILD_CONTENT_ERROR',
  },
  {
    value: 'MISSING_RECEIVER',
    label: 'Missing receiver',
    propertyCode: 'MISSING_RECEIVER',
  },
  {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    label: 'Violate destination frequency capping',
    propertyCode: 'DESTINATION_FREQUENCY_CAPPING',
  },
  {
    value: 'BOUNCED_N_ERROR',
    label: 'Bounce & Error',
    propertyCode: 'BOUNCED_N_ERROR',
  },
];
export const DEFAULT_OPTIN = ['TOTAL_DELIVERY', 'DELIVERED'];

export const formatTimeMoment = time => {
  const portalTimezone = getPortalTimeZone();
  if (time) {
    try {
      const timeCurrent = moment(
        utcToZonedTime(new Date(time), portalTimezone),
      ).format('MMM DD,YYYY HH:mm:ss');
      return timeCurrent;
    } catch {
      return time;
    }
  }
  return 'null';
};

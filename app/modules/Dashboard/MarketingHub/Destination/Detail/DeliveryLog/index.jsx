/* eslint-disable indent */
/* eslint-disable func-names */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import CalendarSelection from 'components/Templates/CalendarSelection';
import NavBarRightContent from 'components/Templates/LayoutContent/NavBarRightContent';
import { Divider } from 'components/Atoms/Divider';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import DestinationServices from '../../../../../../services/Destination';
// import ModalDownload from 'containers/modals/ModalDownload';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListStoryMapDataFooter,
  makeSelectListStoryVariantColumn,
  makeSelectListStoryVariantDomainMain,
  makeSelectListStoryVariantDomainMainData,
  makeSelectListStoryVariantFilter,
  makeSelectListStoryVariantTable,
  makeSelectListVariantCalendarView,
  makeSelectListVariantDateRange,
  makeSelectListVariantDefaultMetrics,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import { MODULE_CONFIG } from './config';
import Filters from '../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import {
  addNotification,
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import {
  PerformanceChartDelivery,
  TableRelative,
  TableWrapper,
} from './styles';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import Search from '../../../../../../containers/Search';
// import { toConditionAPI } from '../../../../../../containers/Filters/utils';
import LayoutContent, {
  LayoutContentLoading,
} from '../../../../../../components/Templates/LayoutContent';
// import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
import { useLocalStorage } from '../../../../../../utils/web/useHooks';
import { safeParse } from '../../../../../../utils/common';
import useToggle from '../../../../../../hooks/useToggle';
import {
  ARRAY_OPTION_SCORECARD,
  DEFAULT_OPTIN,
  // getBreadcrums,
  // mapDataToConfigSusggestion,
} from './utils';
// import { makeSelectStoryDetailDomainActiveTab } from '../Main/selectors';
import {
  // configToAPISuggestion,
  // getColumnsExport,
  getViewType,
  // setViewType as setViewTypeLocalStorage,
} from '../../../Journey/utils';
// import {
//   APP_ACTION,
//   MENU_CODE,
//   validateAction,
// } from '../../../../../../utils/web/permission';
// import { getCurrentAccessUserId } from '../../../../../../utils/web/cookie';
import { StyleWrapper } from '../../../Journey/List/styles';
import useNotificationBar from 'hooks/useNotificationBar';
import DrawerDetails from './DrawerDetails';
import ModalExport from '../../../../../../containers/modals/ModalExport';

const MAP_TITLE = {
  titlStories: getTranslateMessage(TRANSLATE_KEY._, 'ProcessId'),
  // actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  itemNameStories: getTranslateMessage(
    TRANSLATE_KEY._ITEM_NAME_SEGMENT,
    'campaign(ies)',
  ),
  titlCalendar: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Calendar'),
};
const layoutStyle = {
  overflow: 'hidden',
  // height: '83vh',
};
// const navbarStyle = {
//   width: '0',
//   height: '0',
//   position: 'absolute',
//   top: '25px',
//   right: '357px',
// };

// const defaultMetric = ['impression', 'click'];

export function Variant(props) {
  // const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  // const [configFilter, setConfigFilter] = useState(
  //   mapDataToConfigSusggestion(),
  // );

  // const hasCreateRole = validateAction(
  //   MENU_CODE.JOURNEY,
  //   APP_ACTION.CREATE,
  //   getCurrentAccessUserId(),
  // );

  const { isShow } = useNotificationBar();
  // const [showChart, setShowChart] = useState(false);
  const [showChart, setShowChart] = useLocalStorage(
    'show-chart-list-delivery-log',
    true,
  );

  // const [width, setWidth] = useState(window.innerWidth);

  const {
    main,
    table,
    filter,
    column,
    data,
    channelId = 2,
    moduleConfig,
    moduleConfigColumn,
    dateRange,
    defaultMetrics,
    // activeRow,
    channelActive = { value: 2 },
    activeTab = 'variants',
    calendarView = {},
    destinationId,
  } = props;
  // console.log('storyId, channelId', storyId, channelId);

  const storyId = safeParse(props.storyId, 0);

  const { isInitDone, isFistLoadTable, drawerDetailsInfo } = main;

  const keyViewType = `${MODULE_CONFIG.objectType}-${channelId}`;
  // eslint-disable-next-line no-unused-vars
  const [viewType, setViewType] = useState(getViewType(keyViewType));
  // const viewType = getViewType(keyViewType);

  useEffect(() => {
    // if (storyId !== '') props.init({ storyId, channelId });
    if (activeTab === 'variants') {
      // const filtersBody = {
      //   objectType: 'ACTION_HISTORY',
      //   filtersBody: {
      //     column: 'process_id',
      //     data_type: 'string',
      //     operator: 'equals',
      //     destinationId,
      //     // value: channelActive.value,
      //   },
      //   isGetOnlySuggestParams: true,
      //   isFilters: channelActive.value !== 0 && true,
      // };
      // setConfigFilter(configToAPISuggestion(filtersBody));
      // setConfigFilter(mapDataToConfigSusggestion(channelActive));
      props.init({ storyId, channelId, destinationId });
    }
    return () => {
      // props.onSaveDateRange(dateRange);
      props.reset();
      props.resetIsLoadingModify();

      // setDateValue(state.value);
    };
  }, [channelId, activeTab]);

  useEffect(() => {
    // khong fetch lan dau (do da dung props.init())
    if (!main.isLoading) {
      props.fetchData();
    }
  }, [viewType, calendarView.dateRange]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'ON_OPEN_DRAWER': {
        props.onChangeDrawerDetailsInfo({
          isOpen: true,
          processId: dataIn && dataIn.row && dataIn.row.process_id,
          id: dataIn && dataIn.row && dataIn.row.id,
          destinationId,
        });
        break;
      }
      case 'FETCH_DATA': {
        props.fetchData(dataIn);
        break;
      }
      // case 'ACTION_TABLE_DELETE': {
      //   setIsOpenModalDelete(true);
      //   break;
      // }
      case 'EDIT_CELL_NAME': {
        props.editCellName(dataIn);
        break;
      }
      // case 'ACTION_TABLE_CHANGE_STATUS': {
      //   props.onChangeStatus(dataIn);
      //   break;
      // }
      case 'ON_CHANGE_DATERANGE': {
        // console.log('ON_CHANGE_DATERANGE', dataIn, dateRange.value);
        props.onChangeDateRange({ data: dataIn, type: 'date_range' });
        if (isInitDone && isFistLoadTable) {
          props.fetchData();
          props.onSaveDateRange(dateRange);
        }
        break;
      }
      case 'UPDATE_METRICS': {
        props.onChangeDefaultMetrics(dataIn);
        props.onSaveDefaultMetrics({ data: dataIn, type: 'metric' });
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'SET_VIEW_TABLE': {
        // setViewType('table');
        // setViewTypeLocalStorage(keyViewType, 'table');
        // handleSetViewType('table');
        break;
      }
      case 'ON_CHANGE_CALENDAR_DATERANGE': {
        props.onChangeCalendarDateRange(dataIn);
        break;
      }
      case 'ON_CLICK_EVENT': {
        // console.log({ dataIn });
        break;
      }
      case 'CHANGE_CHART_TYPE': {
        props.onChangeChartType({ data: dataIn, type: 'chart_type' });
        break;
      }
      default:
        break;
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const isEmptyRecord = React.useMemo(() => table.paging.totalRecord === 0, [
    table,
  ]);
  // const mapParamsDeleteFn = useCallback(
  //   (oldData, newData) => ({
  //     data: {
  //       totalSelected: oldData.isSelectedAll
  //         ? oldData.totalRecord
  //         : newData.accepted.length,
  //       objectIds: newData.accepted.map(each => each.story_id),
  //       isCheckUsedStatus: 0,
  //       filters: oldData.isSelectedAll ? toConditionAPI(oldData.rules) : {},
  //     },
  //   }),
  //   [],
  // );

  // const initDateRangeData = useMemo(() => dateRange, [isInitDone]);

  // console.log('render Campaigns', channelId, moduleConfigColumn);

  // const breadcrums = useMemo(() => getBreadcrums(channelActive), [
  //   channelActive.label,
  // ]);

  const renderListingTable = () => (
    <>
      <NavBarRightContent
        right="14px"
        className="p-right-4"
        style={{ position: 'fixed' }}
      >
        <CalendarSelection
          initData={dateRange}
          callback={callback}
          maxWidth={channelActive.value === 2 && '135px'}
          hiddenLabel={props.isShowOverviewTab}
        />
      </NavBarRightContent>
      <StyleWrapper>
        {/* <CustomHeader breadcrums={breadcrums} /> */}
        <LayoutContent
          padding="0px 15px"
          // margin="-8px -4px -4px -4px"
          style={layoutStyle}
        >
          <LayoutContentLoading isLoading={!isInitDone}>
            <PerformanceChartDelivery
              defaultMetric={defaultMetrics}
              dateRange={dateRange.value}
              showChart={showChart}
              listSelection={main.groupAttributes.metricsPerformanceChart}
              stylesChart={{ width: '100%', height: 200 }}
              ServiceFn={DestinationServices.deliveryLog.getChart}
              style={{ marginTop: 0 }}
              callback={callback}
              rules={filter.rules}
              destination_id={destinationId}
              // channelId={null} // Tương ứng story id
              objectType="DELIVERY_LOG"
              optionScoreCard={ARRAY_OPTION_SCORECARD}
              defaultOptionScoreCard={DEFAULT_OPTIN}
              isScoreCard
              chartTypeDefault={main.chartType}
            />

            <TableRelative>
              <TableWrapper isShow={isShow}>
                <TableContainer
                  global={{ activeRow: props.activeRow }}
                  columnActive={column.columnObj}
                  table={table}
                  isLoading={main.isLoading}
                  moduleConfig={moduleConfig}
                  selectedIds={table.selectedIds}
                  noCheckboxAction
                  selectedRows={table.selectedRows}
                  isSelectedAll={table.isSelectedAll}
                  isSelectedAllPage={table.isSelectedAllPage}
                  columns={tableColumns}
                  data={data}
                  callback={callback}
                  noDataText="No data"
                  resizeColName="process_id"
                  ComponentControlTable={() => <></>}
                  menuCode={MODULE_CONFIG.menuCode}
                  widthFirstColumns={198} // 150 + 48
                  initialWidthColumns={478} // 150 + 280 + 48
                  // widthFirstColumns={124}
                  // initialWidthColumns={width > 1600 ? 291 : 218}
                  // initialWidthColumns={274}
                  // isShowFooter
                  // mapDataFooter={props.mapDataFooter}
                >
                  <>
                    <Filters
                      use="list"
                      rules={filter.rules}
                      moduleConfig={moduleConfig}
                      filterActive={filter.config.filterObj}
                      filterCustom={filter.config.library.filterCustom}
                      libraryFilters={filter.config.library.filters}
                      groups={main.groupAttributes.groupsFilter}
                      isFilter={filter.config.design.isFilter}
                      isLoading={filter.config.isLoading}
                    />
                    <WrapActionTable
                      show={!filter.config.design.isFilter}
                      className="p-x-4"
                    >
                      <div className="actionTable__inner">
                        <Search
                          moduleConfig={moduleConfig}
                          config={{
                            limit: 20,
                            page: 1,
                            objectType: 'DESTINATION_HISTORY',
                            propertyCode: 'suggestion_with_process_id',
                            scope: 1,
                            extra: {
                              sug_column: 'destination_id',
                              sug_value: Number(destinationId),
                            },
                            columns: ['process_id'],
                            destinationId: Number(destinationId),
                          }}
                          suggestionType="suggestionHistoriesJourney"
                          moduleLabel={MAP_TITLE.titlStories}
                          // disabled
                          isAddFilter
                          isGoTo={false}
                        />
                        <ModifyColumn
                          sort={table.sort}
                          moduleConfig={moduleConfigColumn}
                          columnActive={column.columnObj}
                          columnCustom={column.library.columnCustom}
                          libraryColumns={column.library.columns}
                          defaults={moduleConfigColumn.columnsDefault}
                          // defaults={props.main.groupAttributes.requires}
                          defaultSortColumns={
                            moduleConfigColumn.defaultSortColumns
                          }
                          columns={column.columnObj.columns.columnsAlias}
                          groups={main.groupAttributes.groups}
                          isLoading={column.isLoading}
                          isLoadingInfoProperties={
                            // Using for loading modify column info-properties
                            main.isLoadingModifyColumn
                          }
                        />
                        <IconButton
                          iconName="file_download"
                          size="24px"
                          onClick={
                            isEmptyRecord ? () => {} : toggleModalDownload
                          }
                          isVertical
                          color={isEmptyRecord ? '#BEBEBE' : undefined}
                          disabled={isEmptyRecord}
                        >
                          <span
                            style={{
                              color: isEmptyRecord ? '#A2A2A2' : undefined,
                            }}
                          >
                            {MAP_TITLE.actExport.toUpperCase()}
                          </span>
                        </IconButton>
                      </div>
                      <Divider className="m-x-1" />
                      <IconButton
                        iconName={showChart ? 'arrow-up' : 'arrow-down'}
                        size="24px"
                        onClick={() => setShowChart(prev => !prev)}
                        // disabled
                      />
                    </WrapActionTable>
                  </>
                </TableContainer>
              </TableWrapper>
            </TableRelative>
          </LayoutContentLoading>
        </LayoutContent>
        <ModalExport
          isOpen={isOpenModalDownload}
          toggle={toggleModalDownload}
          paging={table.paging}
          object_type="DELIVERY_LOG"
          // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
          sort={table.sort}
          destinationId={parseInt(destinationId)}
          filters={filter}
          objectName="Destination"
          durations={dateRange.value}
          exportType="DeliveryLog"
          sortDefault="delivery_start_date"
          // itemTypeId={itemTypeId}
          // itemTypeName={activeRow.item_type_name}
          columns={column.columnObj.columns.columnsAlias}
        />
      </StyleWrapper>
      <DrawerDetails
        openData={drawerDetailsInfo}
        setOpenDrawer={newOpen =>
          props.onChangeDrawerDetailsInfo({ isOpen: newOpen })
        }
      />
    </>
  );

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/index.jsx">
      {isInitDone ? (
        renderListingTable()
      ) : (
        <div style={{ position: 'relative', height: 'calc(100vh - 120px)' }}>
          <Loading isLoading={!isInitDone} />
        </div>
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListStoryVariantDomainMain(),
  table: makeSelectListStoryVariantTable(),
  filter: makeSelectListStoryVariantFilter(),
  column: makeSelectListStoryVariantColumn(),
  data: makeSelectListStoryVariantDomainMainData(),
  moduleConfig: makeSelectModuleConfig(),
  moduleConfigColumn: makeSelectModuleConfigColumn(),
  mapDataFooter: makeSelectListStoryMapDataFooter(),
  dateRange: makeSelectListVariantDateRange(),
  defaultMetrics: makeSelectListVariantDefaultMetrics(),
  // activeRow: makeSelectActiveRow(),
  // channelActive: makeSelectJourneyChannelActive(),
  // activeTab: makeSelectStoryDetailDomainActiveTab(),
  calendarView: makeSelectListVariantCalendarView(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onSaveDateRange: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    onChangeDefaultMetrics: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    onSaveDefaultMetrics: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DEFAULT_METRICS`, params));
    },
    resetIsLoadingModify: params => {
      dispatch(reset(`${MODULE_CONFIG.key}@@IS_LOADING_MODIFY_COLUMN`, params));
    },
    onChangeCalendarDateRange: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CALENDAR_DATE_RANGE`, params),
      );
    },
    updateIsInitDone: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_INIT_DONE`, params));
    },
    onChangeChartType: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@UPDATE_CHART_TYPE`, params));
    },
    onChangeDrawerDetailsInfo: newInfo => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_DRAWER_DETAIL_INFO`, newInfo),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(Variant);

import styled, { css } from 'styled-components';
import { UITabs as Tabs } from '@xlab-team/ui-components';

import { breakdownMd } from 'utils/variables';
// import TableContainer from 'containers/Table';

// export const StyleTableCampaign = styled(TableContainer)`
//   &::-webkit-scrollbar-track {
//     margin-left: ${props =>
//       props.firstColWidth
//         ? `${props.firstColWidth + 148}px`
//         : '291px'} !important;
//     ${breakdownMd(
//       css`
//         margin-left: ${props =>
//           props.firstColWidth
//             ? `${props.firstColWidth + 148}px`
//             : '218px'} !important;
//       `,
//     )}
//   }
// `;

export const StyleTabs = styled(Tabs)`
  margin-top: 10px;
`;

export const WrapperDetail = styled.div`
  position: relative;
  /* height: calc(100vh - 76px); */
  ${({ isLoading }) =>
    isLoading && `overflow: hidden; box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);`}

  .private-overlay {
    padding: 0 !important;
    z-index: 902;
  }

  .navbar-name {
    height: 120px;
    padding-left: 24px;
    align-items: start;
    padding-top: 16px;
  }
`;

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  margin-top: 8px;
  padding: 3.25rem 0.75rem 0.2rem 0.75rem;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  /* ${breakdownMd(
    css`
      height: calc(100vh - 108px);
      top: 108px;
      left: 0.75rem;
      right: 0.75rem;
    `,
  )} */
`;

export const StyledFooterTabShareAccess = styled.div`
  display: flex;
  padding: 15px;
  gap: 10px;
  background-color: #fff;
`;

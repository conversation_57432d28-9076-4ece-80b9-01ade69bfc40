/* eslint-disable camelcase */
import { takeLatest, select, call, put } from 'redux-saga/effects';
import DestinationServices from 'services/Destination';
import _isEmpty from 'lodash/isEmpty';
import _ from 'lodash';
import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_INTEGRATION } from 'containers/Drawer/DrawerIntegration/config';
import { makeSelectDestDetailDomainMain } from './selectors';
import { getDetailDone, updateValue } from '../../../../../redux/actions';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { makeSelectPortal } from '../../../selector';
import { push } from 'react-router-redux';
import { makeUrlPermisison } from '../../../../../utils/web/permission';
import APP from '../../../../../appConfig';
import { getCurrentAccessUserId } from '../../../../../utils/web/cookie';
import { getObjectPropSafely } from '../../../../../utils/common';
import { convertShareAccessToFE } from '../../../../../components/common/ShareAccessAntsomiUI/utils';

const PREFIX = MODULE_CONFIG.key;
const PREFIX_INTEGRATION = MODULE_CONFIG_INTEGRATION.key;

export default function* workerSegmentDetailSaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(
    `${PREFIX}${ReduxTypes.GET_DETAIL}`,
    handleFetchObjectDetail,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
  );
}

function* handleInit(action, args) {
  yield call(handleFetchObjectDetail);
}

function* handleFetchObjectDetail(action) {
  try {
    const { destinationId } = yield select(makeSelectDestDetailDomainMain());
    if (!_isEmpty(destinationId)) {
      const { data = {}, ...restApi } = yield call(
        DestinationServices.data.getDetailV2,
        {
          objectId: destinationId,
        },
      );

      if (restApi && restApi.code !== 200) {
        const dataDisplay = {};
        if (
          restApi.codeMessage === 'NO_PERMISSION_TO_VIEW' ||
          restApi.codeMessage === '_TITL_NO_PERMISSION'
        ) {
          dataDisplay.isRequestAccess = true;
        } else if (restApi.codeMessage === 'DESTINATION_ID_NOT_EXISTS') {
          dataDisplay.isItemNotFound = true;
        }
        yield put(updateValue(`${PREFIX}@@UPDATE_DISPLAY_PAGE`, dataDisplay));
      } else {
        const { catalogCode, isEdit, ownerId } = data;
        const accessUserId = getCurrentAccessUserId();

        if (catalogCode === 'antsomi_email') {
          const resIdentityStatus = yield call(
            DestinationServices.history.getIdentityStatus,
            {
              emailIdentity: getObjectPropSafely(
                () => data.destinationSetting.identityFields.emailIdentity,
              ),
            },
          );
          const identityStatus = getObjectPropSafely(
            () => resIdentityStatus.data.status,
          );

          if (typeof data.destinationSetting === 'object') {
            data.destinationSetting.identityStatus = identityStatus;
          }
        }

        const destinationName = getObjectPropSafely(
          () => data.destinationName,
          '',
        );
        yield put(
          updateValue(
            `${PREFIX_INTEGRATION}@@CHANGE_DESTINATION_NAME_WP`,
            destinationName,
          ),
        );
        const accessInfoAPI = _.get(data, ['shareAccess'], {});
        const accessInfoParsed = convertShareAccessToFE(accessInfoAPI);

        yield put(
          updateValue(
            `${PREFIX_INTEGRATION}@@UPDATE_SHARE_ACCESS`,
            accessInfoParsed,
          ),
        );
        yield put(updateValue(`${PREFIX}@@UPDATE_PERMISSION_EDIT`, isEdit));
        yield put(
          updateValue(
            `${PREFIX}@@UPDATE_OWNER`,
            ownerId.toString() === accessUserId.toString(),
          ),
        );
        yield put(getDetailDone(PREFIX, data));
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Detail/saga.js',
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
  }
}

function* handleGoToList() {
  const portal = yield select(makeSelectPortal);
  yield put(
    push(
      makeUrlPermisison(
        `${APP.PREFIX}/${
          portal.portalId
        }/${getCurrentAccessUserId()}/marketing-hub/destinations`,
      ),
    ),
  );
}

import {
  CellText,
  CellMainDestination,
  CellToggle,
  CellDate,
  CellNumber,
  CellArray,
  CellLabelWithIcon,
  CellStatusText,
} from '../../../../../containers/Table/Cell';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { initNumberId } from '../../../../../utils/web/portalSetting';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { COLUMNS_WIDTH } from '../../../../../containers/Table/constants';

export const serializeData = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.destination_id,
  }));

export const mapChannels = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.channelId,
    value: item.channelCode,
    label: item.translateLabel || item.channelName,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export function buildTableGroupColumns(
  columnName,
  columnStatus,
  columnsActive,
) {
  return [
    {
      Header: columnStatus.label,
      id: columnStatus.value,
      name: columnStatus.value,
      accessor: columnStatus.value,
      width: 120,
      minWidth: COLUMNS_WIDTH.SWITCH,
      maxWidth: COLUMNS_WIDTH.SWITCH,
      disableResizing: true,
      sticky: 'left',
      Cell: CellToggle,
      placement: 'center',
      className: `${columnStatus.value}`,
      Footer: '',
    },
    {
      Header: columnName.label,
      id: columnName.value,
      accessor: columnName.value,
      width: COLUMNS_WIDTH.MAIN,
      minWidth: COLUMNS_WIDTH.MAIN,
      // disableResizing: true,
      sticky: 'left',
      Cell: CellMainDestination,
      getIsDisabledEditName: () => false,
      Footer: getTranslateMessage(TRANSLATE_KEY._TITL_TOTAL, 'Total: Account'),
    },

    {
      Header: 'Info Remaining',
      columns: buildTableColumns(columnsActive),
      Footer: '',
    },
  ];
}

export function buildTableColumns(columnsActive) {
  const columns = [];
  columnsActive.forEach(property => {
    // console.log('columnsActive', property);
    const { dataType, displayFormat } = property;
    const disableSortBy = parseInt(property.isSort) !== 1;
    const column = {
      Header: property.label,
      id: property.value,
      accessor: MAP_ACCESSOR[property.value] || property.value,
      Cell: CellText,
      disableSortBy,
      mapPropertyValues: new Map(property.mapPropertyValues),
      displayFormat,
      minWidth: COLUMNS_WIDTH.DEFAULT,
      Footer: property.type === 2 ? '--' : '',
      className: `${dataType} ${property.value}`,
    };
    column.Cell =
      MAP_CELL_BY_VALUE[property.value] ||
      MAP_CELL_BY_DATATYPE[dataType] ||
      CellText;

    if (
      ['destination_status', 'catalog_status'].includes(property.propertyCode)
    ) {
      column.Cell = CellStatusText;
    }

    ['destination_id'].forEach(each => {
      if (each === property.propertyCode) {
        column.displayFormat = initNumberId();
      }
    });
    columns.push(column);
  });
  return columns;
}

const MAP_ACCESSOR = {
  status: 'status_display',
};

const MAP_CELL_BY_VALUE = {
  c_user_id: CellText,
  u_user_id: CellText,
  catalog_name: CellLabelWithIcon,
};

const MAP_CELL_BY_DATATYPE = {
  string: CellText,
  datetime: CellDate,
  number: CellNumber,
  boolean: CellText,
  array: CellArray,
};

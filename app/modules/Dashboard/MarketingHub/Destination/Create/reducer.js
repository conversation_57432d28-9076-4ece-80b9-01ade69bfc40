/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _pick from 'lodash/pick';
import _has from 'lodash/has';
import _different from 'lodash/difference';
import _union from 'lodash/union';
import { combineReducers } from 'redux';
import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import {
  mapValueToFE,
  initDefaultDataConfig,
  mapDestCatalog,
  mapMethods,
  mapConfigureField,
  TYPE_DELIVERY_INTERVAL,
  MINUTE_OPTIONS,
  EXTRA_EMAIL_FIELDS,
} from './utils';
import { createTooltip } from './utils.map';
import { getObjectPropSafely } from '../../../../../utils/common';
import { DEFAULT_INTERVAL_SETTING } from './constants';
import isEmpty from 'lodash/isEmpty';

export const initStateCreateDestionation = () => ({
  isInitDone: false,
  dataConfig: initDefaultDataConfig(),
  destCatalogs: { map: {}, list: [] },
  methods: { map: {}, list: [] },
  activeDestCatalog: {},
  activeRow: {},
  channelId: null,
  catalogId: null,
  design: 'create',
  allowSave: true,
  cacheOptionsDataDes: null,
  isLoadingConfigFields: false,
  isLoading: true,
  isDoing: false,
  isLimitedDestination: false,
  isValidate: false,
  extraEmailBtn: [],
  accessInfo: {
    general: {},
    listAccess: [],
  },
});

const validateAll = draft => {
  const {
    dataConfig: { configFields, infoFields, extraInfoFields },
  } = draft;
  return [...configFields, ...infoFields, ...extraInfoFields].every(each => {
    return draft.dataConfig[each].isValidate;
  });
};
const mainReducerFor = moduleConfig => {
  const PREFIX = MODULE_CONFIG.key;
  const mainReducer = (state = initStateCreateDestionation(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initStateCreateDestionation();
        }
        case `${PREFIX}@@RESET_SETTING${ReduxTypes.RESET}`: {
          const initDataConfig = initDefaultDataConfig();
          const defaultGeneralSetting = _pick(initDataConfig, [
            'frequencyCapping',
            'deliveryInterval',
            'deliveryRate',
            'deliveryBroadcast',
          ]);

          if (state.design === 'create') {
            Object.entries(defaultGeneralSetting).forEach(([key, value]) => {
              if (_has(state.dataConfig, key)) {
                draft.dataConfig[key] = value;
              }
            });
          }

          break;
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          draft.isInitDone = false;
          draft.allowSave = true;
          const { channelId, activeRow, design, queryDesign } = action.payload;
          draft.channelId = channelId;
          draft.design = design;
          if (design === 'create') {
            draft.activeRow = {};
          } else if (design === 'preview' || design === 'update') {
            draft.activeRow = activeRow;
            draft.catalogId = activeRow.catalogId;
          }
          return draft;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          const {
            dataConfig: { configFields, infoFields, extraInfoFields },
            design,
            activeRow,
            dataConfig,
          } = state;
          const extraEmailBtn = [];
          if (design === 'preview' || design === 'update') {
            const mapValue = mapValueToFE({ activeRow, dataConfig });
            [...configFields, ...infoFields, ...extraInfoFields].forEach(
              each => {
                draft.dataConfig[each].value = mapValue[each]; // string only
                draft.dataConfig[each].initValue = mapValue[each]; // string only
                const { isValidate, errors } = draft.dataConfig[each].validate(
                  draft.dataConfig[each],
                );
                draft.dataConfig[each].errors = errors;
                draft.dataConfig[each].isValidate = isValidate;
                if (design === 'preview') {
                  draft.dataConfig[each].disabled = true;
                }

                if (
                  design !== 'create' &&
                  Object.values(EXTRA_EMAIL_FIELDS).includes(each) &&
                  !mapValue[each]
                ) {
                  extraEmailBtn.push(each);
                }
              },
            );
            if (design === 'update') {
              draft.dataConfig.destinationCatalog.disabled = true;
              draft.dataConfig.method.disabled = true;
              draft.allowSave = false;
            }
            draft.extraEmailBtn = extraEmailBtn;
            draft.dataConfig.frequencyCapping.value = mapValue.frequencyCapping;
            draft.dataConfig.deliveryBroadcast = mapValue.deliveryBroadcast || {
              isBroadcast: false,
              canSendBroadcast: false,
              from: 1,
              to: 1000,
              repeatInterval: DEFAULT_INTERVAL_SETTING,
              disabled: false,
            };
            draft.dataConfig.deliveryRate =
              Object.keys(mapValue.deliveryRate).length > 1
                ? mapValue.deliveryRate
                : state.dataConfig.deliveryRate;
            draft.dataConfig.deliveryInterval = !_isEmpty(
              getObjectPropSafely(() => mapValue.deliveryInterval, {}),
            )
              ? mapValue.deliveryInterval
              : state.dataConfig.deliveryInterval;
            draft.dataConfig.frequencyCapping.disabled = design === 'preview';
            draft.dataConfig.deliveryRate.disabled = design === 'preview';
            draft.dataConfig.deliveryInterval.disabled = design === 'preview';
            draft.dataConfig.deliveryBroadcast.disabled = design === 'preview';
            draft.dataConfig.isValidate = validateAll(draft);
          }
          draft.isInitDone = true;
          draft.isLoading = false;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE}`: {
          draft.isDoing = true;
          return draft;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.isDoing = false;
          draft.isLimitedDestination = false;
          return draft;
        }
        case `${PREFIX}@@DEST_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          const destinationName = action.payload;

          draft.dataConfig.destinationName.value = destinationName;
          const {
            errors,
            isValidate,
          } = draft.dataConfig.destinationName.validate(
            draft.dataConfig.destinationName,
          );
          draft.dataConfig.destinationName.errors = errors;
          draft.dataConfig.destinationName.isValidate = isValidate;
          draft.dataConfig.isValidate = validateAll(draft);

          return draft;
        }
        case `${PREFIX}@@DATA_CONFIG_FORCE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name = '', value = {} } = action.payload;

          if (name === 'deliveryBroadcast') {
            const { isBroadcast = true, from = 0, to = 0 } = value;
            draft.dataConfig.deliveryBroadcast.canSendBroadcast = true;
            draft.dataConfig.deliveryBroadcast.isBroadcast = isBroadcast;
            if (typeof from === 'number') {
              draft.dataConfig.deliveryBroadcast.from = from;
            }
            if (typeof to === 'number') {
              draft.dataConfig.deliveryBroadcast.to = to;
            }
          }
          break;
        }
        case `${PREFIX}@@DATA_CONFIG@@${ReduxTypes.UPDATE_VALUE}`: {
          const { name, value, extraValue } = action.payload;

          // Allow save when user change value
          draft.allowSave = true;

          if (
            name !== 'deliveryRate' &&
            name !== 'deliveryInterval' &&
            name !== 'deliveryBroadcast'
          ) {
            draft.dataConfig[name].value = value;

            if (name === 'method') {
              const isForceHide = _get(value, 'forceHide', false);

              if (isForceHide) {
                // Need to reset deliveryBroadcast to default when new method is force hide
                draft.dataConfig.deliveryBroadcast = {
                  ...state.dataConfig.deliveryBroadcast,
                  from: 1,
                  to: 1000,
                  repeatInterval: DEFAULT_INTERVAL_SETTING,
                  isBroadcast: false,
                };
              }
            }
          }
          if (name === 'frequencyCapping') {
            //
          } else if (name === 'deliveryRate') {
            if (typeof value === 'number') {
              draft.dataConfig[name].limit = value;
            } else {
              draft.dataConfig[name].type = value.target.value;
              if (value.target.value === 'normal') {
                draft.isValidate = false;
                // delete draft.dataConfig[name].limit;
              }
            }
          } else if (name === 'deliveryInterval') {
            if (typeof value === 'number') {
              draft.dataConfig[name].value = value;
            } else {
              const { value: valueType = '' } = value;
              const isOptionMinute = MINUTE_OPTIONS.findIndex(
                option => option.value === valueType,
              );

              if (isOptionMinute !== -1) {
                draft.dataConfig[name].value = valueType;
              } else {
                draft.dataConfig[name].mapOptions = value;

                if (valueType === TYPE_DELIVERY_INTERVAL.NUM_RECORDS) {
                  draft.dataConfig[name].value = 100;
                } else if (valueType === TYPE_DELIVERY_INTERVAL.MINUTE) {
                  draft.dataConfig[name].value = 15;
                } else {
                  draft.dataConfig[name].value = 1;
                }
              }
            }
          } else if (name === 'deliveryBroadcast') {
            if (typeof value === 'boolean') {
              draft.dataConfig[name].canSendBroadcast = value;
            } else if (typeof value === 'string') {
              if (value === 'onCheckingBroadCast') {
                draft.dataConfig[name].isBroadcast = extraValue;

                // If user uncheck onCheckingBroadCast, we need to set isSendMultiple to false
                if (!extraValue && state.dataConfig[name].isSendMultiple) {
                  draft.dataConfig[name].isSendMultiple = extraValue;
                } else if (
                  extraValue &&
                  _has(state.dataConfig[name], 'isSendMultiple')
                ) {
                  draft.dataConfig[name].isSendMultiple = extraValue;
                }
              }
              if (value === 'onCheckingMultiple') {
                draft.dataConfig[name].isSendMultiple = extraValue;
              }
            } else {
              const {
                from = 0,
                to = 0,
                isFrom = false,
                repeatInterval = {},
              } = value;

              if (!isEmpty(repeatInterval)) {
                draft.dataConfig[name].repeatInterval = repeatInterval;
              } else {
                if (isFrom) {
                  draft.dataConfig[name].from = from;
                } else {
                  draft.dataConfig[name].to = to;
                }
              }
            }
          } else {
            const { errors, isValidate } = draft.dataConfig[name].validate(
              draft.dataConfig[name],
            );
            draft.dataConfig[name].errors = errors;
            draft.dataConfig[name].isValidate = isValidate;
          }
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_LIST_DONE}`: {
          const data = action.payload;
          const map = mapDestCatalog(data);
          draft.cacheOptionsDataDes = data;
          draft.dataConfig.destinationCatalog.options = map.list;
          draft.dataConfig.destinationCatalog.mapOptions = map.map;
          return draft;
        }
        case `${PREFIX}@@ERROR_API${ReduxTypes.UPDATE_VALUE}`: {
          const { name, errors } = action.payload;
          draft.dataConfig[name].errors = errors;
          draft.dataConfig[name].isValidate = false;
          draft.dataConfig.isValidate = false;
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL}`: {
          const { value } = action.payload;
          draft.catalogId = value.id;
          draft.isLoadingConfigFields = true;
          return draft;
        }
        case `${PREFIX}@@UPDATE_EXTRA_EMAIL_BTN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { btnName = '', type = '' } = action.payload;
          if (!btnName) return draft;

          if (type === 'ADD') {
            draft.extraEmailBtn = _different(state.extraEmailBtn, [btnName]);
          } else if (type === 'REMOVE') {
            draft.extraEmailBtn = _union(state.extraEmailBtn, [btnName]);

            draft.dataConfig[btnName].value = '';
            draft.dataConfig[btnName].initValue = '';
          }
          return draft;
        }
        case `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL_DONE}`: {
          draft.isLoadingConfigFields = false;
          const data = action.payload;
          const {
            catalogInput: { methods, settings, basicSettings, inputConfig },
          } = data;
          // update method
          if (!state.dataConfig.extraInfoFields.includes('method'))
            draft.dataConfig.extraInfoFields.push('method');
          const mapMethod = mapMethods(methods);
          draft.methods = mapMethod;
          draft.dataConfig.method.options = mapMethod.list;
          draft.dataConfig.method.mapOptions = mapMethod.map;
          if (mapMethod.list.length === 1) {
            [draft.dataConfig.method.value] = mapMethod.list;
            draft.dataConfig.method.isValidate = true;
          }
          // update tooltip

          const { basicInputs = [], inputs = {} } =
            inputConfig[draft.dataConfig.method.value.name] || {};
          draft.dataConfig.destinationCatalog.tooltip = createTooltip({
            basicSettings,
            settings,
            basicInputs,
            inputs,
          });

          // update config form
          const mapConfigFields = mapConfigureField(basicSettings, settings);

          // Map extra btn for channel email
          if (
            Number(data.channelId) === 1 &&
            Array.isArray(basicSettings) &&
            state.design === 'create'
          ) {
            const extraEmailBtn = [];

            basicSettings.forEach(each => {
              if (Object.values(EXTRA_EMAIL_FIELDS).includes(each)) {
                extraEmailBtn.push(each);
              }
            });

            draft.extraEmailBtn = extraEmailBtn;
          }
          draft.dataConfig.configFields = basicSettings;
          basicSettings.forEach(each => {
            draft.dataConfig[each] = mapConfigFields[each];
          });
          draft.activeDestCatalog = data;
          draft.dataConfig.isValidate = validateAll(draft);
          return draft;
        }
        case `${PREFIX}@@DESIGN${ReduxTypes.UPDATE_VALUE}`: {
          const { design } = action.payload;
          const {
            dataConfig: { configFields, infoFields, extraInfoFields },
          } = state;
          draft.design = design;
          // handle disable here
          if (design === 'update') {
            [...configFields, ...infoFields, ...extraInfoFields].forEach(
              each => {
                draft.dataConfig[each].disabled = false;
              },
            );
            draft.dataConfig.frequencyCapping.disabled = false;
            draft.dataConfig.destinationCatalog.disabled = true;
            draft.dataConfig.deliveryRate.disabled = false;
            draft.dataConfig.deliveryInterval.disabled = false;
            draft.dataConfig.deliveryBroadcast.disabled = false;
            draft.dataConfig.method.disabled = false;
          } else if (design === 'preview') {
            [...configFields, ...infoFields, ...extraInfoFields].forEach(
              each => {
                draft.dataConfig[each].disabled = true;
              },
            );
            draft.dataConfig.frequencyCapping.disabled = true;
            draft.dataConfig.deliveryRate.disabled = true;
            draft.dataConfig.deliveryInterval.disabled = true;
          }
          return draft;
        }
        case `${PREFIX}@@WARNING_LIMIT_DESTINATION${ReduxTypes.UPDATE_VALUE}`: {
          const { isLimited } = action.payload;
          draft.isLimitedDestination = isLimited;
          return draft;
        }
        case `${PREFIX}@@VALIDATE_LIMIT${ReduxTypes.UPDATE_VALUE}`: {
          const { isValidate } = action.payload;
          draft.isValidate = isValidate;
          return draft;
        }
        case `${PREFIX}@@UPDATE_SHARE_ACCESS${ReduxTypes.UPDATE_VALUE}`: {
          const { accessInfo } = action.payload;
          draft.accessInfo = accessInfo;
          return draft;
        }
        case `${PREFIX}@@CHANGE_STATUS_ALLOW_SAVE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newStatus = action.payload;
          draft.allowSave = newStatus;
          return draft;
        }
        case `${PREFIX}@@INIT_CATALOG@@${ReduxTypes.UPDATE_VALUE}`: {
          const data = action.payload;
          draft.dataConfig.destinationCatalog.value = {
            ...data,
            label: data.catalogName,
            value: data.catalogId,
          };
          const {
            errors,
            isValidate,
          } = draft.dataConfig.destinationCatalog.validate(
            draft.dataConfig.destinationCatalog,
          );
          draft.dataConfig.destinationCatalog.errors = errors;
          draft.dataConfig.destinationCatalog.isValidate = isValidate;
          return draft;
        }

        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default combineReducers({
  main: mainReducerFor(),
});

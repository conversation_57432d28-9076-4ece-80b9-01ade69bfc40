/* eslint-disable no-restricted-syntax */
/* eslint-disable default-case */
/* eslint-disable no-unused-vars */
// Libraries
import { takeLatest, call, put, select, all } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import get from 'lodash/get';

// Locales
import TRANSLATE_KEY from '../../../../../messages/constant';
import { addMessageToQueue } from '../../../../../utils/web/queue';

// Services
import DestinationServices from 'services/Destination';
import ThirdPartyService from 'services/3rd';

// Constants
import ReduxTypes from '../../../../../redux/constants';
import APP from '../../../../../appConfig';
import { CHANNEL_TYPE, CATALOG_CODE } from './constants';

// MODULE_CONFIGS
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_INTEGRATION } from 'containers/Drawer/DrawerIntegration/config';

// Selectors
import { makeSelectPortal } from '../../../selector';
import { makeSelectCreateDesMain } from './selectors';
import {
  makeSelectChannelIdIntegration,
  makeSelectDestinationNameWorkspace,
  makeSelectAccessInfoIntegration,
} from 'containers/Drawer/DrawerIntegration/selectors';

// Actions
import {
  getListDone,
  getDetailDone,
  addNotification,
  updateDone,
  initDone,
  updateValue,
  clearDone,
} from '../../../../../redux/actions';

// Utils
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { toEntryAPI } from './utils';
import { makeUrlPermisison } from '../../../../../utils/web/permission';
import { getCurrentAccessUserId } from '../../../../../utils/web/cookie';
import { safeParse } from '../../../../../utils/common';
import { getUntitledName } from '../../../../../utils/web/properties';
import { mapAccessInfoToApi } from '../../../../../components/common/ShareAccessAntsomiUI/utils';

const PREFIX = MODULE_CONFIG.key;
const PREFIX_INTEGRATION_WORKSPACE = MODULE_CONFIG_INTEGRATION.key;
// const getReducer = state => state.get(PREFIX);

export default function* workerCustomerSaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}${ReduxTypes.UPDATE}`, handleSave);
  // yield takeLatest(
  //   `${PREFIX}@@DATA_CONFIG@@${ReduxTypes.UPDATE_VALUE}`,
  //   handleUpdateDataConfig,
  // );
  yield takeLatest(
    `${PREFIX}@@DEST_CATALOG@@${ReduxTypes.GET_DETAIL}`,
    handleGetDetailDestCatalog,
  );
  yield takeLatest(
    `${PREFIX}@@INIT_CATALOG@@${ReduxTypes.UPDATE_VALUE}`,
    handleGetDetailDestCatalog,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@GO_TO_LIST${ReduxTypes.UPDATE_VALUE}`,
    handleGoToList,
  );
  yield takeLatest(
    `${MODULE_CONFIG.key}@@GO_TO_CREATE_V2${ReduxTypes.UPDATE_VALUE}`,
    handleGoToCreateV2,
  );
}

function* handleInit(action, args) {
  try {
    const { design, activeRow = {} } = action.payload;
    yield call(handleGetListDestCatalog);
    if (design === 'preview' || design === 'update') {
      yield call(handleGetDetailDestCatalog, {
        payload: {
          catalogId: activeRow.catalogId,
        },
      });
    } else {
      const nameWorkspace = yield select(makeSelectDestinationNameWorkspace());
      const initName = nameWorkspace || getUntitledName('Untitled Destination');
      yield put(
        updateValue(`${PREFIX}@@DEST_NAME@@`, {
          EN: initName,
          DEFAULT_LANG: 'EN',
        }),
      );
    }
    yield put(initDone(PREFIX));
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
      func: 'handleInit',
      data: error.stack,
    });
    console.warn(error);
  }
}

function* handleGetListDestCatalog(action) {
  try {
    const { channelId } = yield select(makeSelectCreateDesMain());
    const channelIdIntegration = yield select(makeSelectChannelIdIntegration());
    const id = Number(channelId) || Number(channelIdIntegration);

    const res = yield call(DestinationServices.info.getListDestCatalog, {
      objectId: id,
    });
    yield put(getListDone(`${PREFIX}@@DEST_CATALOG@@`, res.data));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
      func: 'handleGetListDestCatalog',
      data: err.stack,
    });
    console.error(err);
  }
}
function* handleGoToList() {
  const portal = yield select(makeSelectPortal);
  const { channelId } = yield select(makeSelectCreateDesMain());
  yield put(
    push(
      makeUrlPermisison(
        `${APP.PREFIX}/${
          portal.portalId
        }/${getCurrentAccessUserId()}/marketing-hub/destinations/${channelId}`,
      ),
    ),
  );
}

function* handleGoToCreateV2(data) {
  const portal = yield select(makeSelectPortal);

  const channelId = safeParse(data.payload.channelId, '');

  yield put(
    push(
      makeUrlPermisison(
        `${APP.PREFIX}/${
          portal.portalId
        }/${getCurrentAccessUserId()}/marketing-hub/destinations/create/${channelId}/new`,
      ),
    ),
  );
}

// function* handleUpdateDataConfig(action) {
//   try {
//     const { name, value } = action.payload;
//     if (name === 'destinationCatalog') {
//       yield put(getDetail(`${PREFIX}@@DEST_CATALOG@@`, action.payload));
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
//       func: 'handleUpdateDataConfig',
//       data: err.stack,
//     });
//     console.error(err);
//   }
// }

function* handleGetDetailDestCatalog(action) {
  try {
    const { catalogId } = action.payload;
    if (catalogId) {
      const params = {
        objectId: catalogId,
        data: {},
      };
      const res = yield call(
        DestinationServices.info.getDestCatalogDetail,
        params,
      );
      yield put(getDetailDone(`${PREFIX}@@DEST_CATALOG@@`, res.data[0]));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
      func: 'handleGetDetailDestCatalog',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleSave(action) {
  const { channelId } = action.payload;
  try {
    const { dataConfig, design, activeRow } = yield select(
      makeSelectCreateDesMain(),
    );
    const accessInfo = yield select(makeSelectAccessInfoIntegration());
    const channelIdTemp = design === 'create' ? channelId : activeRow.channelId;
    let params = {
      objectId: activeRow.id,
      data: {
        ...toEntryAPI(dataConfig, channelIdTemp),
        channelId,
        shareAccess: mapAccessInfoToApi(accessInfo),
      },
    };

    const errors = yield call(validate3rdSettings, {
      channelId: channelIdTemp,
      dataConfig,
    });

    if (errors.length) {
      for (const error of errors) {
        yield put(
          updateValue(`${PREFIX}@@ERROR_API`, {
            name: error.name,
            errors: error.errors,
          }),
        );
      }

      // loading done
      yield all([
        put(
          updateValue(`${PREFIX_INTEGRATION_WORKSPACE}@@IS_LOADING`, {
            isLoading: false,
          }),
        ),
        put(updateDone(PREFIX)),
      ]);

      return;
    }

    let res = '';
    if (design === 'preview' || design === 'update') {
      const { channelCode } = activeRow;

      if (channelCode) {
        params = {
          ...params,
          data: {
            ...params.data,
            channelCode,
          },
        };
      }

      res = yield call(DestinationServices.data.update, params);
    } else if (design === 'create') {
      res = yield call(DestinationServices.data.create, params);
    }
    if (res.code === 200) {
      const notification = NOTI.updateDestination.success({ design });
      yield call(handleGoToList);
      yield put(clearDone(`${PREFIX_INTEGRATION_WORKSPACE}@@CLEAR_ALL_DONE`));
      yield put(addNotification(notification));
    } else if (res.messageAPI === 'reached the limit of Destination') {
      yield put(
        updateValue(`${PREFIX}@@WARNING_LIMIT_DESTINATION`, {
          isLimited: true,
        }),
      );
    } else if (res.codeMessage === '_NOTIFICATION_NAMESAKE') {
      const dataError = {
        name: 'destinationName',
        errors: [
          getTranslateMessage(res.codeMessage, 'This name already existed'),
        ],
      };
      yield put(updateValue(`${PREFIX}@@ERROR_API`, dataError));
      // let notification = NOTI.updateDestination.fail();
      // notification = {
      //   ...notification,
      //   ...getErrorMessageV2Translate(res.codeMessage),
      // };
      // yield put(addNotification(notification));
    } else {
      const notification = NOTI.updateDestination.fail();
      yield put(addNotification(notification));
    }
    yield put(updateDone(PREFIX));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
      func: 'handleSave',
      data: err.stack,
    });
    const notification = NOTI.updateDestination.fail();
    yield put(addNotification(notification));
    console.log(err);
  }
}

function* validate3rdSettings(args) {
  try {
    const { channelId, dataConfig } = args;
    const catalogCode = get(
      dataConfig,
      'destinationCatalog.value.catalogCode',
      null,
    );

    const errors = [];

    switch (channelId) {
      case CHANNEL_TYPE.WEBHOOK: {
        if (catalogCode === CATALOG_CODE.AEON_MALL_IN_APP_MESSAGE) {
          const partnerId = get(dataConfig, 'partnerId.value', null);
          const partnerSecret = get(dataConfig, 'partnerSecret.value', null);

          const response = yield call(
            ThirdPartyService.AeonMall.validatePartner,
            { data: { partnerId, partnerSecret } },
          );

          const isValid = get(response, 'data.isValid', false);

          if (!isValid) {
            errors.push(
              {
                name: 'partnerId',
                errors: ['Partner ID or Secret is invalid'],
              },
              {
                name: 'partnerSecret',
                errors: ['Partner ID or Secret is invalid'],
              },
            );
          }
        }

        break;
      }
    }

    return errors;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/saga.js',
      func: 'validateSettings',
      data: err.stack,
    });
    console.error(err);

    return [];
  }
}

/*
 +-+-+-+-+
 |N|O|T|I|
 +-+-+-+-+
*/

const NOTI = {
  updateDestination: {
    fail: (res = {}) => ({
      id: 'error',
      message: `Fail to action the destination, please try again.`,
      translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 5000,
      timestamp: new Date().getTime(),
      type: 'danger',
    }),
    success: (res = {}) => ({
      id: 'success',
      message: res.design === 'create' ? 'Created' : 'Updates saved!',
      translateCode:
        res.design === 'create'
          ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
          : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
      timeout: 1500,
      timestamp: new Date().getTime(),
      type: 'success',
    }),
  },
};

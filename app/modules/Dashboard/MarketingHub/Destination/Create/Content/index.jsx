/* eslint-disable prettier/prettier */
/* eslint-disable no-else-return */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, {
  useEffect,
  memo,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { Grid, Step } from '@material-ui/core';
import {
  UICheckbox,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  TabPanel,
} from '@xlab-team/ui-components';
import UISelect from 'components/form/UISelectCondition';
import Radio from '@material-ui/core/Radio';
import WarningIcon from '@material-ui/icons/WarningOutlined';
import UIFrequencyCapping from 'components/common/UIFrequencyCappingV2';
// import UIFrequencyCapping from 'components/common/UIFrequencyCapping';
import FormHelperText from '@material-ui/core/FormHelperText';
import colors from 'utils/colors';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import useNotificationBar from 'hooks/useNotificationBar';
import RadioGroup from '@material-ui/core/RadioGroup';
import DestinationServices from 'services/Destination';
import { updateValue, getListDone } from '../../../../../../redux/actions';
import { MODULE_CONFIG } from '../config';
import Footer from './Footer';

import {
  makeSelectCreateDesDataConfig,
  makeSelectCreateDesMain,
} from '../selectors';
import {
  WrapperContent,
  StyleContent,
  useStyles,
  DivMessageComputing,
  StyleContentLoading,
  LabelText,
  StyledStepper,
  BlockTitle,
  StyleCheckboxWrapper,
} from './styles';
import { StyledTabShareAccess, Title } from '../styles';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import {
  generateKey,
  getObjectPropSafely,
  safeParseInt,
} from '../../../../../../utils/common';
import {
  WrapperFixWidth,
  WrapperLabelOption,
  WrapperTitleItem,
} from '../../../../../../containers/Segment/Content/SegmentMember/styled';
import { UINumberStyled } from '../../../Journey/Create/Content/Nodes/Destination/styles';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import {
  CHANNEL_FILE_TRANSFER,
  MAX_TO_SEND_BROADCAST,
  MINUTE_OPTIONS,
  TYPE_DELIVERY_INTERVAL,
  mapLabelDeliveryInterval,
} from '../utils';
import LayoutContent from '../../../../../../components/Templates/LayoutContent';
import LayoutLoading from '../../../../../../components/Templates/LayoutContent/LayoutLoading';
import ShareAccess from '../../../../../../components/common/ShareAccess';
import {
  makeSelectIsHasPermissionDestDetail,
  makeSelectIsOwnerDestDetail,
} from '../../Detail/selectors';
import { StyledUITabs } from '../../styles';
import {
  StepWrapper,
  StyleFooter,
  StyledStepButton,
  WrapperInner,
} from '../../CreateV2/Design/Templates/Content/styles';
import { MAP_STEP_NAME, STEPS, STEP_NAME } from '../constants';
import CreateJourney from '../../CreateV2/Design/CreateTemplate';
import makeSelectCreateDesMainCatalog from '../../CreateV2/Design/CreateTemplate/selectors';
import CreateDestination from '../../CreateV2';
import { makeSelectCreateDesMain as makeSelectCreateDesMainAntsomiEmail } from '../../CreateV2/Design/AntsomiEmail/selectors';
import { BlockContainer } from '../../../../../../containers/Drawer/DrawerIntegration/components/Content/styled';

const CHANNEL_ID_TYPE = {
  WEB_PUSH: 3,
  APP_PUSH: 4,
};
export const CATALOG_CODE_CHANNEL_LIMIT = {
  [CHANNEL_ID_TYPE.WEB_PUSH]: 'antsomi_web_push',
  [CHANNEL_ID_TYPE.APP_PUSH]: 'antsomi_app_push',
};

const PATH = 'appmodulesDashboardMarketingHubDestinationCreateContentindex.jsx';
// Constants
const styleFooter = {
  borderTop: '1px solid #D4D4D4',
  backgroundColor: '#ffffff',
};
const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ALERT_EDIT_DESTINATION,
    'If you edit this destination will affect the input of other features.',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_SETTING,
    'General Setting',
  ),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titlDeliveredRate: getTranslateMessage(
    TRANSLATE_KEY._TITLE_DES_DELIVERED_RATE,
    'Delivered rate',
  ),
  titlDeliveredInterval: getTranslateMessage(
    TRANSLATE_KEY._DES_DELIVERY_INTERVAL,
    'Delivery Interval Setting',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  limitSentRate: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_LIMIT_SEND_RATE,
    'Limit send rate',
  ),
  timePerson: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_TIME_PER_SECOND,
    'time(s) per second',
  ),
};

const CheckBoxCustom = ({
  name,
  disabled,
  size = 'small',
  onChange,
  checked,
  label,
}) => {
  // eslint-disable-next-line no-shadow
  const onChangeLocal = checked => {
    onChange(name, checked);
  };
  return (
    <UICheckbox
      color="primary"
      size={size}
      disabled={disabled}
      checked={checked}
      onChange={onChangeLocal}
      label={label}
      className="label-delivery-interval-settings"
    />
  );
};

const InputComponent = memo(props => (
  <BlockContainer
    container
    className={props.classes.item}
    style={props.isHideField ? { padding: 0 } : {}}
  >
    {props.componentEl(props)}
  </BlockContainer>
));
function Content(props) {
  const classes = useStyles();
  const { main, dataConfig, mainCatalog } = props;
  const { catalogActive = {} } = mainCatalog;
  const { isLoading, isInitDone, design, channelId, isValidate } = main;
  const { isShow } = useNotificationBar();
  const [isValidBroadCast, setIsValidBroadCast] = useState(true);
  const [isNewsUICatalog, setisNewsUICatalog] = useState(false);
  const [isNewsUICatalogSTep, setisNewsUICatalogStep] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');
  const [activeStep, setActiveStep] = useState(STEP_NAME.CATALOG);
  const [visitedSteps, setVisitedSteps] = useState([
    MAP_STEP_NAME[STEP_NAME.GENERAL],
  ]);
  const stepRef = useRef(null);
  useEffect(() => {
    if (design === 'create' && Object.keys(catalogActive).length > 0) {
      const isAntsomiWebPush = getObjectPropSafely(
        () =>
          +channelId === 3 && catalogActive.catalogCode === 'antsomi_web_push',
      );
      const isAntsomiEmail = getObjectPropSafely(
        () => +channelId === 1 && catalogActive.catalogCode === 'antsomi_email',
      );

      const isAntsomiAppPush = getObjectPropSafely(
        () =>
          +channelId === 4 && catalogActive.catalogCode === 'antsomi_app_push',
      );
      const isZaloOA = getObjectPropSafely(
        () =>
          +channelId === 10 &&
          catalogActive.catalogCode === 'zalo_official_account',
      );

      const isNewUI = isAntsomiWebPush || isAntsomiEmail;
      const isNewUIStep = isZaloOA || isAntsomiAppPush;
      setisNewsUICatalog(isNewUI);
      setisNewsUICatalogStep(isNewUIStep);
      props.onChangeInitCataLog(catalogActive);
    } else {
      setisNewsUICatalog(false);
      setisNewsUICatalogStep(false);
    }
  }, [catalogActive, activeStep, channelId]);
  const onMemoInitdata = useMemo(() => dataConfig.frequencyCapping.value, [
    isInitDone,
  ]);
  const onMemoComponenId = useMemo(() => generateKey(), [isInitDone]);
  const onChangeData = useCallback(
    name => value => {
      const isAntsomiWebPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 3 &&
          value.catalogCode === 'antsomi_web_push',
      );
      const isAntsomiEmail = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 1 &&
          value.catalogCode === 'antsomi_email',
      );

      const isAntsomiAppPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 4 &&
          value.catalogCode === 'antsomi_app_push',
      );

      const isZaloOA = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 10 &&
          value.catalogCode === 'zalo_official_account',
      );

      const isNewUI =
        isAntsomiWebPush || isAntsomiEmail || isAntsomiAppPush || isZaloOA;

      if (isNewUI) {
        props.goToCreateV2({ channelId });
      } else if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  const canSendBroadcast = useMemo(
    () =>
      getObjectPropSafely(
        () => props.main.activeDestCatalog.catalogInput.canSendBroadcast,
        false,
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const intervalBroadcastSetting = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          props.main.activeDestCatalog.catalogSetting.intervalBroadcastSetting,
        {},
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const { isSendBroadcast, fromNumber, toNumber } =
    intervalBroadcastSetting || {};

  const isForceHideBroadCast = useMemo(
    () => _get(dataConfig, 'method.value.forceHide', false),
    [dataConfig && dataConfig.method],
  );

  const isHideMethod = useMemo(
    () => _get(dataConfig, 'method.options.length', 0) === 1,
    [dataConfig?.method?.options],
  );

  useEffect(() => {
    if (!_isEmpty(intervalBroadcastSetting)) {
      props.onChangeDataConfigForce({
        name: 'deliveryBroadcast',
        value: {
          from: fromNumber,
          to: toNumber,
          isBroadcast: isSendBroadcast,
        },
      });
    }
  }, [intervalBroadcastSetting]);

  useEffect(() => {
    if (canSendBroadcast && design === 'create') {
      onChangeData('deliveryBroadcast')(canSendBroadcast);
    }
  }, [canSendBroadcast]);

  useEffect(() => {
    const el = document.getElementById('setting-container');
    el.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth', // or can get `auto` variable
    });
  }, [design]);

  const handleValidateDestinationCount = async catalogId => {
    try {
      const res = await DestinationServices.data.getCountDestination({
        catalogId,
      });

      const newOptions = main.cacheOptionsDataDes.filter(
        option =>
          option.catalogCode !==
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      if (res.code === 200 && safeParseInt(res.data.destinationCount) > 0) {
        props.onChangeDesCataLog(newOptions);
      }
    } catch (err) {
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Destination/Create/Content/index.jsx',
        func: 'handleValidateDestinationCount',
        data: err.stack,
      });
      console.warn('err', err);
    }
  };

  useEffect(() => {
    if (
      design === 'create' &&
      Object.values(CHANNEL_ID_TYPE).includes(safeParseInt(channelId))
    ) {
      const catalogTemp = dataConfig.destinationCatalog.options.find(
        option =>
          option.catalogCode ===
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      const catalogId = getObjectPropSafely(() => catalogTemp.catalogId, '');

      if (catalogId) {
        handleValidateDestinationCount(catalogId);
      }
    }
  }, [dataConfig.destinationCatalog.options]);

  // callback
  const callback = (type, dataIn) => {
    try {
      // console.log('type :>>', type);
      // console.log('dataIn :>>', dataIn);
      switch (type) {
        case 'ON_BACK': {
          setActiveStep(activeStep - 1);
          break;
        }
        case 'ON_NEXT': {
          const nextStep = activeStep + 1;
          setActiveStep(nextStep);

          break;
        }
        case 'ON_CANCEL':
        case 'ON_CANCEL_UPDATE': {
          props.callback(type);
          break;
        }
        case 'ON_SAVE': {
          props.callback(type, dataIn);
          break;
        }
        case 'ON_CHANGE_DESIGN_TYPE': {
          props.callback(type, dataIn);
          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'callback',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };
  const renderDeliveredFields = channelIdDes => {
    if (+channelIdDes === CHANNEL_FILE_TRANSFER) {
      const { value = '' } = getObjectPropSafely(
        () => dataConfig.deliveryInterval.mapOptions,
        {},
      );
      const valueTemp = getObjectPropSafely(
        () => dataConfig.deliveryInterval.value,
        1,
      );
      const label = mapLabelDeliveryInterval[value] || '';
      const mapValueMinute =
        MINUTE_OPTIONS.find(minuteOption => minuteOption.value === valueTemp) ||
        MINUTE_OPTIONS[0];

      return (
        <>
          <Grid item xs={3} style={{ display: 'flex', alignItems: 'center' }}>
            <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
          </Grid>
          <Grid item xs={9}>
            <WrapperDisable disabled={dataConfig.deliveryInterval.disabled}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <WrapperFixWidth width="250px">
                  <UISelect
                    onlyParent
                    use="tree"
                    isSearchable={false}
                    options={dataConfig.deliveryInterval.options}
                    value={dataConfig.deliveryInterval.mapOptions}
                    onChange={onChangeData('deliveryInterval')}
                    placeholder={getTranslateMessage(
                      TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                      'Select an item',
                    )}
                    fullWidthPopover
                    disabled={false}
                  />
                </WrapperFixWidth>
                {value !== TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED && (
                  <>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._EVERY, 'every')}
                    </LabelText>
                    <WrapperFixWidth width="100px">
                      {value === TYPE_DELIVERY_INTERVAL.MINUTE ? (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          options={MINUTE_OPTIONS}
                          value={mapValueMinute}
                          onChange={onChangeData('deliveryInterval')}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                          disabled={false}
                        />
                      ) : (
                        <UINumberStyled
                          name="times"
                          onChange={onChangeData('deliveryInterval')}
                          value={valueTemp}
                          min={1}
                          // max={100}
                          defaultValue={1}
                          width={100}
                        />
                      )}
                    </WrapperFixWidth>
                    <LabelText>{label}</LabelText>
                  </>
                )}
              </div>
            </WrapperDisable>
          </Grid>
        </>
      );
    }

    return (
      <>
        {canSendBroadcast && !isForceHideBroadCast && (
          <>
            <Grid
              item
              xs={3}
              style={{
                display: 'flex',
                alignItems: 'flex-start',
                marginTop: -12,
                marginBottom: 15,
              }}
            >
              <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
            </Grid>
            <Grid item xs={9} style={{ marginTop: -20, marginBottom: 15 }}>
              <WrapperDisable disabled={dataConfig.deliveryBroadcast.disabled}>
                <StyleCheckboxWrapper>
                  <RadioGroup
                    className="p-left-3"
                    aria-label="deliveryIntervalSettings"
                    name="deliveryInterval_1"
                  >
                    <FormControlLabel
                      value="onDeliveryIntervalSettings"
                      control={
                        <CheckBoxCustom
                          checked={dataConfig.deliveryBroadcast.isBroadcast}
                          // checked={
                          //   !_isEmpty(intervalBroadcastSetting) && isSendBroadcast
                          //     ? isSendBroadcast
                          //     : dataConfig.deliveryBroadcast.isBroadcast
                          // }
                          name="onCheckingBroadCast"
                          size="small"
                          // disabled={
                          //   !_isEmpty(intervalBroadcastSetting) && isSendBroadcast
                          //     ? isSendBroadcast
                          //     : dataConfig.deliveryBroadcast.disabled
                          // }
                          disabled={dataConfig.deliveryBroadcast.disabled}
                          onChange={onChangeData('deliveryBroadcast')}
                          label={getTranslateMessage(
                            TRANSLATE_KEY._,
                            'Enable Broadcast',
                          )}
                        />
                      }
                    />
                  </RadioGroup>
                </StyleCheckboxWrapper>
                {((!_isEmpty(intervalBroadcastSetting) && isSendBroadcast) ||
                  dataConfig.deliveryBroadcast.isBroadcast) && (
                  <div
                    style={{ display: 'flex', alignItems: 'center', gap: 12 }}
                  >
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'From')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        onChange={value => {
                          setIsValidBroadCast(
                            getObjectPropSafely(
                              () => value <= dataConfig.deliveryBroadcast.to,
                            ),
                          );

                          onChangeData('deliveryBroadcast')({
                            from: value,
                            isFrom: true,
                          });
                        }}
                        value={dataConfig.deliveryBroadcast.from}
                        min={
                          !_isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 0
                        }
                        max={
                          !_isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.from}
                        defaultEmptyValue={
                          !_isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 1
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'to')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        onChange={value => {
                          setIsValidBroadCast(
                            getObjectPropSafely(
                              () => value >= dataConfig.deliveryBroadcast.from,
                            ),
                          );

                          onChangeData('deliveryBroadcast')({
                            to: value,
                            isFrom: false,
                          });
                        }}
                        value={dataConfig.deliveryBroadcast.to}
                        min={Math.max(
                          !_isEmpty(intervalBroadcastSetting) &&
                            typeof fromNumber === 'number'
                            ? fromNumber
                            : 1,
                          dataConfig.deliveryBroadcast.from,
                        )}
                        max={
                          !_isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.to}
                        defaultEmptyValue={
                          !_isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : 1000
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(
                        TRANSLATE_KEY._,
                        'person(s) per once',
                      )}
                    </LabelText>
                  </div>
                )}
                {!isValidBroadCast && dataConfig.deliveryBroadcast.isBroadcast && (
                  <div
                    style={{
                      marginTop: 6,
                      fontSize: '11px',
                      color: '#f44336',
                    }}
                  >
                    From must be less than or equal to To
                  </div>
                )}
              </WrapperDisable>
            </Grid>
          </>
        )}
        <Grid item xs={3}>
          <Title>{MAP_TITLE.titlDeliveredRate}</Title>
        </Grid>
        <Grid item xs={9}>
          <WrapperDisable disabled={dataConfig.deliveryRate.disabled}>
            <RadioGroup
              aria-label="segmentMember"
              name="segmentMember"
              defaultValue={dataConfig.deliveryRate.type}
              value={dataConfig.deliveryRate.type}
              onChange={onChangeData('deliveryRate')}
              style={{ gap: '5px', marginLeft: '10px' }}
            >
              <FormControlLabel
                value="normal"
                control={
                  <Radio
                    color="primary"
                    style={{ width: '20px', height: '20px' }}
                  />
                }
                label={
                  <WrapperLabelOption style={{ marginLeft: '4px' }}>
                    <WrapperDisable>{MAP_TITLE.possible}</WrapperDisable>
                  </WrapperLabelOption>
                }
                className={classes.customSizeInput}
              />
              <div className="d-flex align-items-center">
                <FormControlLabel
                  value="limit"
                  control={
                    <Radio
                      color="primary"
                      style={{ width: '20px', height: '20px' }}
                    />
                  }
                  label={
                    <WrapperLabelOption style={{ marginLeft: '4px' }}>
                      {/* {MAP_TRANSLATE.labelLimit} */}
                      {MAP_TITLE.limitSentRate}
                    </WrapperLabelOption>
                  }
                  className={classes.customSizeInput}
                />
                <WrapperFixWidth>
                  <UINumberStyled
                    name="times"
                    onChange={onChangeData('deliveryRate')}
                    value={dataConfig.deliveryRate.limit}
                    min={1}
                    // max={100}
                    defaultValue={1}
                    width={60}
                  />
                </WrapperFixWidth>

                <WrapperTitleItem style={{ marginLeft: '16px' }}>
                  {MAP_TITLE.timePerson}
                </WrapperTitleItem>
              </div>
              {isValidate && (
                <div style={{ color: '#EF3340', fontSize: '12px' }}>
                  Please input limit send rate values
                </div>
              )}
            </RadioGroup>
          </WrapperDisable>
        </Grid>
      </>
    );
  };

  const renderContentSettings = () => (
    <StyleContentLoading>
      <Loading isLoading={isLoading} />
      {/* <Box className="row width-100 m-x-0 m-bottom-1"> */}
      {design === 'update' && (
        <DivMessageComputing bgColor={colors.selectedYellow}>
          <WarningIcon className="m-x-2" />
          {MAP_TITLE.notiAlertEdit}
        </DivMessageComputing>
      )}
      <Grid container>
        <Grid item xs={2} className="p-all-4 p-top-8">
          <BlockTitle> {MAP_TITLE.titlGeneralInfomation}</BlockTitle>
        </Grid>
        <Grid style={{ marginTop: '5px' }} item xs={8} className="p-all-4">
          {dataConfig.infoFields.map(
            each =>
              each !== 'destinationCatalog' && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={onChangeData}
                  key={each}
                  classes={classes}
                  isOwner={props.isOwner}
                  isHasPermissionEdit={props.isHasPermissionEdit}
                />
              ),
          )}
          {dataConfig.extraInfoFields.map(each => {
            if (each === 'method' && isHideMethod) return null;

            return (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                classes={classes}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            );
          })}
          {dataConfig.destinationCatalog.options.length === 0 && (
            <FormHelperText error>{MAP_TITLE.notiNotCreateDest}</FormHelperText>
          )}
        </Grid>
      </Grid>
      {/* </Box> */}
      {dataConfig.configFields.length > 0 && (
        // <Box className="row width-100 m-x-0 m-y-1">
        <Grid container>
          <Grid item xs={2} className="p-all-4">
            <BlockTitle> {MAP_TITLE.titlConfigFields}</BlockTitle>
          </Grid>
          <Grid item xs={8} className="p-all-4">
            {/* <Loading isLoading={isLoadingConfigFields} /> */}
            {dataConfig.configFields.map(each => (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                classes={classes}
                channelId={channelId}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            ))}
          </Grid>
        </Grid>
        // </Box>
      )}
      {/* <Box className="row width-100 m-x-0" boxShadow={false}> */}
      <Grid container>
        <Grid item xs={2} className="p-all-4">
          <BlockTitle> {MAP_TITLE.titlGeneralSetting}</BlockTitle>
        </Grid>
        <Grid
          style={{ marginTop: '5px' }}
          container
          item
          xs={8}
          className="p-all-4"
        >
          <Grid item xs={3}>
            <Title>{MAP_TITLE.titlFrequencyCapping}</Title>
          </Grid>
          <Grid item xs={9}>
            <UIFrequencyCapping
              isNoPadding
              channelId={channelId}
              isShowLabel={false}
              onChange={onChangeData('frequencyCapping')}
              initData={onMemoInitdata}
              componentId={onMemoComponenId}
              disabled={dataConfig.frequencyCapping.disabled}
            />
          </Grid>
          {renderDeliveredFields(channelId)}
        </Grid>
        {/* <Grid container item xs={8} className="p-all-4"></Grid> */}
      </Grid>
      {/* </Box> */}
    </StyleContentLoading>
  );
  const renderUISteps = step =>
    ({
      [STEP_NAME.CATALOG]: <CreateJourney moduleConfig={MODULE_CONFIG} />,
      [STEP_NAME.GENERAL]: isNewsUICatalog ? (
        <CreateDestination isUIStep={isNewsUICatalog} />
      ) : (
        renderContentSettings()
      ),
    }[step]);
  const renderContentSettingStep = () => (
    <>
      <WrapperInner style={{ marginBottom: '0.5rem', padding: 0 }}>
        <StepWrapper>
          <StyledStepper
            nonLinear
            activeStep={activeStep}
            ref={stepRef}
            className={`${visitedSteps.join(' ')}`}
          >
            {STEPS.map((label, index) => (
              <Step key={label} className={index === 0 ? 'p-left-0' : ''}>
                <StyledStepButton
                  className={`${visitedSteps[index] || ''} ${
                    activeStep === index ? 'active' : ''
                  }`}
                  // onClick={() => handleStep(index)}
                  disableRipple
                  disableTouchRipple
                >
                  {label}
                </StyledStepButton>
              </Step>
            ))}
          </StyledStepper>
        </StepWrapper>
      </WrapperInner>
      <WrapperInner
        style={{
          position: 'relative',
          overflowY: 'auto',
          padding: activeStep === STEP_NAME.GENERAL ? '16px 16px 6px' : '0px',
          height: `calc(100vh - ${design !== 'create' ? '245px' : '255px'})`,
        }}
      >
        {renderUISteps(activeStep)}
      </WrapperInner>
    </>
  );
  return isNewsUICatalogSTep ? (
    <CreateDestination isUIStep={isNewsUICatalogSTep} />
  ) : (
    <div
      id="setting-container"
      style={{ height: isShow ? '90%' : '100%' }}
      className="style-container jc-center"
    >
      <WrapperContent className="pos-relative">
        <StyleContent className="p-left-3" style={{ paddingRight: 6 }}>
          {design === 'create' ? (
            <LayoutContent style={{ height: '100%', padding: 0 }}>
              <LayoutLoading isLoading={false}>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <StyledUITabs
                    activeTab={activeTab}
                    onChange={value => setActiveTab(value)}
                    className="tab-box-shadow"
                  >
                    <TabPanel marginTop label="Settings" eventKey="settings">
                      {renderContentSettingStep()}
                    </TabPanel>
                    <TabPanel
                      marginTop
                      label="Share Access"
                      eventKey="share-access"
                    >
                      <StyledTabShareAccess>
                        <Grid container>
                          <Grid item xs={2}>
                            <span style={{ color: '#000', fontSize: '12px' }}>
                              Share Destination
                            </span>
                          </Grid>
                          <Grid item xs={8}>
                            <ShareAccess
                              accessInfo={props.accessInfo}
                              callback={props.callback}
                            />
                          </Grid>
                        </Grid>
                      </StyledTabShareAccess>
                    </TabPanel>
                  </StyledUITabs>
                </div>
              </LayoutLoading>
            </LayoutContent>
          ) : (
            <WrapperInner
              style={{
                position: 'relative',
                overflowY: 'auto',
                height: `calc(100vh - 200px)`,
              }}
            >
              {renderContentSettings()}
            </WrapperInner>
          )}
          <StyleFooter style={styleFooter}>
            <Footer
              isChooseCatalog={Object.keys(catalogActive).length > 0}
              isValidBroadCast={isValidBroadCast}
              main={main}
              dataConfig={dataConfig}
              callback={callback}
              step={activeStep}
              isHasPermissionEdit={props.isHasPermissionEdit}
              mainNewUI={props.mainAntsomiEmail}
              isNewsUICatalog={isNewsUICatalog}
            />
          </StyleFooter>
        </StyleContent>
        {/* </WrapperAutoSize> */}
      </WrapperContent>
    </div>
  );
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateDesDataConfig(),
  main: makeSelectCreateDesMain(),
  mainCatalog: makeSelectCreateDesMainCatalog(),
  isHasPermissionEdit: makeSelectIsHasPermissionDestDetail(),
  isOwner: makeSelectIsOwnerDestDetail(),
  mainAntsomiEmail: makeSelectCreateDesMainAntsomiEmail(),
});

function mapDispatchToProps(dispatch, props) {
  const { moduleConfig } = props;
  return {
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    onChangeDataConfigForce: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG_FORCE@@`, value)),
    onChangeDesCataLog: value =>
      dispatch(getListDone(`${MODULE_CONFIG.key}@@DEST_CATALOG@@`, value)),
    onChangeInitCataLog: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@INIT_CATALOG@@`, value)),
    onChangeExtraEmailBtn: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_EXTRA_EMAIL_BTN@@`, data),
      );
    },
    goToCreateV2: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_CREATE_V2`, data)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Content);

/* eslint-disable no-param-reassign */
/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { useImmer } from 'use-immer';
import { withRouter } from 'react-router-dom';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import styled from 'styled-components';
import PropTypes from 'prop-types';
// import LogoImage from 'components/common/Logo/Login';
import './style.scss';

import { UIButton } from '@xlab-team/ui-components';

import reducer from './reducer';
import saga from './saga';
import { loginRequest, logout } from './actions';
import { safeParse } from '../../utils/web/utils';
import Input from './_UI/Input2';
import { updateRedirectUrl, validation, getUserNameError } from './utils';
import Footer from './_UI/Footer';
import { PrivateFormSet } from './styled';

const Img = styled.img`
  position: absolute;
  left: calc(50% - 75px);
  z-index: -1;
  top: 3%;
  height: 150px;
  width: auto;
`;

function Login(props) {
  const [state, setState] = useImmer({
    username: '',
    password: '',
    disabled: true,
    errors: {
      email: [],
    },
  });

  useEffect(() => {
    props.logout();
    const stateParam = safeParse(props.location.state, null);
    updateRedirectUrl(stateParam);
    setState(draft => {
      draft.disabled = !validation(state.username, state.password);
    });
  }, []);

  const handleChange = (name, value) => {
    setState(draft => {
      draft[name] = value;
      draft.errors.email = [];
      if (name === 'username') {
        // const errorEmail = getUserNameError(value);
        draft.disabled = !validation(value, state.password);
      } else {
        // case password
        draft.disabled = !validation(state.username, value);
      }
    });
  };

  const handleSubmit = () => {
    // this.setState({ submitted: true });
    const { username, password } = state;
    const { location } = props;
    const errorEmail = getUserNameError(username);
    setState(draft => {
      draft.errors.email = errorEmail;
    });
    if (errorEmail.length > 0) {
      return;
    }
    if (username && password) {
      const redirectTo = location.search
        ? location.search.replace('?redirect=', '') + location.hash
        : '/';
      props.sendLoginRequest(
        username,
        password,
        window.decodeURIComponent(redirectTo),
      );
    }
  };

  const { loggingIn, error } = props;
  const { username, password } = state;

  if (PORTAL_CONFIG.URL_LOGOUT) {
    return null;
  }

  return (
    <React.Fragment>
      <div className="page">
        <div className="private-application" name="LoginUI">
          <Img src={PORTAL_CONFIG.LOGO_LOGIN} alt="" />

          <div className="auth-box marketing-box">
            <form>
              <div className="signup-link">
                {/* <i18n-string data-locale-at-render="en" data-key="login.signupLink.text">Don't have an account?</i18n-string>
                  <a href="/signup" className="m-left-1">
                    <i18n-string data-locale-at-render="en" data-key="login.signupLink.cta">Sign up</i18n-string>
                  </a> */}
              </div>
              <div>
                <PrivateFormSet className="login-form private-form__set m-bottom-4">
                  <Input
                    // className="login-form"
                    label="Email address"
                    type="string"
                    name="username"
                    value={username}
                    onChange={handleChange}
                    errors={state.errors.email}
                    waitInterval={0}
                  />
                </PrivateFormSet>
              </div>
              <div className="password-input m-bottom-8">
                <PrivateFormSet className="login-form private-form__set">
                  <Input
                    // className="login-form"
                    label="Password"
                    type="password"
                    name="password"
                    value={password}
                    onChange={handleChange}
                    waitInterval={0}
                    onSubmit={handleSubmit}
                  />
                </PrivateFormSet>
              </div>
              {error && (
                <div
                  aria-atomic="true"
                  aria-live="assertive"
                  className="alert private-alert private-alert--page-wide alert-danger private-alert--danger m-bottom-4 m-top-4 text-left"
                  role="alert"
                >
                  <div className="private-alert__inner">
                    <h2 className="private-heading-5 private-alert__title">
                      {"This doesn't look right."}
                    </h2>
                    <div className="private-alert__body has--vertical-spacing">
                      {error}
                    </div>
                  </div>
                </div>
              )}
              <UIButton
                type="button"
                use="primary"
                className="m-bottom-4 login-submit"
                disabled={state.disabled}
                isLoading={loggingIn}
                // isLoading={true}
                // isLoading={true}
                onClick={handleSubmit}
              >
                Log in
              </UIButton>
            </form>
          </div>
          <Footer />
        </div>
      </div>
    </React.Fragment>
  );
}

function mapStateToProps(state) {
  const { user, loggingIn, error, loggedIn } = state.get('login');

  return {
    user,
    loggingIn,
    error,
    loggedIn,
  };
}

function mapDispatchToProps(dispatch) {
  return {
    // sendLoginRequest: (username, password) => {
    //   dispatch(loginRequest(username, password));
    // },
    sendLoginRequest: (username, password, from) => {
      dispatch(loginRequest(username, password, from));
    },
    logout: () => {
      dispatch(logout());
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);
const withReducer = injectReducer({ key: 'login', reducer });
const withSaga = injectSaga({ key: 'login', saga });

Login.propTypes = {
  logout: PropTypes.any,
  sendLoginRequest: PropTypes.any,
  loggingIn: PropTypes.any,
  error: PropTypes.any,
};
export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(React.memo(Login));

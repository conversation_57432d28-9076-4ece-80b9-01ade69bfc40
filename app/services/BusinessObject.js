/* eslint-disable arrow-body-style */
/* eslint-disable indent */
/* eslint-disable prefer-destructuring */
import axios from 'axios';
import { ENDPOINT, URL_HISTORY, URL_API } from '../../config/common';
import { encodeURL } from '../utils/common';
import {
  callApiUploadHugeFile,
  callApiWithAuth,
  callApiWithAuthAndCancel,
  callApiWithAuthV2,
  callApiWithDownload,
} from '../utils/request';
import {
  getEntryV2,
  getEntriesWithTotalV2,
  getEntriesV2,
  getEntriesWithTotalV1History,
  getData,
} from './utils';
import { isEmpty, uniq } from 'lodash';

const API = {
  diagram: {
    getBO: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/diagram/bo${params.insight_property_id === 'all'
            ? ``
            : `?insight_property_id=${params.insight_property_id}`
        }`,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV1History(res, []);
      });
    },
    getEvent: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/diagram/event${params.insight_property_id === 'all'
            ? ``
            : `?insight_property_id=${params.insight_property_id}`
        }`,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV1History(res, []);
      });
    },
    getPosition: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/diagram/position`,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV1History(res, []);
      });
    },
    updatePosition: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/diagram/position`,
        method: 'POST',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV1History(res, []);
      });
    },
  },
  computaitonHistories: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.computationV2}/computation-histories/listing`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListByIds: params => {
      return callApiWithAuth(
        `${ENDPOINT.computationV2}/computation-histories/get-by-ids`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  businessObject: {
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/create`,
        'POST',
        params,
        {
          _owner_id: params._owner_id,
        },
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    getEventTracking: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/item-types`,
        'GET',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/listing`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListRelated: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/listing-relationship`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    changeStatus: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/update-status`,
        'POST',
        params.data,
        {
          _owner_id: params.data.ownerId,
        },
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    updateListStatus: params => {
      const url = `${ENDPOINT.itemsV2_1}/objects/update-status`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params.data.ownerId,
      }).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getDetailInfo: params => {
      const url = `${ENDPOINT.itemsV2}/objects/${params.itemTypeId}`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntryV2(res, {});
      });
    },
    getColumns: params => {
      const url = `${ENDPOINT.items}/attributes/group/${params.objectId}`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getLastColumns: params => {
      const url = `${ENDPOINT.toolkit}/columns/last/${params.objectType}/${params.objectId
      }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    update: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.itemsV2}/objects/update/${params.objectTypeId}`;
      return callApiWithAuth(url, 'PUT', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        return getEntriesV2(res, []);
      });
    },
    archiveAllBO: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2_1}/objects/${params.objectId}/relationships`,
        'GET',
        null,
      ).then(res => getEntriesV2(res, {}));
    },
    dowloadBO: params => {
      return callApiWithDownload(
        `${ENDPOINT.itemsV2}/objects/download/`,
        'POST',
        params.data,
        params.config,
        {
          _owner_id: params._owner_id,
        },
      );
    },
    uploadFileBO: params => {
      return callApiWithAuth(
        `${ENDPOINT.explorerV2_2}/upload/meta?originalName=${encodeURL(
          params.originalName,
        )}`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    confirmUploadBO: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/update-by-file`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    checkPermission: params => {
      const url = `${ENDPOINT.toolkitV2}/operate/check-permission`;
      return callApiWithAuth(url, 'POST', params.body).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getByBOCodes: async params => {
      const { itemTypeNames } = params;

      const endpoint = `${ENDPOINT.businessObjectV1}/get-by-codes`;

      const res = await callApiWithAuthV2({
        method: 'POST',
        endpoint,
        domain: URL_HISTORY,
        body: {
          itemTypeNames: uniq(itemTypeNames),
        },
      });

      return getData(res, []);
    },
    updateName: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/business-object/rename/${params.objectId}`,
        method: 'PUT',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, 0);
      });
    },
  },
  data: {
    getListAll: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/list-by-group`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListByType: (params, others = {}) => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/objects/listing`,
        'POST',
        params,
        others,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },

    getListByIds: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/get-info`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    // getDetail: params => {
    //   return callApiWithAuth(
    //     `${ENDPOINT.items}/items/${params.itemTypeId}`,
    //     'GET',
    //     null,
    //   ).then(res => {
    //     return getEntryV2(res, {});
    //   });
    // },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.settingV2}/`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
  },
  columns: {
    getListGroupAttrs: objectType => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsWithQueryParams: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType
        }?excludeProps=${params.excludeProps}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsDataTable: (params, others = {}) => {
      return callApiWithAuth(
        `${ENDPOINT.items}/attributes/group/${params.itemTypeId}`,
        'GET',
        null,
        others,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  groups: {
    getListAll: params => {
      return callApiWithAuth(
        `${ENDPOINT.items}/groups/${params.itemTypeId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListDelete: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/except-group-ids/${params.groupId
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/listing`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getDetail: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/${params.groupId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/create`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    update: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/${params.groupId}`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },

    delete: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/groups/${params.itemTypeId}/${params.fromGroup}/${params.toGroup
        }`,
        'DELETE',
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    getListSelector: params => {
      const url = `${ENDPOINT.items}/groups/${params.itemTypeId}`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  dataTable: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.explorerV2}/search/${params.itemTypeId}?type=${params.type
        }`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    download: params => {
      return callApiWithDownload(
        `${ENDPOINT.explorerV2}/download/${params.itemTypeId}`,
        'POST',
        params.data,
        params.config,
      );
    },
    export: params => {
      return callApiWithAuth(
        `${ENDPOINT.exportManagementV2}/export/`,
        'POST',
        params.data,
      );
    },
    upload: params => {
      return callApiWithAuth(
        `${ENDPOINT.explorerV2_1}/upload/${params.itemTypeId}`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    uploadHugeFile: params => {
      return callApiUploadHugeFile(
        `${ENDPOINT.explorerV2_2}/upload/${params.itemTypeId
        }?originalName=${encodeURL(params.originalName)}`,
        'POST',
        params.data,
      );
      // .then(res => {
      // return getEntriesWithTotalV2(res, []);
      // });
    },
    confirmUpload: params => {
      return callApiWithAuth(
        `${ENDPOINT.explorerV2_1}/upload/${params.itemTypeId}/confirm/${params.importId
        }`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    remove: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.explorerV2_1}/delete/${params.itemTypeId}`;
      return callApiWithAuth(url, 'PUT', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  BOAttribute: {
    checkLimitationUpdateMulti: async ({ attributes }) => {
      const res = await callApiWithAuthV2({
        endpoint: `${ENDPOINT.itemsV2}/attributes/check-update-limitation`,
        method: 'POST',
        body: {
          attributes,
        },
      });

      return getEntriesV2(res);
    },
    checkLimitation: async ({ isComputedAttr }) => {
      const res = await callApiWithAuthV2({
        endpoint: `${ENDPOINT.itemsV2}/attributes/check-create-limitation`,
        method: 'POST',
        body: {
          type: isComputedAttr ? 'computed' : 'custom',
        },
      });

      return getEntriesV2(res);
    },
    sourcePlans: undefined,
    getPlans: params => {
      if (API.BOAttribute.sourcePlans !== undefined) {
        API.BOAttribute.sourcePlans.cancel();
      }
      API.BOAttribute.sourcePlans = axios.CancelToken.source();
      return callApiWithAuthAndCancel(
        API.BOAttribute.sourcePlans,
        `${ENDPOINT.itemsV2}/attributes/plans`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getByBOIds: async params => {
      const {
        itemTypeIds = [],
        isOnlyGetId = false,
        types = [],
        defaultValue = {},
      } = params;

      if (isEmpty(itemTypeIds)) return defaultValue;

      const res = await callApiWithAuthV2({
        domain: URL_HISTORY,
        endpoint: `${ENDPOINT.apiV1}/item-property/get-by-item-ids`,
        method: 'POST',
        body: {
          itemTypeIds,
          isOnlyGetId,
          types,
        },
      });

      return getData(res, defaultValue);
    },
    getListRelationship: async ({
      page,
      limit,
      search,
      sort = 'last_updated_date',
      sd = 'desc',
      columns,
      attrType,
    }) => {
      const andCondition = [];
      andCondition.push(
        {
          type: 1,
          column: 'item_property_display',
          data_type: 'string',
          operator: 'contains',
          value: search,
        },
        {
          column: 'status',
          data_type: 'number',
          operator: 'matches',
          type: 1,
          value: [2, 1],
        },
        attrType === 'computed'
          ? {
              type: 1,
              column: 'type',
              data_type: 'string',
              operator: 'matches',
              value: [
                '3_event_counter',
                '3_aggregation',
                '3_most_frequent',
                '3_first',
                '3_last',
                '3_unique_list',
                '3_unique_list_count',
                '3_custom_function',
                '3_scheduled_custom_function',
                '3_conversion',
              ],
            }
          : {
              type: 1,
              column: 'type',
              data_type: 'string',
              operator: 'matches',
              value: ['2'],
            },
      );

      const filters = {
        OR: [{ AND: andCondition }],
      };

      const res = await callApiWithAuthV2({
        endpoint: `${ENDPOINT.itemsV2}/attributes/listing-relationship`,
        method: 'POST',
        body: {
          page,
          limit,
          sort,
          sd,
          columns,
          filters,
        },
      });

      return getEntriesWithTotalV2(res);
    },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/create`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    update: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/update`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    getDetail: params => {
      const url = `${ENDPOINT.itemsV2}/attributes/${params.itemTypeId}/${params.itemPropertyName
      }/detail`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntryV2(res, {});
      });
    },
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/listing`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    sourcePersonalizeAttrs: undefined,
    getPersonalizeAttributes: params => {
      if (API.BOAttribute.sourcePersonalizeAttrs !== undefined) {
        API.BOAttribute.sourcePersonalizeAttrs.cancel();
      }

      API.BOAttribute.sourcePersonalizeAttrs = axios.CancelToken.source();

      const url = `${ENDPOINT.toolkitV2}/groups/personalize-attributes`;

      return callApiWithAuthAndCancel(
        API.BOAttribute.sourcePersonalizeAttrs,
        url,
        'POST',
        params.data,
      ).then(res => {
        if (res === null) {
          return null;
        }
        return getEntriesWithTotalV2(res, []);
      });
    },
    updateStatus: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/update-status`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    assign: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/assign`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    archiveBOAttr: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2_1}/attributes/${params.itemTypeId}/${params.attribute
        }/relationships?itemTypeIds=-1007,-1003`,
        'GET',
        null,
      ).then(res => getEntriesV2(res, {}));
    },
    updateListStatus: params => {
      const url = `${ENDPOINT.itemsV2_1}/attributes/update-status`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    updateListStatusWithManyItemTypeId: params => {
      const {
        isSelectedAll,
        attributes,
        search,
        isBuild,
        status,
      } = params.data;

      const postData = {
        isBuild,
        status,
        isSelectedAll,
      };

      if (isSelectedAll) {
        postData.filters = {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'type',
                  data_type: 'string',
                  operator: 'matches',
                  value: [
                    '3_event_counter',
                    '3_aggregation',
                    '3_most_frequent',
                    '3_first',
                    '3_last',
                    '3_unique_list',
                    '3_unique_list_count',
                    '3_custom_function',
                    '3_scheduled_custom_function',
                    '3_conversion',
                  ],
                },
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'not_matches',
                  value: [4, 0, 3],
                },
                {
                  type: 1,
                  column: 'type',
                  data_type: 'string',
                  operator: 'not_matches',
                  value: ['1'],
                },
                {
                  type: 1,
                  column: 'item_property_display',
                  data_type: 'string',
                  operator: 'contains',
                  value: search,
                },
              ],
            },
          ],
        };
      } else {
        postData.attributes = attributes;
      }

      return callApiWithAuthV2({
        body: postData,
        endpoint: `${ENDPOINT.itemsV2_1}/attributes/update-many-status`,
        method: 'POST',
      }).then(res => {
        return getEntriesWithTotalV2(res);
      });
    },
    rebuildBoAttribute: params => {
      const url = `${ENDPOINT.itemsV2}/attributes/rebuild`;
      return callApiWithAuth(url, 'PUT', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    dowloadBOAttribute: params => {
      return callApiWithDownload(
        `${ENDPOINT.itemsV2}/attributes/download/`,
        'POST',
        params.data,
        params.config,
      );
    },
    uploadFileBOAttribute: params => {
      return callApiWithAuth(
        `${ENDPOINT.explorerV2_2}/upload/meta/${params.itemTypeId
        }?originalName=${encodeURL(params.originalName)}`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    confirmUploadBOAttribute: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/update-by-file`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    // dowloadBOAttribute: params => {
    //   const url = `${ENDPOINT.itemsV2}/attributes/download`;
    //   return callApiWithAuth(url, 'POST', params.data).then(res => {
    //     return getEntriesV2(res, []);
    //   });
    // },
    updateName: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/item-property/rename/${params.itemPropertyName}`,
        method: 'PUT',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, 0);
      });
    },
  },
  BOCollection: {
    data: {
      getList: params => {
        return callApiWithAuth(
          `${ENDPOINT.itemsV2}/collections/listing`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getDetail: params => {
        const url = `${ENDPOINT.itemsV2}/collections/${params.objectId}`;
        return callApiWithAuth(url, 'GET', null).then(res =>
          getEntryV2(res, {}),
        );
      },
      updateStatus: params => {
        return callApiWithAuth(
          `${ENDPOINT.itemsV2}/collections/update-status`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      updateListStatus: params => {
        const url = `${ENDPOINT.itemsV2_1}/collections/update-status`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
      updateListStatusV2: params => {
        const url = `${ENDPOINT.itemsV2_1}/collections/update-many-status`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
      checkCreateLimitation: params => {
        const url = `${ENDPOINT.itemsV2}/collections/check-create-limitation`;

        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, {});
        });
      },
      checkUpateStatusLimitation: params => {
        const url = `${ENDPOINT.itemsV2}/collections/check-update-limitation`;

        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, {});
        });
      },
      getListRelationship: params => {
        const url = `${ENDPOINT.itemsV2}/collections/listing-relationship`;

        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getListVersion: params => {
        return callApiWithAuthV2({
          endpoint: `api/v1/item-management/collections/get-list-versions?collection_id=${params.segmentId
          }`,
          method: 'POST',
          domain: URL_HISTORY,
        }).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getDetailLastVersion: params => {
        return callApiWithAuthV2({
          endpoint: `api/v1/item-management/collections/${params.segmentId}`,
          method: 'GET',
          domain: URL_HISTORY,
        }).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getDetailwithVersion: params => {
        return callApiWithAuthV2({
          endpoint: `api/v1/item-management/collections/${params.segmentId}/${params.versionId
          }`,
          method: 'GET',
          domain: URL_HISTORY,
        }).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      sourcePlans: undefined,
      getPlans: params => {
        if (API.BOCollection.data.sourcePlans !== undefined) {
          API.BOCollection.data.sourcePlans.cancel();
        }

        API.BOCollection.data.sourcePlans = axios.CancelToken.source();

        return callApiWithAuthAndCancel(
          API.BOCollection.data.sourcePlans,
          `${ENDPOINT.itemsV2}/collections/plans`,
          'POST',
          params.data,
        ).then(res => {
          if (res === null) {
            return null;
          }
          return getEntriesWithTotalV2(res, []);
        });
      },
    },

    columns: {
      getListGroupAttrs: () => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/info-properties/BO_COLLECTION`,
          'GET',
          null,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
    },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/collections/create`,
        'POST',
        params,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    checkDuplicate: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.itemsV2}/collections/check-duplicate-name`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    update: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/collections/update/${params.objectId}`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    getByIds: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/collections/get-by-ids`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    preview: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/collections/preview`,
        'POST',
        params.dataPost,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
        // return getEntriesWithTotalV2(undefined, 0);
      });
    },
    updateName: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/item-management/collections/rename/${params.segmentId
        }`,
        method: 'PUT',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, 0);
      });
    },
  },
};

export default API;

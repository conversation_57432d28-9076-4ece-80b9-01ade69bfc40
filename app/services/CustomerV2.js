/* eslint-disable no-restricted-syntax */
/* eslint-disable arrow-body-style */
import { callApiWithAuth, callApiWithAuthAndCancel } from 'utils/request';
import { safeParse } from 'utils/common';
import axios from 'axios';

import { ENDPOINT } from '../../config/common';
import {
  getEntryV2,
  getEntriesV2,
  getEntriesWithTotalV2,
  getEntryAndMeta,
} from './utils';

const API = {
  sourceGetAll: undefined,
  chart: {
    getDetailChartForecast: params => {
      const url = `${
        ENDPOINT.automationV2
      }/stories/dashboard/forecast-audiences`;
      return callApiWithAuth(url, 'POST', params).then(res => {
        const data = getEntryAndMeta(res, []);
        return data;
      });
    },
    getForecastBlast: params => {
      const url = `${
        ENDPOINT.automationV2_1
      }/stories/dashboard/forecast-audiences`;
      return callApiWithAuth(url, 'POST', params).then(res => {
        const data = getEntryAndMeta(res, {});
        return data;
      });
    },
  },

  explorer: {
    getList: params => {
      const { type = 'personas', ...restParams } = params;
      const url = `${ENDPOINT.explorerV2}/search/-1003?type=${type}`;
      if (typeof API.sourceGetAll !== typeof undefined) {
        API.sourceGetAll.cancel();
      }
      API.sourceGetAll = axios.CancelToken.source();
      return callApiWithAuthAndCancel(
        API.sourceGetAll,
        url,
        'POST',
        restParams,
      ).then(res => {
        if (res === null) {
          return null;
        }
        return getEntriesWithTotalV2(res, []);
      });
    },
    // NOTE: This API call in customer list in "Schedule Trigger"
    getListNotCancel: params => {
      const url = `${ENDPOINT.explorerV2}/search/-1003`;
      return callApiWithAuth(url, 'POST', params).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    getListByItemTypeIdForecast: params => {
      const url = `${
        ENDPOINT.automationV2
      }/stories/dashboard/forecast-audiences`;
      return callApiWithAuth(url, 'POST', params).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return data;
      });
    },
    getById: params => {
      return callApiWithAuth(
        `${ENDPOINT.customers}/customer-v2/${
          params.id
        }/profile?includeAllValues=true&columns=${
          params.columns
        }&productAttrs=${params.productAttrs}&decryptFields=${
          params.decryptFields
        }`,
        'GET',
        null,
      ).then(res => {
        const customer = getEntryV2(res, {});
        return customer;
      });
    },
    // getListV2: params => {
    //   const url = `${ENDPOINT.explorerV2}/search/${params.itemTypeId}`;
    //   if (typeof API.sourceGetAll !== typeof undefined) {
    //     API.sourceGetAll.cancel();
    //   }
    //   API.sourceGetAll = axios.CancelToken.source();
    //   return callApiWithAuthAndCancel(
    //     API.sourceGetAll,
    //     url,
    //     'POST',
    //     params,
    //   ).then(res => {
    //     if (res === null) {
    //       return null;
    //     }
    //     return getEntriesWithTotalV2(res, []);
    //   });
    // },
    getListV2: params => {
      const url = `${ENDPOINT.explorerV2}/search/${params.itemTypeId}`;
      return callApiWithAuth(url, 'POST', params).then(res => {
        if (res === null) {
          return null;
        }
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  cards: {
    timeline: params => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/v2/${
          params.customerId
        }?isFilterV2=true`,
        'POST',
        {
          ...params,
          // eventTrackings: params.eventTrackings,
          // insightPropertyIds: params.insightPropertyIds,
          // notFirstLoad: params.notFirstLoad,
          // page: params.page,
          // limit: params.limit,
        },
      ).then(res => {
        // res.data.data = TIME_LINE;
        const data = {
          userProfiles: [],
          entries: [],
          contextEvents: {},
        };
        if (typeof res !== 'undefined') {
          const code = parseInt(safeParse(res.data.code, 0));
          if (code === 200) {
            const fullData = safeParse(res.data.data, {});
            const meta = safeParse(fullData.meta, {});
            data.entries = safeParse(fullData.entries, []);
            data.userProfiles = safeParse(fullData.userProfiles, []);
            data.contextEvents = safeParse(meta.contextEvents, {});
          }
        }
        return data;
      });
    },
    timelinePreview: params => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/v2/${params.customerId}`,
        'POST',
        {
          ...params,
        },
      ).then(res => {
        const data = {
          userProfiles: [],
          entries: [],
          contextEvents: {},
        };
        if (typeof res !== 'undefined') {
          const code = parseInt(safeParse(res.data.code, 0));
          if (code === 200) {
            const fullData = safeParse(res.data.data, {});
            const meta = safeParse(fullData.meta, {});
            data.entries = safeParse(fullData.entries, []);
            data.userProfiles = safeParse(fullData.userProfiles, []);
            data.contextEvents = safeParse(meta.contextEvents, {});
          }
        }
        return data;
      });
    },
    graph: params => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/${
          params.customerId
        }/graph-v2?userAttributeCodes=${
          params.userAttributeCodes
        }&productAttrs=${params.productAttrs}&decryptFields=${
          params.decryptFields
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    segments: params => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/${params.objectId}/segment`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getLists: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/events`,
        'GET',
        null,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
    performance: params => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/${params.objectId}/performance`,
        'POST',
        params.body,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
    getListEventPerformance: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/events-performance`,
        'GET',
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
  },

  profileCus360(body) {
    return callApiWithAuth(
      `${ENDPOINT.customers}/customer/profile-customer360`,
      'POST',
      body,
    ).then(res => {
      // if (typeof res !== 'undefined') {
      //   const data = safeParse(res.data, {});
      //   return data;
      // }
      const data = getEntryV2(res, {});
      return data;
    });
  },

  filters: {
    getAll: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/events/schema`,
        'GET',
        null,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
  },

  properties: {
    getListFavorites: param => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/columns/${param.objectType}/${param.objectId}`,
        'GET',
        null,
      ).then(res => {
        const data = getEntryV2(res, {});
        return data;
      });
    },
  },
};

export default API;

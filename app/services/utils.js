/* eslint-disable no-param-reassign */
/* eslint-disable no-undef */
/* eslint-disable prefer-destructuring */

import { MESSAGES } from './constants';
import { safeParse, setAppCookieSession } from '../utils/common';
import { setAppCookieSessionSubDomain } from '../utils/web/cookie';
import { addMessageToQueue } from '../utils/web/queue';
import APP from '../appConfig';

const COOKIE_EXPIRE = 2592000; // 60 * 60 * 24 * 30 // 30 days

function validateRedirect(code, message) {
  if (code === 402) {
    // case license expire
    window.location.href = `${window.location.origin}/license-expire`;
  } else if (
    code === 400 &&
    (message === 'Portal not found' || message === 'User not found')
  ) {
    // case license expire
    window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
  } else if (code === 401) {
    window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
  }
}

export function getMessageByCode(messageCode, def) {
  if (def === undefined) {
    def = 'Error happened. Please try again.';
  }
  const tempt = MESSAGES[messageCode];
  if (tempt === undefined) {
    return def;
  }
  return tempt;
}

export function getCode(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 0));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    return code;
  }
  return def;
}

/**
 *
 * @param {*} res
 * @param {*} def
 * {code, message, codeMessage}
 */
export function getCodeV2(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    if (code === 200) {
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
    };
  }
  return def;
}

export function getEntryV2(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    const codeMessage = safeParse(
      res.data.codeMessage,
      'INTERNAL_SERVER_ERROR',
    );

    let entry = def;
    // need get entry for code !== 200
    // if (code === 200) {
    const data = safeParse(res.data.data, {});
    const entries = safeParse(data.entries, []);
    if (entries.length > 0) {
      // eslint-disable-next-line prefer-destructuring
      entry = safeParse(entries[0], def);
      // codeMessage = 'SUCCESS';
    }
    // }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entry,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}

export function getEntryAndMeta(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    let meta = {};
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data.entries, []);
      meta = safeParse(data.meta, {});
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
      meta,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}

export function getEntriesV2(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data.entries, []);
      codeMessage = 'SUCCESS';
    } else {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data.entries, def);
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}
export function getDataPortal(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data, []);
      codeMessage = 'SUCCESS';
    } else {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data, def);
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}
export function getSourceCode(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let data = '';
    if (code === 200) {
      data = safeParse(res.data, '');
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}

export function getEntriesWithTotalV2(res, def = []) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    let totalRecord = 0;
    let totalFilter = 0;
    let perPage = 0;
    let meta = {};
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      totalRecord = !data.meta ? 0 : safeParse(data.meta.total, 0);
      meta = safeParse(data.meta, {});
      totalFilter = !data.meta ? 0 : safeParse(data.meta.totalFilter, 0);
      entries = safeParse(data.entries, []);
      perPage = !data.meta
        ? entries.length
        : safeParse(data.meta.perPage, entries.length);
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
      totalRecord,
      totalFilter,
      perPage,
      meta,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
    totalRecord: 0,
  };
}
export function getEntriesWithTotalV1History(res, def = []) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    let totalRecord = 0;
    let totalFilter = 0;
    let perPage = 0;
    let meta = {};
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      totalRecord = !data.meta ? 0 : safeParse(data.meta.total, 0);
      meta = safeParse(data.meta, {});
      totalFilter = !data.meta ? 0 : safeParse(data.meta.totalFilter, 0);
      entries = safeParse(data, []);
      perPage = !data.meta
        ? entries.length
        : safeParse(data.meta.perPage, entries.length);
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
      totalRecord,
      totalFilter,
      perPage,
      meta,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
    totalRecord: 0,
  };
}
export function getEntriesWithTotalV1(res, def = []) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    let totalRecord = 0;
    let totalFilter = 0;
    let perPage = 0;
    let meta = {};
    if (code === 200) {
      const data = safeParse(res.data.data.data, {});
      totalRecord = safeParse(data.meta.total, 0);
      meta = safeParse(data.meta, {});
      totalFilter = safeParse(data.meta.totalFilter, 0);
      entries = safeParse(data.entries, []);
      perPage = safeParse(data.meta.perPage, entries.length);
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
      totalRecord,
      totalFilter,
      perPage,
      meta,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
    totalRecord: 0,
  };
}
export function getEntriesWithTotalV3(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    let totalRecord = 0;
    let perPage = 0;
    let totalModified = 0;
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      totalRecord = safeParse(data.meta.totalUsers, 0);
      perPage = safeParse(data.meta.perPage, 0);
      totalModified = safeParse(data.meta.total, 0);
      entries = safeParse(data.entries, []);
      codeMessage = 'SUCCESS';
    }
    return {
      code,
      codeMessage,
      message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
      totalRecord,
      perPage,
      totalModified,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
    totalRecord: 0,
  };
}

export function getEntry(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      const entries = safeParse(data.entries, []);
      if (entries.length > 0) {
        // eslint-disable-next-line prefer-destructuring
        return entries[0];
      }
    }
  }
  return def;
}

export function getEntries(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    validateRedirect(code, message);
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      const entries = safeParse(data.entries, def);
      return entries;
    }
  }
  return def;
}

export function formatEncodedUrlByFacebook(url, isRemoveHTTP) {
  const regex = /^https?:\/\/l\.facebook\.com\/l\.php\?u=(http.*?)&.*$/;
  const match = regex.exec(url);
  if (match && match[1]) {
    let uri = match[1]
      .replace(/%3A/g, ':')
      .replace(/%2F/g, '/')
      .replace(/%26/g, '&')
      .replace(/%3D/g, '=')
      .replace(/%3F/g, '?');
    if (isRemoveHTTP) {
      uri = uri.replace(/^(https?:\/\/)?(www\.)?/gi, '');
      uri = uri.split('/')[0];
    }

    return uri;
  }
  return url;
}

export function setAppCookie(params) {
  try {
    // if (params.api_token !== undefined) {
    //   setAppCookieSession('api_token', params.api_token, COOKIE_EXPIRE, '');
    // }
    if (params.api_r_token !== undefined) {
      setAppCookieSession('api_r_token', params.api_r_token, COOKIE_EXPIRE, '');
    }
    // if (params.user_id !== undefined) {
    //   setAppCookieSession('user_id', params.user_id, COOKIE_EXPIRE, '');
    // }
    if (params.c_pid !== undefined) {
      setAppCookieSession('c_pid', params.c_pid, COOKIE_EXPIRE, '');
    }
    if (params.api_pid !== undefined) {
      setAppCookieSession('api_pid', params.api_pid, COOKIE_EXPIRE, '');
      try {
        setAppCookieSessionSubDomain(params.api_pid, COOKIE_EXPIRE, false);
        // setAppCookieSession('c_pid', params.c_pid, COOKIE_EXPIRE, '');
      } catch (e) {
        addMessageToQueue({
          path: 'app/services/utils.js',
          func: 'setAppCookieSessionSubDomain',
          data: err.stack,
        });

        console.log(e);
      }
    }

    if (params.currency) {
      setAppCookieSession('currency', params.currency, COOKIE_EXPIRE, '');
    }
  } catch (e) {
    addMessageToQueue({
      path: 'app/services/utils.js',
      func: 'setAppCookie',
      data: err.stack,
    });
    console.log(e);
  }
}

export const getSourceIdToParam = dataSource => {
  const validateDataSource = safeParse(dataSource, []);

  if (validateDataSource.length === 0) {
    return -1;
  }

  return validateDataSource.join(',');
};

export function getData(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');

    // if (code === 200) {
    const data = safeParse(res.data.data, def);
    return {
      code,
      message,
      data,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}

export const getDataInsight = (res, def) => {
  // console.log('res123123', res);

  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');

    // if (code === 200) {
    const data = safeParse(res.data.data, def);
    return {
      code,
      message,
      data,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
};

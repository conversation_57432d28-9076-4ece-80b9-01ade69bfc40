import { callApiMonitor } from '../utils/request';
import { getEntriesWithTotalV3 } from './utils';
import { ENDPOINT } from '../../config/common';

const API = {
  getTotal: params =>
    callApiMonitor(
      `${
        ENDPOINT.deliveryReportV2
      }/performance-total?campaignId=&storyId=&status=${
        params.status
      }&source=story&startTime=${params.startTime}&endTime=${
        params.endTime
      }&destinationId=${params.destinationId}&filters=${window.encodeURI(
        JSON.stringify(params.filter || []),
      )}`,
    ).then(res => getEntriesWithTotalV3(res)),
  getPerformanceList: params =>
    callApiMonitor(
      `${ENDPOINT.deliveryReportV2}/performance?campaignId=&storyId=&limit=${
        params.limit
      }&page=${params.page}&status=${params.status}&source=story&startTime=${
        params.startTime
      }&endTime=${params.endTime}&destinationId=${
        params.destinationId
      }&filters=${window.encodeURI(JSON.stringify(params.filter || []))}`,
    ).then(res => getEntriesWithTotalV3(res)),
  getChart: params =>
    callApiMonitor(
      `${
        ENDPOINT.deliveryReportV2
      }/performance-chart?campaignId=&storyId=&status=${
        params.status
      }&source=story&startTime=${params.startTime}&endTime=${
        params.endTime
      }&destinationId=${params.destinationId}&filters=${window.encodeURI(
        JSON.stringify(params.filter || []),
      )}`,
    ).then(res => getEntriesWithTotalV3(res)),
  getProcessInformation: params =>
    callApiMonitor(
      `${
        ENDPOINT.deliveryMarketingReportV2
      }/destination-histories/processId/${params.processId}?portal_id=${
        params.portalId
      }`,
    ).then(res => {
      return res.data
    }),
};

export default API;

/* eslint-disable indent */
import { getTranslateMessage } from '../containers/Translate/util';
import TRANSLATE_KEY from '../messages/constant';
import {
  MODELS as PREDICTIVE_MODELS,
  MODEL_LABELS as PREDICTIVE_MODEL_LABELS,
} from '../modules/Dashboard/Profile/PredictiveModel/config';
// import { safeParse } from '../utils/common';

// console.log('This is list AM');
const labelUnsub = {
  label: 'Unsubscribe',
  translateCode: TRANSLATE_KEY._,
};
const labelResub = {
  label: 'Resubscribe',
  translateCode: TRANSLATE_KEY._,
};
const labelEmail = {
  label: 'Email',
  translateCode: TRANSLATE_KEY._,
};
const labelSMS = {
  label: 'SMS',
  translateCode: TRANSLATE_KEY._,
};

const labelActionBaseTrigger = {
  label: 'Action-based trigger',
  translateCode: TRANSLATE_KEY._NODE_TRIGGER_ACTION,
};

const labelVisitor = {
  label: 'Visitor',
  translateCode: TRANSLATE_KEY._FILTER_SEGMENT_VISITOR,
};

const labelScheduleTrigger = {
  label: 'Schedule trigger',
  translateCode: TRANSLATE_KEY._NODE_TRIGGER_SCHEDULE,
};

const labelCustomer = {
  label: 'Customer',
  translateCode: TRANSLATE_KEY._FILTER_SEGMENT_CUSTOMER,
};

const labelUser = {
  label: 'Visitor',
  translateCode: TRANSLATE_KEY._FILTER_SEGMENT_VISITOR,
};
export const labelEnable = {
  label: 'Enable',
  translateCode: TRANSLATE_KEY._FILTER_ENABLED,
};

export const labelDisable = {
  label: 'Disable',
  translateCode: TRANSLATE_KEY._FILTER_DISABLED,
};

const labelSuccess = {
  label: 'Success',
  translateCode: TRANSLATE_KEY._STATUS_SUCCESS,
};

const labelComputing = {
  label: 'Computing',
  translateCode: TRANSLATE_KEY._STATUS_COMPUTING,
};

const labelWaitingInitial = {
  label: 'Wating inital',
  translateCode: TRANSLATE_KEY._STATUS_WAITING_INITAL,
};

const labelPaused = {
  label: 'Paused',
  translateCode: TRANSLATE_KEY._STATUS_PAUSED,
};

const labelLiveUpdate = {
  label: 'Live Update',
  translateCode: TRANSLATE_KEY._STATUS_LIVE_UPDATE,
};

const labelUnSuccess = {
  label: 'Unsuccess',
  translateCode: TRANSLATE_KEY._STATUS_UNSUCCESS,
};

const labelWaiting = {
  label: 'Waiting',
  translateCode: TRANSLATE_KEY._STATUS_WAITING,
};
const labelReadyUse = {
  label: 'Ready to use ',
  translateCode: TRANSLATE_KEY._STATUS_READY_TO_USE,
};
const labelDone = {
  label: 'Done',
  translateCode: TRANSLATE_KEY._IMPORT_STATUS_DONE,
};

const labelImporting = {
  label: 'In Process',
  translateCode: TRANSLATE_KEY._IMPORT_STATUS_IN_PROCESS,
};
const labelProcess = {
  label: 'Processing',
  translateCode: TRANSLATE_KEY._,
};

const labelFail = {
  label: 'Failed',
  translateCode: TRANSLATE_KEY._IMPORT_STATUS_FAILED,
};
const labelActive = {
  label: 'Active',
  translateCode: TRANSLATE_KEY._INFO_STORY_STATUS_ACTIVE,
};
const labelInactive = {
  label: 'Inactive',
  translateCode: TRANSLATE_KEY._USER_GUIDE_IP_RESTRICTION_STATUS_INACTIVE,
};
const labelDeactive = {
  label: 'Deactive',
  translateCode: TRANSLATE_KEY._POOL_STATUS_DEACTIVE,
};

const labelExpired = {
  label: 'Expired',
  translateCode: TRANSLATE_KEY._POOL_STATUS_EXPIRED,
};

const labelAvailable = {
  label: 'Available',
  translateCode: TRANSLATE_KEY._PROMOTION_STATUS_AVAILABLE,
};

const labelAllocated = {
  label: 'Allocated',
  translateCode: TRANSLATE_KEY._PROMOTION_STATUS_ALLOCATED,
};

const labelSent = {
  label: 'Sent',
  translateCode: TRANSLATE_KEY._DELIVERY_STATUS_SENT,
};
const labelUsed = {
  label: 'Used',
  translateCode: TRANSLATE_KEY._,
};
// const labelCampaignEnable = getTranslateMessage(
//   TRANSLATE_KEY._INFO_CAMPAIGN_STATUS_ENABLE,
//   'Enable',
// );

// const labelCampaignDisable = getTranslateMessage(
//   TRANSLATE_KEY._INFO_CAMPAIGN_STATUS_DISABLE,
//   'Disabled',
// );

const labelCampaignRemove = {
  label: 'Remove',
  translateCode: TRANSLATE_KEY._INFO_CAMPAIGN_STATUS_REMOVE,
};
const labelArchived = {
  label: 'Archived',
  translateCode: TRANSLATE_KEY._,
};
const labelZoneGetScript = getTranslateMessage(
  TRANSLATE_KEY._INFO_CAMPAIGN_STAT,
  'Get script',
);
const labelZoneCssSelector = getTranslateMessage(
  TRANSLATE_KEY._INFO_CAMPAIGN_STA,
  'CSS Selector',
);
const labelNo = getTranslateMessage(TRANSLATE_KEY._NODE_IF_THEN_TITL_NO, 'No');
const labelYes = getTranslateMessage(
  TRANSLATE_KEY._NODE_IF_THEN_TITL_YES,
  'Yes',
);
const labelSystem = {
  label: 'System',
  translateCode: TRANSLATE_KEY._STORAGE_TYPE_SYSTEM,
};
const labelDefault = {
  label: 'Default',
  translateCode: TRANSLATE_KEY._ATTR_TYPE_DEFAULT,
};
const labelPredefined = {
  label: 'Predefined',
  translateCode: TRANSLATE_KEY._STORAGE_TYPE_PREDEFINED,
};
const labelCustom = {
  label: 'Custom',
  translateCode: TRANSLATE_KEY._STORAGE_TYPE_CUSTOM,
};
const labelComputed = {
  label: 'Computed',
  translateCode: TRANSLATE_KEY._ATTR_TYPE_COMPUTED,
};

const labelEventOriented = {
  label: 'Event-oriented Model',
  translateCode: TRANSLATE_KEY._ACT_EVENT_ORIENT_MODEL,
};
const labelObjectOriented = {
  label: 'Object-oriented Model',
  translateCode: TRANSLATE_KEY._ACT_OBJECT_ORIENT_MODEL,
};
const labelVisitorProfile = {
  label: 'Visitor profile',
  translateCode: TRANSLATE_KEY._ACT_VISITOR_PROFILE,
};
const labelCustomerProfile = {
  label: 'Customer profile',
  translateCode: TRANSLATE_KEY._ACT_CUSTOMER_PROFILE,
};
const labelCustomObject = getTranslateMessage(
  TRANSLATE_KEY._ATTR_TYPE_CUSTOM_TYPE,
  'Custom Object',
);
const labelPredefinedObject = getTranslateMessage(
  TRANSLATE_KEY._STORAGE_TYPE_PREDEFINED,
  'Predefined Object',
);

const labelFieldAtt = {
  label: 'Attributes fields',
  translateCode: TRANSLATE_KEY._FILTER_OPTION_ATTRIBUTE_FIELD,
};

const labelFieldMeasure = {
  label: 'Event Measure',
  translateCode: TRANSLATE_KEY._FILTER_OPTION_EVENT_MEASURE_FIELD,
};
const labelFieldObjectMeasure = {
  label: 'Object Measure',
  translateCode: TRANSLATE_KEY._FILTER_OPTION_OBJECT_MEASURE_FIELD,
};
const labelFieldDefault = getTranslateMessage(
  TRANSLATE_KEY._ATTR_TYPE_DEFAULT,
  'Default',
);

const labelCompleted = {
  label: 'Completed',
  translateCode: TRANSLATE_KEY._STATE_COMPLETED,
};

const labelEnd = {
  label: 'End',
  translateCode: TRANSLATE_KEY._STATE_END,
};

const labelExitJourney = {
  label: 'Exit journey',
  translateCode: TRANSLATE_KEY._STATE_EXIT_JOURNEY,
};
const labelInvalid = {
  label: 'Invalid time frame',
  translateCode: TRANSLATE_KEY._DELIVERY_STATUS_INVALID_TIME,
};
const labelHold = {
  label: 'Hold until valid time',
  translateCode: TRANSLATE_KEY._DELIVERY_STATUS_HOLD_TIL_VALID,
};
const labelSkip = {
  label: 'Resume skip',
  translateCode: TRANSLATE_KEY._DELIVERY_STATUS_RESUME_SKIP,
};
const labelBuildContentError = {
  label: 'Build content error',
  translateCode: TRANSLATE_KEY._CODE_BUILD_CONTENT_ERROR,
};

const labelMissingReceiver = {
  label: 'Missing receiver',
  translateCode: TRANSLATE_KEY._CODE_MISSING_RECEIVER,
};

const labelTriggerFrequencyCapping = {
  label: 'Violate trigger frequency capping',
  translateCode: TRANSLATE_KEY._STATE_VIOLATE_TRIGGER_FC,
};
const labelViolateDestination = {
  label: 'Violate destination frequency capping',
  translateCode: TRANSLATE_KEY._CODE_DESTINATION_FREQUENCY_CAPPING,
};

const labelDestinationFrequencyCapping = {
  label: 'Violate destination frequency capping',
  translateCode: TRANSLATE_KEY._CODE_DESTINATION_FREQUENCY_CAPPING,
};

const labelDelivered = {
  label: 'Delivered',
  translateCode: TRANSLATE_KEY._CODE_DELIVERED,
};
const labelBounceError = {
  label: 'Bounce & error',
  translateCode: TRANSLATE_KEY._CODE_BOUNCED_N_ERROR,
};

const labelBouncedAndError = {
  label: 'Bounce & Error',
  translateCode: TRANSLATE_KEY._CODE_BOUNCED_N_ERROR,
};

const labelHoldUntilSendBroadcast = {
  label: 'Hold until send broadcast',
  translateCode: TRANSLATE_KEY._JOURNEY_PROCESS_STATE_BROADCAST,
};
const labelPassed = {
  label: 'Passed',
  translateCode: TRANSLATE_KEY._,
};
const labelShortlink = {
  label: 'ShortLink Service Error',
  translateCode: TRANSLATE_KEY._SHORTLINK_SERVICE_ERROR,
};
const labelError = {
  label: 'Error',
  translateCode: TRANSLATE_KEY._CODE_ERROR,
};
const labelSuccessLimit = {
  label: 'Success but limited',
  translateCode: TRANSLATE_KEY._STATUS_SUCCESS_BUT_LIMIT,
};
const labelWebsite = {
  label: 'Website',
  translateCode: TRANSLATE_KEY._TITL_SOURCE_CATALOG_WESITE,
};
const labelMobile = {
  label: 'Mobile App',
  translateCode: TRANSLATE_KEY._TITL_SOURCE_CATALOG_MOBILE_APP,
};
const labelServer = {
  label: 'Server',
  translateCode: TRANSLATE_KEY._TITL_SOURCE_CATALOG_SERVER,
};
const labelClound = {
  label: 'Cloud Service',
  translateCode: TRANSLATE_KEY._TITL_SOURCE_CATALOG_CLOUD,
};
const labelSystemDefault = {
  label: 'System',
  translateCode: TRANSLATE_KEY._,
};
const DATA_FIELD_TYPE = [
  {
    value: 'attribute',
    ...labelFieldAtt,
  },
  {
    value: 'measure',
    ...labelFieldMeasure,
  },
  {
    value: 'object_measure',
    ...labelFieldObjectMeasure,
  },
];
const DATA_DELIVERY_LOG_STATUS = [
  {
    value: 'BUILD_CONTENT_ERROR',
    ...labelBuildContentError,
  },
  {
    value: 'MISSING_RECEIVER',
    ...labelMissingReceiver,
  },
  {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    ...labelViolateDestination,
  },
  {
    value: 'DELIVERED',
    ...labelDelivered,
  },
  {
    value: 'BOUNCED_N_ERROR',
    ...labelBounceError,
  },
];
const DATA_FIELD_TREAT_AS_TABLE = [
  {
    value: '1',
    ...labelFieldAtt,
  },
  {
    value: '2',
    ...labelFieldMeasure,
  },
  {
    value: '3',
    ...labelFieldAtt,
  },
  {
    value: '4',
    ...labelFieldMeasure,
  },
  {
    value: '5',
    ...labelFieldObjectMeasure,
  },
  {
    value: '6',
    ...labelFieldObjectMeasure,
  },
];
export const AM_TYPE = {
  EVENT_ORIENTED: 1,
  OBJECT_ORIENTED: 2,
  VISITOR_PROFILE: 4,
  CUSTOMER_PROFILE: 3,
};
const ANALYTIC_MODAL_TYPE = {
  [AM_TYPE.EVENT_ORIENTED]: { value: '1', ...labelEventOriented },
  [AM_TYPE.OBJECT_ORIENTED]: { value: '2', ...labelObjectOriented },
  [AM_TYPE.CUSTOMER_PROFILE]: { value: '3', ...labelCustomerProfile },
  [AM_TYPE.VISITOR_PROFILE]: { value: '4', ...labelVisitorProfile },
};

const STORAGE_FIELD = [
  { value: '1', label: labelFieldDefault },
  { value: '2', ...labelPredefined },
  { value: '3', ...labelCustom },
];
const SOURCE_TYPE = [
  { value: '1', ...labelWebsite },
  { value: '2', ...labelMobile },
  { value: '3', ...labelServer },
  // { value: '4', ...labelClound },
  { value: '5', ...labelSystemDefault },
];
const DATA_STATUS = [
  {
    value: '1',
    ...labelEnable,
  },
  {
    value: '2',
    ...labelDisable,
  },
];
const DATA_STATUS_ACTIVE = [
  {
    value: '1',
    ...labelActive,
  },
  {
    value: '2',
    ...labelInactive,
  },
];
const DATA_SCHEDULE_PROCESS_TYPE = [
  {
    value: 'SCHEDULED',
    ...labelScheduleTrigger,
  },
];

const DATA_ACTION_PROCESS_TYPE = [
  {
    value: 'EVENT_BASED',
    ...labelActionBaseTrigger,
  },
];

const DATA_SCHEDULE_DETAIL_PROCESS_TYPE = [
  {
    value: 'SCHEDULED',
    ...labelScheduleTrigger,
  },
];

const DATA_USER_TYPE = [
  {
    value: 'user',
    ...labelVisitor,
  },
  {
    value: 'customer',
    ...labelCustomer,
  },
];

const DATA_STATE = [
  {
    value: 'WAITING',
    ...labelWaiting,
  },
  {
    value: 'COMPLETED',
    ...labelCompleted,
  },
  {
    value: 'END',
    ...labelEnd,
  },
  {
    value: 'BUILD_CONTENT_ERROR',
    ...labelBuildContentError,
  },
  {
    value: 'MISSING_RECEIVER',
    ...labelMissingReceiver,
  },
  {
    value: 'TRIGGER_FREQUENCY_CAPPING',
    ...labelTriggerFrequencyCapping,
  },
  {
    value: 'DESTINATION_FREQUENCY_CAPPING',
    ...labelDestinationFrequencyCapping,
  },
  {
    value: 'DELIVERED',
    ...labelDelivered,
  },
  {
    value: 'BOUNCED_N_ERROR',
    ...labelBouncedAndError,
  },
  {
    value: 'PAUSED',
    ...labelPaused,
  },
  {
    value: 'ERROR',
    ...labelError,
  },
  {
    value: 'EXIT',
    ...labelExitJourney,
  },
  {
    value: 'INVALID_TIME_FRAME',
    ...labelInvalid,
  },
  {
    value: 'HOLD_UNTIL_VALID_TIME',
    ...labelHold,
  },
  {
    value: 'SKIP',
    ...labelSkip,
  },
  {
    value: 'HOLD_UNTIL_SEND_BROADCAST',
    ...labelHoldUntilSendBroadcast,
  },
  {
    value: 'PASSED',
    ...labelPassed,
  },
  {
    value: 'SHORTLINK_SERVICE_ERROR',
    ...labelShortlink,
  },
];

const DATA_NODE_NAME = [
  { label: 'Action-based Trigger', value: 'EVENT_BASED' },
  { label: 'Scheduled Trigger', value: 'SCHEDULED' },
  { label: 'Delay', value: 'DELAY' },
  { label: 'If/then branches', value: 'CLASSIC_LIST_BRANCH' },
  { label: 'Parallel filter', value: 'PARALLEL_LIST_BRANCH' },
  { label: 'Filter', value: 'FILTER' },
  { label: 'Go to', value: 'GO_TO' },
  { label: 'Destination', value: 'DESTINATION' },
  { label: 'End', value: 'END' },
  { label: 'Yes', value: 'CONDITION_YES' },
  { label: 'No', value: 'CONDITION_NO' },
  { label: 'A/B Split', value: 'AB_SPLIT' },
  { label: 'Branch', value: 'AB_SPLIT_NODE' },
  { label: 'Wait for event', value: 'WAIT_EVENT' },
  { label: 'Event happened', value: 'WAIT_EVENT_HAPPENED' },
  { label: 'Waiting timeout', value: 'WAIT_EVENT_TIMEOUT' },
  { label: 'Update Info', value: 'UPDATE_INFO' },
  { label: 'Update segment', value: 'UPDATE_SEGMENT' },
  { label: 'Wait for response', value: 'WAIT_FOR_RESPONSE' },
  { label: 'Wait for response node', value: 'WAIT_FOR_RESPONSE_NODE' },
  { label: 'Wait for response node No', value: 'WAIT_FOR_RESPONSE_NODE_NO' },
];
const DATA_STATUS_WITH_REMOVE = [
  {
    value: '1',
    ...labelEnable,
  },
  {
    value: '2',
    ...labelDisable,
  },
  {
    value: '3',
    ...labelCampaignRemove,
  },
];
const DATA_CHANNEL_UNSUBSCRIBE = [
  {
    value: '1',
    ...labelEmail,
  },
  {
    value: '2',
    ...labelSMS,
  },
];
const DATA_STATUS_PROCESS = [
  {
    value: '1',
    ...labelProcess,
  },
  {
    value: '2',
    ...labelSuccess,
  },
  {
    value: '3',
    ...labelFail,
  },
];
const DATA_STATUS_WITH_PROVITY = [
  {
    value: '0',
    ...labelDisable,
  },
  {
    value: '1',
    ...labelEnable,
  },
];

const DATA_STATUS_WITH_REMOVE_CUSTOM = [
  {
    value: '1',
    ...labelEnable,
  },
  {
    value: '0',
    ...labelDisable,
  },
  {
    value: '3',
    ...labelCampaignRemove,
  },
];
const DATA_STATUS_WITH_ARCHIVED = [
  {
    value: '1',
    ...labelEnable,
  },
  {
    value: '2',
    ...labelDisable,
  },
  {
    value: '4',
    ...labelArchived,
  },
];
const DATA_SEGMENT_TYPE = [
  { value: '-1003', ...labelCustomer },
  { value: '-1007', ...labelUser },
];

const DATA_PREDICTIVE_MODEL_TYPE = [
  {
    value: PREDICTIVE_MODELS.RFM_MODEL,
    label: PREDICTIVE_MODEL_LABELS[PREDICTIVE_MODELS.RFM_MODEL],
  },
  {
    value: PREDICTIVE_MODELS.LIFECYCLE_STAGE,
    label: PREDICTIVE_MODEL_LABELS[PREDICTIVE_MODELS.LIFECYCLE_STAGE],
  },
];

const DATA_SEGMENT_UPDATE_METHOD = [
  {
    value: 'static',
    label: 'Static',
    translateCode: TRANSLATE_KEY._COMPUTE_STATIC,
  },
  {
    value: 'dynamic',
    label: 'Dynamic',
    translateCode: TRANSLATE_KEY._COMPUTE_DYNAMIC,
  },
];

const DATA_COMPUTE_STATUS = [
  { value: '2', ...labelSuccess },
  { value: '1', ...labelComputing },
  { value: '4', ...labelPaused },
  { value: '3', ...labelUnSuccess },
];
const DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE = [
  { value: '2', ...labelSuccess },
  { value: '1', ...labelComputing },
  { value: '3', ...labelUnSuccess },
  { value: '4', ...labelPaused },
  { value: '5', ...labelWaiting },
  { value: '6', ...labelReadyUse },
  { value: '9', ...labelSuccessLimit },
];
const DATA_COMPUTE_STATUS_WITH_REMOVE = [
  { value: '2', ...labelSuccess },
  { value: '1', ...labelComputing },
  // { value: '6', ...labelCampaignRemove },
  // { value: '4', ...labelPaused },
  { value: '3', ...labelUnSuccess },
  { value: '5', ...labelWaiting },
  { value: '7', ...labelWaitingInitial },
  { value: '6', ...labelReadyUse },
  { value: '9', ...labelSuccessLimit },
];

const DATA_IMPORT_STATUS = [
  { value: '2', ...labelImporting },
  { value: '3', ...labelDone },
  { value: '4', ...labelFail },
  { value: '9', ...labelExpired },
];
const DATA_EXPORT_STATUS = [
  { value: '1', ...labelImporting },
  { value: '2', ...labelDone },
  { value: '3', ...labelFail },
  { value: '9', ...labelExpired },
];

export const DATA_STATUS_PROMOTION_CODE = [
  { value: '1', ...labelAvailable },
  { value: '2', ...labelAllocated },
  { value: '3', ...labelSent },
  { value: '4', ...labelUsed },
];

const DATA_COMPUTE_STATUS_ATTIRBUTE = [
  { value: '2', ...labelSuccess },
  { value: '1', ...labelComputing },
  { value: '8', ...labelLiveUpdate },
  { value: '4', ...labelPaused },
  { value: '3', ...labelUnSuccess },
  { value: '5', ...labelWaiting },
  { value: '9', ...labelSuccessLimit },
];

const DATA_ZONE_TYPE = [
  { value: '1', label: labelZoneGetScript },
  { value: '2', label: labelZoneCssSelector },
];

const DATA_IS_REQUIRED = [
  { value: '0', label: labelNo },
  { value: '1', label: labelYes },
];

const DATA_STORAGE_TYPE = [
  { value: '1', ...labelDefault },
  { value: '2', ...labelPredefined },
  { value: '3', ...labelCustom },
];

const OBJECT_ATTRIBUTE_TYPE = [
  { value: '1', ...labelSystem },
  { value: '2', ...labelCustom },
  { value: '3', ...labelComputed },
];

const BUSINESS_OBJECT = [
  { value: '0', label: labelCustomObject },
  { value: '1', label: labelPredefinedObject },
  { value: '2', label: labelFieldDefault },
];

const DATA_OBJECT_TYPE_BO = [
  { value: '0', ...labelCustom },
  { value: '1', ...labelPredefined },
];

const DATA_IMPORT_METHOD = [
  {
    value: '1',
    label: 'Local Upload',
    translateCode: TRANSLATE_KEY._IMPORT_METHOD_LOCAL_UPLOAD,
  },
];

const DATA_TRIGGER_TYPE = [
  {
    value: 'EVENT_BASED',
    label: getTranslateMessage(
      TRANSLATE_KEY._INFO_STORY_ACTION_BASED,
      'Action-based',
    ),
  },
  {
    value: 'SCHEDULED',
    label: getTranslateMessage(
      TRANSLATE_KEY._INFO_STORY_SCHEDULED_TIME,
      'Scheduled-time',
    ),
  },
];
const DATA_COMPUTION_TRIGGER_TYPE = [
  {
    value: 'client',
    label: 'User',
    translateCode: TRANSLATE_KEY._COMPUTE_TRIGGER_BY_USER,
  },
  {
    value: 'schedule',
    label: 'System',

    translateCode: TRANSLATE_KEY._COMPUTE_TRIGGER_BY_SYSTEM,
  },
];
const ERROR_INFO_COMPUTION_HISTORY_AM_MODEL = [
  {
    value: 'SERVER_ERROR',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  },
  {
    value: 'INVALID_META',
    label: 'Invalid meta data',
    translateCode: TRANSLATE_KEY._COMPUTATION_INVALID_META,
  },
  {
    value: 'TIME_OUT',
    label: 'Request time out',
    translateCode: TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  },
  {
    value: 'BUILD_SQL_ERROR',
    label: 'SQL executing-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Unknown error',
    translateCode: TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_ARCHIVED',
    label: 'Object Archived',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_PROPERTY_ARCHIVED,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_DELETED',
    label: 'Object Deleted',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_PROPERTY_DELETED,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED',
    label: 'Object Archived',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_DELETED',
    label: 'Object Deleted',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_DELETED,
  },
  {
    value: '_COMPUTATION_OUT_RANGE_TTL',
    label:
      'Insufficient data due to the chosen time range falling outside the Time to Live',
    translateCode: TRANSLATE_KEY._SEG_RESULT_MESS_1,
  },
];
const ERROR_INFO_PREDICTIVE_MODEL = [
  {
    value: 'SERVER_ERROR',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  },
  {
    value: 'INVALID_META',
    label: 'Invalid meta data',
    translateCode: TRANSLATE_KEY._COMPUTATION_INVALID_META,
  },
  {
    value: 'TIME_OUT',
    label: 'Request time out',
    translateCode: TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  },
  {
    value: 'MODEL_NOT_FOUND',
    label: 'Computation Error',
    translateCode: TRANSLATE_KEY._COMPUTATION_MODEL_NOT_FOUND,
  },
  {
    value: 'BUILD_SQL_ERROR',
    label: 'Computation Error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  },
  {
    value: 'BUILD_PARAM_ERROR',
    label: 'Computation Error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_PARAM_ERROR,
  },
  {
    value: 'SYNC_ES_ERROR',
    label: 'Computation Error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_ES_ERROR,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Computation Error',
    translateCode: TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  },
];
const ERROR_INFO_COMPUTION_HISTORY = [
  {
    value: 'SERVER_ERROR',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  },
  {
    value: 'INVALID_META',
    label: 'Invalid meta data',
    translateCode: TRANSLATE_KEY._COMPUTATION_INVALID_META,
  },
  {
    value: 'TIME_OUT',
    label: 'Request time out',
    translateCode: TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  },
  {
    value: 'SEGMENT_NOT_FOUND',
    label: 'Computing-object not found',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_NOT_FOUND,
  },
  {
    value: 'BUILD_SQL_ERROR',
    label: 'SQL executing-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  },
  {
    value: 'SYNC_DW_ERROR',
    label: 'DW sync-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_DW_ERROR,
  },
  {
    value: 'BUILD_PARAM_ERROR',
    label: 'Invalid parameter',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_PARAM_ERROR,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Unknown error',
    translateCode: TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  },
  {
    value: 'UPLOAD_FILE_ERROR',
    label: 'Upload file error',
    translateCode: TRANSLATE_KEY._COMPUTAION_UPLOAD_FILE_ERROR,
  },
  {
    value: 'SYNC_ES_ERROR',
    label: 'Sync es error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_ES_ERROR,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_ARCHIVED',
    label: 'Object Archived',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_PROPERTY_ARCHIVED,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_DELETED',
    label: 'Object Deleted',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_PROPERTY_DELETED,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED',
    label: 'Object Archived',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_DELETED',
    label: 'Object Deleted',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_DELETED,
  },
  {
    value: 'UPLOAD_FILE_DELETE_ERROR_MESSAGE',
    label: 'The uploaded file was deleted',
    translateCode: TRANSLATE_KEY._UPLOAD_FILE_DELETED_ERROR_MESSAGE,
  },
  {
    value: '_COMPUTATION_OUT_RANGE_TTL',
    label:
      'Insufficient data due to the chosen time range falling outside the Time to Live',
    translateCode: TRANSLATE_KEY._SEG_RESULT_MESS_1,
  },
];
const ERROR_INFO_COMPUTION_HISTORY_BO = [
  {
    value: 'SERVER_ERROR',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  },
  {
    value: 'INVALID_META',
    label: 'Invalid meta data',
    translateCode: TRANSLATE_KEY._COMPUTATION_INVALID_META,
  },
  {
    value: 'TIME_OUT',
    label: 'Request time out',
    translateCode: TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  },
  {
    value: 'COLLECTION_NOT_FOUND',
    label: 'Computing-object not found',
    translateCode: TRANSLATE_KEY._COMPUTATION_COLLECTION_NOT_FOUND,
  },
  {
    value: 'BUILD_SQL_ERROR',
    label: 'SQL executing-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  },
  {
    value: 'SYNC_DW_ERROR',
    label: 'DW sync-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_DW_ERROR,
  },
  {
    value: 'BUILD_PARAM_ERROR',
    label: 'Invalid parameter',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_PARAM_ERROR,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Unknown error',
    translateCode: TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  },
  {
    value: 'UPLOAD_FILE_ERROR',
    label: 'Upload file error',
    translateCode: TRANSLATE_KEY._COMPUTAION_UPLOAD_FILE_ERROR,
  },
  {
    value: 'SYNC_ES_ERROR',
    label: 'Sync es error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_ES_ERROR,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_ARCHIVED',
    label: `The object used in the collection's property is being archived`,
    translateCode: TRANSLATE_KEY._,
  },
  {
    value: '_COMPUTATION_SEGMENT_PROPERTY_DELETED',
    label: `The object used in the collection's property does not exist`,
    translateCode: TRANSLATE_KEY._,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED',
    label: 'Object Archived',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED,
  },
  {
    value: '_COMPUTATION_ATTRIBUTE_PROPERTY_DELETED',
    label: 'Object Deleted',
    translateCode: TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_DELETED,
  },
  {
    value: 'UPLOAD_FILE_DELETE_ERROR_MESSAGE',
    label: 'The uploaded file was deleted',
    translateCode: TRANSLATE_KEY._UPLOAD_FILE_DELETED_ERROR_MESSAGE,
  },
];
const DATA_STATUS_STORY = [
  {
    value: '1',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_ACTIVE),
  },
  {
    value: '2',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_PAUSED),
  },
  {
    value: '3',
    label: getTranslateMessage(TRANSLATE_KEY._ACT_STORY_REMOVE, 'Removed'),
  },
  {
    value: '4',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_SCHEDULED),
  },
  {
    value: '5',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_FROZEN),
  },
  {
    value: '6',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_ERROR),
  },
  {
    value: '7',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_CLOSED),
  },
  {
    value: '8',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_ABORTED),
  },
  {
    value: '9',
    label: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_STATUS_DESIGNED),
  },
];

const DATA_STATUS_PROMOTION_POOL = [
  { value: '2', ...labelActive },
  { value: '1', ...labelImporting },
  { value: '6', ...labelCampaignRemove },
  { value: '4', ...labelDeactive },
  { value: '3', ...labelExpired },
  // { value: '5', ...labelWaiting },
];

export const MAP_DATA_TYPE = {
  number: {
    name: 'number',
    label: 'Number',
    translateCode: TRANSLATE_KEY._DATA_TYPE_NUMBER,
    value: 'number',
  },
  string: {
    name: 'string',
    label: 'String',
    translateCode: TRANSLATE_KEY._DATA_TYPE_STRING,
    value: 'string',
  },
  text: {
    name: 'text',
    label: 'Text',
    translateCode: TRANSLATE_KEY._DATA_TYPE_TEXT,
    value: 'text',
  },
  datetime: {
    name: 'datetime',
    label: 'Datetime',
    translateCode: TRANSLATE_KEY._DATA_TYPE_DATETIME,
    value: 'datetime',
  },
  boolean: {
    name: 'boolean',
    label: 'Boolean',
    translateCode: TRANSLATE_KEY._DATA_TYPE_BOOLEAN,
    value: 'boolean',
  },
  array_number: {
    name: 'array_number',
    label: 'Array of Numbers',
    translateCode: TRANSLATE_KEY._DATA_TYPE_ARRAY_NUMBER,
    value: 'array_number',
  },
  array_string: {
    name: 'array_string',
    label: 'Array of Strings',
    translateCode: TRANSLATE_KEY._DATA_TYPE_ARRAY_STRING,
    value: 'array_string',
  },
  array_datetime: {
    name: 'array_datetime',
    label: 'Array of Datetimes',
    translateCode: TRANSLATE_KEY._DATA_TYPE_ARRAY_DATETIME,
    value: 'array_datetime',
  },
  object: {
    name: 'object',
    label: 'Object',
    translateCode: TRANSLATE_KEY._DATA_TYPE_OBJECT,
    value: 'object',
  },
};

export const DATA_TYPE_BO_ATT = [
  {
    name: 'default',
    label: getTranslateMessage(TRANSLATE_KEY._STORAGE_TYPE_SYSTEM, 'System'),
    value: '1',
  },
  {
    name: 'custom',
    label: getTranslateMessage(TRANSLATE_KEY._TITL_CUSTOM, 'Custom'),
    value: '2',
  },
  {
    name: 'computed_event_counter',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_Computed,
      'Computed - Event Counter',
    ),
    value: '3_event_counter',
  },
  {
    name: 'computed_aggregation',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedAggregation,
      'Computed - Aggregation',
    ),
    value: '3_aggregation',
  },
  {
    name: 'computed_most_frequent',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedMostFrequent,
      'Computed - Most Frequent',
    ),
    value: '3_most_frequent',
  },
  {
    name: 'computed_first',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedFirst,
      'Computed - First',
    ),
    value: '3_first',
  },
  {
    name: 'computed_last',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedLast,
      'Computed - Last',
    ),
    value: '3_last',
  },
  {
    name: 'computed_unique_lilist',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list,
      'Computed - Unique List',
    ),
    value: '3_unique_list',
  },
  {
    name: 'computed_unique_list_count',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list_count,
      'Computed - Unique List Count',
    ),
    value: '3_unique_list_count',
  },
  {
    name: 'computed_custom_function',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_custom_function,
      'Computed - Virtual Custom Function',
    ),
    value: '3_custom_function',
  },
  {
    name: 'computed_schedule_custom_function',
    label: getTranslateMessage(
      TRANSLATE_KEY._004,
      'Computed - Schedule Custom Function',
    ),
    value: '3_scheduled_custom_function',
  },
  {
    name: 'conversion_conversion',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list_count,
      'Conversion Attribution',
    ),
    value: '3_conversion',
  },
];

export const DATA_TYPE_COMPUTATION_BO_ATT = [
  {
    name: 'default',
    label: getTranslateMessage(TRANSLATE_KEY._STORAGE_TYPE_SYSTEM, 'System'),
    value: '1',
  },
  {
    name: 'custom',
    label: getTranslateMessage(TRANSLATE_KEY._TITL_CUSTOM, 'Custom'),
    value: '2',
  },
  {
    name: 'computed_event_counter',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_Computed,
      'Computed - Event Counter',
    ),
    value: 'event_counter',
  },
  {
    name: 'computed_aggregation',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedAggregation,
      'Computed - Aggregation',
    ),
    value: 'aggregation',
  },
  {
    name: 'computed_most_frequent',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedMostFrequent,
      'Computed - Most Frequent',
    ),
    value: 'most_frequent',
  },
  {
    name: 'computed_first',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedFirst,
      'Computed - First',
    ),
    value: 'first',
  },
  {
    name: 'computed_last',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_ComputedLast,
      'Computed - Last',
    ),
    value: 'last',
  },
  {
    name: 'computed_unique_lilist',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list,
      'Computed - Unique List',
    ),
    value: 'unique_list',
  },
  {
    name: 'computed_unique_list_count',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list_count,
      'Computed - Unique List Count',
    ),
    value: 'unique_list_count',
  },
  {
    name: 'computed_custom_function',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_custom_function,
      'Computed - Virtual Custom Function',
    ),
    value: 'custom_function',
  },
  {
    name: 'computed_schedule_custom_function',
    label: getTranslateMessage(
      TRANSLATE_KEY._004,
      'Computed - Schedule Custom Function',
    ),
    value: 'scheduled_custom_function',
  },
  {
    name: 'conversion_conversion',
    label: getTranslateMessage(
      TRANSLATE_KEY._DATA_TYPE_computed_unique_list_count,
      'Conversion Attribution',
    ),
    value: 'conversion',
  },
];

const ERROR_INFO_IMPORT_HISTORY = [
  {
    value: 'SERVER_ERROR',
    label: 'Error info: Server error',
    translateCode: TRANSLATE_KEY._IMPORT_SERVER_ERROR,
  },
  {
    value: 'INVALID_FILE',
    label: 'Error info: Invalid file',
    translateCode: TRANSLATE_KEY._IMPORT_INVALID_FILE,
  },
  {
    value: 'VALIDATE_ERROR',
    label: 'VALIDATE_ERROR',
    translateCode: TRANSLATE_KEY._IMPORT_VALIDATE_ERROR,
  },
  {
    value: 'TIME_OUT',
    label: 'Error info: Time out',
    translateCode: TRANSLATE_KEY._IMPORT_TIME_OUT,
  },
  {
    value: 'FILE_NOT_FOUND',
    label: 'Error info: File not found',
    translateCode: TRANSLATE_KEY._IMPORT_FILE_NOT_FOUND,
  },
  {
    value: 'FILE_META_INCORRECT',
    label: 'Error info: Incorrect Metadata',
    translateCode: TRANSLATE_KEY._IMPORT_FILE_META_INCORRECT,
  },
  {
    value: 'MAPPING_FIELDS_ERROR',
    label: 'Error info: Mapping fields error',
    translateCode: TRANSLATE_KEY._IMPORT_MAPPING_FIELDS_ERROR,
  },
  {
    value: 'CONVERT_FILE_FAIL',
    label: 'Error info: Converting file failed',
    translateCode: TRANSLATE_KEY._IMPORT_CONVERT_FILE_FAIL,
  },
  {
    value: 'MISSING_REQUIRED_FIELD',
    label: 'Error info: Missing required field(s)',
    translateCode: TRANSLATE_KEY._IMPORT_MISSING_REQUIRED_FIELD,
  },
  {
    value: 'LIMIT_ROWS_SIZE',
    label: 'Error info: File rows over allowed limit',
    translateCode: TRANSLATE_KEY._IMPORT_LIMIT_ROWS_SIZE,
  },
  {
    value: 'LIMIT_COLUMNS_SIZE',
    label: 'Error info: File columns over allowed limit',
    translateCode: TRANSLATE_KEY._IMPORT_LIMIT_COLUMNS_SIZE,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Error info: Unknown',
    translateCode: TRANSLATE_KEY._IMPORT_UNKNOWN_ERROR,
  },
];
const ERROR_INFO_SEG_COL = [
  {
    value: 'SERVER_ERROR',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  },
  {
    value: 'INVALID_META',
    label: 'Invalid meta data',
    translateCode: TRANSLATE_KEY._COMPUTATION_INVALID_META,
  },
  {
    value: 'TIME_OUT',
    label: 'Request time out',
    translateCode: TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  },
  {
    value: 'SEGMENT_NOT_FOUND',
    label: 'Computing-object not found',
    translateCode: TRANSLATE_KEY._COMPUTATION_SEGMENT_NOT_FOUND,
  },
  {
    value: 'BUILD_SQL_ERROR',
    label: 'SQL executing-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  },
  {
    value: 'SYNC_DW_ERROR',
    label: 'DW sync-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_DW_ERROR,
  },
  {
    value: 'BUILD_PARAM_ERROR',
    label: 'Invalid parameter',
    translateCode: TRANSLATE_KEY._COMPUTATION_BUILD_PARAM_ERROR,
  },
  {
    value: 'UPLOAD_FILE_ERROR',
    label: 'File uploading failed',
    translateCode: TRANSLATE_KEY._COMPUTATION_UPLOAD_FILE_ERROR,
  },
  {
    value: 'SYNC_ES_ERROR',
    label: 'ES sync-error',
    translateCode: TRANSLATE_KEY._COMPUTATION_SYNC_ES_ERROR,
  },
  {
    value: 'UNKNOWN_ERROR',
    label: 'Unknown error',
    translateCode: TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  },
  {
    value: 'UPLOAD_FILE_DELETE_ERROR_MESSAGE',
    label: 'The uploaded file was deleted',
    translateCode: TRANSLATE_KEY._UPLOAD_FILE_DELETED_ERROR_MESSAGE,
  },
  {
    value: '_COMPUTATION_OUT_RANGE_TTL',
    label:
      'Insufficient data due to the chosen time range falling outside the Time to Live',
    translateCode: TRANSLATE_KEY._SEG_RESULT_MESS_1,
  },
];
const ERROR_INFO_EXPORT_HISTORY = [
  {
    value: 'metadata_error',
    label: 'Meta data error',
    translateCode: TRANSLATE_KEY._EXPORT_METADATA_ERROR,
  },
  {
    value: 'server_error',
    label: 'Server error',
    translateCode: TRANSLATE_KEY._EXPORT_SERVER_ERROR,
  },
  {
    value: 'sql_build_error',
    label: 'SQL build error',
    translateCode: TRANSLATE_KEY._EXPORT_SQL_BUILD_ERROR,
  },
  {
    value: 'execute_query_error',
    label: 'Execute query error',
    translateCode: TRANSLATE_KEY._EXPORT_EXECUTE_QUERY_ERROR,
  },
  {
    value: 'export_file_error',
    label: 'Export file error',
    translateCode: TRANSLATE_KEY._EXPORT_EXPORT_FILE_ERROR,
  },
  {
    value: 'update_statistic_error',
    label: 'Update statistic error',
    translateCode: TRANSLATE_KEY._EXPORT_UPDATE_STATISTIC_ERROR,
  },
  {
    value: 'server_timeout',
    label: 'Server timeout',
    translateCode: TRANSLATE_KEY._EXPORT_SERVER_TIMEOUT,
  },
  {
    value: 'unknown_error',
    label: 'Unknown error',
    translateCode: TRANSLATE_KEY._EXPORT_UNKNOWN_ERROR,
  },
];
// const labelLocalUpload = {
//   label: 'Local Upload',
//   translateCode: TRANSLATE_KEY._IMPORT_METHOD_LOCAL_UPLOAD,
// };

export const MAP_MEASURE_METHOD = {
  event_counter: {
    name: 'event_counter',
    label: 'Count',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_EVENT_COUNT,
    value: 'event_counter',
  },
  attribute_counter: {
    name: 'attribute_counter',
    label: 'Count',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_EVENT_COUNT,
    value: 'attribute_counter',
  },
  sum: {
    name: 'sum',
    label: 'Sum',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_SUM,
    value: 'sum',
  },
  min: {
    name: 'min',
    label: 'Min',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_MIN,
    value: 'min',
  },
  max: {
    name: 'max',
    label: 'Max',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_MAX,
    value: 'max',
  },
  avg: {
    name: 'avg',
    label: 'Average',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_AVERAGE,
    value: 'avg',
  },
  unique_list_count: {
    name: 'unique_list_count',
    label: 'Unique Count',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_ULC,
    value: 'unique_list_count',
  },
  unique_list_aprox: {
    name: 'unique_list_aprox',
    label: 'Unique Count Approximate(HyperLogLog)',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_UC_APPROXIMATE_HYPERLOGLOG,
    value: 'unique_list_aprox',
  },
  unique_sketch: {
    name: 'unique_sketch',
    label: 'Unique Count Approximate(DataSketches)',
    translateCode: TRANSLATE_KEY._MEASURE_TYPE_UC_APPROXIMATE_DATASKETCHES,
    value: 'unique_sketch',
  },
  custom_formula: {
    name: 'custom_formula',
    label: 'CUSTOM FORMULA',
    translateCode: TRANSLATE_KEY._TITL_CUSTOM_FORMULA,
    value: 'custom_formula',
  },
};

export const DATA_ATTR_TYPE = [
  MAP_DATA_TYPE.number,
  MAP_DATA_TYPE.string,
  MAP_DATA_TYPE.text,
  MAP_DATA_TYPE.datetime,
  MAP_DATA_TYPE.boolean,
  {
    name: 'array',
    label: getTranslateMessage(TRANSLATE_KEY._DATA_TYPE_ARRAY, 'Array'),
    value: 'array',
    options: [
      MAP_DATA_TYPE.array_number,
      MAP_DATA_TYPE.array_string,
      MAP_DATA_TYPE.array_datetime,
    ],
  },
  MAP_DATA_TYPE.object,
];

export const DATA_DECRYPT_VALIDATION_METHOD = [
  {
    label: getTranslateMessage(
      TRANSLATE_KEY._TITL_CONFIRM_PASSWORD,
      'Comfirm Password',
    ),
    value: '1',
  },
];

export const DATA_MEASURE_METHOD = [
  MAP_MEASURE_METHOD.sum,
  MAP_MEASURE_METHOD.event_counter,
  MAP_MEASURE_METHOD.min,
  MAP_MEASURE_METHOD.max,
  MAP_MEASURE_METHOD.avg,
  MAP_MEASURE_METHOD.unique_list_count,
  MAP_MEASURE_METHOD.unique_sketch,
  MAP_MEASURE_METHOD.unique_list_aprox,
  MAP_MEASURE_METHOD.custom_formula,
];

export const DATA_BO_MEASURE_METHOD = [
  MAP_MEASURE_METHOD.sum,
  MAP_MEASURE_METHOD.attribute_counter,
  MAP_MEASURE_METHOD.min,
  MAP_MEASURE_METHOD.max,
  MAP_MEASURE_METHOD.avg,
  MAP_MEASURE_METHOD.unique_list_count,
  MAP_MEASURE_METHOD.unique_sketch,
  MAP_MEASURE_METHOD.unique_list_aprox,
  MAP_MEASURE_METHOD.custom_formula,
];

export const DATA_EVENT_ATTR_TYPE = [
  MAP_DATA_TYPE.number,
  MAP_DATA_TYPE.string,
  MAP_DATA_TYPE.text,
  MAP_DATA_TYPE.datetime,
  MAP_DATA_TYPE.boolean,
  MAP_DATA_TYPE.array_number,
  MAP_DATA_TYPE.array_string,
  MAP_DATA_TYPE.array_datetime,
  MAP_DATA_TYPE.object,
];
export const DATA_TYPE_FIELDS = [
  MAP_DATA_TYPE.number,
  MAP_DATA_TYPE.string,
  MAP_DATA_TYPE.text,
  MAP_DATA_TYPE.datetime,
  MAP_DATA_TYPE.array_number,
];
export const DATA_STATUS_IDENTIFIER = [
  {
    value: '1',
    ...labelUnsub,
  },
  {
    value: '2',
    ...labelResub,
  },
];
export const DATA_INFO = {
  identifier_status: {
    list: DATA_STATUS_IDENTIFIER,
    map: {
      [DATA_STATUS_IDENTIFIER[0].value]: DATA_STATUS_IDENTIFIER[0],
      [DATA_STATUS_IDENTIFIER[1].value]: DATA_STATUS_IDENTIFIER[1],
    },
  },
  unsubscribe_channel: {
    list: DATA_CHANNEL_UNSUBSCRIBE,
    map: {
      [DATA_CHANNEL_UNSUBSCRIBE[0].value]: DATA_CHANNEL_UNSUBSCRIBE[0],
      [DATA_CHANNEL_UNSUBSCRIBE[1].value]: DATA_CHANNEL_UNSUBSCRIBE[1],
    },
  },
  user_type: {
    list: DATA_USER_TYPE,
    map: {
      [DATA_USER_TYPE[0].value]: DATA_USER_TYPE[0],
      [DATA_USER_TYPE[1].value]: DATA_USER_TYPE[1],
    },
  },
  schedule_process_type: {
    list: DATA_SCHEDULE_PROCESS_TYPE,
    map: {
      [DATA_SCHEDULE_PROCESS_TYPE[0].value]: DATA_SCHEDULE_PROCESS_TYPE[0],
    },
  },
  action_process_type: {
    list: DATA_ACTION_PROCESS_TYPE,
    map: {
      [DATA_ACTION_PROCESS_TYPE[0].value]: DATA_ACTION_PROCESS_TYPE[0],
    },
  },
  schedule_detail_process_type: {
    list: DATA_SCHEDULE_DETAIL_PROCESS_TYPE,
    map: {
      [DATA_SCHEDULE_DETAIL_PROCESS_TYPE[0].value]:
        DATA_SCHEDULE_DETAIL_PROCESS_TYPE[0],
    },
  },
  state: {
    list: DATA_STATE,
    map: {
      [DATA_STATE[0].value]: DATA_STATE[0],
      [DATA_STATE[1].value]: DATA_STATE[1],
      [DATA_STATE[2].value]: DATA_STATE[2],
      [DATA_STATE[3].value]: DATA_STATE[3],
      [DATA_STATE[4].value]: DATA_STATE[4],
      [DATA_STATE[5].value]: DATA_STATE[5],
      [DATA_STATE[6].value]: DATA_STATE[6],
      [DATA_STATE[7].value]: DATA_STATE[7],
      [DATA_STATE[8].value]: DATA_STATE[8],
      [DATA_STATE[9].value]: DATA_STATE[9],
      [DATA_STATE[10].value]: DATA_STATE[10],
      [DATA_STATE[11].value]: DATA_STATE[11],
      [DATA_STATE[12].value]: DATA_STATE[12],
      [DATA_STATE[13].value]: DATA_STATE[13],
      [DATA_STATE[14].value]: DATA_STATE[14],
      [DATA_STATE[15].value]: DATA_STATE[15],
      [DATA_STATE[16].value]: DATA_STATE[16],
      [DATA_STATE[17].value]: DATA_STATE[17],
    },
  },
  node_name: {
    list: DATA_NODE_NAME,
    map: {
      [DATA_NODE_NAME[0].value]: DATA_NODE_NAME[0],
      [DATA_NODE_NAME[1].value]: DATA_NODE_NAME[1],
      [DATA_NODE_NAME[2].value]: DATA_NODE_NAME[2],
      [DATA_NODE_NAME[3].value]: DATA_NODE_NAME[3],
      [DATA_NODE_NAME[4].value]: DATA_NODE_NAME[4],
      [DATA_NODE_NAME[5].value]: DATA_NODE_NAME[5],
      [DATA_NODE_NAME[6].value]: DATA_NODE_NAME[6],
      [DATA_NODE_NAME[7].value]: DATA_NODE_NAME[7],
      [DATA_NODE_NAME[8].value]: DATA_NODE_NAME[8],
      [DATA_NODE_NAME[9].value]: DATA_NODE_NAME[9],
      [DATA_NODE_NAME[10].value]: DATA_NODE_NAME[10],
      [DATA_NODE_NAME[11].value]: DATA_NODE_NAME[11],
      [DATA_NODE_NAME[12].value]: DATA_NODE_NAME[12],
      [DATA_NODE_NAME[13].value]: DATA_NODE_NAME[13],
      [DATA_NODE_NAME[14].value]: DATA_NODE_NAME[14],
      [DATA_NODE_NAME[15].value]: DATA_NODE_NAME[15],
      [DATA_NODE_NAME[16].value]: DATA_NODE_NAME[16],
      [DATA_NODE_NAME[17].value]: DATA_NODE_NAME[17],
      [DATA_NODE_NAME[18].value]: DATA_NODE_NAME[18],
      [DATA_NODE_NAME[19].value]: DATA_NODE_NAME[19],
      [DATA_NODE_NAME[20].value]: DATA_NODE_NAME[20],
    },
  },
  status: {
    list: DATA_STATUS,
    map: {
      [DATA_STATUS[0].value]: DATA_STATUS[0],
      [DATA_STATUS[1].value]: DATA_STATUS[1],
      // [DATA_STATUS[2].value]: DATA_STATUS[2],
    },
  },
  status_active: {
    list: DATA_STATUS_ACTIVE,
    map: {
      [DATA_STATUS_ACTIVE[0].value]: DATA_STATUS_ACTIVE[0],
      [DATA_STATUS_ACTIVE[1].value]: DATA_STATUS_ACTIVE[1],
    },
  },
  item_type_id: {
    list: DATA_SEGMENT_TYPE,
    map: {
      [DATA_SEGMENT_TYPE[0].value]: DATA_SEGMENT_TYPE[0],
      [DATA_SEGMENT_TYPE[1].value]: DATA_SEGMENT_TYPE[1],
    },
  },
  audience_type: {
    list: DATA_SEGMENT_TYPE,
    map: {
      [DATA_SEGMENT_TYPE[0].value]: DATA_SEGMENT_TYPE[0],
      [DATA_SEGMENT_TYPE[1].value]: DATA_SEGMENT_TYPE[1],
    },
  },
  predictive_model_response_code: {
    list: ERROR_INFO_PREDICTIVE_MODEL,
    map: Object.fromEntries(ERROR_INFO_PREDICTIVE_MODEL.map(i => [i.value, i])),
  },
  predictive_model_type: {
    list: DATA_PREDICTIVE_MODEL_TYPE,
    map: Object.fromEntries(DATA_PREDICTIVE_MODEL_TYPE.map(i => [i.value, i])),
  },
  process_status: {
    list: DATA_COMPUTE_STATUS,
    map: {
      [DATA_COMPUTE_STATUS[0].value]: DATA_COMPUTE_STATUS[0],
      [DATA_COMPUTE_STATUS[1].value]: DATA_COMPUTE_STATUS[1],
      [DATA_COMPUTE_STATUS[2].value]: DATA_COMPUTE_STATUS[2],
      [DATA_COMPUTE_STATUS[3].value]: DATA_COMPUTE_STATUS[3],
    },
  },
  process_status_no_remove_pause: {
    list: DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE,
    map: {
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[0].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[0],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[1].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[1],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[2].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[2],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[3].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[3],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[4].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[4],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[5].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[5],
      [DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[6].value]:
        DATA_COMPUTE_STATUS_NO_REMOVE_PAUSE[6],
    },
  },
  process_status_with_remove: {
    list: DATA_COMPUTE_STATUS_WITH_REMOVE,
    map: {
      [DATA_COMPUTE_STATUS_WITH_REMOVE[0].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[0],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[1].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[1],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[2].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[2],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[3].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[3],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[4].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[4],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[5].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[5],
      [DATA_COMPUTE_STATUS_WITH_REMOVE[6].value]:
        DATA_COMPUTE_STATUS_WITH_REMOVE[6],
    },
  },
  import_status: {
    list: DATA_IMPORT_STATUS,
    map: {
      [DATA_IMPORT_STATUS[0].value]: DATA_IMPORT_STATUS[0],
      [DATA_IMPORT_STATUS[1].value]: DATA_IMPORT_STATUS[1],
      [DATA_IMPORT_STATUS[2].value]: DATA_IMPORT_STATUS[2],
      [DATA_IMPORT_STATUS[3].value]: DATA_IMPORT_STATUS[3],
    },
  },
  export_status: {
    list: DATA_EXPORT_STATUS,
    map: {
      [DATA_EXPORT_STATUS[0].value]: DATA_EXPORT_STATUS[0],
      [DATA_EXPORT_STATUS[1].value]: DATA_EXPORT_STATUS[1],
      [DATA_EXPORT_STATUS[2].value]: DATA_EXPORT_STATUS[2],
      [DATA_EXPORT_STATUS[3].value]: DATA_EXPORT_STATUS[3],
    },
  },
  promotion_code_status: {
    list: DATA_STATUS_PROMOTION_CODE,
    map: {
      [DATA_STATUS_PROMOTION_CODE[0].value]: DATA_STATUS_PROMOTION_CODE[0],
      [DATA_STATUS_PROMOTION_CODE[1].value]: DATA_STATUS_PROMOTION_CODE[1],
      [DATA_STATUS_PROMOTION_CODE[2].value]: DATA_STATUS_PROMOTION_CODE[2],
      [DATA_STATUS_PROMOTION_CODE[3].value]: DATA_STATUS_PROMOTION_CODE[3],
    },
  },
  pool_status: {
    list: DATA_STATUS_PROMOTION_POOL,
    map: {
      [DATA_STATUS_PROMOTION_POOL[0].value]: DATA_STATUS_PROMOTION_POOL[0],
      [DATA_STATUS_PROMOTION_POOL[1].value]: DATA_STATUS_PROMOTION_POOL[1],
      [DATA_STATUS_PROMOTION_POOL[2].value]: DATA_STATUS_PROMOTION_POOL[2],
      [DATA_STATUS_PROMOTION_POOL[3].value]: DATA_STATUS_PROMOTION_POOL[3],
      [DATA_STATUS_PROMOTION_POOL[4].value]: DATA_STATUS_PROMOTION_POOL[4],
    },
  },
  process_status_attribute: {
    list: DATA_COMPUTE_STATUS_ATTIRBUTE,
    map: {
      [DATA_COMPUTE_STATUS_ATTIRBUTE[0].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[0],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[1].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[1],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[2].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[2],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[3].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[3],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[4].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[4],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[5].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[5],
      [DATA_COMPUTE_STATUS_ATTIRBUTE[6].value]:
        DATA_COMPUTE_STATUS_ATTIRBUTE[6],
    },
  },
  update_method: {
    list: DATA_SEGMENT_UPDATE_METHOD,
    map: {
      [DATA_SEGMENT_UPDATE_METHOD[0].value]: DATA_SEGMENT_UPDATE_METHOD[0],
      [DATA_SEGMENT_UPDATE_METHOD[1].value]: DATA_SEGMENT_UPDATE_METHOD[1],
    },
  },
  status_story: {
    list: DATA_STATUS_STORY,
    map: {
      [DATA_STATUS_STORY[0].value]: DATA_STATUS_STORY[0],
      [DATA_STATUS_STORY[1].value]: DATA_STATUS_STORY[1],
      [DATA_STATUS_STORY[2].value]: DATA_STATUS_STORY[2],
      [DATA_STATUS_STORY[3].value]: DATA_STATUS_STORY[3],
      [DATA_STATUS_STORY[4].value]: DATA_STATUS_STORY[4],
      [DATA_STATUS_STORY[5].value]: DATA_STATUS_STORY[5],
      [DATA_STATUS_STORY[6].value]: DATA_STATUS_STORY[6],
      [DATA_STATUS_STORY[7].value]: DATA_STATUS_STORY[7],
      [DATA_STATUS_STORY[8].value]: DATA_STATUS_STORY[8],
    },
  },
  zone_type: {
    list: DATA_ZONE_TYPE,
    map: {
      [DATA_ZONE_TYPE[0].value]: DATA_ZONE_TYPE[0],
      [DATA_ZONE_TYPE[1].value]: DATA_ZONE_TYPE[1],
    },
  },
  trigger_type: {
    list: DATA_TRIGGER_TYPE,
    map: {
      [DATA_TRIGGER_TYPE[0].value]: DATA_TRIGGER_TYPE[0],
      [DATA_TRIGGER_TYPE[1].value]: DATA_TRIGGER_TYPE[1],
    },
  },
  compution_trigger_type: {
    list: DATA_COMPUTION_TRIGGER_TYPE,
    map: {
      [DATA_COMPUTION_TRIGGER_TYPE[0].value]: DATA_COMPUTION_TRIGGER_TYPE[0],
      [DATA_COMPUTION_TRIGGER_TYPE[1].value]: DATA_COMPUTION_TRIGGER_TYPE[1],
    },
  },
  error_info_comp_history_bo: {
    list: ERROR_INFO_COMPUTION_HISTORY_BO,
    map: {
      [ERROR_INFO_COMPUTION_HISTORY_BO[0].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[0],
      [ERROR_INFO_COMPUTION_HISTORY_BO[1].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[1],
      [ERROR_INFO_COMPUTION_HISTORY_BO[2].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[2],
      [ERROR_INFO_COMPUTION_HISTORY_BO[3].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[3],
      [ERROR_INFO_COMPUTION_HISTORY_BO[4].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[4],
      [ERROR_INFO_COMPUTION_HISTORY_BO[5].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[5],
      [ERROR_INFO_COMPUTION_HISTORY_BO[6].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[6],
      [ERROR_INFO_COMPUTION_HISTORY_BO[7].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[7],
      [ERROR_INFO_COMPUTION_HISTORY_BO[8].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[8],
      [ERROR_INFO_COMPUTION_HISTORY_BO[9].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[9],
      [ERROR_INFO_COMPUTION_HISTORY_BO[10].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[10],
      [ERROR_INFO_COMPUTION_HISTORY_BO[11].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[11],
      [ERROR_INFO_COMPUTION_HISTORY_BO[12].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[12],
      [ERROR_INFO_COMPUTION_HISTORY_BO[13].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[13],
      [ERROR_INFO_COMPUTION_HISTORY_BO[14].value]:
        ERROR_INFO_COMPUTION_HISTORY_BO[14],
    },
  },

  error_info_comp_history: {
    list: ERROR_INFO_COMPUTION_HISTORY,
    map: {
      [ERROR_INFO_COMPUTION_HISTORY[0].value]: ERROR_INFO_COMPUTION_HISTORY[0],
      [ERROR_INFO_COMPUTION_HISTORY[1].value]: ERROR_INFO_COMPUTION_HISTORY[1],
      [ERROR_INFO_COMPUTION_HISTORY[2].value]: ERROR_INFO_COMPUTION_HISTORY[2],
      [ERROR_INFO_COMPUTION_HISTORY[3].value]: ERROR_INFO_COMPUTION_HISTORY[3],
      [ERROR_INFO_COMPUTION_HISTORY[4].value]: ERROR_INFO_COMPUTION_HISTORY[4],
      [ERROR_INFO_COMPUTION_HISTORY[5].value]: ERROR_INFO_COMPUTION_HISTORY[5],
      [ERROR_INFO_COMPUTION_HISTORY[6].value]: ERROR_INFO_COMPUTION_HISTORY[6],
      [ERROR_INFO_COMPUTION_HISTORY[7].value]: ERROR_INFO_COMPUTION_HISTORY[7],
      [ERROR_INFO_COMPUTION_HISTORY[8].value]: ERROR_INFO_COMPUTION_HISTORY[8],
      [ERROR_INFO_COMPUTION_HISTORY[9].value]: ERROR_INFO_COMPUTION_HISTORY[9],
      [ERROR_INFO_COMPUTION_HISTORY[10].value]:
        ERROR_INFO_COMPUTION_HISTORY[10],
      [ERROR_INFO_COMPUTION_HISTORY[11].value]:
        ERROR_INFO_COMPUTION_HISTORY[11],
      [ERROR_INFO_COMPUTION_HISTORY[12].value]:
        ERROR_INFO_COMPUTION_HISTORY[12],
      [ERROR_INFO_COMPUTION_HISTORY[13].value]:
        ERROR_INFO_COMPUTION_HISTORY[13],
      [ERROR_INFO_COMPUTION_HISTORY[14].value]:
        ERROR_INFO_COMPUTION_HISTORY[14],
      [ERROR_INFO_COMPUTION_HISTORY[15].value]:
        ERROR_INFO_COMPUTION_HISTORY[15],
    },
  },
  error_info_comp_history_am_model: {
    list: ERROR_INFO_COMPUTION_HISTORY_AM_MODEL,
    map: {
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[0].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[0],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[1].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[1],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[2].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[2],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[3].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[3],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[4].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[4],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[5].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[5],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[6].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[6],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[7].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[7],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[8].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[8],
      [ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[9].value]:
        ERROR_INFO_COMPUTION_HISTORY_AM_MODEL[9],
    },
  },
  error_info_export_history: {
    list: ERROR_INFO_EXPORT_HISTORY,
    map: {
      [ERROR_INFO_EXPORT_HISTORY[0].value]: ERROR_INFO_EXPORT_HISTORY[0],
      [ERROR_INFO_EXPORT_HISTORY[1].value]: ERROR_INFO_EXPORT_HISTORY[1],
      [ERROR_INFO_EXPORT_HISTORY[2].value]: ERROR_INFO_EXPORT_HISTORY[2],
      [ERROR_INFO_EXPORT_HISTORY[3].value]: ERROR_INFO_EXPORT_HISTORY[3],
      [ERROR_INFO_EXPORT_HISTORY[4].value]: ERROR_INFO_EXPORT_HISTORY[4],
      [ERROR_INFO_EXPORT_HISTORY[5].value]: ERROR_INFO_EXPORT_HISTORY[5],
      [ERROR_INFO_EXPORT_HISTORY[6].value]: ERROR_INFO_EXPORT_HISTORY[6],
      [ERROR_INFO_EXPORT_HISTORY[7].value]: ERROR_INFO_EXPORT_HISTORY[7],
    },
  },
  error_info_imp_history: {
    list: ERROR_INFO_IMPORT_HISTORY,
    map: {
      [ERROR_INFO_IMPORT_HISTORY[0].value]: ERROR_INFO_IMPORT_HISTORY[0],
      [ERROR_INFO_IMPORT_HISTORY[1].value]: ERROR_INFO_IMPORT_HISTORY[1],
      [ERROR_INFO_IMPORT_HISTORY[2].value]: ERROR_INFO_IMPORT_HISTORY[2],
      [ERROR_INFO_IMPORT_HISTORY[3].value]: ERROR_INFO_IMPORT_HISTORY[3],
      [ERROR_INFO_IMPORT_HISTORY[4].value]: ERROR_INFO_IMPORT_HISTORY[4],
      [ERROR_INFO_IMPORT_HISTORY[5].value]: ERROR_INFO_IMPORT_HISTORY[5],
      [ERROR_INFO_IMPORT_HISTORY[6].value]: ERROR_INFO_IMPORT_HISTORY[6],
      [ERROR_INFO_IMPORT_HISTORY[7].value]: ERROR_INFO_IMPORT_HISTORY[7],
      [ERROR_INFO_IMPORT_HISTORY[8].value]: ERROR_INFO_IMPORT_HISTORY[8],
      [ERROR_INFO_IMPORT_HISTORY[9].value]: ERROR_INFO_IMPORT_HISTORY[9],
      [ERROR_INFO_IMPORT_HISTORY[10].value]: ERROR_INFO_IMPORT_HISTORY[10],
      [ERROR_INFO_IMPORT_HISTORY[11].value]: ERROR_INFO_IMPORT_HISTORY[11],
    },
  },
  event_attr_data_type: {
    list: DATA_EVENT_ATTR_TYPE,
    map: {
      [DATA_EVENT_ATTR_TYPE[0].value]: DATA_EVENT_ATTR_TYPE[0],
      [DATA_EVENT_ATTR_TYPE[1].value]: DATA_EVENT_ATTR_TYPE[1],
      [DATA_EVENT_ATTR_TYPE[2].value]: DATA_EVENT_ATTR_TYPE[2],
      [DATA_EVENT_ATTR_TYPE[3].value]: DATA_EVENT_ATTR_TYPE[3],
      [DATA_EVENT_ATTR_TYPE[4].value]: DATA_EVENT_ATTR_TYPE[4],
      [DATA_EVENT_ATTR_TYPE[5].value]: DATA_EVENT_ATTR_TYPE[5],
      [DATA_EVENT_ATTR_TYPE[6].value]: DATA_EVENT_ATTR_TYPE[6],
      [DATA_EVENT_ATTR_TYPE[7].value]: DATA_EVENT_ATTR_TYPE[7],
      [DATA_EVENT_ATTR_TYPE[8].value]: DATA_EVENT_ATTR_TYPE[8],
    },
  },
  bo_attribute_type: {
    list: DATA_EVENT_ATTR_TYPE,
    map: {
      [DATA_EVENT_ATTR_TYPE[0].value]: DATA_EVENT_ATTR_TYPE[0],
      [DATA_EVENT_ATTR_TYPE[1].value]: DATA_EVENT_ATTR_TYPE[1],
      [DATA_EVENT_ATTR_TYPE[2].value]: DATA_EVENT_ATTR_TYPE[2],
      [DATA_EVENT_ATTR_TYPE[3].value]: DATA_EVENT_ATTR_TYPE[3],
      [DATA_EVENT_ATTR_TYPE[4].value]: DATA_EVENT_ATTR_TYPE[4],
      [DATA_EVENT_ATTR_TYPE[5].value]: DATA_EVENT_ATTR_TYPE[5],
      [DATA_EVENT_ATTR_TYPE[6].value]: DATA_EVENT_ATTR_TYPE[6],
      [DATA_EVENT_ATTR_TYPE[7].value]: DATA_EVENT_ATTR_TYPE[7],
      [DATA_EVENT_ATTR_TYPE[8].value]: DATA_EVENT_ATTR_TYPE[8],
    },
  },
  attribute_type_BO: {
    list: DATA_TYPE_BO_ATT,
    map: {
      [DATA_TYPE_BO_ATT[0].value]: DATA_TYPE_BO_ATT[0],
      [DATA_TYPE_BO_ATT[1].value]: DATA_TYPE_BO_ATT[1],
      [DATA_TYPE_BO_ATT[2].value]: DATA_TYPE_BO_ATT[2],
      [DATA_TYPE_BO_ATT[3].value]: DATA_TYPE_BO_ATT[3],
      [DATA_TYPE_BO_ATT[4].value]: DATA_TYPE_BO_ATT[4],
      [DATA_TYPE_BO_ATT[5].value]: DATA_TYPE_BO_ATT[5],
      [DATA_TYPE_BO_ATT[6].value]: DATA_TYPE_BO_ATT[6],
      [DATA_TYPE_BO_ATT[7].value]: DATA_TYPE_BO_ATT[7],
      [DATA_TYPE_BO_ATT[8].value]: DATA_TYPE_BO_ATT[8],
      [DATA_TYPE_BO_ATT[9].value]: DATA_TYPE_BO_ATT[9],
      [DATA_TYPE_BO_ATT[10].value]: DATA_TYPE_BO_ATT[10],
      [DATA_TYPE_BO_ATT[11].value]: DATA_TYPE_BO_ATT[11],
    },
  },
  is_required: {
    list: DATA_IS_REQUIRED,
    map: {
      [DATA_IS_REQUIRED[0].value]: DATA_IS_REQUIRED[0],
      [DATA_IS_REQUIRED[1].value]: DATA_IS_REQUIRED[1],
    },
  },
  storage_type: {
    list: DATA_STORAGE_TYPE,
    map: {
      [DATA_STORAGE_TYPE[0].value]: DATA_STORAGE_TYPE[0],
      [DATA_STORAGE_TYPE[1].value]: DATA_STORAGE_TYPE[1],
      [DATA_STORAGE_TYPE[2].value]: DATA_STORAGE_TYPE[2],
    },
  },
  import_method: {
    list: DATA_IMPORT_METHOD,
    map: {
      [DATA_IMPORT_METHOD[0].value]: DATA_IMPORT_METHOD[0],
    },
  },
  storage_type_field: {
    list: STORAGE_FIELD,
    map: {
      [STORAGE_FIELD[0].value]: STORAGE_FIELD[0],
      [STORAGE_FIELD[1].value]: STORAGE_FIELD[1],
      [STORAGE_FIELD[2].value]: STORAGE_FIELD[2],
    },
  },
  source_type: {
    list: SOURCE_TYPE,
    map: {
      [SOURCE_TYPE[0].value]: SOURCE_TYPE[0],
      [SOURCE_TYPE[1].value]: SOURCE_TYPE[1],
      [SOURCE_TYPE[2].value]: SOURCE_TYPE[2],
      [SOURCE_TYPE[3].value]: SOURCE_TYPE[3],
      // [SOURCE_TYPE[4].value]: SOURCE_TYPE[4],
    },
  },
  object_attribute_type: {
    list: OBJECT_ATTRIBUTE_TYPE,
    map: {
      [OBJECT_ATTRIBUTE_TYPE[0].value]: OBJECT_ATTRIBUTE_TYPE[0],
      [OBJECT_ATTRIBUTE_TYPE[1].value]: OBJECT_ATTRIBUTE_TYPE[1],
      [OBJECT_ATTRIBUTE_TYPE[2].value]: OBJECT_ATTRIBUTE_TYPE[2],
    },
  },
  status_with_remove: {
    list: DATA_STATUS_WITH_REMOVE,
    map: {
      [DATA_STATUS_WITH_REMOVE[0].value]: DATA_STATUS_WITH_REMOVE[0],
      [DATA_STATUS_WITH_REMOVE[1].value]: DATA_STATUS_WITH_REMOVE[1],
      [DATA_STATUS_WITH_REMOVE[2].value]: DATA_STATUS_WITH_REMOVE[2],
    },
  },
  status_pool_process: {
    list: DATA_STATUS_PROCESS,
    map: {
      [DATA_STATUS_PROCESS[0].value]: DATA_STATUS_PROCESS[0],
      [DATA_STATUS_PROCESS[1].value]: DATA_STATUS_PROCESS[1],
      [DATA_STATUS_PROCESS[2].value]: DATA_STATUS_PROCESS[2],
    },
  },
  status_with_provity: {
    list: DATA_STATUS_WITH_PROVITY,
    map: {
      [DATA_STATUS_WITH_PROVITY[0].value]: DATA_STATUS_WITH_PROVITY[0],
      [DATA_STATUS_WITH_PROVITY[1].value]: DATA_STATUS_WITH_PROVITY[1],
    },
  },
  status_with_remove_custom: {
    list: DATA_STATUS_WITH_REMOVE_CUSTOM,
    map: {
      [DATA_STATUS_WITH_REMOVE_CUSTOM[0].value]:
        DATA_STATUS_WITH_REMOVE_CUSTOM[0],
      [DATA_STATUS_WITH_REMOVE_CUSTOM[1].value]:
        DATA_STATUS_WITH_REMOVE_CUSTOM[1],
      [DATA_STATUS_WITH_REMOVE_CUSTOM[2].value]:
        DATA_STATUS_WITH_REMOVE_CUSTOM[2],
    },
  },
  status_with_archived: {
    list: DATA_STATUS_WITH_ARCHIVED,
    map: {
      [DATA_STATUS_WITH_ARCHIVED[0].value]: DATA_STATUS_WITH_ARCHIVED[0],
      [DATA_STATUS_WITH_ARCHIVED[1].value]: DATA_STATUS_WITH_ARCHIVED[1],
      [DATA_STATUS_WITH_ARCHIVED[2].value]: DATA_STATUS_WITH_ARCHIVED[2],
    },
  },
  status_delivery_log: {
    list: DATA_DELIVERY_LOG_STATUS,
    map: {
      [DATA_DELIVERY_LOG_STATUS[0].value]: DATA_DELIVERY_LOG_STATUS[0],
      [DATA_DELIVERY_LOG_STATUS[1].value]: DATA_DELIVERY_LOG_STATUS[1],
      [DATA_DELIVERY_LOG_STATUS[2].value]: DATA_DELIVERY_LOG_STATUS[2],
      [DATA_DELIVERY_LOG_STATUS[3].value]: DATA_DELIVERY_LOG_STATUS[3],
      [DATA_DELIVERY_LOG_STATUS[4].value]: DATA_DELIVERY_LOG_STATUS[4],
    },
  },
  analytic_model_type: {
    list: [
      ANALYTIC_MODAL_TYPE[AM_TYPE.EVENT_ORIENTED],
      ANALYTIC_MODAL_TYPE[AM_TYPE.OBJECT_ORIENTED],
      ANALYTIC_MODAL_TYPE[AM_TYPE.CUSTOMER_PROFILE],
      ANALYTIC_MODAL_TYPE[AM_TYPE.VISITOR_PROFILE],
    ],
    map: ANALYTIC_MODAL_TYPE,
  },

  field_type: {
    list: DATA_FIELD_TYPE,
    map: {
      [DATA_FIELD_TYPE[0].value]: DATA_FIELD_TYPE[0],
      [DATA_FIELD_TYPE[1].value]: DATA_FIELD_TYPE[1],
      [DATA_FIELD_TYPE[2].value]: DATA_FIELD_TYPE[2],
      // [DATA_FIELD_LIST[2].value]: DATA_FIELD[0],
      // [DATA_FIELD_LIST[3].value]: DATA_FIELD[1],
    },
  },
  treat_as_field_list_table: {
    list: DATA_FIELD_TREAT_AS_TABLE,
    map: {
      [DATA_FIELD_TREAT_AS_TABLE[0].value]: DATA_FIELD_TREAT_AS_TABLE[0],
      [DATA_FIELD_TREAT_AS_TABLE[1].value]: DATA_FIELD_TREAT_AS_TABLE[1],
      [DATA_FIELD_TREAT_AS_TABLE[2].value]: DATA_FIELD_TREAT_AS_TABLE[0],
      [DATA_FIELD_TREAT_AS_TABLE[3].value]: DATA_FIELD_TREAT_AS_TABLE[1],
      [DATA_FIELD_TREAT_AS_TABLE[4].value]: DATA_FIELD_TREAT_AS_TABLE[4],
      [DATA_FIELD_TREAT_AS_TABLE[5].value]: DATA_FIELD_TREAT_AS_TABLE[5],
    },
  },
  data_type_field: {
    list: DATA_TYPE_FIELDS,
    map: {
      [DATA_TYPE_FIELDS[0].value]: DATA_TYPE_FIELDS[0],
      [DATA_TYPE_FIELDS[1].value]: DATA_TYPE_FIELDS[1],
      [DATA_TYPE_FIELDS[2].value]: DATA_TYPE_FIELDS[2],
      [DATA_TYPE_FIELDS[3].value]: DATA_TYPE_FIELDS[3],
      [DATA_TYPE_FIELDS[4].value]: DATA_TYPE_FIELDS[4],
    },
  },
  validate_decrypt: {
    list: DATA_DECRYPT_VALIDATION_METHOD,
    map: {
      [DATA_DECRYPT_VALIDATION_METHOD[0].value]:
        DATA_DECRYPT_VALIDATION_METHOD[0],
    },
  },

  pre_defined: {
    list: BUSINESS_OBJECT,
    map: {
      [BUSINESS_OBJECT[0].value]: BUSINESS_OBJECT[0],
      [BUSINESS_OBJECT[1].value]: BUSINESS_OBJECT[1],
      [BUSINESS_OBJECT[2].value]: BUSINESS_OBJECT[2],
    },
  },
  error_info_segment: {
    list: ERROR_INFO_SEG_COL,
    map: {
      [ERROR_INFO_SEG_COL[0].value]: ERROR_INFO_SEG_COL[0],
      [ERROR_INFO_SEG_COL[1].value]: ERROR_INFO_SEG_COL[1],
      [ERROR_INFO_SEG_COL[2].value]: ERROR_INFO_SEG_COL[2],
      [ERROR_INFO_SEG_COL[3].value]: ERROR_INFO_SEG_COL[3],
      [ERROR_INFO_SEG_COL[4].value]: ERROR_INFO_SEG_COL[4],
      [ERROR_INFO_SEG_COL[5].value]: ERROR_INFO_SEG_COL[5],
      [ERROR_INFO_SEG_COL[6].value]: ERROR_INFO_SEG_COL[6],
      [ERROR_INFO_SEG_COL[7].value]: ERROR_INFO_SEG_COL[7],
      [ERROR_INFO_SEG_COL[8].value]: ERROR_INFO_SEG_COL[8],
      [ERROR_INFO_SEG_COL[9].value]: ERROR_INFO_SEG_COL[9],
      [ERROR_INFO_SEG_COL[10].value]: ERROR_INFO_SEG_COL[10],
      [ERROR_INFO_SEG_COL[11].value]: ERROR_INFO_SEG_COL[11],
    },
  },
  object_type_bo: {
    list: DATA_OBJECT_TYPE_BO,
    map: {
      [DATA_OBJECT_TYPE_BO[0].value]: DATA_OBJECT_TYPE_BO[0],
      [DATA_OBJECT_TYPE_BO[1].value]: DATA_OBJECT_TYPE_BO[1],
    },
  },
  attribute_type_computation_BO: {
    list: DATA_TYPE_COMPUTATION_BO_ATT,
    map: {
      [DATA_TYPE_COMPUTATION_BO_ATT[0].value]: DATA_TYPE_COMPUTATION_BO_ATT[0],
      [DATA_TYPE_COMPUTATION_BO_ATT[1].value]: DATA_TYPE_COMPUTATION_BO_ATT[1],
      [DATA_TYPE_COMPUTATION_BO_ATT[2].value]: DATA_TYPE_COMPUTATION_BO_ATT[2],
      [DATA_TYPE_COMPUTATION_BO_ATT[3].value]: DATA_TYPE_COMPUTATION_BO_ATT[3],
      [DATA_TYPE_COMPUTATION_BO_ATT[4].value]: DATA_TYPE_COMPUTATION_BO_ATT[4],
      [DATA_TYPE_COMPUTATION_BO_ATT[5].value]: DATA_TYPE_COMPUTATION_BO_ATT[5],
      [DATA_TYPE_COMPUTATION_BO_ATT[6].value]: DATA_TYPE_COMPUTATION_BO_ATT[6],
      [DATA_TYPE_COMPUTATION_BO_ATT[7].value]: DATA_TYPE_COMPUTATION_BO_ATT[7],
      [DATA_TYPE_COMPUTATION_BO_ATT[8].value]: DATA_TYPE_COMPUTATION_BO_ATT[8],
      [DATA_TYPE_COMPUTATION_BO_ATT[9].value]: DATA_TYPE_COMPUTATION_BO_ATT[9],
      [DATA_TYPE_COMPUTATION_BO_ATT[10].value]:
        DATA_TYPE_COMPUTATION_BO_ATT[10],
      [DATA_TYPE_COMPUTATION_BO_ATT[11].value]:
        DATA_TYPE_COMPUTATION_BO_ATT[11],
    },
  },
};
export function getStatusIdentifier(value) {
  // console.log({ itemTypeId, getSegmentTypeLabel });
  // console.log('itemTypeId', itemTypeId, DATA_INFO.item_type_id.map[itemTypeId]);
  return DATA_INFO.identifier_status.map[value]
    ? getTranslateMessage(
        DATA_INFO.identifier_status.map[value].translateCode,
        DATA_INFO.identifier_status.map[value].label,
      )
    : '--';
}
export function getChannelUnsubscribe(value) {
  // console.log({ itemTypeId, getSegmentTypeLabel });
  // console.log('itemTypeId', itemTypeId, DATA_INFO.item_type_id.map[itemTypeId]);
  return DATA_INFO.unsubscribe_channel.map[value]
    ? getTranslateMessage(
        DATA_INFO.unsubscribe_channel.map[value].translateCode,
        DATA_INFO.unsubscribe_channel.map[value].label,
      )
    : '--';
}

export function getSegmentTypeLabel(itemTypeId) {
  // console.log({ itemTypeId, getSegmentTypeLabel });
  // console.log('itemTypeId', itemTypeId, DATA_INFO.item_type_id.map[itemTypeId]);
  return DATA_INFO.item_type_id.map[itemTypeId]
    ? getTranslateMessage(
        DATA_INFO.item_type_id.map[itemTypeId].translateCode,
        DATA_INFO.item_type_id.map[itemTypeId].label,
      )
    : '--';
}

export function getStorageTypeLabel(storageType) {
  return DATA_INFO.storage_type.map[storageType]
    ? getTranslateMessage(
        DATA_INFO.storage_type.map[storageType].translateCode,
        DATA_INFO.storage_type.map[storageType].label,
      )
    : '--';
}

export function getUpdateMethodLabel(method) {
  return DATA_INFO.update_method.map[method]
    ? getTranslateMessage(
        DATA_INFO.update_method.map[method].translateCode,
        DATA_INFO.update_method.map[method].label,
      )
    : '--';
}
export function getCollectionTypeLabel(itemTypeId) {
  // console.log('itemTypeId', itemTypeId, DATA_INFO.item_type_id.map[itemTypeId]);
  return DATA_INFO.item_type_id.map[itemTypeId]
    ? DATA_INFO.item_type_id.map[itemTypeId].label
    : '--';
}

export function getstorageTypeLabel(storageType) {
  // console.log('itemTypeId', itemTypeId, DATA_INFO.item_type_id.map[itemTypeId]);
  return DATA_INFO.storage_type.map[storageType]
    ? DATA_INFO.storage_type.map[storageType].label
    : '--';
}

export function getImportMethodLabel(method) {
  return DATA_INFO.import_method.map[method]
    ? getTranslateMessage(
        DATA_INFO.import_method.map[method].translateCode,
        DATA_INFO.import_method.map[method].label,
      )
    : '--';
}

export function getLabelImportHistoryErrorInfo(errorCode) {
  return DATA_INFO.error_info_imp_history.map[errorCode]
    ? getTranslateMessage(
        DATA_INFO.error_info_imp_history.map[errorCode].translateCode,
        DATA_INFO.error_info_imp_history.map[errorCode].label,
      )
    : '--';
}
export function getLabelExportHistoryErrorInfo(errorCode) {
  return DATA_INFO.error_info_export_history.map[errorCode]
    ? getTranslateMessage(
        DATA_INFO.error_info_export_history.map[errorCode].translateCode,
        DATA_INFO.error_info_export_history.map[errorCode].label,
      )
    : getTranslateMessage(
        DATA_INFO.error_info_export_history.map.unknown_error.translateCode,
        DATA_INFO.error_info_export_history.map.unknown_error.label,
      );
}
export function getLabelComputation(triggerType) {
  return DATA_INFO.compution_trigger_type.map[triggerType]
    ? getTranslateMessage(
        DATA_INFO.compution_trigger_type.map[triggerType].translateCode,
        DATA_INFO.compution_trigger_type.map[triggerType].label,
      )
    : '--';
  // let label = {
  //   // label: '',
  //   // name: '', // FE used only
  // };
  // if (triggerType === 'client') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTE_TRIGGER_BY_USER,
  //       'User',
  //     ),
  //   };
  // } else if (triggerType === 'schedule') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTE_TRIGGER_BY_SYSTEM,
  //       'System',
  //     ),
  //   };
  // }
  // return label;
}
export function getLabelSegmentErrorInfo(errorCode) {
  return DATA_INFO.error_info_segment.map[errorCode]
    ? getTranslateMessage(
        DATA_INFO.error_info_segment.map[errorCode].translateCode,
        DATA_INFO.error_info_segment.map[errorCode].label,
      )
    : '--';
}
export function getLabelComputationErrorBOInfo(errorCode) {
  return DATA_INFO.error_info_comp_history_bo.map[errorCode]
    ? getTranslateMessage(
        DATA_INFO.error_info_comp_history_bo.map[errorCode].translateCode,
        DATA_INFO.error_info_comp_history_bo.map[errorCode].label,
      )
    : getTranslateMessage(
        DATA_INFO.error_info_comp_history_bo.map.UNKNOWN_ERROR.translateCode,
        DATA_INFO.error_info_comp_history_bo.map.UNKNOWN_ERROR.label,
      );
}
export function getLabelComputationErrorInfoAM(errorCode) {
  return (
    DATA_INFO.error_info_comp_history_am_model.map[errorCode] &&
    getTranslateMessage(
      DATA_INFO.error_info_comp_history_am_model.map[errorCode].translateCode,
      DATA_INFO.error_info_comp_history_am_model.map[errorCode].label,
    )
  );
}
export function getLabelComputationErrorInfo(errorCode) {
  return DATA_INFO.error_info_comp_history.map[errorCode]
    ? getTranslateMessage(
        DATA_INFO.error_info_comp_history.map[errorCode].translateCode,
        DATA_INFO.error_info_comp_history.map[errorCode].label,
      )
    : getTranslateMessage(
        DATA_INFO.error_info_comp_history.map.UNKNOWN_ERROR.translateCode,
        DATA_INFO.error_info_comp_history.map.UNKNOWN_ERROR.label,
      );
  // let label = {
  //   // label: '',
  //   // name: '', // FE used only
  // };
  // if (errorCode === 'SERVER_ERROR') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_SERVER_ERROR,
  //       'Error info: Server error',
  //     ),
  //   };
  // } else if (errorCode === 'INVALID_META') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_INVALID_META,
  //       'Error info: Invalid meta data',
  //     ),
  //   };
  // } else if (errorCode === 'TIME_OUT') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_TIME_OUT,
  //       'Error info: Request time out',
  //     ),
  //   };
  // } else if (errorCode === 'SEGMENT_NOT_FOUND') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_SEGMENT_NOT_FOUND,
  //       'Error info: Computing-object not found',
  //     ),
  //   };
  // } else if (errorCode === 'BUILD_SQL_ERROR') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_BUILD_SQL_ERROR,
  //       'Error info: SQL executing-error',
  //     ),
  //   };
  // } else if (errorCode === 'SYNC_DW_ERROR') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_SYNC_DW_ERROR,
  //       'Error info: DW sync-error',
  //     ),
  //   };
  // } else if (errorCode === 'BUILD_PARAM_ERROR') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_BUILD_PARAM_ERROR,
  //       'Error info: Invalid parameter ',
  //     ),
  //   };
  // } else if (errorCode === 'UNKNOWN_ERROR') {
  //   label = {
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY._COMPUTATION_UNKNOWN_ERROR,
  //       'Error info: Unknown error',
  //     ),
  //   };
  // }
  // return label;
}
export function getLabelDeliveryStatus(status) {
  return DATA_INFO.status_delivery_log.map[status]
    ? getTranslateMessage(
        DATA_INFO.status_delivery_log.map[status].translateCode,
        DATA_INFO.status_delivery_log.map[status].label,
      )
    : '--';
}
export function getLabelCodeStatus(status) {
  return (
    DATA_INFO.promotion_code_status.map[status] &&
    getTranslateMessage(
      DATA_INFO.promotion_code_status.map[status].translateCode,
      DATA_INFO.promotion_code_status.map[status].label,
    )
  );
}
export const MAP_OPTION_INPUT_VIA = {
  singleLineText: {
    name: 'singleLineText',
    label: 'Single-Line Text',
    translateCode: 'Single-Line Text',
    value: 'singleLineText',
  },
  multiText: {
    name: 'multiLineText',
    label: 'Multi-Line Text',
    translateCode: 'Multi-Line Text',
    value: 'multiLineText',
  },
  singleCheckbox: {
    name: 'singleCheckbox',
    label: 'Single Checkbox',
    translateCode: 'Single Checkbox',
    value: 'singleCheckbox',
  },
  radio: {
    name: 'radio',
    label: 'Radio',
    translateCode: 'Radio',
    value: 'radio',
  },
  dropdown: {
    name: 'dropdown',
    label: 'Dropdown',
    translateCode: 'Dropdown',
    value: 'dropdown',
  },
  number: {
    name: 'number',
    label: 'Number',
    translateCode: TRANSLATE_KEY._DATA_TYPE_NUMBER,
    value: 'number',
  },
  datePicker: {
    name: 'datePicker',
    label: 'Date picker',
    translateCode: 'Date picker',
    value: 'datePicker',
  },
  '': {
    name: 'singleLineText',
    label: 'Single-Line Text',
    translateCode: 'Single-Line Text',
    value: 'singleLineText',
  },
};

export const OPTION_INPUT_VIA = [
  MAP_OPTION_INPUT_VIA.singleLineText,
  MAP_OPTION_INPUT_VIA.datePicker,
  MAP_OPTION_INPUT_VIA.dropdown,
  MAP_OPTION_INPUT_VIA.number,
  MAP_OPTION_INPUT_VIA.radio,
  MAP_OPTION_INPUT_VIA.multiText,
  MAP_OPTION_INPUT_VIA.singleCheckbox,
];
export function getLabelDescriptionComputation(status) {
  return (
    MAP_DESCRIPTION_COMPUTATION_STATUS[status] &&
    getTranslateMessage(
      MAP_DESCRIPTION_COMPUTATION_STATUS[status].translateCode,
      MAP_DESCRIPTION_COMPUTATION_STATUS[status].label,
    )
  );
}
export const MAP_DESCRIPTION_COMPUTATION_STATUS = {
  1: {
    label: 'Journey is in progress',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_COMPUTING,
  },
  2: {
    label:
      'The campaign is temporarily paused but still allowing new audiences enter',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_SUCCESS,
  },
  3: {
    label: 'Journey is deleted',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_UNSUCCESS,
  },
  4: {
    label: 'The campaign is about to run',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_WAITING_INITIAL,
  },
  5: {
    label: 'The campaign is on hold and does not allow new audiences enter',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_WAITING,
  },
  6: {
    label: 'An error occurred during journey processing.',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_READY_TO_USE,
  },
  7: {
    label: 'Journey is coming to processing time',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_WAITING_INITIAL,
  },
  9: {
    label: 'Object is waiting for the computation schedule',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COMPUTE_STATUS_SUCCESS_BUT_LIMIT,
  },
};

export function getLabelDescriptionJourney(status) {
  return (
    MAP_DESCRIPTION_JOURNEY_STATUS[status] &&
    getTranslateMessage(
      MAP_DESCRIPTION_JOURNEY_STATUS[status].translateCode,
      MAP_DESCRIPTION_JOURNEY_STATUS[status].label,
    )
  );
}

export const JOURNEY_STATUS = {
  ACTIVE: 1,
  PAUSED: 2,
  REMOVED: 3,
  SCHEDULED: 4,
  FROZEN: 5,
  ERROR: 6,
  CLOSED: 7,
  ABORTED: 8,
  IN_DESIGN: 9,
  REACTIVED: 10,
};

export const MAP_DESCRIPTION_JOURNEY_STATUS = {
  [JOURNEY_STATUS.ACTIVE]: {
    label: 'Journey is in progress',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_ACTIVE,
  },
  [JOURNEY_STATUS.PAUSED]: {
    label:
      'The campaign is temporarily paused but still allowing new audiences enter',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_PAUSED,
  },
  [JOURNEY_STATUS.REMOVED]: {
    label: 'Journey is deleted',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_REMOVED,
  },
  [JOURNEY_STATUS.SCHEDULED]: {
    label: 'The campaign is about to run',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_SCHEDULED,
  },
  [JOURNEY_STATUS.FROZEN]: {
    label: 'The campaign is on hold and does not allow new audiences enter',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_FROZEN,
  },
  [JOURNEY_STATUS.ERROR]: {
    label: 'An error occurred during journey processing.',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_ERROR,
  },
  [JOURNEY_STATUS.CLOSED]: {
    label: 'Journey is coming to processing time',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_CLOSED,
  },
  [JOURNEY_STATUS.ABORTED]: {
    label: 'Journey is forced to a complete stop',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_ABORTED,
  },
  [JOURNEY_STATUS.IN_DESIGN]: {
    label: 'Journey is in design, not ready to run',
    translateCode: TRANSLATE_KEY._USER_GUIDE_JOURNEY_STATUS_IN_DESIGN,
  },
};

export function getLabelDescriptionSegment(status) {
  return (
    MAP_DESCRIPTION_SEGMENTS_STATUS[status] &&
    getTranslateMessage(
      MAP_DESCRIPTION_SEGMENTS_STATUS[status].translateCode,
      MAP_DESCRIPTION_SEGMENTS_STATUS[status].label,
    )
  );
}
export const MAP_DESCRIPTION_SEGMENTS_STATUS = {
  1: {
    label: 'Segment can be interval build and can be used by other objects',
    translateCode: TRANSLATE_KEY._USER_GUIDE_SEG_STATUS_ENABLE,
  },
  2: {
    label:
      'Segment has stopped interval building. Segment can be used by other objects but not new data',
    translateCode: TRANSLATE_KEY._USER_GUIDE_SEG_STATUS_DISABLE,
  },
  4: {
    label:
      'Segment has stopped interval build and cannot be used by other objects',
    translateCode: TRANSLATE_KEY._USER_GUIDE_SEG_STATUS_ARCHIVE,
  },
};
export function getLabelDescriptionCollection(status) {
  return (
    MAP_DESCRIPTION_COLLECTION_STATUS[status] &&
    getTranslateMessage(
      MAP_DESCRIPTION_COLLECTION_STATUS[status].translateCode,
      MAP_DESCRIPTION_COLLECTION_STATUS[status].label,
    )
  );
}

export function getLabelWithRemoveStatus(status) {
  return (
    DATA_INFO.status_with_remove.map[status] &&
    getTranslateMessage(
      DATA_INFO.status_with_remove.map[status].translateCode,
      DATA_INFO.status_with_remove.map[status].label,
    )
  );
}
export function getLabelWithArchiveStatus(status) {
  return (
    DATA_INFO.status_with_archived.map[status] &&
    getTranslateMessage(
      DATA_INFO.status_with_archived.map[status].translateCode,
      DATA_INFO.status_with_archived.map[status].label,
    )
  );
}
export const MAP_DESCRIPTION_COLLECTION_STATUS = {
  1: {
    label: 'Segment can be interval build and can be used by other objects',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COL_STATUS_ENABLE,
  },
  2: {
    label:
      'Segment has stopped interval building. Segment can be used by other objects but not new data',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COL_STATUS_DISABLE,
  },
  4: {
    label:
      'Segment has stopped interval build and cannot be used by other objects',
    translateCode: TRANSLATE_KEY._USER_GUIDE_COL_STATUS_ARCHIVE,
  },
};
export function getLabelStateSchedule(state) {
  return (
    DATA_INFO.state.map[state] &&
    getTranslateMessage(
      DATA_INFO.state.map[state].translateCode,
      DATA_INFO.state.map[state].label,
    )
  );
}

/* eslint-disable no-undef */
/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import {
  callApiWithAuth,
  callApiWithDownload,
  callApiWithOnlyToken,
  callApiMediaTemplateWithAuthV2,
  callApiWithAuthV2,
  callApiWithAuthAndCancel,
} from '../utils/request';
import axios from 'axios';

// import { safeParse } from 'utils/common';
import { ENDPOINT, URL_HISTORY } from '../../config/common';
import { getPortalId } from '../utils/web/cookie';
import { getUserLocaleLanguage } from '../utils/web/portalSetting';
import {
  getDataInsight,
  getEntriesV2,
  getEntriesWithTotalV2,
  getEntryAndMeta,
  getEntryV2,
} from './utils';

const apiPid = getPortalId();
const API = {
  sourceGetListCampaignSameStory: undefined,
  sourceGetListCampaignOtherStory: undefined,
  thirdParty: {
    getReponseSchema: async ({ catalogCode, signal }) => {
      // prettier-ignore
      const url = `${ENDPOINT.apiV1}/automation/stories/3rd-response-schema?catalog_code=${catalogCode}`;

      const res = await callApiWithAuthV2({
        method: 'GET',
        domain: URL_HISTORY,
        endpoint: url,
        others: { signal },
      });

      return getEntriesV2(res, []);
    },
  },
  quicktest: {
    getDetail: params => {
      const url = `${ENDPOINT.automationV2}/stories/dashboard/testing-campaign`;
      return callApiWithAuth(url, 'POST', params).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    getLastResultExperiment: params => {
      const url = `${ENDPOINT.toolkitV2}/experiments/last/${
        params.objectType
      }/${params.objectId}`;
      return callApiWithAuth(url, 'GET', params).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    updateExperiment: params => {
      const url = `${ENDPOINT.toolkitV2}/experiments/update/${
        params.objectType
      }/${params.objectId}`;
      return callApiWithAuth(url, 'PUT', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
  },
  data: {
    getAttrsCustomInput: params => {
      return callApiWithAuth(
        `${ENDPOINT.itemsV2}/attributes/${params.itemTypeId}/custom-input${
          params.includeStatus
            ? `?include_status=${JSON.stringify(params.includeStatus)}`
            : ``
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getList: params => {
      const url = `${ENDPOINT.automationV2_1}/stories/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListV2_1: params => {
      const url = `${ENDPOINT.automationV2_1}/stories/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListTotal: params => {
      const url = `${ENDPOINT.automationV2_1}/stories/performance/total`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    deleteWithCondition: params => {
      const url = `${ENDPOINT.automationV2_1}/operation/remove/stories/-1009`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => getEntriesV2(res, []));
    },

    // deleteWithCondition: params => {
    //   const url = `${ENDPOINT.toolkitV2}/action/STORIES/-1009/remove`;
    //   return callApiWithAuth(url, 'POST', params.data).then(res =>
    //     getEntriesV2(res, []),
    //   );
    // },
    updateStatus: params => {
      const url = `${ENDPOINT.automationV2}/stories/update-status`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        // console.log('res', res);
        return getEntryAndMeta(res, []);
      });
    },
    updateStatusOwner: params => {
      const url = `${ENDPOINT.automationV2}/stories/update-status`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params.data.owner_id,
      }).then(res => {
        // console.log('res', res);
        return getEntryAndMeta(res, []);
      });
    },
    getByIds: params => {
      const url = `${ENDPOINT.automationV2}/stories/list-by-ids`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getDestinationByCatalogIds: params =>
      callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/catalog/${params.objectId}/destination`,
        'GET',
        null,
        {
          _owner_id: params._owner_id,
        },
      ).then(res => {
        return getEntryAndMeta(res, null);
      }),
    updateName: params =>
      callApiWithAuth(
        `${ENDPOINT.automationV2}/stories/rename/${params.objectId}`,
        'PUT',
        params.data,
        {
          _owner_id: params._owner_id,
        },
      ).then(res => getEntryV2(res, null)),
    getOptions: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/object/${params.extendUrl}`,
        'POST',
        params.data,
        {
          _owner_id: params._owner_id,
        },
      ).then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },

    download: params => {
      return callApiWithDownload(
        `${ENDPOINT.automationV2_1}/downloads`,
        'POST',
        params.data,
        params.config,
      );
    },
    resumeTrigger: params => {
      const url = `${ENDPOINT.automationV2_1}/scheduled-histories/resume/${
        params.storyId
      }`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        // console.log('res', res);
        return getEntryAndMeta(res, []);
      });
    },
    getNameDestinationById: params => {
      const url = `${ENDPOINT.toolkitV2}/selector/get-name/destination/${
        params.destinationId
      }`;
      return callApiWithAuth(url, 'GET', params).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
  },
  campaigns: {
    listingCampaignById: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.automationV2_1}/campaigns/listing-by-catalog-id`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    listingCampaignSameStoryById: params => {
      if (API.sourceGetListCampaignSameStory !== undefined) {
        API.sourceGetListCampaignSameStory.cancel();
      }

      API.sourceGetListCampaignSameStory = axios.CancelToken.source();

      const url = `${
        ENDPOINT.automationV2_1
      }/campaigns/listing-by-catalog-id-v2`;

      return callApiWithAuthAndCancel(
        API.sourceGetListCampaignSameStory,
        url,
        'POST',
        params.data,
      ).then(res => getEntriesWithTotalV2(res, []));
    },

    listingCampaignOtherStoryById: params => {
      if (API.sourceGetListCampaignOtherStory !== undefined) {
        API.sourceGetListCampaignOtherStory.cancel();
      }

      API.sourceGetListCampaignOtherStory = axios.CancelToken.source();

      const url = `${
        ENDPOINT.automationV2_1
      }/campaigns/listing-by-catalog-id-v2`;

      return callApiWithAuthAndCancel(
        API.sourceGetListCampaignOtherStory,
        url,
        'POST',
        params.data,
      ).then(res => getEntriesWithTotalV2(res, []));
    },
    // rename: params => {
    //   // console.log('params', params);
    //   const url = `${ENDPOINT.automationV2}/campaigns/rename`;
    //   return callApiWithAuth(url, 'PUT', params.data).then(res =>
    //     getEntriesWithTotalV2(res, []),
    //   );
    // },
    renameV2: params => {
      const url = `${ENDPOINT.automationV2}/campaigns/rename`;
      return callApiWithAuth(url, 'PUT', params.data, {
        _owner_id: params._owner_id,
      }).then(res => getEntriesWithTotalV2(res, []));
    },
    checkDuplicate: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.automationV2}/campaigns/check-duplicate-name`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    updateStatus: params => {
      const url = `${ENDPOINT.automationV2_1}/campaigns/update-status`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        // console.log('res', res);
        return getEntryAndMeta(res, []);
      });
    },
    getList: params => {
      const url = `${ENDPOINT.automationV2}/stories/${
        params.objectId
      }/campaigns`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getByIds: (params = {}) => {
      // vì dùng vào luồng cancel request nên kiểm tra điều kiệu không gọi API ở đây là hợp lý
      if (
        Object.keys(params).length === 0 ||
        !Array.isArray(params.data.campaign_ids) ||
        params.data.campaign_ids.length === 0
      ) {
        return getEntriesWithTotalV2(undefined, []);
      }
      const url = `${ENDPOINT.automationV2}/campaigns/get-by-ids`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListV2_1: params => {
      const url = `${ENDPOINT.automationV2_1}/campaigns/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListTotal: params => {
      const url = `${ENDPOINT.automationV2}/stories/${
        params.objectId
      }/campaigns/performance/total`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListGroupAttrs: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsForCampaignsListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}${
          params.storyType
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    updateName: params => {
      const url = `${ENDPOINT.automationV2}/stories/update-campaign/${
        params.objectId
      }`;
      return callApiWithAuth(url, 'PUT', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    chart: {
      getList: params => {
        const url = `${ENDPOINT.automationV2}/stories/${
          params.objectId
        }/campaigns/performance/chart`;
        return callApiWithAuth(url, 'POST', params.data).then(res =>
          getEntriesV2(res, []),
        );
      },
      getListV2_1: params => {
        const url = `${ENDPOINT.automationV2_1}/campaigns/performance/chart`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
    download: params => {
      return callApiWithDownload(
        `${ENDPOINT.automationV2_1}/downloads`,
        'POST',
        params.data,
        params.config,
      );
    },
  },
  actionsHistory: {
    getListActionHistory: params => {
      const url = `${ENDPOINT.actionsHistoryV2}/${params.data.story_id}`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        others: {
          _owner_id: params.data._owner_id,
        },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListGroupAttrsForActionHistoryListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${
          params.objectType
        }?excludeProps=${params.excludeProps}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getChartActionHistory: params => {
      const url = `${ENDPOINT.actionsHistoryV2}/chart`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        // others: {
        //   _owner_id: params.data._owner_id,
        // },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    updateScoreCard: params => {
      const url = `${ENDPOINT.settingV2}/user-dashboard-settings/upsert`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getScoreCard: params => {
      const url = `${ENDPOINT.settingV2}/user-dashboard-settings/get?type=${
        params.type
      }`;
      return callApiWithAuth(url, 'GET', params).then(res =>
        getEntriesV2(res, []),
      );
    },
    getListActionHistoryNodes: params => {
      const url = `${ENDPOINT.actionsHistoryV2}/nodes/${params.data.story_id}`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        others: {
          _owner_id: params.data._owner_id,
        },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  schedulesHistory: {
    getListSchedulesHistory: params => {
      const url = `${ENDPOINT.schedulesHistory}/${params.data.story_id}`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        others: {
          _owner_id: params.data._owner_id,
        },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getChartActionHistory: params => {
      const url = `${ENDPOINT.schedulesHistoryV2}/${params.data.story_id}/${
        params.data.processId
      }/chart`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        // others: {
        //   _owner_id: params.data._owner_id,
        // },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListGroupAttrsForSchedulesHistoryListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${
          params.objectType
        }?excludeProps=${params.excludeProps}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  variant: {
    getCustomFieldListCaresoft: (params = {}) => {
      const { destinationId, body = {} } = params;
      const searchParams = new URLSearchParams({
        'destination-id': destinationId,
      });

      const url = `${
        ENDPOINT.apiV1
      }/3rd/caresoft/list-custom-fields?${searchParams.toString()}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body,
        domain: URL_HISTORY,
      }).then(res => getEntriesWithTotalV2(res, []));
    },
    checkDuplicate: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.automationV2}/variants/check-duplicate-name`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    updateStatus: params => {
      const url = `${ENDPOINT.automationV2_1}/variants/update-status`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        // console.log('res', res);
        return getEntryAndMeta(res, []);
      });
    },
    getListV2_1: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.automationV2_1}/variants/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    getByIds: params => {
      // console.log('params', params);
      // vì dùng vào luồng cancel request nên kiểm tra điều kiệu không gọi API ở đây là hợp lý
      if (
        Object.keys(params).length === 0 ||
        !Array.isArray(params.data.variant_ids) ||
        params.data.variant_ids.length === 0
      ) {
        return getEntriesWithTotalV2(undefined, []);
      }
      const url = `${ENDPOINT.automationV2}/variants/get-by-ids`;
      // console.log('params.data', params.data);
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => getEntriesWithTotalV2(res, []));
    },
    getListByIds: params => {
      const url = `${ENDPOINT.automationV2}/variants/list-by-ids`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    chart: {
      getList: params => {
        // console.log(params);
        const url = `${ENDPOINT.automationV2_1}/variants/performance/chart`;
        return callApiWithAuth(url, 'POST', params.data).then(res =>
          getEntriesV2(res, []),
        );
      },
    },
    // getListTotal: params => {
    //   const url = `${ENDPOINT.automationV2}/stories/${
    //     params.objectId
    //   }/campaigns/performance/total`;
    //   return callApiWithAuth(url, 'POST', params.data).then(res => {
    //     return getEntriesWithTotalV2(res, []);
    //   });
    // },
    getListGroupAttrs: params =>
      callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}`,
        'GET',
        null,
      ).then(res => {
        // console.log('res', res);
        return getEntriesV2(res, null);
      }),
    getListGroupAttrsForVariantListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}${
          params.storyType
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    updateName: params => {
      const url = `${ENDPOINT.automationV2}/variants/rename`;
      return callApiWithAuth(url, 'PUT', params.data, {
        _owner_id: params._owner_id,
      }).then(res => getEntriesWithTotalV2(res, []));
    },
    previewContent: params => {
      const url = `${ENDPOINT.automationV2}/variants/${
        params.objectId
      }?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&isGetCatalogInfo=1`;
      return callApiWithOnlyToken(url, 'GET', params.data).then(res => {
        // console.log('res', res);
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  columns: {
    getListGroupAttrs: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsForJourneyListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}${
          params.storyType
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  fetch: {
    personalizationAttrs: params => {
      const url = `${ENDPOINT.toolkitV2}/groups/personalize-attributes`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    savedWhatsappTemplate: params => {
      const url = `${
        ENDPOINT.thirdParty
      }/infobip/whatsapp-template?destinationId=${params.destinationId}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  chart: {
    getList: params => {
      const url = `${ENDPOINT.automationV2}/stories/performance/chart`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListV2_1: params => {
      const url = `${ENDPOINT.automationV2_1}/stories/performance/chart`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesV2(res, []),
      );
    },
    getListZone: params => {
      const url = `${ENDPOINT.automationV2_1}/zones/performance/chart`;
      return callApiWithAuth(url, 'POST', params.data).then(res =>
        getEntriesV2(res, []),
      );
    },
  },
  node: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.automationV2}/nodes/list-by-groups?channelId=${
          params.channelId
        }`,
        'GET',
        null,
      ).then(res => {
        const temp = getEntriesV2(res, []);

        // if (+params?.channelId === 8) {
        //   temp.data[1].nodes.push({
        //     name: 'Wait for Response',
        //     code: 'WAIT_FOR_RESPONSE',
        //     actionType: 'WAIT_FOR_RESPONSE',
        //     iconUrl: '',
        //     translateCode: '_WAIT_FOR_RESPONSE',
        //   });
        // }

        return temp;
      });
    },
  },
  create: data =>
    callApiWithAuth(
      `${ENDPOINT.automationV2}/stories/create`,
      'POST',
      data,
    ).then(res => getEntriesV2(res, [{}])),
  createV2_1: params =>
    callApiWithAuth(
      `${ENDPOINT.automationV2_1}/stories/create`,
      'POST',
      params.data,
      {
        _owner_id: params._owner_id,
      },
    ).then(res => getEntriesV2(res, [{}])),
  update: params =>
    callApiWithAuth(
      `${ENDPOINT.automationV2}/stories/update/${params.objectId}`,
      'PUT',
      params.data,
    ).then(res => getEntriesV2(res, [{}])),
  updateV2_1: params =>
    callApiWithAuth(
      `${ENDPOINT.automationV2_1}/stories/update/${params.objectId}`,
      'PUT',
      params.data,
      {
        _owner_id: params._owner_id,
      },
    ).then(res => getEntriesV2(res, [{}])),
  getDetail: params =>
    callApiWithAuth(
      `${ENDPOINT.automationV2}/stories/${params.objectId}`,
      'GET',
      null,
      {
        _owner_id: params._owner_id,
      },
    ).then(res => getEntryV2(res, {})),
  updateStatus: params =>
    callApiWithAuth(
      `${ENDPOINT.automationV2}/stories/update-status`,
      'POST',
      params.data,
      {
        _owner_id: params._owner_id,
      },
    ).then(res => getEntryV2(res, {})),
  zone: {
    createZone: params => {
      const url = `${ENDPOINT.automationV2}/zones/create`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    updateZone: params => {
      const url = `${ENDPOINT.automationV2}/zones/update/${params.objectId}`;
      return callApiWithAuth(url, 'PUT', params.data, {
        _owner_id: params._owner_id,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    checkZoneCodeDuplicate: params => {
      return callApiWithAuth(
        `${ENDPOINT.automationV2_1}/zones/check-zone-code/${params.zoneCode}${
          // eslint-disable-next-line no-template-curly-in-string
          params.zone_id ? `?zone_id=${params.zone_id}` : ''
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrs: () => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/zones`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsForZoneListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/zones${params.storyType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },

    getList: params => {
      const url = `${ENDPOINT.automationV2}/zones/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListV2_1: params => {
      const url = `${ENDPOINT.automationV2_1}/zones/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getZoneById: params => {
      const url = `${ENDPOINT.automationV2}/zones/${params.objectId}`;
      return callApiWithAuth(url, 'GET', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    deleteWithCondition: params => {
      const url = `${ENDPOINT.automationV2_1}/operation/remove/ZONES/-1009`;
      return callApiWithAuth(url, 'POST', params.data, {
        _owner_id: params._owner_id,
      }).then(res => getEntriesV2(res, []));
    },
  },
  dashboard: {
    getOperationLogs: params => {
      return callApiWithAuth(
        `${ENDPOINT.automationV2}/stories/dashboard/node-performances`,
        'POST',
        params.body,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  mediaTemplate: {
    getListType: () => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.mediaTemplateTypeV1}/index`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getListByType: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.mediaTemplateV1}/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getListGallery: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.V1}/gallery/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getDetail: params => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.mediaTemplateV1}/index/${params.id}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getVersion: () => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `version/script`,
        method: 'GET',
      }).then(res => {
        return res.data;
      });
    },
    getObjectTemplate: params => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.objectTemplate}/${params.id}?object_type=${
          params.objectType
        }&public_level=${params.publicId}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
  },
  jsonTemplate: {
    getListByType: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.jsonTemplateV1}/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getListGallery: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.jsonTemplateV1}/gallery/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
  },
  emailTemplate: {
    getListType: () => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.emailTemplateTypeV1}/index`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getListByType: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.emailTemplateV1}/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getListGallery: query => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.V1}/email-template/gallery/performance${query}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getDetail: params => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `${ENDPOINT.emailTemplateV1}/index/${params.id}`,
        method: 'GET',
      }).then(res => {
        return getDataInsight(res);
      });
    },
    getVersion: () => {
      return callApiMediaTemplateWithAuthV2({
        endpoint: `version/script`,
        method: 'GET',
      }).then(res => {
        return res.data;
      });
    },
  },
  versionHistory: {
    getList: params => {
      return callApiWithAuthV2({
        endpoint: `${
          ENDPOINT.historyAutomation
        }/stories/get-list-versions?story_id=${params.storyId}`,
        method: 'POST',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListVersionHistory: params => {
      return callApiWithAuthV2({
        endpoint: `${
          ENDPOINT.historyAutomation
        }/stories/get-list-versions?story_id=${params.storyId}`,
        method: 'POST',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, {});
      });
    },
    getListGroupAttrsForVersionHistoryListing: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${
          params.objectType
        }?excludeProps=${params.excludeProps}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getDetail: params => {
      return callApiWithAuthV2({
        endpoint: `${ENDPOINT.historyAutomation}/stories/${params.objectId}/${
          params.versionId ? params.versionId : ''
        }`,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, {});
      });
    },
    getCampaigns: (params = {}) => {
      // vì dùng vào luồng cancel request nên kiểm tra điều kiệu không gọi API ở đây là hợp lý
      if (
        Object.keys(params).length === 0 ||
        !Array.isArray(params.data.campaign_ids) ||
        params.data.campaign_ids.length === 0
      ) {
        return getEntriesWithTotalV2(undefined, []);
      }

      const url = `${ENDPOINT.historyAutomation}/campaigns/get-by-ids`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getVariants: params => {
      // vì dùng vào luồng cancel request nên kiểm tra điều kiệu không gọi API ở đây là hợp lý
      if (
        Object.keys(params).length === 0 ||
        !Array.isArray(params.data.variant_ids) ||
        params.data.variant_ids.length === 0
      ) {
        return getEntriesWithTotalV2(undefined, []);
      }
      const url = `${ENDPOINT.historyAutomation}/variants/get-by-ids`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  actionHistory: {
    getList: params => {
      const { storyId, processId, data } = params;
      const url = `${ENDPOINT.actionsHistoryV2}/${storyId}/${processId}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        domain: URL_HISTORY,
        data,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getDetail: params => {
      return callApiWithAuthV2({
        endpoint: `${ENDPOINT.historyLog}/action-histories/${params.storyId}/${
          params.processId
        }`,
        method: 'POST',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, {});
      });
    },
    getListGroupAttrs: () => {
      return callApiWithAuth(
        `${
          ENDPOINT.toolkitV2
        }/info-properties/ACTION_HISTORY_DETAIL?excludeProps=is_identity`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  scheduleHistory: {
    getDetailScheduleLog: params => {
      const url = `${ENDPOINT.automationV2_1}/scheduled-histories/${
        params.scheduleId
      }?storyId=${params.storyId}`;
      return callApiWithAuth(url, 'GET', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getList: params => {
      const { storyId, processId, data } = params;
      const url = `${ENDPOINT.schedulesHistoryV2}/${storyId}/${processId}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        domain: URL_HISTORY,
        body: data,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getDetail: params => {
      return callApiWithAuthV2({
        endpoint: `${ENDPOINT.historyLog}/scheduled-histories/${
          params.storyId
        }/${params.scheduleId}`,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return res.data;
      });
    },
    getListGroupAttrs: () => {
      return callApiWithAuth(
        `${
          ENDPOINT.toolkitV2
        }/info-properties/SCHEDULE_HISTORY_PROCESS?excludeProps=is_identity`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListScheduleNodes: params => {
      const url = `${ENDPOINT.schedulesHistoryV2}/nodes/${params.storyId}/${
        params.processId
      }`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        others: {
          _owner_id: params.data._owner_id,
        },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  personalization: {
    getListCustom: () => {
      return callApiWithAuth(
        `${ENDPOINT.personalizationV1}/custom-function/listing`,
        'POST',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    create: params =>
      callApiWithAuth(
        `${ENDPOINT.personalizationV1}/custom-function/create`,
        'POST',
        params.data,
      ).then(res => getEntriesV2(res, [{}])),
    update: params =>
      callApiWithAuth(
        `${ENDPOINT.personalizationV1}/custom-function/update/${
          params.templateId
        }`,
        'PUT',
        params.data,
      ).then(res => getEntriesV2(res, [{}])),
  },
};

export default API;

import { ENDPOINT, URL_SHORT_LINK } from '../../config/common';
import {
  callApiWithAuth,
  callApiWithAuthV2,
  callApiWithoutHeaders,
} from '../utils/request';
import {
  getData,
  getEntriesV2,
  getEntriesWithTotalV2,
  getEntryV2,
} from './utils';

const LinkManagementService = {
  vendor: {
    getRows: params => {
      const { source = 'cdp', type = '2' } = params;
      const endpoint = `${
        ENDPOINT.apiPerformance
      }/vendor-account?source=${source}&type=${type}`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body: params.data,
        domain: URL_SHORT_LINK,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListAttributes: objectType =>
      callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
        'GET',
        null,
      ).then(res => getEntriesV2(res, null)),
    getListCreate: params => {
      const endpoint = `api/vendor`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'GET',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      }).then(res => {
        const data = res.data;
        const response = {
          code: data.code,
          data: data.data.rows || [],
          message: data.data.message,
        };
        return response;
      });
    },
    updatePrimary: params => {
      const { id = '', is_primary = false } = params.data;
      const endpoint = `api/vendor-account/${id}`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'PUT',
        body: params.data,
        domain: URL_SHORT_LINK,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getById: async ({ accountId }) => {
      const res = await callApiWithoutHeaders({
        endpoint: `api/vendor-account/${accountId}`,
        method: 'GET',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getData(res);
    },
    save: async ({ data, accountId }) => {
      // return new Promise(rs => {
      //   setTimeout(() => {
      //     rs({
      //       code: 200,
      //     });
      //   }, 2000);
      // });

      let endpoint = 'api/vendor-account';

      if (accountId) endpoint += `/${accountId}`;

      const res = await callApiWithoutHeaders({
        endpoint,
        method: accountId ? 'PUT' : 'POST',
        body: data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getEntryV2(res);
    },
    delete: params => {
      let endpoint = 'api/vendor-account';
      return callApiWithoutHeaders({
        endpoint,
        method: 'DELETE',
        body: params.data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },

  link: {
    getRows: params => {
      const { source = 'cdp', type = 3 } = params;
      const endpoint = `${
        ENDPOINT.performanceLink
      }?source=${source}&type=${type}`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body: params.data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListAttributes: objectType =>
      callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
        'GET',
        null,
      ).then(res => getEntriesV2(res, null)),
  },

  antsomiAccount: {
    domainDetail: async ({ domainId }) => {
      const res = await callApiWithoutHeaders({
        endpoint: `api/domain/${domainId}`,
        method: 'GET',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getData(res);
    },
    domainExists: async ({ domainUrl }) => {
      const search = new URLSearchParams();

      search.set('type', 'find-by-name');
      search.set('domain_url', domainUrl);

      const res = await callApiWithoutHeaders({
        endpoint: `api/domain?${search.toString()}`,
        method: 'GET',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      const { data } = getData(res, []);

      return data.length > 0;
    },
    checkCName: async ({ domainUrl }) => {
      const search = new URLSearchParams();

      search.set('type', 'check-cname');
      search.set('domain_url', domainUrl);

      const res = await callApiWithoutHeaders({
        endpoint: `api/domain?${search.toString()}`,
        method: 'GET',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      const { code, message } = getData(res, []);

      return { isValid: code === 200, errorMsg: message };
    },

    updatePrimaryDomain: async ({ domainId, status }) => {
      const search = new URLSearchParams();

      search.set('type', 'update-primary');

      const res = await callApiWithoutHeaders({
        endpoint: `api/domain/${domainId}/?${search.toString()}`,
        method: 'PUT',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
        body: {
          is_primary: Number(status),
        },
      });

      return getData(res, []);
    },

    removeDomain: async ({ domainId }) => {
      const endpoint = `api/domain/${domainId}`;

      const res = await callApiWithoutHeaders({
        endpoint,
        method: 'DELETE',
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getData(res, []);
    },

    getListAttributes: async ({ objectType }) => {
      const endpoint = `${ENDPOINT.toolkitV2}/info-properties/${objectType}`;

      const res = await callApiWithAuthV2({
        method: 'GET',
        endpoint,
      });

      return getEntriesV2(res, null);
    },

    getListDomain: async ({ body }) => {
      const endpoint = `${ENDPOINT.apiPerformance}/domain?source=cdp`;

      const res = await callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getEntriesWithTotalV2(res);
    },

    saveDomain: async params => {
      const { data, domainId } = params;

      // return new Promise(rs => {
      //   setTimeout(() => {
      //     rs({
      //       code: 200,
      //     });
      //   }, 2000);
      // });

      let endpoint = 'api/domain';

      if (domainId) endpoint += `/${domainId}`;

      const res = await callApiWithoutHeaders({
        endpoint,
        method: domainId ? 'PUT' : 'POST',
        body: data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      });

      return getEntryV2(res);
    },
  },
};

export default LinkManagementService;

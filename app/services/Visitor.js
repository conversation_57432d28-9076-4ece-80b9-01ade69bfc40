/* eslint-disable no-restricted-syntax */
/* eslint-disable arrow-body-style */
import { callApiWithAuth, callApiWithAuthAndCancel } from 'utils/request';
import { safeParse } from 'utils/common';
import axios from 'axios';

import { ENDPOINT } from '../../config/common';
import { getEntryV2, getEntriesV2, getEntriesWithTotalV2 } from './utils';
import { callApiWithAuthV2 } from '../utils/request';

const API = {
  sourceGetAll: undefined,

  explorer: {
    getList: params => {
      const { type = 'personas', ...restParams } = params;
      const url = `${ENDPOINT.explorerV2}/search/-1007?type=${type}`;
      if (typeof API.sourceGetAll !== typeof undefined) {
        API.sourceGetAll.cancel();
      }

      API.sourceGetAll = axios.CancelToken.source();
      return callApiWithAuthAndCancel(
        API.sourceGetAll,
        url,
        'POST',
        restParams,
      ).then(res => {
        if (res === null) {
          return null;
        }
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListNotCancel: params => {
      const url = `${ENDPOINT.explorerV2}/search/-1007`;
      return callApiWithAuth(url, 'POST', params).then(res =>
        getEntriesWithTotalV2(res, []),
      );
    },
    getListV2: params => {
      const url = `${ENDPOINT.explorerV2}/search/${
        params.itemTypeId
      }?type=personas`;
      if (typeof API.sourceGetAll !== typeof undefined) {
        API.sourceGetAll.cancel();
      }
      API.sourceGetAll = axios.CancelToken.source();
      return callApiWithAuthAndCancel(
        API.sourceGetAll,
        url,
        'POST',
        params,
      ).then(res => {
        if (res === null) {
          return null;
        }
        return getEntriesWithTotalV2(res, []);
      });
    },
    getById: params => {
      return callApiWithAuth(
        `${ENDPOINT.userExplorerV2}/visitor/${params.id}/profile?columns=${
          params.columns
        }&productAttrs=${params.productAttrs}&decryptFields=${
          params.decryptFields
        }`,
        'GET',
        null,
      ).then(res => {
        const customer = getEntryV2(res, {});
        return customer;
      });
    },
  },

  filters: {
    getAll: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/events/schema`,
        'GET',
        null,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
  },

  properties: {
    getListFavorites: param => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/columns/${param.objectType}/${param.objectId}`,
        'GET',
        null,
      ).then(res => {
        const data = getEntryV2(res, {});
        return data;
      });
    },
  },

  cards: {
    timeline: async params => {
      const res = await callApiWithAuthV2({
        // domain: 'http://localhost:8004/hub',
        endpoint: `${ENDPOINT.userExplorerV2}/visitor/${
          params.objectId
        }/timeline`,
        method: 'POST',
        body: params.data,
      });

      const data = {
        userProfiles: [],
        entries: [],
        contextEvents: {},
      };

      if (typeof res !== 'undefined') {
        const code = parseInt(safeParse(res.data.code, 0));
        if (code === 200) {
          const fullData = safeParse(res.data.data, {});
          const meta = safeParse(fullData.meta, {});
          data.entries = safeParse(fullData.entries, []);
          data.userProfiles = safeParse(fullData.userProfiles, []);
          data.contextEvents = safeParse(meta.contextEvents, {});
        }
      }
      return data;
    },
    graph: params => {
      return callApiWithAuth(
        `${ENDPOINT.userExplorerV2}/visitor/${
          params.objectId
        }/identity?userAttributeCodes=${
          params.userAttributeCodes
        }&productAttrs=${params.productAttrs}&decryptFields=${
          params.decryptFields
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    segments: params => {
      return callApiWithAuth(
        `${ENDPOINT.userExplorerV2}/visitor/${params.objectId}/segment`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getLists: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/customers/events`,
        'GET',
        null,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
    performance: params => {
      return callApiWithAuth(
        `${ENDPOINT.userExplorerV2}/visitor/${params.objectId}/performance`,
        'POST',
        params.body,
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
    getListEventPerformance: () => {
      return callApiWithAuth(
        `${ENDPOINT.timelineV2}/visitors/events-performance`,
        'GET',
      ).then(res => {
        const data = getEntriesV2(res, []);
        return data;
      });
    },
  },
};

export default API;

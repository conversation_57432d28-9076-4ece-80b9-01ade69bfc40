/* eslint-disable prefer-destructuring */
import { isEmpty } from 'lodash';
import { ENDPOINT } from '../../config/common';
import { getEntriesV2, getEntriesWithTotalV2, getEntryV2 } from './utils';
import { callApiWithAuthV2, callApiWithAuth } from '../utils/request';
import { ensureDataInfos, withAccessInfo } from './Operate';
import { DATA_ACCESS_OBJECT } from '../utils/constants';
import { safeParseArrayNumber } from '../utils/web/attribute';

const API = {
  zones: {
    getList: async params => {
      const res = await callApiWithAuthV2({
        endpoint: `${ENDPOINT.toolkitV2}/selector/object/ZONES`,
        method: 'POST',
        params,
      });

      return getEntriesV2(res, []);
    },
  },
  itemTypes: {
    getList: async params => {
      const res = await callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/item-types`,
        'GET',
        params,
      );

      return getEntriesV2(res, []);
    },
  },
  common: {
    getInfoProperties: (params = {}) => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  promotionSource: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-source/listing`,
        'POST',
        params,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  eventAction: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/object/EVENT_DIMENSIONS`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  conversion: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/conversion-sources`,
        'GET',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  eventCategory: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/object/EVENT_DIMENSIONS`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  event: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/selector/full-event-tracking?isUniqueEvent=true`,
        'GET',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListBySource: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/events-by-sources`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListByBO: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/events-by-bo`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/dsource/event/${params.insightPropertyId
        }`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        // console.log('res: ', res);
        return getEntriesV2(res, null);
      });
    },
    lookupByIdsV2: params => {
      const url = `${ENDPOINT.toolkitV2}/info/sources/event-tracking`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        // console.log('res: ', res);
        return getEntriesV2(res, null);
      });
    },
  },
  source: {
    getSourceByEvent: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit
        }/selector/list-data-source-by-event-tracking?eventCategoryId=${params.eventCategoryId
        }&eventActionId=${params.eventActionId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    // getSourceByEventAndBO: params => {
    //   return callApiWithAuth(
    //     `${
    //       ENDPOINT.toolkit
    //     }/selector/list-data-source-by-event-tracking?eventCategoryId=${
    //       params.eventCategoryId
    //     }&eventActionId=${params.eventActionId}&itemTypeIds=${
    //       params.itemTypeIdsUrl
    //     }`,
    //     'GET',
    //     null,
    //   ).then(res => {
    //     return getEntriesV2(res, null);
    //   });
    // },
    getSourceByEventAndBO: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/selector/list-data-source-by-list-event-tracking`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getSourceByEventAndSource: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/list-data-source-by-events`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    // getSourceByEventAndSource: params => {
    //   return callApiWithAuth(
    //     `${
    //       ENDPOINT.toolkit
    //     }/selector/list-data-source-by-event-tracking?eventCategoryId=${
    //       params.eventCategoryId
    //     }&eventActionId=${params.eventActionId}&insightPropertyIds=${
    //       params.sourceUrl
    //     }`,
    //     'GET',
    //     null,
    //   ).then(res => {
    //     return getEntriesV2(res, null);
    //   });
    // },
    getListAll: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/selector/data-source`,
        'GET',
        null,
      ).then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/dsource/${params.insightPropertyIds
        }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  eventAttribute: {
    getListByEventAndSource: params => {
      const url = `${ENDPOINT.datasource}/schemas/event-props/${params.sourceUrl
        }/${params.eventCategoryId}/${params.eventActionId
        }?isFilter=1&objectType=${params.objectType}`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListAttrMultiEvent: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/event-tracking-attributes/get-by-event-name`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  attribute: {
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/properties/${params.insightPropertyId
        }/${params.eventCategoryId}/${params.eventActionId}`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntryV2(res, null);
      });
    },
    getListBOAttribute: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/object/BO_ATTRIBUTE`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListByEventAndSource: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info/sources/event-attributes`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, []);
      });
    },
  },
  eventsBySources: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/events-by-sources`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  boBySources: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/bo-by-sources`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  eventsByBo: {
    getList: params => {
      // console.log({ params });
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/events-by-bo`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  boByBo: {
    getList: params => {
      // console.log({ params });
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/bo/objects`,
        'POST',
        params,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  businessObject: {
    getListAll: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/bo/objects`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListWithAttribute: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/bo/objects`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  segments: {
    getListByObjectType: async params => {
      const url = `${ENDPOINT.toolkitV2}/selector/object`;

      const res = await callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
      });

      const serializeSegmentRes = getEntriesWithTotalV2(res, []);

      if (params.withAccessInfo) {
        serializeSegmentRes.data = await withAccessInfo({
          data: serializeSegmentRes.data,
          objectIdKey: 'segment_id',
          objectType: DATA_ACCESS_OBJECT.SEGMENT,
        });
      }

      serializeSegmentRes.data = await ensureDataInfos({
        objects: serializeSegmentRes.data,
        objectType: DATA_ACCESS_OBJECT.SEGMENT,
        objectIdKey: 'segment_id',
        requiredByIds: safeParseArrayNumber(params.segmentIds),
      });

      // console.log(serializeSegmentRes);
      return serializeSegmentRes;
    },
  },
};

export default API;

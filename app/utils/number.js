/* eslint-disable no-plusplus */
export function randomNumber(min, max, numDecimals = 0) {
  const randomNum = Math.random() * (max - min) + min;
  return Number(randomNum.toFixed(numDecimals));
}

export function shortenNumber(num, config = {}) {
  const {
    thousandSeparator = ',',
    abbreviation = ['k', 'M', 'B', 'T', 'Q'],
  } = config;

  let precision = config.precision || 1;

  if (precision > 3) precision = 3;

  if (num === 0 || num < 1) return `${num}`;

  const magnitude = Math.floor(Math.log10(num) / 3);

  const shortNum = num / 10 ** (magnitude * 3);

  let roundedNum = shortNum.toFixed(precision);

  // kiểm tra nếu roundedNum là số nguyên, thì loại bỏ các chữ số thập phân
  if (Number.isInteger(shortNum)) {
    roundedNum = shortNum.toString();
  } else if (thousandSeparator) {
    // nếu có thousandSeparator, thêm dấu phân cách
    roundedNum = roundedNum.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
  }

  let abb = '';
  if (abbreviation) {
    if (magnitude < abbreviation.length) {
      abb = abbreviation[magnitude - 1];
    } else {
      abb = `*10^${magnitude * 3}`;
    }
  }

  if (abb) {
    roundedNum += abb;
  }

  return roundedNum;
}

export function calculateBuildProgress({
  currentProgress = 0,
  min = 0,
  max = 100,
  randomBetween = [0, 2],
}) {
  const [randomMin, randomMax] = randomBetween;
  let result = currentProgress;

  if (currentProgress < min) result = min;

  if (currentProgress >= min && currentProgress < max) {
    const random = Math.random() * (randomMax - randomMin) + randomMin;

    if (currentProgress + random > max) {
      result = max;
    } else {
      result += random;
    }
  }

  // console.log(currentProgress, min, max);

  return result;
}

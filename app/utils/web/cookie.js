/* eslint-disable no-undef */

import { isUrlEmbedded, safeParse, safeParseInt } from './utils';
import { PORTAL_KEYS } from '../constants';
import APP from '../../appConfig';

/* --------------------------------- Cookie --------------------------------- */
export function setCookie(name, value, exdays, domain) {
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(name, value);
    return;
  }
  const d = new Date();

  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  const expires = `expires=${d.toGMTString()}`;

  document.cookie = `${name}=${value};${expires};domain=${domain};path=/`;

  // alert('setCookie'+name+'='+value+'='+domain);
}

export function getCookie(name) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(name);
  }

  const cname = `${name}=`;
  const decodedCookie = decodeURIComponent(document.cookie);
  const ca = decodedCookie.split(';');

  for (let i = 0; i < ca.length; i += 1) {
    let c = ca[i];

    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(cname) === 0) {
      return c.substring(cname.length, c.length);
    }
  }

  return '';
}

/* ------------------------------- End Cookie ------------------------------- */

/* ---------------------------- Sesstion Storage ---------------------------- */
export function setSessionStorage(key, value) {
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(key, value);
    return;
  }
  if (typeof Storage !== 'undefined') {
    // Khởi tạo sesionStorage
    setWindowParam(key, value);
    sessionStorage.setItem(key, value);
  }
}

export function getSessionStorage(key) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key);
  }
  const tempt = getWindowParam(key);
  if (tempt !== undefined) {
    return tempt;
  }
  if (typeof Storage !== 'undefined') {
    return sessionStorage.getItem(key);
  }
  return '';
}

/* ------------------------- End of Session Storage ------------------------- */

/* ------------------------------ Local Storage ----------------------------- */
export function setLocalStorage(key, value, withCache = true) {
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(key, value);
    return;
  }
  if (typeof Storage !== 'undefined') {
    // Khởi tạo sesionStorage
    if (withCache) {
      setWindowParam(key, value);
    }
    localStorage.setItem(key, value);
  }
}

export function getLocalStorage(key) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key);
  }
  const tempt = getWindowParam(key);
  if (tempt !== undefined) {
    return tempt;
  }
  if (typeof Storage !== 'undefined') {
    return localStorage.getItem(key);
  }
  return '';
}

export function deleteLocalStorage(key) {
  if (typeof Storage !== 'undefined') {
    // Khởi tạo sesionStorage
    setWindowParam(key, undefined);
    localStorage.removeItem(key);
  }
}
/* -------------------------- End of Local Storage -------------------------- */

/* --------------------------- App Cookie Session --------------------------- */

export function getAppCookieSession(key) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key);
  }
  let tempt = safeParse(getSessionStorage(key), '');

  if (tempt === '') {
    tempt = getCookie(key);
  }
  return window.decodeURIComponent(tempt);
}

export function setAppCookieSession(
  name,
  value,
  exdays = 2592000,
  domain = '',
) {
  // console.log({ name, value, exdays, domain });
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(name, value);
    return;
  }
  const tempt = window.encodeURIComponent(value);
  setCookie(name, tempt, exdays, domain);
  setSessionStorage(name, tempt);
}
/* ------------------------ End of App Cookie Session ----------------------- */

export function setAppSession(key, value) {
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(key, value);
    return;
  }
  setSessionStorage(key, value);
}

export function getAppSession(key, useCookie = true) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key);
  }
  let tempt = safeParse(getSessionStorage(key), '');

  if (tempt === '' && useCookie) {
    // console.error('------CAN"T NOT GET APP SESSION');
    tempt = getCookie(key);
  }
  return window.decodeURIComponent(tempt);
}

export function setAppCookieSessionSubDomain(
  name,
  value,
  exdays,
  isEncode = true,
) {
  if (isUrlEmbedded()) {
    setDataWithEmbeddedPublic(name, value);
    return;
  }
  // eslint-disable-next-line no-restricted-globals
  const arrTmp = location.hostname.split('.');
  const domain =
    arrTmp.length < 4 ? arrTmp.slice(-2).join('.') : arrTmp.slice(-3).join('.');
  const tempt = isEncode ? window.encodeURIComponent(value) : value;
  setCookie(name, tempt, exdays, domain);
  setSessionStorage(name, tempt);
}

export function deleteAppCookieSessionSubdomain(name) {
  // eslint-disable-next-line no-restricted-globals
  const arrTmp = location.hostname.split('.');
  const domain =
    arrTmp.length < 4 ? arrTmp.slice(-2).join('.') : arrTmp.slice(-3).join('.');
  // setCookie(name, '', -1, '');
  setCookie(name, '', -1, domain);
  if (typeof Storage !== 'undefined') {
    setWindowParam(name, undefined);
    sessionStorage.removeItem(name);
  }
}

export function deleteAppCookieSession(name) {
  setCookie(name, '', -1, '');
  if (typeof Storage !== 'undefined') {
    setWindowParam(name, undefined);
    sessionStorage.removeItem(name);
  }
}

export function deleteCookieSessionPrefix(prefix) {
  const decodedCookie = decodeURIComponent(document.cookie);
  const ca = decodedCookie.split(';');

  for (let i = 0; i < ca.length; i += 1) {
    let c = ca[i];

    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    const tmpArr = c.split('=');
    if (tmpArr[0].startsWith(prefix)) {
      deleteAppCookieSessionSubdomain(tmpArr[0]);
    }
  }

  return '';
}

export function getAppCookieSessionCurrency() {
  // return safeParse(getAppCookieSession('currency'), 'VND');
  return safeParse(getWindowParam(PORTAL_KEYS.CURRENCY), 'USD');
}

export function setWindowParam(name, value) {
  // window[`__cdp_${name}__`] = value;
  APP_CACHE_PARAMS[name] = value;
}

export function getWindowParam(name) {
  return APP_CACHE_PARAMS[name];
}
export function getPortalId() {
  const { portalId } = getAppParamURL(window.location.pathname);

  if (!portalId) {
    return safeParse(getSessionStorage('api_pid'), 0);
  }
  return portalId;
  // return safeParse(getAppSession('api_pid'), 0);
}

export function getPortalIdFromLink() {
  return getAppParamURL(window.location.pathname).portalId;
}

// get portal from URL
export function getAppParamURL(pathname, forceVersionV2 = false) {
  const output = {
    portalId: 0,
    accessUserId: 0,
  };
  const isV3 = pathname.indexOf('marketing-hub') > -1;

  let portalId = 0;
  let accessUserId = 0;
  if (forceVersionV2 || !isV3) {
    try {
      // const arr = pathname.split('/');
      const arr = pathname.split(APP.PREFIX);

      if (arr.length >= 2) {
        const arrTmp = arr[1].split('/');
        // console.log('arrTmp', arrTmp);
        if (arrTmp.length >= 2) {
          // console.log('arr', arrTmp);
          portalId = safeParseInt(arrTmp[2], 0);
          if (portalId <= 0) {
            portalId = safeParseInt(arrTmp[1], 0);
          }
        }
      }
      if (portalId <= 0) {
        portalId = safeParseInt(getAppSession('api_pid'), 0);
      }
    } catch (e) {
      console.error(e);
    }
  }

  // V3 thêm param userid trên URL
  // http://localhost:5000/gen2/33167/marketing-hub/journeys/3/create
  // => http://localhost:5000/gen2/33167/1600082431/marketing-hub/journeys/3/create
  if (isV3) {
    try {
      // const arr = pathname.split('/');
      const arr = pathname.split(APP.PREFIX);

      if (arr.length >= 2) {
        const arrTmp = arr[1].split('/');
        if (arrTmp.length >= 2) {
          portalId = safeParseInt(arrTmp[1], 0);
          accessUserId = safeParseInt(arrTmp[2], 0);
          // if (portalId <= 0) {
          //   portalId = safeParseInt(arrTmp[1], 0);
          // }
        }
      }
      if (portalId <= 0) {
        portalId = safeParseInt(getAppSession('api_pid'), 0);
      }
    } catch (e) {
      console.error(e);
    }
  }

  output.portalId = portalId;
  output.accessUserId = accessUserId;
  return output;
}

/**
 *
 * @returns current account login
 */
export function setCurrentUserId(value) {
  setAppSession('user_id', value);
}
export function getCurrentUserId() {
  const userId = safeParse(getAppSession('user_id', false), 0);
  if (userId === 0) {
    // if user_id not set, read 3rd cookie
    try {
      const tempt = getCookie(
        `${PORTAL_CONFIG.INSIGHT_U_OGS}_${getPortalId()}`,
      );
      // console.log(tempt);
      if (tempt) {
        const data = JSON.parse(tempt);
        return parseInt(window.decodeURIComponent(data.user_id));
      }
    } catch (err) {
      console.error(err);
    }
  }
  return userId;
}

export function getCurrentUserRole() {
  try {
    const tempt = getCookie(`${PORTAL_CONFIG.INSIGHT_U_OGS}_${getPortalId()}`);
    // console.log(tempt);
    if (tempt) {
      const data = JSON.parse(tempt);
      return parseInt(window.decodeURIComponent(data.role));
    }
  } catch (err) {
    console.error(err);
  }
  return 'Unknown';
}

export function getCurrentUserLanguage() {
  try {
    const tempt = getCookie(`${PORTAL_CONFIG.INSIGHT_U_OGS}_${getPortalId()}`);

    if (tempt) {
      const data = JSON.parse(tempt);
      return window.decodeURIComponent(data.language);
    }
  } catch (err) {
    console.error(err);
  }

  return 'en';
}

/**
 * A login then access to B
 * @returns user được share access vào
 */
export function setCurrentAccessUserId(value) {
  setAppSession('share_access_user_id', value);
}
export function getCurrentAccessUserId() {
  const userId = safeParse(getAppSession('share_access_user_id'), 0);
  // no data safeParse to currentUserId: understand case is not access to any account
  if (Number(userId) === 0) return getCurrentUserId();
  return userId;
}

/**
 * A login then access to B, then choose account C
 * C => ownerId, ownerId call ALL/ specific ID
 * @returns user đang được chọn tại case account
 * @returns all nếu đang không chọn owner nào
 */
export function setCurrentOwnerId(value) {
  setAppSession('owner_id', value);
}
export function setCurrentOwnerName(value) {
  setAppSession('owner_name', value);
}
export function setCurrentLastOwnerName(value) {
  setAppSession('last_owner_name', value);
}
export function getCurrentOwnerId() {
  const userId = safeParse(getAppSession('owner_id'), 'all');
  // khi set nếu value = all, Number parse bị về NaN
  // if (isNaN(userId) || 'Nan') return 'all';
  return userId;
}
export function getCurrentOwnerIds() {
  const userId = safeParse(getAppSession('owner_id'), 'all');
  if (userId === 'all') {
    return getCurrentUserId();
  }
  return userId;
}
/**
 * Last access ownerId, using for reinit ownerId with VIEW EVERYTHING
 * có những trường hợp đi từ VIEW EVERYTHING, chọn ownerId, rồi sau đó đi qua menu ko có VIEW EVERYTHING, sau đó back lại
 */
export function setLastOwnerId(value) {
  setAppSession('last_owner_id', value);
}
export function getLastOwnerId() {
  const userId = safeParse(getAppSession('last_owner_id'), 'all');
  return userId;
}

/**
 * setCurrentCreateOwnerId
 */
export function setCurrentCreateOwnerId(value) {
  setAppSession('create_owner_id', value);
}
export function getCurrentCreateOwnerId() {
  const userId =
    safeParse(getAppSession('create_owner_id'), 0) || getCurrentOwnerIds();
  return userId;
}

export function getToken() {
  // const tempt = getCookie('api_token');
  // return window.decodeURIComponent(tempt);
  return getTokenNetwork(getPortalId());
}

export function getTokenNetwork(networkId) {
  // const dataCookie =
  try {
    const tempt = getCookie(`${PORTAL_CONFIG.INSIGHT_U_OGS}_${networkId}`);
    // console.log(tempt);
    if (tempt) {
      const data = JSON.parse(tempt);
      return window.decodeURIComponent(data.token);
    }
    return window.decodeURIComponent(tempt);
  } catch (err) {
    console.error(err);
  }
  return '';
}

export function getRootToken() {
  const tempt = getCookie('api_r_token');
  return window.decodeURIComponent(tempt);
}

export function validateAuthentication() {
  // console.log('object', getRootToken(), '--', getToken(), '--', getPortalId());
  return getRootToken() || getToken();
}

// when user use embedded, for security, not save data at cookie, using APP_CACHE_PARAMS for tempt caching
function getDataWithEmbeddedPublic(key) {
  const data = safeParse(APP_CACHE_PARAMS.embeddedData, {});
  return data[key];
}

function setDataWithEmbeddedPublic(key, value) {
  const data = safeParse(APP_CACHE_PARAMS.embeddedData, {});
  console.log('setDataWithEmbeddedPublic', { key, value, data });
  data[key] = value;
  APP_CACHE_PARAMS.embeddedData = data;
}

export function checkInitSession() {
  if (
    window.location.href.includes('atm_source=login') &&
    typeof Storage !== 'undefined'
  ) {
    setCurrentOwnerId('all');
    sessionStorage.removeItem('user_id');
    sessionStorage.removeItem('user_name');
  }
}

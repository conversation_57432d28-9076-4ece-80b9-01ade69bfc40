/* eslint-disable no-else-return */
import { STATUS_ITEM_CODE } from '../constants';
import { safeParse } from './utils';
import { getTranslateMessage } from '../../containers/Translate/util';
import TRANSLATE_KEY from '../../messages/constant';
import { formatDate, PortalDate } from '../date';
import { getLocalStorage, setLocalStorage } from './cookie';
import { addMessageToQueue } from './queue';

export function getStatusItemCode(type, status, isFilter = 1) {
  if (type === 'info') {
    if (status === 3 || status === 0) {
      return STATUS_ITEM_CODE.REMOVED;
    } else if (status === 2) {
      return STATUS_ITEM_CODE.DISABLED;
    } else if (isFilter !== 1) {
      return STATUS_ITEM_CODE.NO_FILTER;
    }

    return STATUS_ITEM_CODE.ACTIVE;
  } else if (type === 'lookup') {
    if (status === 4) {
      return STATUS_ITEM_CODE.ARCHIVED;
    } else if (status === 3 || status === 0) {
      return STATUS_ITEM_CODE.REMOVED;
    } else if (status === 2) {
      return STATUS_ITEM_CODE.DISABLED;
    } else if (isFilter !== 1) {
      return STATUS_ITEM_CODE.NO_FILTER;
    }
    return STATUS_ITEM_CODE.FORBIDDEN;
  }

  return STATUS_ITEM_CODE.REMOVED;
}

export function getNameTypeItemAttributes(type) {
  if (type === 1) {
    return 'default_attr';
  } else if (type === 2) {
    return 'custom_attr';
  } else if (type === 3) {
    return 'comp_attr';
  }
  return '';
}

export function serializePropertyValues(options = []) {
  if (options !== null && options.length > 0) {
    // result = arr.reduce((map, obj) => (map[obj.key] = obj.val, map), {});
    const result = new Map(
      // property.propertyValues.map(obj => [obj.value, obj.label]),
      // property.propertyValues.map(obj => [obj.value, obj]),
      options.map(obj => [`${obj.value}`, obj]),
    );

    return result;
  }
  return new Map();
}

export function getErrorsByStatusItemCode(label = '--', value) {
  if (value === null || value === undefined) {
    return [];
  }
  const statusItemCode = safeParse(
    value.statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  if (statusItemCode === STATUS_ITEM_CODE.REMOVED) {
    if (value.available) {
      return [
        `${getTranslateMessage(
          TRANSLATE_KEY._,
          `${label.trim()} not available`,
          {
            module_name: label,
          },
        )}`,
      ];
    }

    return [
      `${getTranslateMessage(
        TRANSLATE_KEY._,
        `This ${label} does not exist`,
        // has been deleted. You still can use the output value of the last time it is processed`,
        { module_name: label },
      )}`,
    ];
  } else if (statusItemCode === STATUS_ITEM_CODE.NO_FILTER) {
    return [
      `${getTranslateMessage(
        TRANSLATE_KEY._INFO_OBJECT_NOT_ALLOW_APPEAR_FILTER,
        `This ${label} is turned off from Filters.`,
        { module_name: label },
      )}`,
    ];
  } else if (statusItemCode === STATUS_ITEM_CODE.FORBIDDEN) {
    return [
      `${getTranslateMessage(
        TRANSLATE_KEY._INFO_OBJECT_PERMISSION_TO_USE,
        `Your usage permission of this ${label} is turned-off/limited.`,
        { module_name: label },
      )}`,
    ];
  }
  return [];
}

export function getWarnsByStatusItemCode(label = '--', value) {
  if (value === null || value === undefined) {
    return [];
  }
  const statusItemCode = safeParse(
    value.statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  if (statusItemCode === STATUS_ITEM_CODE.DISABLED) {
    return [
      `${getTranslateMessage(
        TRANSLATE_KEY._INFO_OBJECT_DISABLED,
        `Data-updating of this ${label} has been disabled. You still can use the output value of the last time it is processed`,
        { module_name: label },
      )}`,
    ];
  }
  return [];
}

export function fixAvatarCustomer(customerId) {
  if (
    customerId == 564709364 ||
    customerId == 564434421 ||
    customerId == 564371161 ||
    customerId == 556034523 ||
    customerId == 564228811 ||
    customerId == 93 ||
    customerId == 98 ||
    customerId == 102
  ) {
    return true;
  }
  return false;
}

export function buildPropertiesWithFilter(properties) {
  // return properties;
  if (properties.length > 0) {
    const data = [];
    properties.forEach(item => {
      if (item.options !== undefined) {
        const temp = { ...item };
        const options = [];
        item.options.forEach(obj => {
          if (obj.isFilter === 1) {
            options.push(obj);
          }
        });
        temp.options = options;
        data.push(temp);
      } else if (item.isFilter === 1) {
        data.push(item);
      }
    });

    return data;
  }

  return properties;
}

export function buildMapPropertiesWithFilter(mapProperties) {
  // return properties;
  const map = {};
  Object.keys(mapProperties).forEach(key => {
    if (mapProperties[key].isFilter) {
      map[key] = mapProperties[key];
    }
  });
  return map;
}

export function getUntitledName(objectName = 'Untitled object') {
  const dateTime = formatDate(new PortalDate(), 'yyyy-MM-dd HH:mm:ss');

  return `${objectName}#${dateTime}`;
}
export function getUntitledCopyName(objectName = 'Untitled object') {
  return `${objectName} copy ${formatDate(
    new PortalDate(),
    'yyyy-MM-dd HH:mm:ss',
  )}`;
}

export function getDefaultMetricChart(
  key,
  dataSetLocalStorage,
  defalultMetrix,
  JOURNEY_CHART_METRIC,
  pathFile,
) {
  try {
    /* ------------ Check localStorage ở những User cũ có dạng Array ------------ */
    const item = getLocalStorage(JOURNEY_CHART_METRIC);
    if (Array.isArray(JSON.parse(item))) {
      localStorage.removeItem(JOURNEY_CHART_METRIC);
      // eslint-disable-next-line no-param-reassign
      dataSetLocalStorage[key] = JSON.parse(item);
      setLocalStorage(
        JOURNEY_CHART_METRIC,
        JSON.stringify(dataSetLocalStorage),
      );
      return dataSetLocalStorage;
    }
    return item ? JSON.parse(item) : defalultMetrix;
  } catch (error) {
    addMessageToQueue({
      path: pathFile,
      func: 'handleGetDefaultMetrics',
      data: error.stack,
    });
    console.log(error);
    const item = defalultMetrix;
    setLocalStorage(JOURNEY_CHART_METRIC, JSON.stringify(item));
    return item;
  }
}

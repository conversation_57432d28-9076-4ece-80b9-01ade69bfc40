import { setWindowParam, getWindowParam } from './cookie';
import { PORTAL_KEYS } from '../constants';
import { safeParse, convertNumberByDisplayFormat } from './utils';
import { multiPercentageValue } from '../../components/form/InputNumberFormat/NumberFormat/utils';
let _cacheNumberWithoutDecimal = null;
let _cacheDateWithoutTime = null;

function getLanguageLocale(lang = 'EN') {
  return lang.toLowerCase();
}

export function getListLanguage(list = []) {
  const listLang = [
    { name: 'vi', value: 'VI', label: 'Việt Nam' },
    { name: 'en', value: 'EN', label: 'English' },
    { name: 'ja', value: 'JA', label: '日本' },
  ];
  return listLang;
}

function internalInitNumberWithoutDecimal() {
  return {
    type: 'NUMBER',
    // config: {
    //   group: 'space',
    //   decimal: '.',
    //   decimalPlace: 3,
    // },
    config: {
      ...getPortalFormatNumber(),
      decimalPlace: 0,
    },
  };
}

function internalInitDateWithoutTime() {
  return {
    ...getPortalFormatDateTime(),
    time: { check: false },
  };
}
// export const initNumberWithoutDecimal = internalInitNumberWithoutDecimal();
export function initNumberWithoutDecimal() {
  if (_cacheNumberWithoutDecimal === null) {
    _cacheNumberWithoutDecimal = internalInitNumberWithoutDecimal();
  }
  return _cacheNumberWithoutDecimal;
}

// export const initNumberWithoutDecimal = internalInitNumberWithoutDecimal();
export function initDateWithoutTime() {
  if (_cacheDateWithoutTime === null) {
    _cacheDateWithoutTime = internalInitDateWithoutTime();
  }
  return _cacheDateWithoutTime;
}

export const initDateAndTimeFormat = {
  format: 'MM/DD/YYYY',
  date: {
    check: true,
    value: 'medium',
  },
  time: {
    check: true,
    value: 'medium',
    timeFormat: '12',
  },
};

export const initCurrencyFormat = {
  currency: 'USD',
  group: ',',
  decimal: '.',
  decimalPlace: 2,
};

export const initNumberFormat = {
  group: ',',
  decimal: '.',
  decimalPlace: 2,
};

export const initPercentageFormat = {
  group: ',',
  decimal: '.',
  decimalPlace: 2,
};

export const initIdFormat = {
  group: '',
  decimal: '',
  decimalPlace: 0,
};

export function initNumberId() {
  return {
    type: 'NUMBER',
    config: {
      ...initIdFormat,
    },
  };
}

// export const initStringFormat = {
//   type: 'RAW_STRING',
//   config: {},
// };

function initDefaultConfig() {
  return {
    language: 'EN',
    timeZone: 'Asia/Ho_Chi_Minh',
    dateAndTimeFormat: initDateAndTimeFormat,
    numberFormat: initNumberFormat,
    currencyFormat: initCurrencyFormat,
    percentageFormat: initPercentageFormat,
    // VERSION: 'DEV',
  };
}

export function getUserInfoPortal(data) {
  const defaultConfig = { ...initDefaultConfig(), ...data.defaultConfig };
  return { ...data, defaultConfig };
}

// nếu false tất cả đều lấy format của kiểu medium
const MAP_FORMAT_DATETIME = {
  'DD/MM/YYYY-short-true': 'dd/MM',
  'DD/MM/YYYY-short-false': 'dd MMM',
  'DD/MM/YYYY-medium-true': 'dd MMM',
  'DD/MM/YYYY-medium-false': 'dd MMM',
  'DD/MM/YYYY-long-true': 'dd MMMM',
  'DD/MM/YYYY-long-false': 'dd MMM',
  'MM/DD/YYYY-short-true': 'MM/dd',
  'MM/DD/YYYY-short-false': 'MMM dd',
  'MM/DD/YYYY-medium-true': 'MMM dd',
  'MM/DD/YYYY-medium-false': 'MMM dd',
  'MM/DD/YYYY-long-true': 'MMMM dd',
  'MM/DD/YYYY-long-false': 'MMM dd',
};

const MAP_FORMAT_TIME = {
  '12': 'pp',
  '24': 'HH:mm:ss',
};

export const convertDateTimeToFormatLong = formatDatetime => {
  let dataDateTime = formatDatetime;
  if (Object.keys(dataDateTime).length === 0) {
    dataDateTime = initDateAndTimeFormat;
  }

  const formatString = `${
    MAP_FORMAT_DATETIME[
      `${dataDateTime.format}-${dataDateTime.date.value}-${
        dataDateTime.date.check
      }`
    ]
  } 'at' ${MAP_FORMAT_TIME[dataDateTime.time.timeFormat]}`;

  return formatString;
};

export function setUserAndPortalDefaultConfig(config, user = {}, portal = {}) {
  // console.log('user', user, portal);

  // info user and portal
  setWindowParam('user_email', user.email);
  setWindowParam('p_name', portal.portalName);

  setWindowParam(PORTAL_KEYS.CURRENCY, config.currencyFormat.currency);
  setWindowParam(PORTAL_KEYS.DATE_TIME_FORMAT, config.dateAndTimeFormat);
  setWindowParam(PORTAL_KEYS.CURRENCY_FORMAT, config.currencyFormat);
  setWindowParam(PORTAL_KEYS.NUMBER_FORMAT, config.numberFormat);
  setWindowParam(PORTAL_KEYS.PERCENTAGE_FORMAT, config.percentageFormat);
  setWindowParam(
    PORTAL_KEYS.PORTAL_LANGUAGE,
    getLanguageLocale(config.language),
  );
  setWindowParam(PORTAL_KEYS.TIME_ZONE, config.timeZone);
  setWindowParam(
    PORTAL_KEYS.DATE_TIME_FORMAT_LONG,
    convertDateTimeToFormatLong(config.dateAndTimeFormat),
  );

  /** for user */
  // setWindowParam(
  //   PORTAL_KEYS.USER_LANGUAGE,
  //   getLanguageLocale(safeParse(user.language, config.language)),
  // );
  // console.log('set user language');
}

export function getPortalCurrency() {
  return getWindowParam(PORTAL_KEYS.CURRENCY);
}

export function getPortalFormatCurrency() {
  return getWindowParam(PORTAL_KEYS.CURRENCY_FORMAT);
}

export function getPortalFormatDateTime() {
  return getWindowParam(PORTAL_KEYS.DATE_TIME_FORMAT);
}

export function getPortalFormatNumber() {
  return getWindowParam(PORTAL_KEYS.NUMBER_FORMAT);
}

export function getPortalFormatPercentage() {
  return getWindowParam(PORTAL_KEYS.PERCENTAGE_FORMAT);
}
export function getPortalLocaleLanguage() {
  return safeParse(getWindowParam(PORTAL_KEYS.PORTAL_LANGUAGE), 'en');
}
export function getUserLocaleLanguage() {
  // console.log('---', getWindowParam(PORTAL_KEYS.USER_LANGUAGE));
  return safeParse(getWindowParam(PORTAL_KEYS.USER_LANGUAGE), 'en');
}
export function getPortalTimeZone() {
  return getWindowParam(PORTAL_KEYS.TIME_ZONE);
}
export function getPortalFormatDateTimeLong() {
  return getWindowParam(PORTAL_KEYS.DATE_TIME_FORMAT_LONG);
}

export function safeParseDisplayFormat(displayFormat = null, params = {}) {
  if (displayFormat !== null) {
    const { type } = displayFormat;
    const config = safeParse(displayFormat.config, {});
    // if (config !== undefined && Object.keys(config).length > 0) {
    //   return displayFormat;
    // }
    if (type === 'NUMBER') {
      return {
        type: 'NUMBER',
        config: { ...getPortalFormatNumber(), ...config },
      };
    }
    if (type === 'CURRENCY') {
      return {
        type: 'CURRENCY',
        config: { ...getPortalFormatCurrency(), ...config },
      };
    }
    if (type === 'PERCENTAGE') {
      return {
        type: 'PERCENTAGE',
        config: { ...getPortalFormatPercentage(), ...config },
      };
    }
    if (type === 'DATE_AND_TIME') {
      return {
        type: 'DATE_AND_TIME',
        config: { ...getPortalFormatDateTime(), ...config },
      };
    }
    if (
      type === 'RAW_STRING' ||
      type === 'IMAGE' ||
      type === 'LINK' ||
      type === 'CUSTOM_LINK'
    ) {
      return {
        type,
        config: { ...config },
      };
    }
  }

  const { dataType } = params;

  if (dataType === 'number') {
    const { isCurrency } = params;

    if (parseInt(isCurrency) === 1) {
      return {
        type: 'CURRENCY',
        config: getPortalFormatCurrency(),
      };
    }
    return {
      type: 'NUMBER',
      config: getPortalFormatNumber(),
    };
  }
  if (dataType === 'datetime') {
    return {
      type: 'DATE_AND_TIME',
      config: getPortalFormatDateTime(),
    };
  }
  if (dataType === 'string') {
    return {
      type: 'RAW_STRING',
      config: {},
    };
  }

  // string is default
  return {
    type: 'RAW_STRING',
    config: {},
  };
}

export function safeParseDisplayFormatNumber(value, displayFormat = {}) {
  if (Object.keys(displayFormat).length === 0) {
    // eslint-disable-next-line no-param-reassign
    displayFormat = getPortalFormatNumber();
  }
  const temp = convertNumberByDisplayFormat(
    value,
    displayFormat.group,
    displayFormat.decimal,
    displayFormat.decimalPlace,
  );

  //   'safeParseDisplayFormatNumber',
  //   value,
  //   displayFormat,
  //   initNumberWithoutDecimal,
  // );
  return temp;
}

export function safeParseDisplayFormatCurrency(value, displayFormat = {}) {
  if (Object.keys(displayFormat).length === 0) {
    // eslint-disable-next-line no-param-reassign
    displayFormat = getPortalFormatCurrency();
  }
  const temp = convertNumberByDisplayFormat(
    value,
    displayFormat.group,
    displayFormat.decimal,
    displayFormat.decimalPlace,
  );

  return `${temp} ${displayFormat.currency}`;
}

export function safeParseDisplayFormatPercentage(
  valueIn,
  displayFormat = getPortalFormatPercentage(),
) {
  let value = valueIn;
  value = multiPercentageValue(valueIn);
  const temp = convertNumberByDisplayFormat(
    value,
    displayFormat.group,
    displayFormat.decimal,
    displayFormat.decimalPlace,
  );
  return `${temp}%`;
}

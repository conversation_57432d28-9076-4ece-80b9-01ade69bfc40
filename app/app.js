/* eslint-disable no-undef */
/**
 * app.js
 *
 * This is the entry file for the application, only setup and boilerplate
 * code.
 */

// Needed for redux-saga es6 generator support
import '@babel/polyfill';

// Import all the third party stuff
import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router/immutable';
// import { BrowserRouter } from 'react-router-dom';
import FontFaceObserver from 'fontfaceobserver';
import history from 'utils/history';
import {
  ConfigProvider,
  ContentSourceProvider,
  QueryClientProviderAntsomiUI,
  queryClientAntsomiUI,
} from '@antscorp/antsomi-ui';
import 'sanitize.css/sanitize.css';
import '@antscorp/icons/main.css';
import '@antscorp/antsomi-charts/es/index.css';
// import registerServiceWorker from './serviceWorkerRegistration';

// import 'simplebar/src/simplebar.css';
// import 'react-perfect-scrollbar/dist/css/styles.css';
// Import root app
// import App from 'containers/App';
import Modules from 'modules/index';

// Import Language Provider
// import LanguageProvider from 'containers/LanguageProvider';
// Load the favicon and the .htaccess file
// import '!file-loader?name=[name].[ext]!./images/favicon.ico';
// import 'file-loader?name=.htaccess!./.htaccess'; // eslint-disable-line import/extensions
import configureStore from './configureStore';

// Import i18n messages
import { translationMessages } from './i18n';
import translationKey from './translations/constant';
import {
  getCurrentOwnerId,
  getCurrentUserId,
  getCurrentUserLanguage,
  getPortalId,
  getToken,
  checkInitSession,
} from './utils/web/cookie';
import { getUserLocaleLanguage } from './utils/web/portalSetting';
import APP from './appConfig';

// Observe loading of Open Sans (to remove open sans, remove the <link> tag in
// the index.html file and this observer)
const openSansObserver = new FontFaceObserver('Open Sans', {});

// When Open Sans is loaded, add a font-family using Open Sans to the body
openSansObserver.load().then(() => {
  document.body.classList.add('fontLoaded');
});

// Create redux store with history
const initialState = {};
const store = configureStore(initialState, history);
const MOUNT_NODE = document.getElementById('app');

// Check session to reset owner on first time login
checkInitSession();

const render = () => {
  const env =
    process.env.NODE_ENV === 'development' || +getPortalId() !== 33167
      ? process.env.NODE_ENV
      : 'sandbox';

  const userId = getCurrentOwnerId();
  const accountId = getCurrentUserId();

  const urlLogout = `${window.location.origin}${APP.PREFIX}/login`;
  ReactDOM.render(
    <Provider store={store}>
      {/* <LanguageProvider messages={messages}> */}
      <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
        <ConfigProvider
          appConfig={{
            env,
            auth: {
              token: getToken(),
              portalId: getPortalId(),
              userId: userId === 'all' ? accountId : userId,
              accountId,
              // userId: getCurrentUserId(),
              // accountId: getCurrentOwnerId(),
            },
            urlLogout,
            languageCode: getUserLocaleLanguage().toLowerCase(),
          }}
          locale={getCurrentUserLanguage()}
        >
          <ConnectedRouter history={history}>
            <ContentSourceProvider>
              <Modules />
            </ContentSourceProvider>
          </ConnectedRouter>
        </ConfigProvider>
      </QueryClientProviderAntsomiUI>
      {/* </LanguageProvider> */}
    </Provider>,
    MOUNT_NODE,
  );
};

if (module.hot) {
  // Hot reloadable React components and translation json files
  // modules.hot.accept does not accept dynamic dependencies,
  // have to be constants at compile-time
  module.hot.accept(['./i18n', 'modules/index'], () => {
    ReactDOM.unmountComponentAtNode(MOUNT_NODE);
    render(translationMessages);
  });
}

APP_CACHE_PARAMS._XLAB__COMPONENT_MAP_KEY_TRANSLATE = translationKey;
APP_CACHE_PARAMS._XLAB__COMPONENT_TRANSLATION_MESSAGES = translationMessages;
// registerServiceWorker();
render();

window.addEventListener('message', event => {
  const {
    data: { type },
  } = event;

  if (type && type === 'cdp_user_authenticated_change') {
    setTimeout(() => {
      render();
    }, 100);
  }
});

/* eslint-disable no-param-reassign */
import { useCallback, useEffect, useRef, useState } from 'react';

function useDebounceCallback(callback = () => {}, delay = 600) {
  const [isDebouncing, setIsDebouncing] = useState(false);
  const [args, setArgs] = useState([]);
  const callbackRef = useRef(callback);
  const timerRef = useRef(null);

  const debouncedCallback = useCallback(
    (...callbackArgs) => {
      setIsDebouncing(true);
      setArgs(callbackArgs);

      if (timerRef.current) {
        handleClearTimeOut();
      }

      timerRef.current = setTimeout(() => {
        setIsDebouncing(false);
        callbackRef.current(...callbackArgs);
      }, delay);
    },
    [delay],
  );

  const handleClearTimeOut = useCallback(() => {
    clearTimeout(timerRef.current);
    timerRef.current = null;
  }, []);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(
    () => () => {
      handleClearTimeOut();
    },
    [],
  );

  return {
    debounce: Object.assign(debouncedCallback, {
      flush: () => {
        if (timerRef.current) {
          handleClearTimeOut();

          setIsDebouncing(false);
          callbackRef.current(...args);
        }
      },
      cancel: () => {
        if (timerRef.current) {
          handleClearTimeOut();

          setIsDebouncing(false);
        }
      },
    }),
    isDebouncing,
  };
}

export default useDebounceCallback;

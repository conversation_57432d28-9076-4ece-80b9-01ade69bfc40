import styled, { css } from 'styled-components';

import colors from 'utils/colors';

export const ContainerCustomHeader = styled.header`
  border-bottom: 1px solid ${colors.platinum};
  align-items: center;
  position: fixed;
  top: ${props => (props.isJourneyV2 ? '10px' : 0)};
  left: ${props => (props ? `${props.left + 60}px` : '0px')};
  right: ${props => (props ? `${props.right + 20}px` : '0px')};
  background-color: ${colors.white};
  /* isInit */
  z-index: 999;
  height: ${props => !props.isShowDrawer && '3.5rem'};
  ${({ isFitContent }) => isFitContent && `width: fit-content;`};
  ${({ isJourneyV2, isBlastCampaign }) =>
    isJourneyV2 &&
    css`
      position: ${isBlastCampaign ? 'fixed' : 'absolute'};
      left: ${isBlastCampaign ? 'unset' : '0px'};
      right: unset;
      padding-left: 15px;
      background-color: unset;
      border: none;
    `}
`;

export const WrapperCustomerHeaderContent = styled.div`
  ants-tech-side-navigation {
    height: 34px;
    width: 32px;
  }
  display: flex;
  align-items: center;
  li {
    list-style: none;
  }
  ${props =>
    props.marginLeft &&
    css`
      margin-left: 12px;
    `};
  ${props =>
    props.marginRight &&
    css`
      margin-right: 1rem;
    `};
  .ants-tech-component-wrapper {
    margin-top: 4px;
  }

  .icon-arrow {
    position: relative;
    top: 1px;
  }
`;

export const WrapperCloseIcon = styled.div`
  position: fixed;
  left: 0px;
  top: 0;
  z-index: 999;
  width: 52px;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  ._xlab_contain_icon {
    cursor: pointer;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    /* margin-right: 16px; */
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      color: #7f7f7f;
    }
    &:hover {
      background: #f1f1f1;
    }
  }
`;

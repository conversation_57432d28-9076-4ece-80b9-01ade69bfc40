/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable no-undef */
import React, { useEffect } from 'react';
import styled from 'styled-components';
import '@antscorp/ants-account-sharing/dist/index.css';
import { AntsAccountSharing } from '@antscorp/ants-account-sharing';

import { AntsAccountSupport } from '@antscorp/ants-account-support';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

import '@antscorp/ants-account-support/dist/index.css';

import { WrapperCenter, HeaderProfile } from './styles';
import {
  getCurrentAccessUserId,
  getCurrentUserId,
  getPortalId,
  getToken,
} from '../../../../../utils/web/cookie';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { getUserLocaleLanguage } from '../../../../../utils/web/portalSetting';
import APP from '../../../../../appConfig';
import {
  makeSelectDashboard,
  makeSelectDashboardMenuCodeActive,
} from '../../../../../modules/Dashboard/selector';
import { updateValue } from '../../../../../redux/actions';
import {
  APP_CODE,
  getAppCodeByMenuCode,
} from '../../../../../utils/web/permission';
import { useImmer } from 'use-immer';

const translateData = {
  KEY_CHANGE_PORTAL: getTranslateMessage(
    TRANSLATE_KEY._ACT_CHANGE_PORTAL,
    'Change Portal',
  ),
  KEY_EDIT_PROFILE: getTranslateMessage(
    TRANSLATE_KEY._ACT_EDIT_PROFILE,
    'Edit profile',
  ),
  KEY_SEARCH_PLACEHOLDER: getTranslateMessage(
    TRANSLATE_KEY._ACT_SEARCH,
    'Search',
  ),
  KEY_LOADING: 'loading',
  KEY_LOGOUT: getTranslateMessage(TRANSLATE_KEY._ACT_LOGOUT, 'Logout'),
  KEY_BACK: getTranslateMessage(TRANSLATE_KEY._ACT_BACK, 'Back'),
};

const DivAnchor = styled(WrapperCenter)`
  min-height: 3.3rem;
`;

// const accountId = getCurrentUserId();
// // const userId = getCurrentUserId();
// const networkId = getPortalId();
// const token = getToken();`
const urlEditProfile = `${
  PORTAL_CONFIG.PROFILE_URL
}/${getPortalId()}#/account/profile/`; // https://permission.antsomi.com/33167#/account/profile/
const urlLogout = `${window.location.origin}${APP.PREFIX}/login`;

// eslint-disable-next-line react/prefer-stateless-function
function Account(props) {
  const [state, setState] = useImmer({ appCode: APP_CODE.PROFILE });

  useEffect(() => {
    const handleMessage = e => {
      if (e.data.type === 'cdp_request_logout') {
        window.location.href = urlLogout;
      }
    };

    window.addEventListener('message', handleMessage);

    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    const appCode = getAppCodeByMenuCode(props.menuCodeActive);

    setState(draft => {
      draft.appCode = appCode;
    });
  }, [props.menuCodeActive]);

  const switchAccount = res => {
    // console.log('switchAccount', res);
    props.changeAccountShareAccessId(res);
  };

  const userLogin = getCurrentUserId();
  // const userId = getCurrentUserId();
  const shareUserId = getCurrentAccessUserId();
  const networkId = getPortalId();
  const token = getToken();

  // console.log('render Account', { shareUserId, userLogin });
  return (
    <>
      <DivAnchor>
        <HeaderProfile>
          <AntsAccountSupport
            permissionDomain={PORTAL_CONFIG.URL_PERMISSION_API}
            accountId={shareUserId}
            userId={shareUserId}
            networkId={networkId}
            token={token}
            appCode={state.appCode}
            lang={getUserLocaleLanguage()}
            isShowSupport={false}
            isShowAccountType={false}
          />
          <AntsAccountSharing
            permissionDomain={PORTAL_CONFIG.URL_PERMISSION_API}
            iamDomain={PORTAL_CONFIG.URL_IAM_API}
            accountId={userLogin}
            networkId={networkId}
            // isShowSharing={false}
            isShowSharing
            isShareAccountAccess
            urlEditProfile={urlEditProfile}
            urlLogout={urlLogout}
            token={token}
            u_ogs={PORTAL_CONFIG.INSIGHT_U_OGS}
            translateData={translateData}
            lang={getUserLocaleLanguage()}
            appCode={state.appCode}
            callbackGetInfoAccount={switchAccount}
          />
          {/* <AntsAccountSharing
              permissionDomain="https://permission.antsomi.com"
              iamDomain={'https://iam.ants.tech'}
              accountId={*********}
              networkId={33167}
              urlEditProfile={
                'https://permission.antsomi.com/33167#/account/profile/'
              }
              urlLogout={'https://permission.antsomi.com/#/login'}
              isShowSharing={false}
              token={'9474s20374z294e4x296a4s5i4g5t236m464t2v464'}
              u_ogs={'uogs'}
            /> */}
        </HeaderProfile>
      </DivAnchor>
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  dashboard: makeSelectDashboard(),
  menuCodeActive: makeSelectDashboardMenuCodeActive(),
});

const mapDispatchToProps = dispatch => ({
  changeAccountShareAccessId: value => {
    dispatch(updateValue('@@DASHBOARD_ACCOUNT_SHARE_ACCESS_ID@@', value));
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Account);

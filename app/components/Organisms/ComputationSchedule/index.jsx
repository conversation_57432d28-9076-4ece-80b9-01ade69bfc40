/* eslint-disable import/classNam<PERSON>er */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable eqeqeq */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-curly-brace-presence */
/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/label-has-associated-control */

// Libraries
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import produce from 'immer';
import { get } from 'lodash';
import DateFnsUtils from '@date-io/date-fns';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import moment from 'moment';
import cls from 'classnames';

// Components
import UISelect from 'components/form/UISelectCondition';
import IconXlab from 'components/common/UIIconXlab';

// Styles
import {
  FormControlLabel,
  Grid,
  Input,
  Radio,
  RadioGroup,
} from '@material-ui/core';
import { UIInputCalendar } from '@xlab-team/ui-components';
import {
  Wrapper,
  FormGroup,
  GroupInputNumber,
  DayOfMonths,
  WrapperSelect,
  TextError,
  WrapperTextValue,
  WrapperTextLable,
  TextViewMode,
  Text,
} from './styled';

// Utils
import { parseDayHourToMoment } from './utils';
import COLORS from 'utils/colors';

// Constants
import {
  currentTime,
  DAY_OF_MONTH,
  DAY_OF_WEEK,
  defaultComputationSchedule,
  defaultEndTime,
  defaultRepeatByOptions,
  DEFAULT_START_TIME_HOUR,
  END_OF_MONTH,
  FORMAT_DATE,
  HOURS,
  MAP_HOURS,
  END_TIME_OPTIONS,
  REPEAT_BY_OPTIONS,
} from './constant';
import { useStyles } from '../../../containers/Segment/Content/SegmentMember/styled';
import { getLabelPortalTimeZone } from '../../../modules/Dashboard/utils';
import { DisplayTimeZone } from '../../../modules/Dashboard/Profile/Segment/Create/styles';
import InputPreview from '../../Atoms/InputPreview';
import { Flex } from '@antscorp/antsomi-ui';

const ComputationSchedule = props => {
  const {
    typeRender = 'vertical',
    value,
    onChange,
    repeatByOptions,
    design,
    callback,
    className,
    disabled,
    isViewMode,
    isJNTactic,
  } = props;
  const limitScheduleError = get(props, 'errors.limit_schedule[0]', '');

  const [errors, setErrors] = useImmer({
    repeatOnValue: '',
  });
  const classes = useStyles();
  const [setting, setSetting] = useImmer(defaultComputationSchedule);

  const [checkChangeDayEndTime, setCheckChangeDayEndTime] = useState('');
  const defaultNumRunning = 1;

  useEffect(() => {
    if (value) {
      // set initValue
      setSetting(() => ({ ...defaultComputationSchedule, ...value }));
    }
  }, []);

  // useEffect(() => {
  //   if (value && typeof onChange === 'function') {
  //     onChange(value);
  //   }
  // }, [value]);

  useEffect(() => {
    if (
      (value.repeatType === REPEAT_BY_OPTIONS.weeks.value ||
        value.repeatType === REPEAT_BY_OPTIONS.months.value) &&
      value.repeatOnValue.length === 0
    ) {
      setErrors(draft => {
        draft.repeatOnValue = 'Please complete computation schedule setting';
      });
    } else {
      setErrors(draft => {
        draft.repeatOnValue = '';
      });
    }
  }, [value.repeatType, value.repeatOnValue]);

  const handleOnChange = newValue => {
    if (onChange && !disabled) {
      return onChange(newValue);
    }
  };

  const renderRepeatOn = () => {
    if (
      value.repeatType === REPEAT_BY_OPTIONS.weeks.value ||
      value.repeatType === REPEAT_BY_OPTIONS.months.value
    ) {
      return (
        <FormGroup className="row">
          <WrapperTextLable>Repeat on :</WrapperTextLable>
          <div className="container-field" style={{ maxWidth: '475px' }}>
            <DayOfMonths>{renderArrTime()}</DayOfMonths>
          </div>
        </FormGroup>
      );
    }
  };

  const onChangeRepeatOn = id => {
    handleOnChange(
      produce(value, draft => {
        const index = value.repeatOnValue.findIndex(item => item === id);
        if (index === -1) {
          draft.repeatOnValue.push(id);
        } else {
          draft.repeatOnValue.splice(index, 1);
        }
      }),
    );
  };

  const onChangeRepeatBy = data => {
    if (data) {
      handleOnChange(
        produce(value, draft => {
          draft.repeatType = data.value;
          draft.repeatOnValue =
            data.value === REPEAT_BY_OPTIONS.weeks.value
              ? [1]
              : data.value === REPEAT_BY_OPTIONS.months.value
              ? [END_OF_MONTH]
              : [];

          if (data.value === REPEAT_BY_OPTIONS.hours.value) {
            draft.repeatValue = defaultComputationSchedule.repeatValue;
            draft.repeatStartTime = defaultComputationSchedule.repeatStartTime;
          } else if (data.value === REPEAT_BY_OPTIONS.none.value) {
            draft.repeatStartTime = defaultComputationSchedule.repeatStartTime;
            draft.endTime = defaultComputationSchedule.endTime;
          } else {
            draft.repeatValue = 1;
          }
        }),
      );
      if (typeof callback === 'function') {
        callback('RESET_SUGGESTION_TIME');
      }
    }
  };

  const minusValue = () => {
    if (+value.repeatValue > REPEAT_BY_OPTIONS[value.repeatType].min) {
      handleOnChange(
        produce(value, draft => {
          draft.repeatValue = value.repeatValue - 1;
        }),
      );
    }
  };

  const minusValueEndTime = () => {
    let newValue = produce(value, draft => {
      draft.endTime.type = END_TIME_OPTIONS.AFTER;
    });
    if (value.endTime.numRunning == '') {
      newValue = produce(newValue, draft => {
        draft.endTime.numRunning = defaultNumRunning;
      });
    } else if (+value.endTime.numRunning > 1) {
      newValue = produce(newValue, draft => {
        draft.endTime.numRunning = +value.endTime.numRunning - 1;
      });
    }
    if (value.endTime !== '') {
      newValue = produce(newValue, draft => {
        draft.endTime.onDay = defaultEndTime;
      });
    }
    handleOnChange(newValue);
  };

  const plusValue = () => {
    if (+value.repeatValue < REPEAT_BY_OPTIONS[value.repeatType].max) {
      handleOnChange(
        produce(value, draft => {
          draft.repeatValue = +draft.repeatValue + 1;
        }),
      );
    }
  };

  const plusValueEndTime = () => {
    let newValue = produce(value, draft => {
      draft.endTime.type = END_TIME_OPTIONS.AFTER;
    });
    if (+value.endTime.numRunning < 1000) {
      newValue = produce(newValue, draft => {
        draft.endTime.numRunning = +value.endTime.numRunning + 1;
      });
    } else {
      newValue = produce(newValue, draft => {
        draft.endTime.numRunning = 1000;
      });
    }
    if (value.endTime !== '') {
      newValue = produce(newValue, draft => {
        draft.endTime.onDay = defaultEndTime;
      });
    }
    handleOnChange(newValue);
  };

  const callbackStartDatePicker = newProps => {
    // update Day
    const newDate = moment(newProps).format(FORMAT_DATE);
    let newValue = produce(value, draft => {
      draft.repeatStartTime.day = newDate;
    });

    const startTime = parseDayHourToMoment(
      newProps,
      value.repeatStartTime.hour,
    );

    const endTime = parseDayHourToMoment(
      value.endTime.onDay.day,
      value.endTime.onDay.hour,
    );
    // if startTime > endTime => set valid endTime
    if (startTime > endTime) {
      // update optionEndTime & select first item is not disable
      const validHourEndTime = hoursEndTime(
        { ...value.repeatStartTime, day: newDate },
        { ...value.repeatStartTime, day: newDate },
      ).find(time => !time.disabled);

      newValue = produce(newValue, draft => {
        draft.endTime.onDay.day = newDate;
        draft.endTime.onDay.hour = validHourEndTime.value;
      });
    }
    // if startTime > currentTime => no need to set new hour
    if (startTime > currentTime) {
      handleOnChange(newValue);
      return;
    }
    // update optionStartTime & select first item is not disable
    const validHourStartTime = hoursStartTime(newDate).find(
      time => !time.disabled,
    );
    if (validHourStartTime) {
      newValue = produce(newValue, draft => {
        draft.repeatStartTime.hour = validHourStartTime.value;
      });
    }

    handleOnChange(newValue);
  };

  const callbackEndDatePicker = newProps => {
    // update Day & select
    let newValue = produce(value, draft => {
      draft.endTime.type = END_TIME_OPTIONS.ON;
    });
    const newDate = moment(newProps).format(FORMAT_DATE);

    setCheckChangeDayEndTime(newDate);
    newValue = produce(newValue, draft => {
      draft.endTime.onDay.day = newDate;
      draft.endTime.numRunning = '';
    });

    // update Hour
    const endTime = parseDayHourToMoment(newProps, value.endTime.onDay.hour);
    const startTime = parseDayHourToMoment(
      value.repeatStartTime.day,
      value.repeatStartTime.hour,
    );

    // if endTime > currentTime => no need to set new hour
    if (endTime > currentTime && endTime > startTime) {
      handleOnChange(newValue);
      return;
    }

    const validHour = hoursEndTime(
      { ...value.endTime.onDay, day: newDate },
      value.repeatStartTime,
    ).find(
      time =>
        // moment(`${newDate} ${time.value}`, FORMAT_FULL_DATE) > currentTime,
        !time.disabled,
    );

    if (validHour) {
      newValue = produce(newValue, draft => {
        draft.endTime.onDay.hour = validHour.value;
      });
    }
    handleOnChange(newValue);
  };

  const onChangeStartTimeHour = newHour => {
    handleOnChange(
      produce(value, draft => {
        draft.repeatStartTime.hour = newHour.value;
      }),
    );
  };

  const onChangeEndTimeHour = newHour => {
    handleOnChange(
      produce(value, draft => {
        draft.endTime.onDay.hour = newHour.value;
        draft.endTime.type = END_TIME_OPTIONS.ON;
      }),
    );
  };

  const formatLabelDayOfWeek = key => {
    switch (key) {
      case 'SUNDAY':
        return 'SUN';
      case 'MONDAY':
        return 'MON';
      case 'TUESDAY':
        return 'TUE';
      case 'WEDNESDAY':
        return 'WED';
      case 'THURSDAY':
        return 'THUR';
      case 'FRIDAY':
        return 'FRI';
      case 'SATURDAY':
        return 'SAT';
      default:
        break;
    }
  };
  const renderArrTime = () => {
    let result = null;

    switch (value.repeatType) {
      case REPEAT_BY_OPTIONS.weeks.value: {
        result = DAY_OF_WEEK.map(item => {
          return (
            <span
              key={item.value}
              onClick={() => {
                if (isViewMode) return;

                onChangeRepeatOn(item.value);
              }}
              className={`${isViewMode ? 'viewMode' : 'day'}${
                value.repeatOnValue.includes(item.value) ? ' active' : ''
              }`}
            >
              {formatLabelDayOfWeek(item.label)}
            </span>
          );
        });
        break;
      }
      case REPEAT_BY_OPTIONS.months.value: {
        result = (
          <>
            {DAY_OF_MONTH.map(item => {
              return (
                <span
                  key={item}
                  onClick={() => {
                    if (isViewMode) return;

                    onChangeRepeatOn(item);
                  }}
                  className={`${isViewMode ? 'viewMode' : 'day'}${
                    value.repeatOnValue.includes(item) ? ' active' : ''
                  }`}
                >
                  {item}
                </span>
              );
            })}
            <span
              className={`${isViewMode ? 'viewMode' : 'day'}${
                value.repeatOnValue.includes(END_OF_MONTH) ? ' active' : ''
              }`}
              onClick={() => {
                if (isViewMode) return;

                onChangeRepeatOn(END_OF_MONTH);
              }}
            >
              End of month
            </span>
          </>
        );
        break;
      }
      default:
        break;
    }

    return result;
  };

  const onChangeEndTime = type => {
    let newValue = produce(value, draft => {
      draft.endTime.type = type;
    });
    switch (type) {
      case END_TIME_OPTIONS.NEVER: {
        newValue = produce(newValue, draft => {
          draft.endTime.onDay = defaultEndTime;
          draft.endTime.numRunning = '';
        });

        break;
      }
      case END_TIME_OPTIONS.ON: {
        if (checkChangeDayEndTime == '') {
          newValue = produce(newValue, draft => {
            draft.endTime.onDay.day = defaultEndTime.day;
            draft.endTime.onDay.hour = defaultEndTime.hour;
            draft.endTime.numRunning = '';
          });
        } else {
          newValue = produce(newValue, draft => {
            draft.endTime.onDay.day = checkChangeDayEndTime;
            draft.endTime.onDay.hour = defaultEndTime.hour;
            draft.endTime.numRunning = '';
          });
        }
        break;
      }
      case END_TIME_OPTIONS.AFTER: {
        newValue = produce(newValue, draft => {
          draft.endTime.onDay = defaultEndTime;
          draft.endTime.numRunning = defaultNumRunning;
        });
        break;
      }
      default:
        break;
    }
    handleOnChange(newValue);
  };
  const onChangeInputRepeat = object => {
    const { event, type } = object || {};

    const updatedValue =
      event.target.value < 1
        ? 1
        : event.target.value > 1000
        ? 1000
        : event.target.value;

    if (updatedValue && type) {
      switch (type) {
        case 'REPEAT_VALUE': {
          handleOnChange(
            produce(value, draft => {
              draft.repeatValue = +updatedValue;
            }),
          );
          break;
        }
        case 'END_TIME': {
          handleOnChange(
            produce(value, draft => {
              draft.endTime.numRunning = +updatedValue;
            }),
          );
          break;
        }
        default:
          break;
      }
    }
  };

  const onChangeRepeatEvery = event => {
    const updatedValue =
      event.target.value < REPEAT_BY_OPTIONS[value.repeatType].min
        ? REPEAT_BY_OPTIONS[value.repeatType].min
        : event.target.value > REPEAT_BY_OPTIONS[value.repeatType].max
        ? REPEAT_BY_OPTIONS[value.repeatType].max
        : event.target.value;

    handleOnChange(
      produce(value, draft => {
        draft.repeatValue = updatedValue;
      }),
    );
  };

  const hoursStartTime = startDate =>
    HOURS.map(hour => {
      const [hourOption, minuteOption] = hour.value.split(':');

      const startTimeHour = moment(startDate, FORMAT_DATE).set({
        hour: hourOption,
        minute: minuteOption,
        second: 0,
      });
      return {
        ...hour,
        disabled: currentTime >= startTimeHour,
      };
    });

  const hoursEndTime = (endDate, startDate) =>
    HOURS.map(hour => {
      const [hourOption, minuteOption] = hour.value.split(':');
      const [hourStarTime, minuteStartTime] = startDate.hour.split(':');

      const startTimeHour = moment(startDate.day, FORMAT_DATE).set({
        hour: hourStarTime,
        minute: minuteStartTime,
        second: 0,
      });

      const endTimeHour = moment(endDate.day, FORMAT_DATE).set({
        hour: hourOption,
        minute: minuteOption,
        second: 0,
      });

      return {
        ...hour,
        disabled: currentTime > endTimeHour || startTimeHour >= endTimeHour,
      };
    });

  const onClickViewCalendar = () => {
    if (callback && typeof callback === 'function') {
      callback('TOGGLE_MODAL_CALENDAR_TABLE', true);
    }
  };

  const getTextModeComputeSchedule = type => {
    switch (type) {
      case END_TIME_OPTIONS.NEVER:
        return 'Never';
      case END_TIME_OPTIONS.ON:
        return (
          <div className={'radio-multi d-flex align-items-center'}>
            <label className="d-flex align-items-center">
              <Text style={{ marginRight: '9px' }}>On</Text>
            </label>
            <MuiPickersUtilsProvider utils={DateFnsUtils}>
              <TextViewMode>
                {moment(value.repeatStartTime.day).format('MMMM DD,YYYY')}
              </TextViewMode>
            </MuiPickersUtilsProvider>
            <span
              style={{
                color: '#666',
                fontSize: '11px',
                margin: ' 0 14px 0 25px',
              }}
              className="nowrap"
            >
              at
            </span>
            <WrapperSelect style={{ display: 'flex', alignItems: 'center' }}>
              <TextViewMode>
                {MAP_HOURS[value.endTime.onDay.hour].label}
              </TextViewMode>
              <DisplayTimeZone className="nowrap">
                {getLabelPortalTimeZone()}
              </DisplayTimeZone>
            </WrapperSelect>
          </div>
        );

      case END_TIME_OPTIONS.AFTER:
        return (
          <div className={'radio-multi d-flex align-items-center'}>
            <label className="d-flex align-items-center">
              <Text style={{ marginRight: '9px' }}>After</Text>
            </label>
            <TextViewMode>
              {value.endTime.numRunning
                ? value.endTime.numRunning
                : defaultNumRunning}
            </TextViewMode>
            <WrapperTextValue style={{ marginLeft: '15px' }}>
              occurrences
            </WrapperTextValue>
          </div>
        );

      default:
        return null;
    }
  };

  const renderEndTime = () => {
    return (
      <FormGroup className={'row'}>
        <WrapperTextLable>End time :</WrapperTextLable>
        <div className="container-field">
          <InputPreview
            type="input"
            isViewMode={props.isViewMode}
            value={getTextModeComputeSchedule(value.endTime.type)}
            style={{ padding: 0 }}
          >
            <RadioGroup
              name="endTime"
              value={value.endTime.type}
              onChange={(event, valueType) => onChangeEndTime(valueType)}
              style={{ gap: '10px' }}
            >
              <FormControlLabel
                value={END_TIME_OPTIONS.NEVER}
                className={`${classes.customSizeInput} label-radio-config`}
                control={
                  <Radio
                    color="primary"
                    name={'2'}
                    style={{ width: '20px', height: '20px' }}
                  />
                }
                label="Never"
                disabled={isViewMode}
              />
              <FormControlLabel
                value={END_TIME_OPTIONS.ON}
                className={`${classes.customSizeInput} label-radio-config`}
                control={
                  <Radio
                    color="primary"
                    name={'2'}
                    style={{ width: '20px', height: '20px' }}
                    disabled={isViewMode}
                  />
                }
                label={
                  <div className={'radio-multi d-flex align-items-center'}>
                    <label className="d-flex align-items-center">
                      <Text style={{ marginRight: '9px' }}>On</Text>
                    </label>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      {isViewMode ? (
                        <TextViewMode>
                          {moment(value.repeatStartTime.day).format(
                            'MMMM DD,YYYY',
                          )}
                        </TextViewMode>
                      ) : (
                        <UIInputCalendar
                          format="dd/MM/yyyy"
                          // value={moment(value.endTime.onDay.day, FORMAT_DATE)}
                          value={value.endTime.onDay.day}
                          onChange={callbackEndDatePicker}
                          helperText=""
                          isShowLabel={false}
                          minDate={moment(value.repeatStartTime.day)}
                          className={classes.inputCalendar}
                        />
                      )}

                      {/* <StyledKeyboardDatePicker
                      autoOk
                      disableToolbar
                      variant="inline"
                      format={'dd/MM/yyyy'}
                      value={moment(value.endTime.onDay.day, FORMAT_DATE)}
                      onChange={callbackEndDatePicker}
                      helperText=""
                      className="m-right-3"
                      disablePast
                      minDate={moment(value.repeatStartTime.day)}
                    /> */}
                    </MuiPickersUtilsProvider>
                    <span
                      style={{
                        color: '#666',
                        fontSize: '11px',
                        margin: ' 0 14px 0 25px',
                      }}
                    >
                      at
                    </span>
                    <WrapperSelect
                      style={{ display: 'flex', alignItems: 'center' }}
                    >
                      {isViewMode ? (
                        <TextViewMode>
                          {MAP_HOURS[value.endTime.onDay.hour].label}
                        </TextViewMode>
                      ) : (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          fullWidthPopover
                          // minWidth={100}
                          options={hoursEndTime(
                            value.endTime.onDay,
                            value.repeatStartTime,
                          )}
                          value={MAP_HOURS[value.endTime.onDay.hour]}
                          onChange={item => onChangeEndTimeHour(item)}
                        />
                      )}

                      <DisplayTimeZone>
                        {getLabelPortalTimeZone()}
                      </DisplayTimeZone>
                    </WrapperSelect>
                  </div>
                }
              />
              <FormControlLabel
                value={END_TIME_OPTIONS.AFTER}
                className={`${classes.customSizeInput} label-radio-config`}
                control={
                  <Radio
                    color="primary"
                    name={'2'}
                    style={{ width: '20px', height: '20px' }}
                    disabled={isViewMode}
                  />
                }
                label={
                  <div className={'radio-multi d-flex align-items-center'}>
                    <label className="d-flex align-items-center">
                      <Text style={{ marginRight: '9px' }}>After</Text>
                    </label>
                    {isViewMode ? (
                      <TextViewMode>
                        {value.endTime.numRunning
                          ? value.endTime.numRunning
                          : defaultNumRunning}
                      </TextViewMode>
                    ) : (
                      <GroupInputNumber className={''}>
                        <span
                          className={'input-number-decrement'}
                          onClick={minusValueEndTime}
                        >
                          -
                        </span>
                        <Input
                          inputProps={{
                            className: 'input-component',
                          }}
                          className={`${classes.textValue} ${
                            classes.width
                          } input-number`}
                          type="number"
                          value={
                            value.endTime.numRunning
                              ? value.endTime.numRunning
                              : defaultNumRunning
                          }
                          onChange={e =>
                            onChangeInputRepeat({
                              event: e,
                              type: 'END_TIME',
                            })
                          }
                        />
                        <span
                          className={'input-number-increment'}
                          onClick={plusValueEndTime}
                        >
                          +
                        </span>
                      </GroupInputNumber>
                    )}

                    <WrapperTextValue style={{ marginLeft: '15px' }}>
                      occurrences
                    </WrapperTextValue>
                  </div>
                }
              />
            </RadioGroup>
          </InputPreview>
        </div>
      </FormGroup>
    );
  };

  const renderContent = () => {
    return (
      <div className={'block-sub-section-content'}>
        <div className={'block-border form'}>
          <FormGroup className={'row align-items-center'}>
            <WrapperTextLable>Repeat every:</WrapperTextLable>
            <div className="container-field">
              <div className="d-flex align-items-center">
                {isViewMode ? (
                  <TextViewMode>{+value.repeatValue}</TextViewMode>
                ) : (
                  <GroupInputNumber>
                    <span
                      className={'input-number-decrement'}
                      onClick={minusValue}
                    >
                      –
                    </span>
                    <Input
                      inputProps={{
                        className: 'input-component',
                      }}
                      className={`${classes.textValue} ${
                        classes.width
                      } input-number`}
                      type="number"
                      value={+value.repeatValue}
                      onChange={onChangeRepeatEvery}
                    />
                    <span
                      className={'input-number-increment'}
                      onClick={plusValue}
                    >
                      +
                    </span>
                  </GroupInputNumber>
                )}

                <span className="m-left-4">
                  <WrapperTextValue>
                    {repeatByOptions &&
                      repeatByOptions.map(item => {
                        if (item.value == value.repeatType) {
                          return item.value;
                        }
                      })}
                  </WrapperTextValue>
                </span>
              </div>
            </div>
          </FormGroup>
          <div
            className={typeRender === 'vertical' ? 'vertical' : 'horizontal'}
          >
            <div>
              <FormGroup className={'row align-items-center'}>
                <WrapperTextLable>Start time :</WrapperTextLable>
                <div className="container-field">
                  <div className="d-flex align-items-center">
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      {isViewMode ? (
                        <TextViewMode className="nowrap">
                          {moment(value.repeatStartTime.day).format(
                            'MMMM DD,YYYY',
                          )}
                        </TextViewMode>
                      ) : (
                        <UIInputCalendar
                          format="dd/MM/yyyy"
                          // value={moment(value.repeatStartTime.day, FORMAT_DATE)}
                          value={value.repeatStartTime.day}
                          onChange={callbackStartDatePicker}
                          helperText=""
                          minDate={new Date()}
                          className={classes.inputCalendar}
                          isShowLabel={false}
                        />
                      )}

                      {/* <StyledKeyboardDatePicker
                        autoOk
                        disableToolbar
                        variant="inline"
                        format={'dd/MM/yyyy'}
                        value={moment(value.repeatStartTime.day, FORMAT_DATE)}
                        onChange={callbackStartDatePicker}
                        helperText=""
                        className="m-right-3"
                        disablePast
                        disabled={design === 'update'}
                      /> */}
                    </MuiPickersUtilsProvider>
                    {value.repeatType === REPEAT_BY_OPTIONS.hours.value ? (
                      <>
                        <span
                          style={{
                            color: '#666',
                            fontSize: '11px',
                            margin: '0 14px',
                          }}
                          className="nowrap"
                        >
                          at
                        </span>
                        <WrapperSelect
                          style={{ display: 'flex', alignItems: 'center' }}
                        >
                          {isViewMode ? (
                            <TextViewMode>
                              {value.repeatStartTime.hour}
                            </TextViewMode>
                          ) : (
                            <UISelect
                              onlyParent
                              use="tree"
                              isSearchable={false}
                              fullWidthPopover
                              // minWidth={100}
                              options={hoursStartTime(
                                value.repeatStartTime.day,
                              )}
                              value={MAP_HOURS[value.repeatStartTime.hour]}
                              onChange={item => onChangeStartTimeHour(item)}
                              disabled={design === 'update'}
                            />
                          )}

                          <DisplayTimeZone className="nowrap">
                            {getLabelPortalTimeZone()}
                          </DisplayTimeZone>
                        </WrapperSelect>
                      </>
                    ) : null}
                  </div>
                </div>
              </FormGroup>
              {renderRepeatOn()}
            </div>
            {renderEndTime()}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Wrapper
      className={cls(className, {
        disabled,
      })}
      isViewMode={isViewMode}
    >
      {/* <div className={'block-sub-section-label'}>
        <span>What time do you want the dataflow to execute?</span>
      </div> */}
      <FormGroup
        className={'d-flex align-items-center'}
        style={{ paddingTop: isViewMode ? '0.4rem' : 0 }}
      >
        <Grid container xs={12}>
          <Grid
            item
            sm={2}
            style={{
              display: 'flex',
              alignItems: 'center',
              maxWidth: props.isSegment || isJNTactic ? '72px' : '17%',
            }}
          >
            <label
              className="text-right m-bottom-0"
              style={{
                color: '#000000',
                fontSize: '12px',
              }}
            >
              Repeat by
            </label>
          </Grid>
          <Grid sm={10}>
            <Flex>
              <WrapperSelect
                width="94px"
                style={{ display: 'flex', alignItems: 'center' }}
              >
                {isViewMode ? (
                  <TextViewMode>
                    {REPEAT_BY_OPTIONS[value.repeatType].label}
                  </TextViewMode>
                ) : (
                  <UISelect
                    onlyParent
                    use="tree"
                    isSearchable={false}
                    fullWidthPopover
                    // minWidth={100}
                    // options={repeatTimes}
                    options={repeatByOptions}
                    value={REPEAT_BY_OPTIONS[value.repeatType]}
                    onChange={item => onChangeRepeatBy(item)}
                  />
                )}
              </WrapperSelect>
              <div className="d-flex align-items-center">
                {value.repeatType === REPEAT_BY_OPTIONS.days.value ||
                value.repeatType === REPEAT_BY_OPTIONS.weeks.value ||
                value.repeatType === REPEAT_BY_OPTIONS.months.value ? (
                  <div className="d-flex align-items-center">
                    <span className="m-x-2">at</span>
                    <WrapperSelect
                      style={{ display: 'flex', alignItems: 'center' }}
                    >
                      {isViewMode ? (
                        <TextViewMode>
                          {MAP_HOURS[value.repeatStartTime.hour].label}
                        </TextViewMode>
                      ) : (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          fullWidthPopover
                          // minWidth={100}
                          options={hoursStartTime(value.repeatStartTime.day)}
                          value={MAP_HOURS[value.repeatStartTime.hour]}
                          onChange={item => onChangeStartTimeHour(item)}
                          // disabled={design === 'update'}
                        />
                      )}

                      <DisplayTimeZone>
                        {getLabelPortalTimeZone()}
                      </DisplayTimeZone>
                    </WrapperSelect>
                  </div>
                ) : null}
              </div>
            </Flex>
          </Grid>
        </Grid>
      </FormGroup>
      <Grid container xs={12}>
        <Grid
          sm={2}
          style={{ maxWidth: props.isSegment || isJNTactic ? '72px' : '17%' }}
        />
        <Grid sm={10}>
          {limitScheduleError ? (
            <FormGroup className={'d-flex align-items-center'}>
              <p
                style={{
                  marginLeft:
                    value.repeatType === REPEAT_BY_OPTIONS.hours.value
                      ? '62px'
                      : '190px',
                }}
                className="text-limit"
              >
                <IconXlab
                  name="report-problem"
                  fontSize="20px"
                  color={COLORS.selectedYellow}
                  className="bottom-align m-right-1"
                />
                {limitScheduleError}{' '}
                <span className="text-calendar" onClick={onClickViewCalendar}>
                  View calendar
                </span>
              </p>
            </FormGroup>
          ) : null}
          {value.repeatType !== REPEAT_BY_OPTIONS.none.value &&
          value.repeatType !== REPEAT_BY_OPTIONS.real_time.value
            ? renderContent()
            : null}
          {errors.repeatOnValue ? (
            <TextError>{errors.repeatOnValue}</TextError>
          ) : null}
        </Grid>
      </Grid>
    </Wrapper>
  );
};

ComputationSchedule.propTypes = {
  typeRender: PropTypes.oneOf(['vertical', 'horizontal']),
  value: PropTypes.shape({
    repeatType: PropTypes.string,
    repeatOnValue: PropTypes.array,
    repeatValue: PropTypes.number,
    repeatStartTime: PropTypes.shape({
      day: PropTypes.string,
      hour: PropTypes.string,
    }),
    endTime: PropTypes.shape({
      type: PropTypes.string,
      numRunning: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      onDay: PropTypes.shape({
        day: PropTypes.string,
        hour: PropTypes.string,
      }),
    }),
  }),
  onChange: PropTypes.func,
  repeatByOptions: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
  ),
  design: PropTypes.oneOf(['create', 'update']),
  callback: PropTypes.func,
  // eslint-disable-next-line react/no-unused-prop-types
  errors: PropTypes.object,
  //   { label: 'Hour', value: repeatValue.HOUR },
  className: PropTypes.string,
  disabled: PropTypes.bool,
  isViewMode: PropTypes.bool,
  isSegment: PropTypes.bool,
  isJNTactic: PropTypes.bool,
  isShowDrawer: PropTypes.bool,
};
ComputationSchedule.defaultProps = {
  isJNTactic: false,
  isSegment: false,
  repeatByOptions: defaultRepeatByOptions,
};

export default ComputationSchedule;

/* eslint-disable react/prop-types */
import PropTypes from 'prop-types';
import React from 'react';
import { UIIconButton as IconButton, UITippy } from '@xlab-team/ui-components';
import IconXlab from 'components/common/UIIconXlab';
import colors from 'utils/colors';
import queryString from 'query-string';
import {
  Selection,
  TagSelection,
  LeftSide,
  RightSide,
  WarpperIcon,
  WarpperIconFlex,
  TagSelectionLink,
} from './styles';
import { getLabelCodeStatus } from '../../../../../../services/Abstract.data';
import { toSegmentInsight } from 'containers/Drawer/DrawerSegment/utils';

import { getCellDisplayNumber } from '../../../../../../containers/Table/utils';

// Translations
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';

function ColumnSelection(props) {
  const styleItem =
    props.propertyCode === 'id' ||
    props.propertyCode === 'customer_id' ||
    props.propertyCode === 'segment_ids' ||
    props.propertyCode === 'campaign_id';
  // ||
  // props.propertyCode === 'story_id';
  const isId =
    props.propertyCode === 'suggestion_with_pool_id' ||
    props.propertyCode === 'suggestion_with_source_id';
  const isName = props.propertyCode === 'suggestion_with_connector_id';
  const isCodeStatus = props.propertyCode === 'code_status';
  const isArchive = props.status === 4;
  const isDelete = props.status === 3;
  const isBoderNoti = props.status === 3 || props.status === 4;
  const isFlag = props.status === 5;

  let labelNoti = props.objectLabel || '';
  if (props.itemTypeId !== undefined) {
    labelNoti = +props.itemTypeId === 1 ? 'collection' : 'segment';
  }

  const column = {
    displayFormat: {
      type: 'NUMBER',
      config: { decimal: '.', decimalPlace: 0, group: ',' },
    },
  };

  const getContentTippy = () => {
    if (isArchive) return `This ${labelNoti} is archived`;

    if (!props.hasPermission)
      return getTranslateMessage(
        TRANSLATE_KEY._NOTI_SEGMENT_REMOVE_PERMISSION,
        'You do not have permission for this segment',
      );

    return `This ${labelNoti} does not exist`;
  };

  return (
    <Selection isArchived={isBoderNoti || !props.hasPermission}>
      <LeftSide>
        <UITippy
          content={
            isCodeStatus
              ? getLabelCodeStatus(Number(props.data.name))
              : props.data.name
          }
          placement="top"
          arrow
          distance={10}
        >
          <div style={{ marginLeft: '1rem', width: '240px' }}>
            {props.propertyCode === 'segment_ids' ? (
              <TagSelectionLink
                onClick={() => {
                  const url = toSegmentInsight({
                    portalId: getPortalId(),
                    segmentId: props.data.id,
                  });

                  window.open(url, '_blank');
                }}
              >
                {props.data.name}
              </TagSelectionLink>
            ) : (
              <TagSelection>
                {isId
                  ? props.data.id
                  : isCodeStatus
                  ? getLabelCodeStatus(Number(props.data.name))
                  : props.data.name}
              </TagSelection>
            )}

            {styleItem && (
              <span style={{ fontSize: '12px', color: '#999' }}>
                {props.data.id}
              </span>
            )}
          </div>
        </UITippy>
        {props.propertyCode === 'segment_ids' && !isDelete && (
          <p style={{ fontSize: '12px' }}>
            {getCellDisplayNumber(column, props.data.segmentSize || 0)}
          </p>
        )}
      </LeftSide>
      <RightSide>
        <WarpperIconFlex>
          {(isArchive || isDelete || !props.hasPermission) && (
            <UITippy
              content={getContentTippy()}
              placement="top"
              arrow
              distance={10}
            >
              <div style={{ marginRight: '10px', marginTop: '3px' }}>
                <IconXlab
                  name="report-problem"
                  fontSize="16px"
                  color={colors.selectedYellow}
                  className="text-right"
                />
              </div>
            </UITippy>
          )}
          {isFlag && (
            <div style={{ marginRight: '10px' }}>
              <IconButton iconName="emoji-flags" size="20px" />
            </div>
          )}
          <WarpperIcon>
            <IconButton
              iconName="close"
              size="20px"
              color="#595959"
              onClick={() => props.handleDelete()}
            />
          </WarpperIcon>
        </WarpperIconFlex>
      </RightSide>
    </Selection>
  );
}

ColumnSelection.defaultProps = {
  hasRemove: true,
  hasPermission: true,
};

ColumnSelection.propTypes = {
  hasRemove: PropTypes.bool,
  hasPermission: PropTypes.bool,
  children: PropTypes.node.isRequired,
  handleDelete: PropTypes.func.isRequired,
};

export default ColumnSelection;

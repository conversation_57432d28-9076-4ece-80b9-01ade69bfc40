/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import {
  <PERSON><PERSON>ip<PERSON>,
  UITextSearch as TextSearch,
  UIIconButton,
} from '@xlab-team/ui-components';

import { useImmer } from 'use-immer';
import {
  AccountItem,
  ContainerTable,
  Content,
  Item,
  ItemName,
  ListContent,
  PopoverStyle,
  Text,
  WrapperAddMore,
  WrapperContent,
  WrapperContentColumns,
  WrapperOriginalAttribute,
  WrapperSearch,
  WrapperTitle,
} from './styled';
import { getErrorMessageV2Translate } from '../../../utils/web/message';
import TRANSLATE_KEY from '../../../messages/constant';
import { Scrollbars } from '@antscorp/antsomi-ui';

const ATTRIBUTE = 'attribute';
const MEASURE = 'measure';
export const MAP_COLOR = {
  F: '#707070',
  D: '#005fb8',
};
const NOTI = {
  fail: res => ({
    id: 'clone-name-error',
    ...getErrorMessageV2Translate(res.codeMessage),
    timeout: 4000,
    timestamp: *************,
    type: 'danger',
  }),
  success: () => ({
    id: 'clone-name-success',
    message: `Save!`,
    translateCode: TRANSLATE_KEY._NOTIFICATION_SUCCESS,
    timeout: 4000,
    timestamp: *************,
    type: 'success',
  }),
  serviceNotFound: () => ({
    id: 'object-server-error',
    message: 'Service not found',
    timeout: 4000,
    timestamp: *************,
    type: 'danger',
  }),
};

const CustomNode = props => {
  const { data, content, activeValue } = props;
  const [state, setState] = useImmer({
    key: open ? 'simple-popover-action-label' : undefined,
    activeValue: '',
    listEventAttr: [],
    searchValueAttr: '',
    searchListAttr: [],
    listBO: [],
    searchValueBO: '',
    searchListBO: [],
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElEvent, setAnchorElEvent] = useState(null);
  const open = Boolean(anchorEl);
  const openEvent = Boolean(anchorElEvent);
  let hasOriginalAttr = false;
  let hasMeasureField = false;
  const splitContent = content.split('@@');
  const handleClick = (key, event, value) => {
    setState(draft => {
      draft.key = key;
      if (value) {
        draft.activeValue = value.id;
      }
      if (key === 'block-child') {
        draft.listEventAttr = value.columns;
      } else if ('block-bo') {
        draft.listBO = parseObjectToArray(value);
      }
    });
    if (key === 'block-child') {
      data.onClick(value);
    }
    setAnchorEl(event.currentTarget);
  };
  const handleClickContent = value => {
    setState(draft => {
      if (value === state.activeValue) {
        draft.activeValue = '';
      } else {
        draft.activeValue = value;
      }
    });
    data.onClick('ad_zone');
  };
  const handleClickEvent = event => {
    data.onClickToggele(true);
    setAnchorElEvent(event.currentTarget);
  };
  const handleCloseEvent = () => {
    data.onClickToggele(false);
    setAnchorElEvent(null);
    setState(draft => {
      draft.searchValueAttr = '';
      draft.searchListAttr = [];
    });
  };
  const handleClose = () => {
    setState(draft => {
      draft.activeValue = '';
      draft.searchListBO = [];
      draft.searchValueBO = '';
    });
    setAnchorEl(null);
  };
  const onSearch = value => {
    let tempState = [...state.listEventAttr];
    if (value.length > 0) {
      tempState = tempState.filter(item => {
        if (
          item.event_property_name
            .toLowerCase()
            .includes(value.trim().toLowerCase())
        ) {
          return item;
        }
      });
    }
    setState(draft => {
      draft.searchValueAttr = value;
      draft.searchListAttr = tempState;
    });
  };
  const onSearchBO = value => {
    let tempState = [...state.listBO];
    if (value.length > 0) {
      tempState = tempState.filter(item => {
        if (item.label.toLowerCase().includes(value.trim().toLowerCase())) {
          return item;
        }
      });
    }
    setState(draft => {
      draft.searchValueBO = value;
      draft.searchListBO = tempState;
    });
  };
  const id = open ? 'simple-popover-action-label' : undefined;
  const ids = open ? 'simple-child-popover-action-label' : undefined;

  const UITippyData = ({ name, id, onClick, isActive }) => (
    <UITippy content={name} placement="top" arrow distance={10}>
      <Item className="item-content" onClick={onClick} isActive={isActive}>
        <ItemName
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
          className="name--color"
        >
          {name}
        </ItemName>
      </Item>
    </UITippy>
  );
  Object.keys(data).forEach(key => {
    if (data[key].type === ATTRIBUTE) {
      hasOriginalAttr = true;
    } else {
      hasMeasureField = true;
    }
  });
  const indexType = splitContent[1] === 'D' ? 5 : Object.keys(data.data).length;
  const indexTypeBO =
    splitContent[1] === 'F' ? 5 : Object.keys(data.data).length;
  return (
    <ContainerTable className="node-wrapper">
      <WrapperContent
        bgColor={MAP_COLOR[splitContent[1]]}
        className="node-wrapper-content"
      >
        <UITippy
          arrow
          distance={10}
          content={
            data.status == 4
              ? 'The Business Object is archived'
              : splitContent[0]
          }
        >
          <WrapperTitle bgColor={MAP_COLOR[splitContent[1]]}>
            {splitContent[0]}
          </WrapperTitle>
        </UITippy>
        <WrapperContentColumns
          bgColor={MAP_COLOR[splitContent[1]]}
          type={splitContent[1]}
          className="node-wrapper-content-columns"
        >
          {Object.keys(data.data).map((key, index) => {
            if (index < indexType) {
              return (
                <UITippy
                  key={data.data[key].label}
                  arrow
                  distance={10}
                  content={data.data[key].label}
                >
                  {data.data[key].columns ? (
                    <Content
                      onClick={event =>
                        handleClick('block-child', event, data.data[key])
                      }
                      isActive={key === state.activeValue}
                    >
                      <Text className="measure-attribute-field">
                        {data.data[key].label}
                      </Text>
                      <UIIconButton
                        iconName="arrow-right"
                        className="icon-arrow-right"
                        size="20px"
                        aria-describedby={key}
                      />
                    </Content>
                  ) : (
                    <Content
                      onClick={() => handleClickContent(key)}
                      isActive={key === state.activeValue}
                    >
                      <Text className="measure-attribute-field">
                        {data.data[key].label}
                      </Text>
                    </Content>
                  )}
                </UITippy>
              );
            }
          })}
          {splitContent[1] !== 'F' && Object.keys(data.data).length > 5 && (
            <WrapperAddMore
              className="add-more"
              aria-describedby={id}
              onClick={event => handleClick('block-bo', event, data.data)}
            >
              {`View ${Object.keys(data.data).length - 5} more...`}
            </WrapperAddMore>
          )}
          {/* <WrapperAddMore
            className="add-more"
            aria-describedby={id}
            onClick={event => handleClick('block-bo', event)}
          >
            View 23 more...
          </WrapperAddMore> */}
          <PopoverStyle
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          // anchorPosition={{ left: -100, top: -400 }}
          >
            {state.key === 'block-bo' && (
              <WrapperSearch className="wrapper-search">
                <TextSearch
                  classDiv="search-filter"
                  placeholder="Search..."
                  // translateCode={TRANSLATE_KEY._ACT_SEARCH}
                  isTimeout
                  onChange={onSearchBO}
                  value={state.searchValueBO}
                  focus
                  type="text"
                // use="full-border"
                />
              </WrapperSearch>
            )}

            <div style={{ position: 'relative', marginTop: '15px' }}>
              <Scrollbars
                // onScroll={e => onScroll(e)}
                autoHeight
                autoHeightMax={300}
                style={{ padding: 'unset' }}
                className="all--account"
                data-testid="all--account"
              >
                {(splitContent[1] !== 'F'
                  ? state.searchValueBO
                    ? state.searchListBO
                    : state.listBO
                  : state.listEventAttr
                ).map((each, index) => {
                  if (index < indexTypeBO) {
                    return (
                      <div
                        style={{ width: state.key !== 'block-bo' && '150px' }}
                        data-testid="account-item"
                      >
                        <UITippyData
                          name={
                            splitContent[1] !== 'F'
                              ? each.label
                              : each.event_property_name
                          }
                        // onClick={() => onSelectData(each)}
                        // isActive={each.id === state.activeData.id}
                        />
                      </div>
                    );
                  }
                })}
              </Scrollbars>
            </div>
            {state.key !== 'block-bo' && state.listEventAttr.length > 5 && (
              <>
                <WrapperAddMore
                  style={{
                    padding: '5px 0px 5px 10px',
                    width: '100%',
                    textAlign: 'left',
                  }}
                  className="add-more"
                  aria-describedby={id}
                  onClick={handleClickEvent}
                >
                  {`View ${state.listEventAttr.length - 5} more...`}
                </WrapperAddMore>
                <PopoverStyle
                  id={ids}
                  open={openEvent}
                  anchorEl={anchorElEvent}
                  onClose={handleCloseEvent}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                  }}
                // anchorPosition={{ left: -100, top: -400 }}
                >
                  <WrapperSearch className="wrapper-search">
                    <TextSearch
                      classDiv="search-filter"
                      placeholder="Search..."
                      // translateCode={TRANSLATE_KEY._ACT_SEARCH}
                      isTimeout
                      onChange={onSearch}
                      value={state.searchValueAttr}
                      focus
                      type="text"
                    // use="full-border"
                    />
                  </WrapperSearch>

                  <div style={{ position: 'relative', marginTop: '15px' }}>
                    <Scrollbars
                      autoHeight
                      autoHeightMax={300}
                      // onScroll={e => onScroll(e)}
                      className="all--account"
                      data-testid="all--account"
                    >
                      {(state.searchValueAttr
                        ? state.searchListAttr
                        : state.listEventAttr
                      ).map((each, index) => {
                        return (
                          <div
                            // style={{ width: '150px' }}
                            data-testid="account-item"
                          >
                            <UITippyData
                              name={each.event_property_name}
                            // onClick={() => onSelectData(each)}
                            // isActive={each.id === state.activeData.id}
                            />
                          </div>
                        );
                      })}
                    </Scrollbars>
                  </div>
                </PopoverStyle>
              </>
            )}
          </PopoverStyle>
        </WrapperContentColumns>
      </WrapperContent>
    </ContainerTable>
  );
};

export const isDataArray = data =>
  data && Array.isArray(data) && data.length > 0;

export const convertDataToFe = (
  data,
  dataSetting,
  activeNodeFromSchema,
  actionToggleChild,
) => {
  const validateData = data || [];
  const dataNodes = {
    list: [],
    map: {},
  };
  const dataLinks = [];

  if (validateData.length === 0) {
    return dataNodes;
  }

  const floorLenght = Math.floor((validateData.length - 1) / 4);
  let left = 200 * floorLenght;
  let leftReset = 150 * (floorLenght / 2);
  let topReset = 0;
  let top = 10;
  let topMax = 0;
  validateData.forEach((item, index) => {
    // if (index > 0 && index < 10) {
    //   axisX += 75;
    //   axisY += 50;
    // } else if (index > 0 && index > 10 && axisX - 75 > 0) {
    //   axisX -= 75;
    // }
    let coordinates = [left, top];
    if (item.item_type_id === 'events') {
      coordinates = [200 * floorLenght, 400];
    } else if (index > 0) {
      if (index <= floorLenght) {
        left += 155;
        top += 400 / floorLenght;
      }
      // else if (index === floorLenght) {
      //   left += 150;
      //   top += 400 / floorLenght + 200;
      // }
      else if (index > floorLenght && index <= floorLenght * 2) {
        if (index === floorLenght + 1) {
          top += 200;
          left += 155;
        }
        left -= 155;
        top += 400 / floorLenght;
        if (index === floorLenght * 2) {
          topMax = top;
        }
      } else if (index > floorLenght * 2 && index <= floorLenght * 3) {
        if (index === floorLenght * 2 + 1) {
          left -= 200;
        }
        left -= 155;
        top -= 400 / floorLenght;
      }
      // else if (index === floorLenght * 3) {
      //   left -= 155;
      //   top = 400;
      // }
      else if (index >= floorLenght * 3 && index <= floorLenght * 4) {
        if (index === floorLenght * 3 + 1) {
          top -= 200;
        }
        left += 155;
        top -= 400 / floorLenght;
      }
      coordinates = [left, top];
      if (index > floorLenght * 4) {
        leftReset += 155;
        topReset = topMax + 200;
        coordinates = [leftReset, topReset];
      }
    }

    const temp = {
      id: item.item_type_id,
      content: `${item.item_type_display || item.item_type_id}@@${item.entityType
        }`,
      render: CustomNode,
      data: {
        onClick: activeNodeFromSchema,
        onClickToggele: actionToggleChild,
        status: item.item_status || 0,
        data: {},
      },
      // coordinates,
      coordinates:
        dataSetting && dataSetting[item.item_type_id]
          ? dataSetting[item.item_type_id]
          : coordinates,
    };

    if (isDataArray(item.columns)) {
      let tempField = {};
      item.columns.forEach(ele => {
        if (item.item_type_id === 'events') {
          tempField = {
            id: `${ele.event_action_id}_${ele.event_category_id}`,
            label: ele.event_name_display || ele.event_property_name,
            columns: ele.columns,
            link: ele.boAssociate,
            // type: ele.treatAs == 1 || ele.treatAs == 3 ? ATTRIBUTE : MEASURE,
          };
        } else {
          tempField = {
            id: ele.item_property_name,
            label: ele.item_property_display || ele.item_property_name,
            // type: ele.treatAs == 1 || ele.treatAs == 3 ? ATTRIBUTE : MEASURE,
          };
        }

        temp.data.data[tempField.id] = tempField;
      });
    }

    dataNodes.list.push(temp);
    dataNodes.map[temp.id] = temp;

    if (isDataArray(item.entityRelationships)) {
      item.entityRelationships.forEach(entityRelation => {
        const link = {
          input: temp.id,
          output: entityRelation.item_type_id,
          label: entityRelation.joinKey || '',
          // readonly: true,
        };

        dataLinks.push(link);
      });
    }
  });

  const newDataLinks = [];
  if (dataLinks.length > 0) {
    dataLinks.forEach(link => {
      if (dataNodes.map[link.output]) {
        newDataLinks.push(link);
      }
    });
  }
  // return { dataNodes: createSchema(dataNodes), dataLinks };
  return { dataNodes, dataLinks: newDataLinks };
};

export const getCoordinatesToAPI = data => {
  const { nodes } = data;
  const dataToAPI = {};

  if (nodes.length === 0) {
    return dataToAPI;
  }

  nodes.forEach(ele => {
    dataToAPI[ele.id] = ele.coordinates;
  });

  return dataToAPI;
};
export const parseObjectToArray = data => {
  const dataOut = [];
  Object.keys(data).map(each => {
    dataOut.push(data[each]);
  });
  return dataOut;
};

/* eslint-disable no-else-return */
/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import { UILoading as Loading } from '@xlab-team/ui-components';
import PropTypes from 'prop-types';
import EventAttributeServices from 'services/EventAttribute';
import BOServices from 'services/BusinessObject';

import {
  convertDataEventAttrsToFE,
  convertDataObjectRefToFE,
  getPositionLinkNode,
  getTopLeftAttrbute,
  mapDatatoAPI,
} from './utils';
import { addMessageToQueue } from '../../../../utils/web/queue';
import { WrapperAssignAttrs, WrapperContent } from './styled';
import AssignAttributes from '../index';
import { safeParse } from '../../../../utils/common';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import { StyledWrapperLoading } from '../../../Templates/LayoutContent';
import UIDiagramsAssignEvent from '../../UIDiagramsAssignEvent';
import {
  convertDataToFe,
  getCoordinates,
} from '../../UIDiagramsAssignEvent/utils';
import { getMaxHeightWidth } from '../../../../modules/Dashboard/ApiHub/Diagram/Content/utils';
import { useDeepCompareEffect } from '../../../../hooks';
import { MAP_CLASS_NAME } from '../../../../modules/Dashboard/ApiHub/Diagram/config';
import { Zoom } from './Zoom';

const WrapperAssignAttributes = props => {
  const stateRef = useRef();
  const [zoomSize, setZoomSize] = useState(1);
  const [refresh, setRefresh] = useState(1);
  const [scrollValue, setScroll] = useState({
    top: 0,
    left: 0,
  });
  let _isMounted = true;
  const [state, setState] = useImmer({
    dataObjectRef: [],
    dataEventAttrs: [],
    defaultEventAttrs: [],
    dataEventAttrsLineItem: [],
    defaultEventAttrsLineItem: [],
    initData: {},
    isLoading: true,
    tab: 'eventAttribute',
    valueSearch: '',
    dataEventAssign: { nodes: [], links: [] },
    dataCoorinates: {},
    heightLink: null,
    widthLink: null,
    listBOWithColumn: [],
    activeValue: {},
    single: false,
    multi: false,
  });
  // console.log('setState', state);
  stateRef.current = state;
  const setStateCommon = objects => {
    // console.log('objects', objects);
    if (_isMounted) {
      setState(draft => {
        Object.keys(objects).forEach(key => {
          draft[key] = objects[key];
        });
      });
    }
  };
  const documentAttrFect = document.getElementById('diagram-event');
  useEffect(() => {
    const documentAttr = document.getElementById('diagram-event');
    if (documentAttr) {
      // documentAttr.scrollTop = scrollValue.top;
      // documentAttr.scrollLeft = scrollValue.left;
      documentAttr.scrollTo({
        top: scrollValue.top,
        left: scrollValue.left,
        behavior: 'smooth',
      });
    }
  }, [refresh, scrollValue, documentAttrFect]);
  useDeepCompareEffect(() => {
    if (
      props.initData &&
      Object.keys(safeParse(props.initData, {})).length > 0
    ) {
      if (props.type !== 'source') {
        setRefresh(prev => prev + 1);
      }
      setStateCommon({
        initData: props.initData,
      });
      const dataCommon = {
        ...state,
        initData: props.initData,
      };
      if (
        props.type === 'source' &&
        props.initData.eventAttributes.length > 0 &&
        !state.isLoading
      ) {
        const { dataNodes, dataLinks } = convertDataToFe(
          dataCommon,
          handleonChangeData,
          dataCommon.dataCoorinates,
          activeNodeFromSchema,
          props.insightPropertyId,
          props.defaultInitData,
        );
        const { topAttr, leftAttr } = getTopLeftAttrbute(dataNodes, props.type);
        setScroll({
          top: topAttr,
          left: leftAttr,
        });
        const { height, width } = getMaxHeightWidth(dataNodes.list);

        setStateCommon({
          dataEventAssign: { nodes: dataNodes.list, links: dataLinks },
          heightLink: height,
          widthLink: width,
        });
        const dataToAPI = mapDatatoAPI(dataCommon);
        props.onChange({
          data: dataToAPI,
          errors: {
            status: false,
            messages: [],
          },
        });
      }
    }
  }, [props.initData, state.isLoading]);

  const handleonChangeData = (name, type, value, stateData) => {
    const temp = document.getElementById('diagram-event');
    const dataCommon = {
      ...stateData,
      setAssign: new Set([
        'duration',
        'event_id',
        'flag',
        'is_bounce',
        'is_new_user',
        'portal_id',
        'session_id',
        'tracked_time',
        '-1013',
        '-1010',
        '-1001',
        '-1000',
        '-1005',
        '-1009',
        '-1007',
        '-1011',
      ]),
    };
    // / Add
    if (type === 'add') {
      if (name === 'eventAttribute') {
        value.forEach(each => {
          const index = dataCommon.initData.eventAttributes.findIndex(
            element => element.eventPropertyName === each.value,
          );
          if (index === -1) {
            dataCommon.initData.eventAttributes.push({
              eventPropertyName: each.value,
              isRequired: each.isRequired,
              translateLabel: each.label,
              type: each.type,
              propertyType: each.propertyType,
            });
          }
        });
      } else if (name === 'singleItem') {
        // setRefresh(prev => prev + 1);
        // setScroll({
        //   top: temp && temp.scrollTop,
        //   left: temp && temp.scrollLeft,
        // });
        value.forEach(each => {
          const index = dataCommon.initData.foreignObjects.findIndex(
            element => element.itemTypeId === each.itemTypeId,
          );
          if (index === -1) {
            dataCommon.initData.foreignObjects.push({
              itemTypeId: each.itemTypeId,
              isRequired: each.checked ? 1 : 0,
              translateLabel: each.label,
              type: each.type,
            });
          }
        });
      } else if (name === 'multiItem') {
        value.forEach(each => {
          if (each.itemTypeName) {
            // setRefresh(prev => prev + 1);
            // setScroll({
            //   top: temp && temp.scrollTop,
            //   left: temp && temp.scrollLeft,
            // });
            dataCommon.initData.mainObjects = [
              {
                ...each,
                translateLabel: each.label,
                isRequired: each.checked ? 1 : 0,
              },
            ];
          } else {
            const index = dataCommon.initData.eventAttributes.findIndex(
              element => element.eventPropertyName === each.value,
            );
            if (index === -1) {
              dataCommon.initData.eventAttributes.push({
                eventPropertyName: each.value,
                isRequired: each.isRequired,
                translateLabel: each.label,
                type: each.type,
              });
            }
          }
        });
      }
      // / Delete
    } else if (type === 'delete') {
      if (name === 'eventAttribute') {
        const index = dataCommon.initData.eventAttributes.findIndex(
          element => element.eventPropertyName === value.eventPropertyName,
        );
        if (index !== -1) {
          dataCommon.initData.eventAttributes.splice(index, 1);
        }
      } else if (name === 'singleItem') {
        const index = dataCommon.initData.foreignObjects.findIndex(
          element => element.itemTypeId === value.itemTypeId,
        );
        if (index !== -1) {
          dataCommon.initData.foreignObjects.splice(index, 1);
        }
      } else if (name === 'multiItem') {
        if (value.itemTypeId) {
          dataCommon.initData.mainObjects = [];
        } else {
          const index = dataCommon.initData.eventAttributes.findIndex(
            element => element.eventPropertyName === value.eventPropertyName,
          );
          if (index !== -1) {
            dataCommon.initData.eventAttributes.splice(index, 1);
          }
        }
      }
      // /Check
    } else if (type === 'check') {
      if (name === 'eventAttribute') {
        const index = dataCommon.initData.eventAttributes.findIndex(
          element => element.eventPropertyName === value.eventPropertyName,
        );
        if (index !== -1) {
          dataCommon.initData.eventAttributes[index].isRequired =
            value.isRequired === 1 ? 0 : 1;
        }
      } else if (name === 'singleItem') {
        const index = dataCommon.initData.foreignObjects.findIndex(
          element => element.itemTypeId === value.itemTypeId,
        );
        if (index !== -1) {
          dataCommon.initData.foreignObjects[index].isRequired =
            value.isRequired === 1 ? 0 : 1;
        }
      } else if (name === 'multiItem') {
        if (value.itemTypeId) {
          dataCommon.initData.mainObjects[0].isRequired =
            value.isRequired === 1 ? 0 : 1;
        } else {
          const index = dataCommon.initData.eventAttributes.findIndex(
            element => element.eventPropertyName === value.eventPropertyName,
          );
          if (index !== -1) {
            dataCommon.initData.eventAttributes[index].isRequired =
              value.isRequired === 1 ? 0 : 1;
          }
        }
      }
    }
    if (name === 'expend') {
      dataCommon[type] = value;
    }
    const { dataNodes, dataLinks } = convertDataToFe(
      dataCommon,
      handleonChangeData,
      stateRef.current.dataCoorinates,
      activeNodeFromSchema,
      props.insightPropertyId,
      props.defaultInitData,
    );
    const { height, width } = getMaxHeightWidth(dataNodes.list);

    setStateCommon({
      ...dataCommon,
      dataEventAssign: { nodes: dataNodes.list, links: dataLinks },
      heightLink: height,
      widthLink: width,
    });
    const dataToAPI = mapDatatoAPI(dataCommon);
    props.onChange({
      data: dataToAPI,
      errors: {
        status: false,
        messages: [],
      },
    });
  };
  const fetchData = async () => {
    try {
      const dataEventAttrsFromAPI = await EventAttributeServices.data.getListEventAttrsToAssign();
      const dataCommon = {
        dataEventAttrs: [],
        defaultEventAttrs: [],
        dataObjectRef: [],
        listBOWithColumn: [],
      };

      if (dataEventAttrsFromAPI.code === 200) {
        const { dataEventAttrs, defaultEventAttrs } = convertDataEventAttrsToFE(
          dataEventAttrsFromAPI.data,
        );

        dataCommon.dataEventAttrs = dataEventAttrs;
        dataCommon.defaultEventAttrs = defaultEventAttrs;
      }

      const dataObjectRefFromAPI = await EventAttributeServices.data.getListObjectRefToAssign();

      if (dataObjectRefFromAPI.code === 200) {
        const { dataObjectRef, defaultRefObjects } = convertDataObjectRefToFE(
          dataObjectRefFromAPI.data,
        );
        dataCommon.dataObjectRef = dataObjectRef;
      }
      const dataBOWithColumn = await BOServices.diagram.getBO({
        insight_property_id: 'all',
      });
      if (
        dataBOWithColumn.code === 200 &&
        dataBOWithColumn.data &&
        dataBOWithColumn.data.length > 0
      ) {
        dataCommon.listBOWithColumn = dataBOWithColumn.data;
      }
      // Case event attrs by event, using eventCategoryId and eventActionId
      if (props.dataEvent && Object.keys(props.dataEvent).length > 0) {
        // Luồng getDetail
        // dataCommon.initData = MOCK_DATA_API;
        const params = {
          scope: props.scope || 'event_tracking_attributes',
          eventActionId: props.dataEvent.eventActionId,
          eventCategoryId: props.dataEvent.eventCategoryId,
        };
        const response = await EventAttributeServices.data.getDetailAssignAttrs(
          params,
        );

        // example data:{ eventAttributes: [], foreignObjects: [], mainObjects: [] }
        // , response.data);
        if (response.code === 200) {
          const eventAttributesTmp = response.data.eventAttributes;
          eventAttributesTmp.forEach(each => {
            const item = dataCommon.dataEventAttrs.find(
              tmp => tmp.value === each.eventPropertyName,
            );
            if (item) {
              each.propertyType = item.propertyType;
            }
          });
          dataCommon.initData = {
            ...response.data,
            eventAttributes: eventAttributesTmp,
          };
        }
      }
      if (props.type === 'source') {
        dataCommon.initData = props.initData;
      }
      //  else if (
      //   props.initData &&
      //   Object.keys(safeParse(props.initData, {})).length > 0
      // ) {
      //   // case create source
      //   dataCommon.initData = props.initData;
      // }

      const data = {
        ...dataCommon,
        setAssign: new Set([
          'duration',
          'event_id',
          'flag',
          'is_bounce',
          'is_new_user',
          'portal_id',
          'session_id',
          'tracked_time',
          '-1013',
          '-1010',
          '-1001',
          '-1000',
          '-1005',
          '-1009',
          '-1007',
          '-1011',
        ]),
        // dataEventAttrsTmp: dataCommon.dataEventAttrs,
        // dataEventAttrsSearch: dataCommon.dataEventAttrs,
        // dataObjectRefTmp: dataCommon.dataObjectRef,
        // dataObjectRefSearch: dataCommon.dataObjectRef,
      };
      const { dataNodes, dataLinks } = convertDataToFe(
        data,
        handleonChangeData,
        state.dataCoorinates,
        activeNodeFromSchema,
        props.insightPropertyId,
        props.defaultInitData,
      );
      const { height, width } = getMaxHeightWidth(dataNodes.list);
      const { topAttr, leftAttr } = getTopLeftAttrbute(dataNodes, props.type);
      setScroll({
        top: topAttr,
        left: leftAttr,
      });
      setStateCommon({
        ...data,
        isLoading: false,
        dataEventAssign: { nodes: dataNodes.list, links: dataLinks },
        heightLink: height,
        widthLink: width,
      });
      const dataToAPI = mapDatatoAPI(data);
      if (props.type !== 'source') {
        props.onChange({
          data: dataToAPI,
          errors: {
            status: false,
            messages: [],
          },
        });
      }

      // console.log('dataCommon', data);
    } catch (e) {
      addMessageToQueue({
        path:
          'app/components/common/AssignAttributes/WrapperAssignAttributes/index.jsx',
        func: 'fetchData',
        data: e.stack,
      });
      console.log('err ===>', e);
      setStateCommon({ isLoading: false });
    }
  };

  useEffect(() => {
    fetchData();
    return () => {
      _isMounted = false;
    };
  }, []);
  const callback = (type, data) => {
    // console.log('type, data', type, data);
  };
  const onChangeZoomInOut = number => {
    try {
      if (number > 0 && typeof number === 'number') {
        setZoomSize(number);
        // setStateCommon({
        //   dataNodes: {
        //     nodes: state.dataNodes.nodes,
        //     links: state.dataNodes.links,
        //   },
        // });
      }
    } catch (e) {
      addMessageToQueue({
        path: 'appmodulesDashboardApiHubDiagramContentindex.jsx',
        func: 'onChangeZoomInOut',
        data: e.stack,
      });
    }
  };
  const onChangeDiagram = data => {
    const { height, width } = getMaxHeightWidth(data.nodes);
    const dataCoorinates = getCoordinates(data.nodes);
    setStateCommon({
      dataCoorinates: { ...dataCoorinates },
      heightLink: height,
      widthLink: width,
    });
  };
  const activeNodeFromSchema = id => {
    const tempLink = document.querySelectorAll(
      '.bi.bi-link-canvas-layer .bi-diagram-link',
    );
    const tempNode = document.querySelectorAll(
      '.bi-diagram-canvas .bi.bi-diagram-node',
    );

    const { positionLink, positionNode } = getPositionLinkNode(
      id,
      stateRef.current.dataEventAssign,
    );
    // const index = prevExploreInfo.current.dataEventAssign.nodes.findIndex(
    //   node => node.data.data[0].id === id,
    // );
    if (id.itemTypeId === stateRef.current.activeValue.itemTypeId) {
      stateRef.current.dataEventAssign.links.forEach((each, i) => {
        if (positionLink.includes(i)) {
          tempLink[i].classList.remove(`id`);
          //   tempNode[i].classList.add(`not-id`);
        } else {
          tempLink[i].classList.remove(`not-id`);
        }
      });
      // / disable node

      stateRef.current.dataEventAssign.nodes.forEach((each, i) => {
        if (positionNode.includes(i)) {
          tempNode[i].classList.remove(`id`);
          //   tempNode[i].classList.add(`not-id`);
        } else if (each.id !== 'events') {
          tempNode[i].classList.remove(`not-id`);
        }
      });
      setStateCommon({
        activeValue: {},
      });
    } else {
      // / disable link
      stateRef.current.dataEventAssign.links.forEach((each, i) => {
        if (positionLink.includes(i)) {
          if (tempLink[i].classList.length > 1) {
            tempLink[i].classList.remove(`not-id`);
            tempLink[i].classList.add(`id`);
          } else {
            tempLink[i].classList.add(`id`);
          }
        } else if (tempLink[i].classList.length > 1) {
          tempLink[i].classList.remove(`id`);
          tempLink[i].classList.add(`not-id`);
        } else {
          tempLink[i].classList.add(`not-id`);
        }
      });
      stateRef.current.dataEventAssign.nodes.forEach((each, i) => {
        if (positionNode.includes(i)) {
          if (tempNode[i].classList.length > 1) {
            tempNode[i].classList.remove(`not-id`);
            tempNode[i].classList.add(`id`);
          } else {
            tempNode[i].classList.add(`id`);
          }
        } else if (tempNode[i].classList.length > 1) {
          tempNode[i].classList.remove(`id`);
          tempNode[i].classList.add(`not-id`);
        } else {
          tempNode[i].classList.add(`not-id`);
        }
      });
      // / disable node

      // prevExploreInfo.current.dataEventAssign.nodes.forEach((each, i) => {
      //   if (i !== index && each.id !== 'events') {
      //     tempNode[i].classList.add(`not-id`);
      //   }
      // });
      setStateCommon({
        activeValue: id,
      });
    }
  };
  const removeClassName = e => {
    const tempLink = document.querySelectorAll(
      '.bi.bi-link-canvas-layer .bi-diagram-link',
    );
    const tempNode = document.querySelectorAll(
      '.bi-diagram-canvas .bi.bi-diagram-node',
    );
    const { positionLink, positionNode } = getPositionLinkNode(
      state.activeValue,
      state.dataEventAssign,
    );
    const nameClass = e.target.className;

    if (
      positionNode.length > 0 &&
      positionLink.length > 0 &&
      // !state.isOpenToggleChild &&
      !Object.keys(MAP_CLASS_NAME).some(key =>
        nameClass.includes(MAP_CLASS_NAME[key]),
      )
    ) {
      stateRef.current.dataEventAssign.links.forEach((each, i) => {
        if (positionLink.includes(i)) {
          tempLink[i].classList.remove(`id`);
          //   tempNode[i].classList.add(`not-id`);
        } else {
          tempLink[i].classList.remove(`not-id`);
        }
      });
      // / disable node

      stateRef.current.dataEventAssign.nodes.forEach((each, i) => {
        if (positionNode.includes(i)) {
          tempNode[i].classList.remove(`id`);
          //   tempNode[i].classList.add(`not-id`);
        } else if (each.id !== 'events') {
          tempNode[i].classList.remove(`not-id`);
        }
      });
      // / disable node

      // prevExploreInfo.current.dataNodes.nodes.forEach((each, i) => {
      //   if (i !== index && each.id !== 'events') {
      //     tempNode[i].classList.remove(`not-id`);
      //   }
      // });
      setStateCommon({
        activeValue: {},
      });
    }

    // console.log(temp[index]);
  };
  const renderFooter = () => {
    if (typeof props.Footer === 'function') {
      return props.Footer();
    }

    return null;
  };
  const renderContent = () => {
    if (state.isLoading || props.isLoading) {
      return (
        <StyledWrapperLoading>
          <Loading isLoading />
        </StyledWrapperLoading>
      );
    } else if (props.disabled) {
      return (
        <WrapperContent>
          {getTranslateMessage(
            TRANSLATE_KEY._NOTI_ADD_EVENT_BEFORE_ASSIGN_ATTRIBUTE,
            `You need to
          add an event to the source before assigning event attributes &
          reference objects`,
          )}
        </WrapperContent>
      );
    }
    return (
      <>
        <div
          style={{
            transform: `scale(${zoomSize})`,
            transformOrigin: 'top left',
            width: `calc(100% / ${zoomSize})`,
            height: `calc(100% / ${zoomSize})`,
          }}
        >
          <UIDiagramsAssignEvent
            dataEventAssign={state.dataEventAssign}
            onChange={onChangeDiagram}
            refresh={refresh}
          />
        </div>
        <Zoom zoomSize={zoomSize} onChangeZoomInOut={onChangeZoomInOut} />
      </>
    );
    // return (
    //   <AssignAttributes
    //     objectRef={state.dataObjectRef}
    //     eventAttributes={state.dataEventAttrs}
    //     defaultAttrs={state.defaultEventAttrs}
    //     dataEventAttrsLineItem={state.dataEventAttrsLineItem}
    //     defaultEventAttrsLineItem={state.defaultEventAttrsLineItem}
    //     initData={state.initData}
    //     isLoading={state.isLoading}
    //     onChange={props.onChange}
    //     validateRules={props.validateRules}
    //     type={props.type}
    //     isDisabledSaved={props.isDisabledSaved}
    //     callback={callback}
    //     Footer={props.Footer}
    //   />
    // );
  };

  return (
    <>
      <WrapperAssignAttrs
        onClick={removeClassName}
        height={state.heightLink}
        width={state.widthLink}
        className={props.className}
        loading={state.isLoading}
      >
        {renderContent()}
      </WrapperAssignAttrs>
      {renderFooter()}
    </>
  );
};

WrapperAssignAttributes.defaultProps = {
  onChange: () => {},
  dataEvent: {},
  validateRules: [],
  disabled: false,
  type: 'event',
};

WrapperAssignAttributes.propTypes = {
  type: PropTypes.string,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  dataEvent: PropTypes.object,
  validateRules: PropTypes.array, // 'selectedEventAttrs', 'mainObject', 'foreignObjects',
};

// example props dataEvent = { eventCategoryId: -20, eventActionId: -106 }

export default WrapperAssignAttributes;

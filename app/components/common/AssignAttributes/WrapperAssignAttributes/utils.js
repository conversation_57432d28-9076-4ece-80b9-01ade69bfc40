import { checkDuplicateArray } from '../../../../modules/Dashboard/ApiHub/Diagram/Content/utils';
import { safeParse, safeParseInt } from '../../../../utils/common';
import { getStatusItemCode } from '../../../../utils/web/properties';
import {
  OBJECT_REFERENCE,
  OPTION_EVENT_ATTRS,
  OPTION_EVENT_ATTRS_LINE_ITEM,
} from '../constants';

export const convertDataEventAttrsToFE = data => {
  // console.log('convertDataEventAttrsToFE', data);
  const validateData = safeParse(data, []);
  const dataToUI = {
    dataEventAttrs: [],
    defaultEventAttrs: [],
  };

  if (validateData.length === 0) {
    return dataToUI;
  }

  validateData.forEach(item => {
    const propertyType = safeParseInt(item.propertyType);
    const temp = {
      value: item.eventPropertyName,
      label: safeParse(item.translateLabel, item.eventPropertyDisplay),
      type: safeParseInt(item.type),
      checked:
        item.isRequired == 1 ||
        parseInt(item.type) === 1 ||
        (parseInt(item.type) === 2 && item.isRequired == 1),
      status: getStatusItemCode('info', parseInt(item.status)),
      // format: item.displayFormat,
      propertyType,
      isRequired: safeParseInt(item.isRequired, 0),
      autoSuggestion: +item.autoSuggestion === 1,
      feSourceId:
        propertyType === 1 ? OPTION_EVENT_ATTRS : OPTION_EVENT_ATTRS_LINE_ITEM,
    };

    // Lấy những attrs do system init và predefine để làm defaultAttrs
    if (
      parseInt(item.type) === 1 ||
      // parseInt(item.type) === 2 ||
      (parseInt(item.type) === 3 && parseInt(item.isRequired) === 1)
    ) {
      // console.log('pass rule', item);
      // if (propertyType === 2) {
      //   dataToUI.defaultEventAttrsLineItem.push(temp.value);
      // } else {
      //   dataToUI.defaultEventAttrs.push(temp.value);
      // }
      dataToUI.defaultEventAttrs.push(temp.value);
    }

    // if (propertyType === 2) {
    //   dataToUI.dataEventAttrsLineItem.push(temp);
    // } else {
    //   dataToUI.dataEventAttrs.push(temp);
    // }
    dataToUI.dataEventAttrs.push(temp);
  });
  return dataToUI;
};

export const convertDataObjectRefToFE = data => {
  const validateData = safeParse(data, []);
  const dataToUI = {
    dataObjectRef: [],
    defaultRefObjects: [],
  };

  if (validateData.length === 0) {
    return dataToUI;
  }

  validateData.forEach(item => {
    const temp = {
      // ...item,
      value: `${item.itemTypeId}`,
      itemTypeName: item.itemTypeName,
      itemTypeId: item.itemTypeId,
      label: safeParse(item.translateLabel, item.itemTypeDisplay),
      checked: item.isRequired === 1,
      feSourceId: OBJECT_REFERENCE,
      type: safeParseInt(item.type),
    };

    // if (parseInt(item.type) === 1) {
    //   dataToUI.defaultRefObjects.push(temp.value);
    // }

    dataToUI.dataObjectRef.push(temp);
  });

  return dataToUI;
};
export const mapDatatoAPI = dataCommon => {
  const dataToAPI = {
    eventAttributes: [],
    mainObjects: [],
    foreignObjects: [],
  };
  // eventAttribute
  dataCommon.initData.eventAttributes.forEach(each => {
    const item = dataCommon.dataEventAttrs.find(
      tmp => tmp.value === each.eventPropertyName,
    );
    if (item) {
      dataToAPI.eventAttributes.push({
        eventPropertyName: item.value,
        isRequired: each.isRequired,
        isReassign: dataCommon.setAssign.has(item.value) ? 0 : 1,
        propertyType: item.propertyType,
        autoSuggestion: item.autoSuggestion === true ? 1 : 0,
        type: item.type,
      });
    }
  });
  dataCommon.initData.foreignObjects.forEach(each => {
    dataToAPI.foreignObjects.push({
      itemTypeId: parseInt(each.itemTypeId),
      isRequired: each.isRequired,
      isReassign: dataCommon.setAssign.has(each.itemTypeId.toString()) ? 0 : 1,
      type: each.type,
    });
  });
  dataCommon.initData.mainObjects.forEach(each => {
    dataToAPI.mainObjects.push({
      itemTypeId: parseInt(each.itemTypeId),
      isRequired: each.isRequired,
      isReassign: dataCommon.setAssign.has(each.itemTypeId) ? 0 : 1,
      type: each.type,
    });
  });
  return dataToAPI;
};
export const getPositionLinkNode = (value, state) => {
  const positionLink = [];
  const positionNode = [];
  const index = state.links.findIndex(
    ea => Number(ea.output) === Number(value.itemTypeId),
  );
  const indexOf = state.nodes.findIndex(
    ea => Number(ea.id) === Number(value.itemTypeId),
  );
  if (index !== -1) {
    positionLink.push(index);
  }
  if (indexOf !== -1) {
    positionNode.push(indexOf);
  }

  return {
    positionLink: checkDuplicateArray(positionLink),
    positionNode: checkDuplicateArray(positionNode),
  };
};
export const getTopLeftAttrbute = (data, type) => {
  const { eventAttribute = {} } = data.map;

  return {
    topAttr: eventAttribute.coordinates[1] - 50,
    leftAttr:
      type === 'source'
        ? eventAttribute.coordinates[0] - 300
        : eventAttribute.coordinates[0] / 2,
  };
};

/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import _isEmpty from 'lodash/isEmpty';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import PropTypes from 'prop-types';
import UISelect from 'components/form/UISelectCondition';
import {
  isShowFormatEdit,
  initState,
  MAP_DISPLAY_FORMAT,
  MAP_FORMAT,
  MAP_DISPLAY_FORMAT_CUSTOM,
} from './utils';
import {
  getPortalFormatNumber,
  getPortalFormatPercentage,
  getPortalFormatCurrency,
  getPortalFormatDateTime,
} from '../../../../utils/web/portalSetting';
import ModalFormatCurrency from '../ModalFormatCurrency';
import ModalFormatNumberByType from '../ModalFormatNumberByType';
import ModalFormatDateTime from '../ModalFormatDateTime/ModalFormatDatetimeSetting';
import TRANSLATE_KEY from '../../../../messages/constant';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import InputPreview from '../../../Atoms/InputPreview';
import styled from 'styled-components';
import _, { isFunction } from 'lodash';
import { Flex } from '@antscorp/antsomi-ui';
function ContentConfigFormat(props) {
  const { dataType, initData } = props;
  const portalFormat = useMemo(
    () => ({
      NUMBER: getPortalFormatNumber(),
      PERCENTAGE: getPortalFormatPercentage(),
      CURRENCY: getPortalFormatCurrency(),
      DATE_AND_TIME: getPortalFormatDateTime(),
    }),
    [],
  );
  const [state, setstate] = useImmer(initState());
  useEffect(
    () => () => {
      setstate(() => initState());
    },
    [],
  );
  // useEffect(() => {
  //   toOutput();
  // }, [state.config, state.formatType]);

  useEffect(() => {
    if (!dataType) return;
    const { type = '', config = {} } = initData;
    setstate(draft => {
      draft.displayAs.options =
        props.mode === 'modal'
          ? MAP_DISPLAY_FORMAT[dataType] || []
          : MAP_DISPLAY_FORMAT_CUSTOM[dataType] || [];
      draft.displayAs.value = MAP_FORMAT[type];
      draft.config = config;
      draft.formatType = type;
    });
  }, [dataType, initData]);

  // const toOutput = () => {
  //   const config = _isEmpty(state.config)
  //     ? portalFormat[state.formatType] || {}
  //     : state.config;
  //   props.onChange({ type: state.formatType, config });
  // };

  const onChangeConfig = value => {
    // console.log('onChangeConfig', initData);
    setstate(draft => {
      draft.config = value;
      const config = _isEmpty(value) ? {} : value;
      props.usePortal.forEach(each => {
        if (config[each]) {
          delete config[each];
        }
      });
      props.onChange({ type: state.formatType || initData.type, config });
    });
  };
  const onChangeDisplayAs = value => {
    // console.log('onChangeDisplayAs', value);
    setstate(draft => {
      draft.displayAs.value = value;
      draft.formatType = (value || {}).value;
      draft.config = {};
      const config = portalFormat[draft.formatType] || {};

      if (isFunction(props.onChange)) {
        props.onChange({ type: draft.formatType, config });
      }
    });
  };

  return (
    <Flex vertical gap={15} style={{ height: '100%', width: '100%' }}>
      <WrapperDisable disabled={props.disabled && !props.isViewMode}>
        {state.isShowConfig && (
          <>
            <InputPreview
              type="input"
              isViewMode={props.isViewMode}
              value={_.get(state, 'displayAs.value.label', '')}
            >
              <div style={{ width: props.isShowDrawer ? '100%' : '47%' }}>
                <UISelect
                  onlyParent
                  use="tree"
                  isSearchable
                  placeholder={getTranslateMessage(
                    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                    'Select an item',
                  )}
                  fullWidthPopover
                  options={state.displayAs.options}
                  value={state.displayAs.value}
                  disabled={state.displayAs.disabled}
                  onChange={onChangeDisplayAs}
                />
              </div>
            </InputPreview>
            {!props.isViewMode && isShowFormatEdit(dataType) && (
              <>
                {dataType === 'number' && state.formatType === 'CURRENCY' && (
                  <ModalFormatCurrency
                    onChange={onChangeConfig}
                    type={state.type}
                    configDefault={portalFormat.CURRENCY}
                    config={state.config}
                    mode={props.mode}
                    modalName="modalFormatCurrency"
                    disableGroupingAndDecimal={props.disableGroupingAndDecimal}
                  />
                )}

                {dataType === 'number' && state.formatType === 'NUMBER' && (
                  <ModalFormatNumberByType
                    use="number"
                    onChange={onChangeConfig}
                    type={state.type}
                    configDefault={portalFormat.NUMBER}
                    config={state.config}
                    mode={props.mode}
                    modalName="modalFormatNumber"
                    disableGroupingAndDecimal={props.disableGroupingAndDecimal}
                  />
                )}

                {dataType === 'number' && state.formatType === 'PERCENTAGE' && (
                  <ModalFormatNumberByType
                    use="percentage"
                    onChange={onChangeConfig}
                    type={state.type}
                    configDefault={portalFormat.PERCENTAGE}
                    config={state.config}
                    mode={props.mode}
                    modalName="modalFormatPercentage"
                    disableGroupingAndDecimal={props.disableGroupingAndDecimal}
                  />
                )}

                {dataType === 'datetime' && (
                  <ModalFormatDateTime
                    onChange={onChangeConfig}
                    type={state.type}
                    configDefault={portalFormat.DATE_AND_TIME}
                    config={state.config}
                    mode={props.mode}
                    modalName="modalFormatDateTime"
                  />
                )}
              </>
            )}
          </>
        )}
      </WrapperDisable>
    </Flex>
  );
}

export const ViewModeWrapper = styled.div`
  font-size: 12px;
  color: #000000;
  padding-top: '0.5rem';
`;

ContentConfigFormat.defaultProps = {
  dataType: '',
  disabled: false,
  usePortal: ['group', 'decimal'],
  mode: 'modal',
};

ContentConfigFormat.propTypes = {
  dataType: PropTypes.string,
  disabled: PropTypes.bool,
  usePortal: PropTypes.array,
  mode: PropTypes.string,
};

export default memo(ContentConfigFormat);

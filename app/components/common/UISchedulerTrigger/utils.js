/* eslint-disable no-useless-computed-key */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable indent */
import { format, isValid, startOfDay } from 'date-fns';
import { utcToZonedTime, zonedTimeToUtc } from 'date-fns-tz';
import { isDate, isNumber } from 'lodash';
import moment from 'moment';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { safeParse } from '../../../utils/common';
import { PortalDate, toPortalDate } from '../../../utils/date';
import { getPortalTimeZone } from '../../../utils/web/portalSetting';
import { getObjectPropSafely } from '../UIEditorPersonalization/utils.3rd';

/* eslint-disable no-else-return */
export const OPTIONS_TYPE_SCHEDULER_TRIGGER = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._STORY_FREQUENCY_ONCE, 'Once'),
    value: 'once',
  },
  {
    label: getTranslateMessage(
      TRANSLATE_KEY._STORY_FREQUENCY_HOURLY,
      'By Hour',
    ),
    value: 'hourly',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._STORY_FREQUENCY_DAILY, 'Daily'),
    value: 'daily',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._STORY_FREQUENCY_WEEKLY, 'Weekly'),
    value: 'weekly',
  },
  {
    label: getTranslateMessage(
      TRANSLATE_KEY._STORY_FREQUENCY_MONTHLY,
      'Monthly',
    ),
    value: 'monthly',
  },
];
const MAP_OPTIONS_TYPE_SCHEDULER_TRIGGER = {
  once: OPTIONS_TYPE_SCHEDULER_TRIGGER[0],
  hourly: OPTIONS_TYPE_SCHEDULER_TRIGGER[1],
  daily: OPTIONS_TYPE_SCHEDULER_TRIGGER[2],
  weekly: OPTIONS_TYPE_SCHEDULER_TRIGGER[3],
  monthly: OPTIONS_TYPE_SCHEDULER_TRIGGER[4],
};

export const OPTION_TRIGGER_EVERY = {
  once: [
    {
      label: '1',
      value: '1',
    },
  ],
  hourly: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
    {
      label: '4',
      value: '4',
    },
    {
      label: '5',
      value: '5',
    },
    {
      label: '6',
      value: '6',
    },
    {
      label: '7',
      value: '7',
    },
    {
      label: '8',
      value: '8',
    },
    {
      label: '9',
      value: '9',
    },
    {
      label: '10',
      value: '10',
    },
    {
      label: '11',
      value: '11',
    },
    {
      label: '12',
      value: '12',
    },
    {
      label: '13',
      value: '13',
    },
    {
      label: '14',
      value: '14',
    },
    {
      label: '15',
      value: '15',
    },
    {
      label: '16',
      value: '16',
    },
    {
      label: '17',
      value: '17',
    },
    {
      label: '18',
      value: '18',
    },
    {
      label: '19',
      value: '19',
    },
    {
      label: '20',
      value: '20',
    },
    {
      label: '21',
      value: '21',
    },
    {
      label: '22',
      value: '22',
    },
    {
      label: '23',
      value: '23',
    },
    {
      label: '24',
      value: '24',
    },
  ],
  daily: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
    {
      label: '4',
      value: '4',
    },
    {
      label: '5',
      value: '5',
    },
    {
      label: '6',
      value: '6',
    },
    {
      label: '7',
      value: '7',
    },
  ],
  weekly: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
    {
      label: '4',
      value: '4',
    },
  ],
  monthly: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
    {
      label: '4',
      value: '4',
    },
    {
      label: '5',
      value: '5',
    },
    {
      label: '6',
      value: '6',
    },
    {
      label: '7',
      value: '7',
    },
    {
      label: '8',
      value: '8',
    },
    {
      label: '9',
      value: '9',
    },
    {
      label: '10',
      value: '10',
    },
    {
      label: '11',
      value: '11',
    },
    {
      label: '12',
      value: '12',
    },
  ],
};

export const MAP_OPTION_TRIGGER_EVERY = {
  ['once-1']: {
    label: '1',
    value: '1',
  },
  ['hourly-1']: {
    label: '1',
    value: '1',
  },
  ['hourly-2']: {
    label: '2',
    value: '2',
  },
  ['hourly-3']: {
    label: '3',
    value: '3',
  },
  ['hourly-4']: {
    label: '4',
    value: '4',
  },
  ['hourly-5']: {
    label: '5',
    value: '5',
  },
  ['hourly-6']: {
    label: '6',
    value: '6',
  },
  ['hourly-7']: {
    label: '7',
    value: '7',
  },
  ['hourly-8']: {
    label: '8',
    value: '8',
  },
  ['hourly-9']: {
    label: '9',
    value: '9',
  },
  ['hourly-10']: {
    label: '10',
    value: '10',
  },
  ['hourly-11']: {
    label: '11',
    value: '11',
  },
  ['hourly-12']: {
    label: '12',
    value: '12',
  },
  ['hourly-13']: {
    label: '13',
    value: '13',
  },
  ['hourly-14']: {
    label: '14',
    value: '14',
  },
  ['hourly-15']: {
    label: '15',
    value: '15',
  },
  ['hourly-16']: {
    label: '16',
    value: '16',
  },
  ['hourly-17']: {
    label: '17',
    value: '17',
  },
  ['hourly-18']: {
    label: '18',
    value: '18',
  },
  ['hourly-19']: {
    label: '19',
    value: '19',
  },
  ['hourly-20']: {
    label: '20',
    value: '20',
  },
  ['hourly-21']: {
    label: '21',
    value: '21',
  },
  ['hourly-22']: {
    label: '22',
    value: '22',
  },
  ['hourly-23']: {
    label: '23',
    value: '23',
  },
  ['hourly-24']: {
    label: '24',
    value: '24',
  },
  ['daily-1']: {
    label: '1',
    value: '1',
  },
  ['daily-2']: {
    label: '2',
    value: '2',
  },
  ['daily-3']: {
    label: '3',
    value: '3',
  },
  ['daily-4']: {
    label: '4',
    value: '4',
  },
  ['daily-5']: {
    label: '5',
    value: '5',
  },
  ['daily-6']: {
    label: '6',
    value: '6',
  },
  ['daily-7']: {
    label: '7',
    value: '7',
  },
  ['weekly-1']: {
    label: '1',
    value: '1',
  },
  ['weekly-2']: {
    label: '2',
    value: '2',
  },
  ['weekly-3']: {
    label: '3',
    value: '3',
  },
  ['weekly-4']: {
    label: '4',
    value: '4',
  },

  ['monthly-1']: {
    label: '1',
    value: '1',
  },
  ['monthly-2']: {
    label: '2',
    value: '2',
  },
  ['monthly-3']: {
    label: '3',
    value: '3',
  },
  ['monthly-4']: {
    label: '4',
    value: '4',
  },
  ['monthly-5']: {
    label: '5',
    value: '5',
  },
  ['monthly-6']: {
    label: '6',
    value: '6',
  },
  ['monthly-7']: {
    label: '7',
    value: '7',
  },
  ['monthly-8']: {
    label: '8',
    value: '8',
  },
  ['monthly-9']: {
    label: '9',
    value: '9',
  },
  ['monthly-10']: {
    label: '10',
    value: '10',
  },
  ['monthly-11']: {
    label: '11',
    value: '11',
  },
  ['monthly-12']: {
    label: '12',
    value: '12',
  },
};

export const MAP_LABEL_TEXT_TIME = {
  weekly: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_EVERY_WEEK, 'Weeks'),
  monthly: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_EVERY_MONTH, 'Months'),
  daily: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_EVERY_DAY, 'Days'),
  hourly: getTranslateMessage(TRANSLATE_KEY._INFO_DELAY_HOUR, 'hour(s)'),
  once: '',
};

export const DATA_DAYS_OF_WEEKS = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_MO, 'Mon'),
    value: 'monday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_TU, 'Tue'),
    value: 'tuesday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_WE, 'Wed'),
    value: 'wednesday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_TH, 'Thu'),
    value: 'thursday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_FR, 'Fri'),
    value: 'friday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_SA, 'Sat'),
    value: 'saturday',
    checked: true,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._DAY_SU, 'Sun'),
    value: 'sunday',
    checked: true,
  },
];

export const DATA_DAYS_OF_WEEKS_CUSTOM = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_MONDAY, 'Monday'),
    value: 'monday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_TUESDAY, 'Tuesday'),
    value: 'tuesday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_WEDNESDAY, 'Wednesday'),
    value: 'wednesday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_THURSDAY, 'Thursday'),
    value: 'thursday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_FRIDAY, 'Friday'),
    value: 'friday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_SATURDAY, 'Saturday'),
    value: 'saturday',
    timeOfday: new Date(),
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._TITL_SUNDAY, 'Sunday'),
    value: 'sunday',
    timeOfday: new Date(),
  },
];

const MAP_DATA_DAYS_OF_WEEKS = {
  monday: DATA_DAYS_OF_WEEKS[0],
  tuesday: DATA_DAYS_OF_WEEKS[1],
  wednesday: DATA_DAYS_OF_WEEKS[2],
  thursday: DATA_DAYS_OF_WEEKS[3],
  friday: DATA_DAYS_OF_WEEKS[4],
  saturday: DATA_DAYS_OF_WEEKS[5],
  sunday: DATA_DAYS_OF_WEEKS[6],
};

export const DATA_END_DATE = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._STORY_END_NEVER, 'Never'),
    value: 'never',
  },
  {
    label: getTranslateMessage(
      TRANSLATE_KEY._STORY_END_SPECIFIC_DATE,
      'Specific date',
    ),
    value: 'onDate',
  },
];

export const getValueOptions = data => {
  const validateData = safeParse(data, []);
  const values = [];

  if (validateData.length === 0) {
    return values;
  }

  data.forEach(element => {
    values.push(element.value);
  });

  return values;
};

export const initDataScheduleTrigger = {
  frequency: {
    options: OPTIONS_TYPE_SCHEDULER_TRIGGER,
    value: OPTIONS_TYPE_SCHEDULER_TRIGGER[0],
  },
  startingAt: Date.parse(new Date()),
  repeatInterval: {
    options: OPTION_TRIGGER_EVERY.once,
    value: OPTION_TRIGGER_EVERY.once[0],
  },
  selectDaysOfWeek: {
    optionsSelected: getValueOptions(DATA_DAYS_OF_WEEKS),
  },
  selectDaysOfMonth: {
    optionsSelected: ['END_OF_MONTH'],
  },
  frequencyTime: {
    startDate: Date.parse(new Date()),
    startTime: Date.parse(new Date()),
    selectEndDate: {
      options: DATA_END_DATE,
      value: DATA_END_DATE[0],
    },
    endDate: Date.parse(new Date()),
    endTime: Date.parse(new Date()),
  },
  triggerType: 'specific_date',

  // frequencyTimeToAPI: {
  //   startDate: Date.parse(new Date()),
  //   startTime: Date.parse(new Date()),
  //   selectEndDate: {
  //     options: DATA_END_DATE,
  //     value: DATA_END_DATE[0],
  //   },
  //   endDate: Date.parse(new Date()),
  //   endTime: Date.parse(new Date()),
  // },
};

export const setCheckedOptions = (dataOptions, nameOption, valueChecked) =>
  dataOptions.map(option => {
    if (option.value === nameOption) {
      const temp = {
        ...option,
        checked: !valueChecked,
      };
      return temp;
    } else {
      return { ...option };
    }
  });

const initDataFrequence = {
  startDate: Date.parse(new Date()),
  startTime: Date.parse(new Date()),
  selectEndDate: {
    options: DATA_END_DATE,
    value: DATA_END_DATE[0],
  },
  endDate: Date.parse(new Date()),
  endTime: Date.parse(new Date()),
};

export const toAPIScheduleTrigger = data => {
  if (Object.keys(safeParse(data, {})).length === 0) {
    return {
      frequency: OPTIONS_TYPE_SCHEDULER_TRIGGER[0].value,
      repeatInterval: OPTION_TRIGGER_EVERY.once[0].value,
      daysOfWeek: getValueOptions(DATA_DAYS_OF_WEEKS),
      ...toAPISelectTime(initDataFrequence),
    };
  }
  const tempData = {};
  const frequency = safeParse(data.frequency, { value: {} });
  const frequencyTime = safeParse(data.frequencyTime, {});
  const repeatInterval = safeParse(data.repeatInterval, { value: {} });
  const selectDaysOfWeek = safeParse(data.selectDaysOfWeek, {
    optionsSelected: [],
  });
  const selectDaysOfMonth = safeParse(data.selectDaysOfMonth, {
    optionsSelected: [],
  });

  const dataToAPISelectTime = toAPISelectTime(frequencyTime).frequencyTime;
  if (data.triggerType === 'after_activate') {
    tempData.triggerType = 'after_activate';
  } else {
    tempData.triggerType = 'specific_date';
  }
  tempData.frequency = frequency.value.value;
  tempData.startDate = dataToAPISelectTime.startDate;
  tempData.startTime = dataToAPISelectTime.startTime;
  tempData.startTimeOfDay = dataToAPISelectTime.startTimeOfDay;
  tempData.repeatInterval =
    repeatInterval.value.value || OPTION_TRIGGER_EVERY.once[0].value;
  tempData.endCondition = dataToAPISelectTime.endCondition;
  tempData.endDate = dataToAPISelectTime.endDate;
  tempData.endTime = dataToAPISelectTime.endTime;
  tempData.endTimeOfDay = dataToAPISelectTime.endTimeOfDay;
  if (frequency.value.value === 'weekly') {
    tempData.daysOfWeek = selectDaysOfWeek.optionsSelected;
  } else if (frequency.value.value === 'monthly') {
    tempData.daysOfMonth = selectDaysOfMonth.optionsSelected;
    tempData.frequency = 'monthly-v2';
  }
  // if selected option once, send API endDate & endTime : null
  if (frequency.value.value === 'once') {
    tempData.endDate = null;
    tempData.endTime = null;
  }

  return tempData;
};

export const toUIScheduleTrigger = data => {
  const validateData = safeParse(data, {});

  if (Object.keys(validateData).length === 0) {
    return initDataScheduleTrigger;
  }
  const frequency = {
    options: OPTIONS_TYPE_SCHEDULER_TRIGGER,
    value: safeParse(
      MAP_OPTIONS_TYPE_SCHEDULER_TRIGGER[validateData.frequency],
      MAP_OPTIONS_TYPE_SCHEDULER_TRIGGER.once,
    ),
  };

  const repeatInterval = {
    options: OPTION_TRIGGER_EVERY[validateData.frequency],
    value: safeParse(
      MAP_OPTION_TRIGGER_EVERY[
        `${validateData.frequency}-${validateData.repeatInterval}`
      ],
      MAP_OPTION_TRIGGER_EVERY['once-1'],
    ),
  };

  const selectDaysOfWeek = {
    optionsSelected: safeParse(validateData.daysOfWeek, []),
  };
  const selectDaysOfMonth = {
    optionsSelected: safeParse(validateData.daysOfMonth, []),
  };
  const dataFrequencyTime = toUISelectTime(validateData.frequencyTime);
  const { triggerType } = data;

  const dataState = {
    frequency,
    repeatInterval,
    selectDaysOfWeek,
    selectDaysOfMonth,
    frequencyTime: dataFrequencyTime.frequencyTime,
    triggerType,
  };
  return dataState;
};

export const toUIDaysOfWeek = dataDays => {
  const validateData = safeParse(dataDays, []);
  const data = [];

  if (validateData.length === 0) {
    return data;
  }

  dataDays.forEach(element => {
    const tempData = {
      ...element,
      checked: MAP_DATA_DAYS_OF_WEEKS[element] ? true : false,
    };
    data.push(tempData);
  });

  return data;
};

export const toAPISelectTime = data => {
  // console.log('data: ', data);
  if (Object.keys(safeParse(data, {})).length === 0) {
    return {
      frequencyTime: {
        endCondition: DATA_END_DATE[0].value,
        startDate: Date.parse(new Date()),
        endDate: null,
        startTimeOfDay: Date.parse(new Date()),
        startTime: Date.parse(new Date()),
        endTime: null,
      },
    };
  }

  const endCondition = safeParse(
    data.selectEndDate.value.value,
    DATA_END_DATE[0].value,
  );

  const startTime = new Date(data.startTime);
  let tmpStartDate = new Date(data.startDate);

  tmpStartDate.setHours(startTime.getHours());
  tmpStartDate.setMinutes(startTime.getMinutes());

  if (isValid(tmpStartDate)) {
    tmpStartDate = new PortalDate(format(tmpStartDate, 'MM/dd/yyyy HH:mm:ss'));
  }

  const startTimeOfDay = {
    hour: isValid(tmpStartDate) ? tmpStartDate.getHoursPortal() : null,
    minute: isValid(tmpStartDate) ? tmpStartDate.getMinutesPortal() : null,
  };

  let tmpEndDate = null;
  let endTime = null;
  let endTimeOfDay = null;

  if (endCondition !== DATA_END_DATE[0].value) {
    endTime = new Date(data.endTime);
    tmpEndDate = new Date(data.endDate);

    tmpEndDate.setHours(endTime.getHours());
    tmpEndDate.setMinutes(endTime.getMinutes());

    if (isValid(tmpEndDate)) {
      tmpEndDate = new PortalDate(format(tmpEndDate, 'MM/dd/yyyy HH:mm:ss'));
    }

    endTimeOfDay = {
      hour: isValid(tmpEndDate) ? tmpEndDate.getHoursPortal() : null,
      minute: isValid(tmpEndDate) ? tmpEndDate.getMinutesPortal() : null,
    };
  }

  const tempData = {
    endCondition,
    startDate: isValid(tmpStartDate)
      ? format(tmpStartDate, 'yyyy-MM-dd')
      : null,
    startTime: isValid(tmpStartDate) ? tmpStartDate.getTime() : null,
    startTimeOfDay,
    ...(endCondition !== DATA_END_DATE[0].value
      ? {
          endDate: isValid(tmpEndDate)
            ? format(tmpEndDate, 'yyyy-MM-dd')
            : null,
          endTime: isValid(tmpEndDate) ? tmpEndDate.getTime() : null,
          endTimeOfDay,
        }
      : {
          endDate: null,
          endTime: null,
          endTimeOfDay: null,
        }),
  };

  // console.log('tempData', tempData);
  return { frequencyTime: tempData };
};

export const toUISelectTime = data => {
  const {
    endCondition = 'never',
    startTime = new Date().getTime(),
    endTime = new Date().getTime(),
  } = data;

  const tempStartTime = utcToZonedTime(
    new Date(startTime),
    getPortalTimeZone(),
  ).getTime();

  let tempEndTime = null;

  if (endTime) {
    tempEndTime = utcToZonedTime(
      new Date(endTime),
      getPortalTimeZone(),
    ).getTime();
  }

  const dataToUI = {
    frequencyTime: {
      startDate: tempStartTime,
      startTime: tempStartTime,
      selectEndDate: {
        options: DATA_END_DATE,
        value: endCondition === 'never' ? DATA_END_DATE[0] : DATA_END_DATE[1],
      },
      endDate: tempEndTime ? tempEndTime : new Date().getTime(),
      endTime: tempEndTime ? tempEndTime : new Date().getTime(),
    },
  };

  // console.log('dataToUI', dataToUI);

  return dataToUI;
};

export const formatDateToString = (date, time) =>
  `${date.splice('-').join(',')},${time.hour},${time.minute}`;

export const isUncheckAll = data => {
  let uncheckAll = true;

  data.forEach(element => {
    if (element.checked && element.checked === true) {
      uncheckAll = false;
    }
  });

  return uncheckAll;
};

export const validateSelectDaysOfWeek = data => {
  const uncheckAll = safeParse(data, []).length === 0;

  if (uncheckAll) {
    return {
      status: false,
      errors: [
        getTranslateMessage(
          TRANSLATE_KEY._NOTI_SELECT_LEAST_ONE_DAY,
          'Must select at least 1 day.',
        ),
      ],
    };
  }

  return { status: true, errors: [] };
};

// frequencyTime: {
//   startDate: new Date().getTime(),
//   startTime: new Date().getTime(),
//   selectEndDate: {
//     options: DATA_END_DATE,
//     value: DATA_END_DATE[0],
//   },
//   endDate: new Date().getTime(),
//   endTime: new Date().getTime(),
// },

const ERROR_END_TIME_LESS_THAN = {
  status: false,
  errors: {
    endTime: [
      getTranslateMessage(
        TRANSLATE_KEY._NOTI_VALIDATE_END_TIME_GREATER,
        'The end time must be greater than the start time',
      ),
    ],
  },
};
const ERROR_END_DATE_LESS_THAN_CURRENT = {
  status: false,
  errors: {
    endDate: [
      getTranslateMessage(
        TRANSLATE_KEY._NOTI_VALIDATE_END_DATE_GREATER_CURRENT,
        'The close date must be greater than the current',
      ),
    ],
  },
};

const isValidTimestamp = value => isNumber(value) && isDate(new Date(value));

export const validateSelectTime = data => {
  const {
    frequencyTime,
    selectDaysOfWeek,
    frequency,
    selectDaysOfMonth,
  } = data;
  const {
    startDate,
    startTime,
    endDate,
    endTime,
    selectEndDate,
  } = frequencyTime;

  // const formatStartDate = format(new Date(startDate), 'MM/dd/yyyy');
  // const formatEndDate = format(new Date(endDate), 'MM/dd/yyyy');

  if (!isValidTimestamp(startDate) || !isValidTimestamp(startTime)) {
    return {
      status: false,
      errors: { endTime: ['Invalid Time Format'] },
    };
  }
  if (
    safeParse(selectEndDate.value.value, DATA_END_DATE[0].value) === 'never' ||
    getObjectPropSafely(() => frequency.value.value) === 'once'
  ) {
    return { status: true, errors: [] };
  } else if (!isValidTimestamp(endDate) || !isValidTimestamp(endTime)) {
    return {
      status: false,
      errors: { endTime: ['Invalid Time Format'] },
    };
  }

  const hourStartTime = new Date(startTime).getHours();
  const minuteStartTime = new Date(startTime).getMinutes();

  const hourEndTime = new Date(endTime).getHours();
  const minuteEndTime = new Date(endTime).getMinutes();
  const currentDate = moment()
    .startOf('day')
    .valueOf();

  if (startDate > endDate && endDate !== null) {
    return {
      status: false,
      errors: { startDate: ['Start date must be less than end date.'] },
    };
  } else if (startDate === endDate) {
    if (hourStartTime > hourEndTime) {
      return ERROR_END_TIME_LESS_THAN;
    } else if (
      hourStartTime === hourEndTime &&
      minuteStartTime >= minuteEndTime
    ) {
      return ERROR_END_TIME_LESS_THAN;
    }
  } else if (endDate < currentDate && endDate !== null) {
    return ERROR_END_DATE_LESS_THAN_CURRENT;
  }

  if (selectDaysOfWeek && frequency.value.value === 'weekly') {
    return { status: selectDaysOfWeek.optionsSelected.length > 0, errors: [] };
  }
  if (selectDaysOfMonth && frequency.value.value === 'monthly') {
    return { status: selectDaysOfMonth.optionsSelected.length > 0, errors: [] };
  }
  return { status: true, errors: {} };
};

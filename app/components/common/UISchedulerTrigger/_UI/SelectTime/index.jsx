/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect, useRef } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import Grid from '@material-ui/core/Grid';
import AccessTimeIcon from '@material-ui/icons/AccessTime';
import {
  UIInputTime,
  UIInputCalendar,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import { format, isValid as isValidDate, startOfDay } from 'date-fns';

import moment from 'moment';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { useImmer } from 'use-immer';

import RadioGroup from '../../../../Molecules/RadioGroup';
import { ErrorText, useStyles, WrapperEndDate, TitleTime } from '../../styled';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { DATA_END_DATE, validateSelectTime } from '../../utils';
import { SelectTimeWrapper, Text, Wrapper, WrapperInputTime } from './styled';
import { DisplayTimeZone } from '../../../../../modules/Dashboard/Profile/Segment/Create/styles';
import { getLabelPortalTimeZone } from '../../../../../modules/Dashboard/utils';
import { PortalDate } from '../../../../../utils/date';

const initDataTime = type => ({
  frequencyTime: {
    startDate: new Date().getTime(),
    startTime: new Date().getTime(),
    selectEndDate: {
      options: DATA_END_DATE,
      value: DATA_END_DATE[0],
    },
    // add case use change opotion once in node schedule setting endTime & endDate null
    endDate: type !== 'once' ? new Date().getTime() : null,
    endTime: type !== 'once' ? new Date().getTime() : null,
    errors: {},
  },
});

const SelectTime = props => {
  const didMountRef = useRef(false);

  const {
    initData,
    dataSchedule,
    isViewMode,
    // isHiddenAt
  } = props;

  const classes = useStyles();

  const [state, setState] = useImmer(initDataTime());

  const commonValidate = (keyState, valueState) => {
    const temp = {
      frequencyTime: {
        ...state.frequencyTime,
        [keyState]: valueState,
      },
    };
    const { errors } = validateSelectTime(temp);

    setState(draft => {
      draft.frequencyTime.errors = errors;
    });
  };

  const onChangeStartDate = value => {
    setState(draft => {
      draft.frequencyTime.startDate = isValidDate(value) ? value : '';
    });

    const newStartTime = moment().set({
      hour: 24,
      minute: 1,
      second: 0,
      millisecond: 0,
    });
    onChangeStartTime(new Date(newStartTime));

    commonValidate('startDate', value);
  };

  const onChangeStartTime = value => {
    let timeTmp = value;

    if (isValidDate(value)) {
      timeTmp = value.getTime();
    } else {
      timeTmp = '';
    }

    setState(draft => {
      draft.frequencyTime.startTime = timeTmp;
    });

    commonValidate('startTime', timeTmp);
  };

  const onChangeSelectEndDate = event => {
    if (event.target.value === 'never') {
      setState(draft => {
        draft.frequencyTime.selectEndDate.value = {
          label: 'Never',
          value: event.target.value,
        };
      });
    } else if (event.target.value === 'onDate') {
      setState(draft => {
        draft.frequencyTime.selectEndDate.value = {
          label: 'Specific date',
          value: event.target.value,
        };
      });
    }
  };

  const onChangeEndDate = value => {
    setState(draft => {
      draft.frequencyTime.endDate = isValidDate(value) ? value : '';
    });

    const newEndTime = moment().set({
      hour: 23,
      minute: 59,
      second: 0,
      millisecond: 0,
    });
    onChangeEndTime(new Date(newEndTime));

    commonValidate('endDate', value);
  };

  const onChangeEndTime = value => {
    let timeTmp = value;

    if (isValidDate(value)) {
      timeTmp = value.getTime();
    } else {
      timeTmp = '';
    }

    setState(draft => {
      draft.frequencyTime.endTime = timeTmp;
    });

    commonValidate('endTime', timeTmp);
  };

  useEffect(() => {
    if (didMountRef.current === true) {
      // setting endTime & endDate when use selected option once in node schedule
      const stateTmp = {
        ...state,
        frequencyTime: {
          ...state.frequencyTime,
          endTime:
            dataSchedule && dataSchedule.frequency.value.value === 'once'
              ? null
              : state.frequencyTime.endTime,
          endDate:
            dataSchedule && dataSchedule.frequency.value.value === 'once'
              ? null
              : state.frequencyTime.endDate,
        },
      };

      const { errors } = validateSelectTime(stateTmp);
      // setState setting endTime & endDate when use selected option once in node schedule
      if (dataSchedule && dataSchedule.frequency.value.value === 'once') {
        setState(draft => {
          draft.frequencyTime.errors = errors;
          draft.frequencyTime.endTime =
            dataSchedule &&
            dataSchedule.frequency.value.value === 'once' &&
            null;
          draft.frequencyTime.endDate =
            dataSchedule &&
            dataSchedule.frequency.value.value === 'once' &&
            null;
        });
      } else {
        setState(draft => {
          draft.frequencyTime.errors = errors;
        });
      }
    } else didMountRef.current = true;
  }, [props.validateKey]);

  // useEffect(() => {
  //   setState(draft => {
  //     draft.frequencyTime.startDate = new PortalDate().getTime();
  //     draft.frequencyTime.startTime = new PortalDate().getTime();
  //     draft.frequencyTime.endTime = new PortalDate().getTime();
  //     draft.frequencyTime.endDate = new PortalDate().getTime();
  //     draft.frequencyTime.errors = {};
  //   });
  // }, [props.typeFrequency]);

  useEffect(() => {
    setState(draft => {
      draft.frequencyTime =
        Object.keys(safeParse(initData, {})).length === 0
          ? initDataTime().frequencyTime
          : initData;
    });
  }, [props.componentId]);

  useEffect(() => {
    // props.callback('CHANGE_SCHEDULE', state.frequencyTime);
    props.onChange(state.frequencyTime);
  }, [state.frequencyTime]);

  let errorStartDate = [];
  let errorEndDate = [];
  let errorEndTime = [];
  let errorStartTime = [];

  if (
    state &&
    state.frequencyTime &&
    state.frequencyTime.errors &&
    Object.keys(state.frequencyTime.errors).length > 0
  ) {
    if (state.frequencyTime.errors.startDate) {
      errorStartDate = state.frequencyTime.errors.startDate;
    }

    if (state.frequencyTime.errors.startTime) {
      errorStartDate = state.frequencyTime.errors.startTime;
    }

    if (state.frequencyTime.errors.endDate) {
      errorEndDate = state.frequencyTime.errors.endDate;
    }

    if (state.frequencyTime.errors.endTime) {
      errorEndTime = state.frequencyTime.errors.endTime;
    }
  }

  return (
    <Grid
      container
      item
      sm={12}
      className={`${classes.smallText} ${classes.rootTime}`}
      style={
        !isViewMode && props.isBlastCampaign
          ? { paddingLeft: 'calc(16% + 25px)' }
          : {}
      }
    >
      {!props.hiddenDateTime && (
        <Grid
          hidden={props.hiddenDateTime}
          item
          sm="auto"
          className="mr-30"
          style={{ display: isViewMode && 'flex' }}
          data-test="start-date"
        >
          <SelectTimeWrapper style={{ marginBottom: !isViewMode && 10 }}>
            <Wrapper data-test="start-date-day">
              <TitleTime style={{ marginBottom: '5px' }}>
                {getTranslateMessage(
                  TRANSLATE_KEY._INFO_STORY_START_DATE,
                  'Start date',
                )}
              </TitleTime>
              <WrapperDisable
                disabled={props.disabledStartDate}
                className="min-w-170"
              >
                <UIInputCalendar
                  // className="form-control private-form__control"
                  // type={item.get('dataType')}
                  minDate={props.disabledPastDate && new Date()}
                  placeholder="MM/DD/YYYY"
                  isViewMode={isViewMode}
                  value={state.frequencyTime.startDate}
                  onChange={onChangeStartDate}
                  isShowLabel={false}
                  error={!!errorStartDate[0]}
                  helperText={errorStartDate[0]}
                />
              </WrapperDisable>
            </Wrapper>
            <Text className={`${props.isViewMode ? 'm-top-26' : 'm-top-30'}`}>
              at
            </Text>
            <WrapperInputTime
              data-test="start-date-time"
              style={{ marginTop: 21 }}
            >
              <WrapperDisable disabled={props.disabledStartDate}>
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  <UIInputTime
                    margin="normal"
                    id="time-picker"
                    // label="Start time"
                    value={state.frequencyTime.startTime}
                    isViewMode={isViewMode}
                    onChange={onChangeStartTime}
                    KeyboardButtonProps={{
                      'aria-label': 'change time',
                    }}
                    keyboardIcon={<AccessTimeIcon />}
                  />
                </MuiPickersUtilsProvider>
              </WrapperDisable>
            </WrapperInputTime>
            {props.isShowTimeZoneRight && props.hiddenEndDate && (
              <DisplayTimeZone
                style={{ marginTop: isViewMode ? '24px' : '30px' }}
              >
                {getLabelPortalTimeZone()}
              </DisplayTimeZone>
            )}
          </SelectTimeWrapper>
          {!props.isValidTimeScheduled && props.isBlastCampaign && !isViewMode && (
            <div
              style={{
                maxWidth: props.typeFrequency !== 'once' ? '310px' : 'unset',
                fontSize: '12px',
                color: '#FF0000',
              }}
            >
              *
              {getTranslateMessage(
                TRANSLATE_KEY._,
                'The start time of a Journey should be at least 30 minutes after saving this Journey.',
              )}
            </div>
          )}
          {props.typeFrequency !== 'once' && (
            <DisplayTimeZone
              style={{
                marginLeft: isViewMode ? '10px' : '0px',
                marginTop: isViewMode && '24px',
              }}
            >
              {getLabelPortalTimeZone()}
            </DisplayTimeZone>
          )}
        </Grid>
      )}
      {/* <Grid
        hidden={props.hiddenDateTime}
        item
        sm={3}
        className={classes.paddingLeft}
      /> */}

      {isViewMode && props.typeFrequency !== 'once' && (
        <Grid item sm={12} className="p-top-4">
          <WrapperEndDate>
            <WrapperDisable disabled={props.disabledEndDate}>
              <SelectTree
                label={getTranslateMessage(
                  TRANSLATE_KEY._INFO_STORY_SELECT_END_DATE,
                  'End date',
                )}
                options={state.frequencyTime.selectEndDate.options}
                onlyParent={props.onlyParent}
                isViewMode={props.isViewMode}
                // displayFormat={displayFormat}
                use="tree"
                // isMulti
                isSearchable={false}
                isParentOpen={props.isParentOpen}
                value={state.frequencyTime.selectEndDate.value}
                // isViewMode
                // onChange={onChangeSelectEndDate}
                placeholder={getTranslateMessage(
                  TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                  'Select an item',
                )}
                fullWidthPopover
              />
            </WrapperDisable>
          </WrapperEndDate>
        </Grid>
      )}

      <Grid
        item
        sm={
          // eslint-disable-next-line no-nested-ternary
          props.isViewMode ? (props.hiddenEndDate ? 'auto' : 12) : 4
        }
        className={
          // eslint-disable-next-line no-nested-ternary
          props.isViewMode &&
          !props.isCollapsed &&
          state.frequencyTime.selectEndDate.value.value === 'never'
            ? 'hidden-close-date'
            : props.isViewMode && 'm-top-3'
        }
        data-test="close-date"
        // style={{ paddingTop: !props.isCollapsed && 10 }}
      >
        {!props.hiddenEndDate && (
          <RadioGroup
            label={
              <TitleTime>
                {getTranslateMessage(
                  TRANSLATE_KEY._INFO_STORY_END_DATE,
                  'End date',
                )}
              </TitleTime>
            }
            value={state.frequencyTime.selectEndDate.value.value}
            isHiddenListStyle
            onChange={onChangeSelectEndDate}
            isViewMode={props.isViewMode}
            options={[
              {
                key: 'option-none',
                'data-test': 'option-none',
                value: state.frequencyTime.selectEndDate.options[0].value,
                disabled: props.disabled,
                label: getTranslateMessage(TRANSLATE_KEY._TITL_NONE, 'None'),
              },
              {
                key: 'option-end-date',
                'data-test': 'option-specific-date',
                value: state.frequencyTime.selectEndDate.options[1].value,
                disabled: props.disabled,
                style: props.isBlastCampaign
                  ? { alignItems: 'flex-start' }
                  : {},
                label: (
                  <SelectTimeWrapper>
                    <Wrapper>
                      <WrapperDisable className="min-w-170">
                        <UIInputCalendar
                          // className="form-control private-form__control"
                          // type={item.get('dataType')}
                          placeholder="MM/DD/YYYY"
                          isViewMode={isViewMode}
                          value={state.frequencyTime.endDate}
                          onChange={onChangeEndDate}
                          className="width-100"
                          isShowLabel={false}
                          error={!!errorEndDate[0]}
                          minDate={
                            props.design === 'create' &&
                            state.frequencyTime.startDate
                              ? format(
                                  new Date(state.frequencyTime.startDate),
                                  'yyyy-MM-dd',
                                )
                              : format(new PortalDate(), 'yyyy-MM-dd')
                          }
                          helperText={errorEndDate}
                          data-test="close-date-day"
                        />
                      </WrapperDisable>
                    </Wrapper>

                    <Text
                      className={`${props.isViewMode ? 'm-top-5' : 'm-top-7'}`}
                    >
                      at
                    </Text>

                    <WrapperInputTime>
                      <WrapperDisable>
                        <MuiPickersUtilsProvider utils={DateFnsUtils}>
                          <UIInputTime
                            margin="normal"
                            id="time-picker"
                            //   label="Time picker"
                            value={state.frequencyTime.endTime}
                            isViewMode={isViewMode}
                            onChange={onChangeEndTime}
                            KeyboardButtonProps={{
                              'aria-label': 'change time',
                            }}
                            keyboardIcon={<AccessTimeIcon />}
                            data-test="close-date-time"
                          />
                        </MuiPickersUtilsProvider>
                      </WrapperDisable>
                    </WrapperInputTime>
                    <DisplayTimeZone
                      style={{
                        whiteSpace: 'nowrap',
                        marginTop: `${props.isViewMode ? '3px' : '7px'}`,
                      }}
                    >
                      {getLabelPortalTimeZone()}
                    </DisplayTimeZone>
                  </SelectTimeWrapper>
                ),
              },
            ]}
          />
        )}
      </Grid>
    </Grid>
  );
};

SelectTime.defaultProps = {
  initData: {},
  hiddenEndDate: false,
  design: 'create',
  disabledEndDate: false,
  disabledStartDate: false,
  isHiddenAt: false,
  disabledPastDate: false,
};

export default SelectTime;

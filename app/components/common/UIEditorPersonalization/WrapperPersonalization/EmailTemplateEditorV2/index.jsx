/*  global PORTAL_CONFIG:readable */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
/* eslint-disable arrow-body-style */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import { isEmpty, isNull } from 'lodash';
import { UIButton, UILoading } from '@xlab-team/ui-components';
import { useHistory, useLocation } from 'react-router-dom';
import { getTranslateMessage } from 'containers/Translate/util';
import JourneyServices from 'services/Journey';
import UserAttrServices from 'services/UserAttributes';
import TRANSLATE_KEY from 'messages/constant';
import {
  getPortalId,
  getCurrentUserId,
  getWindowParam,
} from 'utils/web/cookie';
import {
  SavedBlock,
  WrapperSaved<PERSON>lock,
  <PERSON><PERSON>e,
} from '../MediaTemplateEditor/styles';
import {
  GALLERY,
  MY_TEMPLATE,
  TYPE_ALL,
  TEMPLATE_TAB_MENU_KEYS,
  TEMPLATE_TAB_MENU_ITEMS,
} from '../MediaTemplateEditor/constants';
import { EMAIL_TEMPLATE } from '../../utils';
import { safeParse } from '../../../../../utils/common';
import { getObjectPropSafely } from '../../utils.3rd';
import { createStructuredSelector } from 'reselect';
import { makeSelectJourneyChannelActive } from '../../../../../modules/Dashboard/MarketingHub/Journey/selectors';
import { updateValue } from '../../../../../redux/actions';
import { MODULE_CONFIG } from '../../../../../modules/Dashboard/MarketingHub/Journey/config';
import { getChannelIdByCode } from '../../../../../modules/Dashboard/MarketingHub/Journey/utils';
import { getUrlCreateJourneyByTemplate } from '../../../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/utils';
import { connect } from 'react-redux';
import { useExternalServiceAuth } from 'hooks';
import { Wrapper } from './styled';
import {
  DRAWER_DETAIL_DIMENSION,
  Drawer,
  Icon,
  Tabs,
  TemplateListing,
  getCategoriesFromObjectTemplate,
  queryClientAntsomiUI,
  useTemplateListing,
} from '@antscorp/antsomi-ui';
import {
  LIMIT_LIST_PER_PAGE,
  OBJECT_TYPES,
  QUERY_KEYS,
} from '@antscorp/antsomi-ui/es/constants';
import { UI_DETAIL_DRAWER } from 'modules/Dashboard/MarketingHub/Journey/constant';
import { usePrevious } from '../../../../../hooks';

const MAP_TITLE = {
  actEdit: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit'),
  actChangeTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Change Template',
  ),
  actUseTemplate: getTranslateMessage(
    TRANSLATE_KEY._TITL_USE_TEMPLATE,
    'USE TEMPLATE',
  ),
  actPreview: getTranslateMessage(TRANSLATE_KEY._ACT_PREVIEW_RESULT, 'Preview'),
  templateType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Template type'),
  deviceType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Device Type'),
  objectiveType: getTranslateMessage(TRANSLATE_KEY._, 'Objective'),
  all: getTranslateMessage(TRANSLATE_KEY.TITL_ALL, 'All'),
  noTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'There is no template'),
  createNewTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Create New Template',
  ),
  noPermission: getTranslateMessage(
    TRANSLATE_KEY._TITL_NO_PERMISSION,
    "you don't have permission to perform this action",
  ),
  blankTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Blank Template'),
};

const CARD_HEIGHT = 410;
const BANNER_HEIGHT = '78vh';

const blankTemplate = {
  template_name: '',
  template_type: 4,
  device_type: 1,
  template_setting: {},
  properties: {
    deviceType: 1,
    template: {
      id: 4,
      type: 'inline',
      name: 'Inline',
    },
  },
  thumbnail: '',
};

const EmailTemplateEditor = props => {
  const [state, setState] = useImmer({
    isOpenDrawer: false,
    isLoadingTypesList: false,
    isLoadingTemplateType: true,
    isLoadingSavedBlock: false,
    selectedTypes: [TYPE_ALL],
    selectedDevices: [1, 2],
    objectiveTypes: [1, 2],
    typesList: [],
    mediaTemplateList: [],
    // template after select
    selectedTemplate: {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
      viewPages: [],
    },
    isSaved: false,
    tabActive: GALLERY,
    postMessageId: null,
    version: null,
    templateName: null,
    isUseTemplate: false,
    isBlank: false,
    activeTab: TEMPLATE_TAB_MENU_KEYS.templateGallery,
    /* Check set Template Detail Success  */
    isSetTemplateDetailSuccess: false,

    /* Preview Modal State */
    isOpenPreviewModal: false,
  });

  let preCatalogCode = usePrevious(props.catalogCode) || props.catalogCode;

  const { activeTab } = state;

  const serviceAuth = useExternalServiceAuth();

  // Memo
  const publicLevel = useMemo(() => {
    return TEMPLATE_TAB_MENU_ITEMS[activeTab].publicLevel;
  }, [activeTab]);

  const getListType = useMemo(() => {
    return TEMPLATE_TAB_MENU_ITEMS[activeTab].getListType;
  }, [activeTab]);

  const {
    categoryItems,
    templateItems,
    templateDetail,
    similarTemplates,
    checkedCategories,
    openCategoryKeys,
    selectTemplateAction,
    isLoadingCategoryList,
    isLoadingTemplateDetail,
    previewTemplateCategories,
    onLoadMore: onLoadMoreTemplates,
    onChangeOpenCategoryKeys,
    onChangeCheckedCategories,
    onRemoveObjectTemplate,
    onSelectTemplate: onSelectObjectTemplate,
    setState: setTemplateState,
  } = useTemplateListing({
    serviceAuth,
    config: {
      getListType,
      objectType: OBJECT_TYPES.EMAIL_TEMPLATE, // 1: Media Template
      publicLevel, // 1: Public, 0: Restricted
      limitListPerPage: LIMIT_LIST_PER_PAGE,
    },
  });

  const history = useHistory();
  const location = useLocation();
  const searchParamsMemoized = useMemo(
    () => new URLSearchParams(location.search),
    [location?.search],
  );
  const isDetailJourney = useMemo(() => {
    const uiSearchKey = searchParamsMemoized.get('ui');
    return (
      searchParamsMemoized.has('journeyId') && uiSearchKey === UI_DETAIL_DRAWER
    );
  }, [searchParamsMemoized]);
  // const previousFeConfigKey = usePrevious(
  //   props.variantExtraData && props.variantExtraData.fe_config_id,
  // );

  // Theo breadcrumb user_id
  // const hasPermisisonCreate = validateAction(
  //   MENU_CODE.MEDIA_TEMPLATE,
  //   APP_ACTION.CREATE,
  //   props.activeRow.c_user_id || getCurrentAccessUserId(),
  // );

  // Theo current user
  // const hasPermisisonCreate = checkingRoleScope(
  //   MENU_CODE.MEDIA_TEMPLATE,
  //   APP_ACTION.CREATE,
  //   APP_ROLE_SCOPE.CREATED_BY_USER,
  // );

  useEffect(() => {
    if (props.variantExtraData) {
      const initValue = props.variantExtraData;
      const isSavedTemplate =
        initValue.template_settings && initValue.template_settings.id;
      // || (initValue.properties && initValue.properties.id);
      const isSavedVariant = initValue.fe_config_id;
      if (
        isSavedTemplate &&
        !isSavedVariant &&
        preCatalogCode === props.catalogCode
      ) {
        // Nếu là variant sau khi select chứ chưa save changes(API) thì mới chạy vào đây
        const viewPages = Object.entries(initValue.views).map(([k, v]) => {
          return { ...v, id: k };
        });

        const selectedTemplate = {
          properties: initValue.properties || {},
          template_setting: initValue.template_settings,
          template_name: initValue.template_settings.name,
          template_type: initValue.template_settings.type,
          viewPages,
        };
        setState(draft => {
          draft.isSaved = true;
          draft.selectedTemplate = selectedTemplate;
        });
      } else {
        // new Variant
        const selectedTemplate = {
          properties: {},
          template_name: '',
          template_setting: {
            rulesets: [],
          },
          template_type: null,
          viewPages: [],
        };
        setState(draft => {
          draft.isSaved = false;
          // draft.selectedTypes = '';
          draft.selectedTemplate = selectedTemplate;
        });
      }
    }
  }, [props.componentKey, props.catalogCode]);

  useEffect(() => {
    const createFromTemplate = !isEmpty(
      getObjectPropSafely(() => props.creatingJourneyInfo.data, {}),
    );

    const selectedTemplateJourneyInfo = !isEmpty(
      getObjectPropSafely(
        () => props.creatingJourneyInfo.selectedTemplateTemp,
        {},
      ),
    );

    if (createFromTemplate) {
      const selectedTemplate = { ...props.creatingJourneyInfo.data };

      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        draft.isSaved = true;
        draft.isUseTemplate = false;
        draft.isBlank = false;
      });

      props.onChangeOthers(EMAIL_TEMPLATE, selectedTemplate, {
        isUseTemplate: state.isUseTemplate,
        templateName: state.templateName,
        isBlank: state.isBlank,
        resetCreatingJourneyInfo: true,
      });
    }

    if (selectedTemplateJourneyInfo) {
      const selectedTemplate = {
        ...props.creatingJourneyInfo.selectedTemplateTemp,
      };
      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        if (selectedTemplate.template_id) {
          draft.isUseTemplate = true;
          draft.templateName = selectedTemplate.template_name;
        } else {
          draft.isBlank = true;
        }
      });

      if (state.isSetTemplateDetailSuccess) {
        props.updateCreatingJourneyInfo({
          selectedTemplateTemp: {},
        });
      }
      props.updateLoadingOnStepTwo(false);
      onOpenDrawer();
    }
  }, [
    props.componentKey,
    props.creatingJourneyInfo,
    state.isSetTemplateDetailSuccess,
  ]);

  useEffect(() => {
    let changed = false;
    let isNullRes = false;

    if (props.variantExtraData) {
      const {
        fe_config_id,
        views,
        template_settings,
        copy_id,
      } = props.variantExtraData;

      const fetchData = async (id, isCopy) => {
        props.setIsFetchData(true);

        setState(draft => {
          draft.isSaved = true;
          draft.isLoadingSavedBlock = true;
        });

        let res;

        if (changed) {
          return;
        }

        props.setIsFetchData(false);

        if (props.variantExtraData?.properties) {
          // use case khi use template ở khác portal thì cần lấy properties trong variantExtraData
          isNullRes = true;

          res = {
            data: [
              {
                properties: props.variantExtraData.properties,
              },
            ],
          };
        } else {
          res = await UserAttrServices.settings.get(id);
        }

        const { properties = {} } = res.data[0];

        const viewPages = Object.entries(safeParse(views, [])).map(([k, v]) => {
          return { ...v, id: k };
        });

        const selectedTemplate = {
          properties,
          template_setting: template_settings,
          template_name: safeParse(template_settings, {}).name,
          template_type: safeParse(template_settings, {}).type,
          viewPages,
          // Nếu là case clone => set fe_config_id = null
          fe_config_id: isCopy ? null : id || null,
        };

        setState(draft => {
          draft.selectedTemplate = selectedTemplate;
          draft.isLoadingSavedBlock = false;
        });

        if (!isNullRes) {
          props.onChangeOthers(EMAIL_TEMPLATE, selectedTemplate, {
            isSavedVariant: true,
          });
        }
      };

      // case Clone
      if (copy_id) {
        fetchData(copy_id, true);
      }

      // case update variant đã save API || case clone
      if (!copy_id && fe_config_id) {
        fetchData(fe_config_id, false);
      }
    }

    return () => {
      changed = true;
    };
  }, [
    props.variantExtraData && props.variantExtraData.fe_config_id,
    props.variantExtraData && props.variantExtraData.copy_id,
    props.componentKey,
  ]);

  // useEffect(() => {
  //   setState(draft => {
  //     draft.isLoadingTypesList = true;
  //   });
  //   const fetchData = async () => {
  //     const res = await JourneyServices.emailTemplate.getListType({
  //       data: {},
  //     });
  //     setState(draft => {
  //       // draft.selectedTypes = res.data[0].template_type_id;
  //       draft.typesList = res.data || [];
  //       draft.isLoadingTypesList = false;
  //     });
  //   };
  //   const fetchVersion = async () => {
  //     const res = await JourneyServices.emailTemplate.getVersion();
  //     setState(draft => {
  //       draft.version = res.data.version || '';
  //     });
  //   };

  //   fetchData();
  //   fetchVersion();
  // }, []);

  const onSelectTemplate = value => {
    setState(draft => {
      // draft.isOpenDrawer = true;
      draft.selectedTemplate = value;
      if (value.template_id) {
        draft.isUseTemplate = true;
        draft.templateName = value.template_name;
      } else {
        draft.isBlank = true;
      }
    });
    // handle pre-select template blast campaign
    if (props.templateType) {
      const channelCode = props.channelActive.code;
      const channelId = getChannelIdByCode(channelCode);
      const redirectUrl = getUrlCreateJourneyByTemplate(
        channelId,
        props.templateType,
      );

      props.updateCreatingJourneyInfo({
        channelCode,
        templateType: props.templateType,
        data: {},
        selectedTemplateTemp: value,
      });

      if (redirectUrl) history.push(redirectUrl);
    } else {
      onOpenDrawer();
    }
  };

  const togglePreviewModal = isOpen => {
    setState(draft => {
      draft.isOpenPreviewModal =
        typeof isOpen === 'boolean' ? isOpen : !draft.isOpenPreviewModal;
    });
  };

  useEffect(() => {
    if (templateDetail) {
      switch (selectTemplateAction) {
        case 'edit':
        case 'use': {
          const templateDetailModel = templateDetail.model || {};

          onSelectTemplate(templateDetailModel);

          setTemplateState(prev => ({
            ...prev,
            selectedTemplateId: undefined,
            selectTemplateAction: undefined,
          }));

          togglePreviewModal(false);
          break;
        }
        default: {
          break;
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateDetail, selectTemplateAction]);

  useEffect(() => {
    if (state.selectedTypes.length && state.selectedDevices && !state.isSaved) {
      setState(draft => {
        draft.isLoadingTemplateType = true;
      });
      const fetchMediaTemplates = async (types, devices, objectives) => {
        const filter = [
          // {
          //   TEMPLATE_TYPE: {
          //     matches_any: types.includes(TYPE_ALL)
          //       ? // type all => matches_any all types
          //         state.typesList.map(template => template.template_type_id)
          //       : types,
          //   },
          // },
          {
            DEVICE_TYPE: { matches_any: devices },
          },
          {
            OBJECTIVE_TYPE: {
              arr_matches_any: objectives,
            },
          },
        ];
        const params = `?page=${1}&columns=["properties","template_setting","thumbnail"]&filter=${JSON.stringify(
          filter,
        )}`;
        // const res = await JourneyServices.emailTemplate.getListByType(params);
        const res =
          state.tabActive === MY_TEMPLATE
            ? await JourneyServices.emailTemplate.getListByType(params)
            : await JourneyServices.emailTemplate.getListGallery(params);

        const data =
          state.tabActive === MY_TEMPLATE
            ? res.data.body || []
            : (res.data.body || []).map(item => ({
                ...item,
                template_id: item.media_gallery_id,
                template_name: item.media_gallery_name,
              }));

        setState(draft => {
          draft.mediaTemplateList = data;
          draft.isLoadingTemplateType = false;
        });
      };
      fetchMediaTemplates(
        state.selectedTypes,
        state.selectedDevices,
        state.objectiveTypes,
      );
    }
  }, [
    state.selectedTypes,
    state.selectedDevices,
    state.objectiveTypes,
    state.tabActive,
    state.typesList,
    state.isSaved,
  ]);

  useEffect(() => {
    const listener = event => {
      const { actionType, data } = event.data;
      switch (actionType) {
        case 'onCancelMediaTemplate': {
          onCloseDrawer();
          break;
        }
        case 'onSaveMediaTemplate': {
          const selectedTemplate = { ...data };
          setState(draft => {
            draft.selectedTemplate = selectedTemplate;
            draft.isSaved = true;
            draft.isUseTemplate = false;
            draft.isBlank = false;
          });
          props.onChangeOthers(EMAIL_TEMPLATE, selectedTemplate, {
            isUseTemplate: state.isUseTemplate,
            templateName: state.templateName,
            isBlank: state.isBlank,
          });
          onCloseDrawer();
          break;
        }
        case 'setMediaTemplateDetailSuccess': {
          setState(draft => {
            draft.isSetTemplateDetailSuccess = true;
          });
          stopPostMessage();
          break;
        }
        default:
          break;
      }
    };

    window.addEventListener('message', listener);
    return () => window.removeEventListener('message', listener);
  }, [props.componentKey, state.postMessageId]);

  // Map Data
  const previewModalProps = useMemo(() => {
    const { settings, name, description } = templateDetail || {};
    const { viewPages } = settings || {};
    const html = viewPages?.[1]?.previewHtml || viewPages?.[0]?.previewHtml;

    return {
      name,
      image: html ? (
        <Iframe
          title="Preview"
          srcDoc={html}
          style={{ width: '100%', height: '100%' }}
        />
      ) : (
        undefined
      ),
      description,
    };
  }, [templateDetail]);

  // const onSelectType = value => {
  //   const index = state.selectedTypes.findIndex(type => type === value);
  //   if (index === -1) {
  //     const indexTypeAll = state.selectedTypes.findIndex(
  //       type => type === TYPE_ALL,
  //     );

  //     // select All => uncheck others
  //     if (value === TYPE_ALL && indexTypeAll === -1) {
  //       setState(draft => {
  //         draft.selectedTypes = [0];
  //       });
  //     } else if (value !== TYPE_ALL && indexTypeAll > -1) {
  //       // select others type & type all selected => select new type & uncheck type all
  //       setState(draft => {
  //         draft.selectedTypes.splice(indexTypeAll, 1, value);
  //       });
  //     } else {
  //       // select others type
  //       setState(draft => {
  //         draft.selectedTypes.push(value);
  //       });
  //     }
  //   } else if (state.selectedTypes.length > 1) {
  //     // Prevent delete last item
  //     setState(draft => {
  //       draft.selectedTypes.splice(index, 1);
  //     });
  //   }
  // };

  // const onSelectDevice = value => {
  //   const index = state.selectedDevices.findIndex(type => type === value);
  //   if (index === -1) {
  //     setState(draft => {
  //       draft.selectedDevices.push(value);
  //     });
  //   } else if (state.selectedDevices.length > 1) {
  //     // Prevent delete last item
  //     setState(draft => {
  //       draft.selectedDevices.splice(index, 1);
  //     });
  //   }
  // };

  const onOpenDrawer = () => {
    props.updateIsOpenSubDrawer(true);
    setState(draft => {
      draft.isOpenDrawer = true;
    });

    if (isDetailJourney) {
      props.updateFullScreen(true);
    }
  };

  const onCloseDrawer = () => {
    props.updateIsOpenSubDrawer(false);
    // Refetch Template listing
    queryClientAntsomiUI.invalidateQueries(
      [QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST],
      { exact: false },
    );

    queryClientAntsomiUI.invalidateQueries(
      [QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST],
      { exact: false },
    );
    setState(draft => {
      draft.isOpenDrawer = false;
    });
    if (isDetailJourney) {
      props.toggleFullscreen(false);
    }
  };

  const onPostMessage = () => {
    const mediaTemplateIframe = document.getElementById(
      'media-template-iframe',
    );
    // remove Previews
    window.postMessage(
      { messageType: 'preview-antsomi-cdp-campaign-close-all' },
      '*',
    );
    if (mediaTemplateIframe) {
      const { journeySettings = {} } = props;

      const { unsubscribeSegmentType, triggerType } = journeySettings;

      const { description } = state.selectedTemplate;

      const data = {
        ...state.selectedTemplate.properties,
        description,
        categories: getCategoriesFromObjectTemplate(state.selectedTemplate),
        journeySettings,
        unsubscribeSegmentType,
        triggerType,
        objectiveTypes:
          state.selectedTemplate.objective_type ||
          state.selectedTemplate.properties.objectiveTypes,
        createOwnerId: getWindowParam('create_owner_id'),
      };

      // console.log({ actionType: 'setMediaTemplateDetail', data });

      const postMessage = () =>
        mediaTemplateIframe.contentWindow.postMessage(
          {
            actionType: 'setMediaTemplateDetail',
            data,
          },
          '*',
        );
      startPostMessage(postMessage);
    }
  };

  const startPostMessage = postMessage => {
    let timer = 0;
    const interval = 200;

    const postMessageId = setInterval(() => {
      if (typeof postMessage === 'function' && timer <= 5000) {
        postMessage();
        timer += interval;
      }
    }, interval);

    setState(draft => {
      draft.postMessageId = postMessageId;
    });
  };

  const stopPostMessage = () => {
    clearInterval(state.postMessageId);
  };

  const onEditTemplate = () => {
    onOpenDrawer();
    if (isDetailJourney) {
      props.toggleFullscreen(true);
    }
    onPostMessage();
  };

  const onResetSelectedTemplate = () => {
    const selectedTemplate = {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
      viewPages: [],
    };
    setState(draft => {
      draft.isSaved = false;
      // draft.selectedTypes = '';
      draft.selectedTemplate = selectedTemplate;
    });

    props.onChangeOthers(EMAIL_TEMPLATE, selectedTemplate);
  };

  const renderSavedBlock = () => {
    return (
      <WrapperSavedBlock>
        <UILoading isLoading={state.isLoadingSavedBlock} />
        <SavedBlock>
          {state.selectedTemplate.properties.thumbnail ? (
            <img
              src={state.selectedTemplate.properties.thumbnail}
              alt={state.selectedTemplate.properties.name}
              width="100%"
              className="img-thumbnail"
              // height="100%"
            />
          ) : null}
          {!props.isViewMode && (
            <div className="btn-group">
              <UIButton theme="primary" onClick={onEditTemplate}>
                {MAP_TITLE.actEdit}
              </UIButton>
              <UIButton
                theme="outline"
                onClick={onResetSelectedTemplate}
                className="m-left-3 bg-white"
              >
                {MAP_TITLE.actChangeTemplate}
              </UIButton>
            </div>
          )}
        </SavedBlock>
      </WrapperSavedBlock>
    );
  };

  // const onChangeCheckbox = (value, key) => {
  //   const index = state[key].findIndex(type => type === value);
  //   if (index === -1) {
  //     setState(draft => {
  //       draft[key].push(value);
  //     });
  //   } else if (state[key].length > 1) {
  //     // Prevent delete last item
  //     setState(draft => {
  //       draft[key].splice(index, 1);
  //     });
  //   }
  // };

  // const renderBlankTemplate = () => {
  //   return (
  //     <StyledGridItem item xs="auto">
  //       <TemplateBlock className="blank-template">
  //         <div className="d-flex flex-column align-items-center justify-content-center">
  //           <AddButton
  //             className="add-button"
  //             onClick={() => onSelectTemplate(blankTemplate)}
  //           />
  //           <p className="blank-template-text">{MAP_TITLE.blankTemplate}</p>
  //         </div>
  //       </TemplateBlock>
  //       <p className="p-all-0 text-center" />
  //     </StyledGridItem>
  //   );
  // };

  const renderDisplayBlock = () => {
    return (
      <>
        {renderUITabs()}
        {/* <WrapperMediaTemplate className="wrapper-media-template">
        </WrapperMediaTemplate> */}
      </>
    );
  };
  const renderDrawer = () => {
    return (
      <Drawer
        placement="right"
        keyboard
        destroyOnClose
        open={state.isOpenDrawer}
        closable={false}
        onClose={onCloseDrawer}
        width={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
        styles={{ body: { padding: 0 } }}
      >
        <Iframe
          id="media-template-iframe"
          title="media-tempalte-iframe"
          src={`${
            PORTAL_CONFIG.URL_MEDIA_TEMPLATE
          }/email/${getPortalId()}#/${getCurrentUserId()}/email-template/embed`}
          allowFullScreen
          width="100%"
          height="100%"
          onLoad={onPostMessage}
        />
      </Drawer>
    );
  };

  const renderUITabs = () => {
    return (
      <>
        {/* <StyledUITabs
          height="40px"
          activeTab={state.tabActive}
          onChange={handleChangeTab}
          classNameTabNav={`border-bottom p-x-3 ui-tabs ${props.classNameTabNav ||
            ''}`}
          tippy
          isBoxShadow={false}
          isBlastCampaign={props.isBlastCampaign}
        >
          <TabPanel
            label={TEMPLATE_TYPE.gallery.label}
            eventKey={TEMPLATE_TYPE.gallery.value}
          />
          <TabPanel
            label={TEMPLATE_TYPE.my_template.label}
            eventKey={TEMPLATE_TYPE.my_template.value}
          />
        </StyledUITabs> */}
        <Wrapper className="template-listing-wrapper">
          <div className="__container">
            <Tabs
              activeKey={activeTab}
              items={Object.values(TEMPLATE_TAB_MENU_ITEMS)}
              // shadow
              onChange={tab => {
                setState(draft => {
                  draft.activeTab = tab;
                });

                onChangeCheckedCategories({});
              }}
            />

            <TemplateListing
              templatesProps={{
                items: templateItems,
                loading: isLoadingTemplateDetail,
                // isLoadingPersistTemplate,
                onLoadMoreTemplates,
              }}
              categoryListingProps={{
                items: categoryItems,
                loading: isLoadingCategoryList,
                checkedCategories,
                openKeys: openCategoryKeys,
                onOpenChange: onChangeOpenCategoryKeys,
                onMenuChange: onChangeCheckedCategories,
              }}
              templateItemProps={{
                showSkeleton: false,
                ...(props.templateType
                  ? { height: 410 }
                  : { width: 241, height: 300 }),
                editBtnProps: {
                  text: getTranslateMessage(TRANSLATE_KEY._, 'Use template'),
                  onClick: id => onSelectObjectTemplate(id, 'edit'),
                },
                previewBtnProps: {
                  text: getTranslateMessage(TRANSLATE_KEY._, 'Preview'),
                  onClick: id => {
                    togglePreviewModal(true);
                    onSelectObjectTemplate(id, 'preview');
                  },
                },
                removable: false,
                removeModalProps: {
                  onOk: onRemoveObjectTemplate,
                },
              }}
              blankTemplateProps={{
                show: false,
                description: getTranslateMessage(
                  TRANSLATE_KEY._,
                  'Blank Template',
                ),
                onClick: () => onSelectTemplate(blankTemplate),
              }}
              previewModalProps={{
                bannerProps: {
                  showSkeleton: false,
                  children: previewModalProps?.image,
                  height: BANNER_HEIGHT,
                },
                informationProps: {
                  itemName: previewModalProps?.name,
                  categoryListing: previewTemplateCategories,
                  description: previewModalProps?.description,
                  buttonText: getTranslateMessage(
                    TRANSLATE_KEY._,
                    'Use template',
                  ),

                  /* Click use/create template button */
                  onButtonClick: () => {
                    if (templateDetail) {
                      onSelectObjectTemplate(templateDetail.id, 'use');
                    }
                  },
                },
                similarTemplateProps: {
                  similarTemplates,
                  similarCardProps: {
                    showSkeleton: false,
                    height: CARD_HEIGHT,
                    onClickWrapper: id => onSelectObjectTemplate(id, 'preview'),
                  },
                },
                open: state.isOpenPreviewModal,
                onCancel: () => togglePreviewModal(false),
                onOk: () => togglePreviewModal(false),
                loading: isLoadingTemplateDetail,
              }}
              emptyProps={{
                icon: <Icon type="icon-ants-email-solid" />,
                description: getTranslateMessage(
                  TRANSLATE_KEY._,
                  'No template available',
                ),
              }}
            />
          </div>
        </Wrapper>
      </>
    );
  };
  return (
    <>
      {state.isSaved ? renderSavedBlock() : renderDisplayBlock()}
      {renderDrawer()}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => ({
  updateCreatingJourneyInfo: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`, value));
  },
  updateIsOpenSubDrawer: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_OPEN_SUB_DRAWER`, value));
  },
  toggleFullscreen: newFullscreen => {
    dispatch(
      updateValue(`${MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`, newFullscreen),
    );
  },
  updateLoadingOnStepTwo: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@LOADING_ON_STEP_TWO`, params));
  },
  updateFullScreen: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`, params));
  },
});

EmailTemplateEditor.propTypes = {
  variantExtraData: PropTypes.object,
  setIsFetchData: PropTypes.func,
  onChangeOthers: PropTypes.func,
  // eslint-disable-next-line react/no-unused-prop-types
  isViewMode: PropTypes.bool,
  componentKey: PropTypes.string,
  creatingJourneyInfo: PropTypes.object,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(EmailTemplateEditor);

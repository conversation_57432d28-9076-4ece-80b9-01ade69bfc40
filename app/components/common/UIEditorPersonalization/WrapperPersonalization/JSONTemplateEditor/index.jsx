/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
/* eslint-disable arrow-body-style */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import { isEmpty } from 'lodash';
import { UIButton, UILoading, TabPanel } from '@xlab-team/ui-components';
import { useParams, useHistory } from 'react-router-dom';
import { getTranslateMessage } from 'containers/Translate/util';
import AddButton from 'components/Atoms/AddButton';
import JourneyServices from 'services/Journey';
import UserAttrServices from 'services/UserAttributes';
import TRANSLATE_KEY from 'messages/constant';
import { Drawer } from '@material-ui/core';
import { getPortalId, getCurrentUserId } from 'utils/web/cookie';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  MENU_CODE,
} from 'utils/web/permission';
import {
  SavedBlock,
  StyledGridItem,
  WrapperMediaTemplate,
  StyledUITabs,
  WrapperSavedBlock,
  StyledGridContainer,
  Iframe,
  WrapperNoData,
} from '../MediaTemplateEditor/styles';
import {
  MY_TEMPLATE,
  TEMPLATE_TYPE,
  TYPE_ALL,
} from '../MediaTemplateEditor/constants';
import { JSON_TEMPLATE } from '../../utils';
import { safeParse } from '../../../../../utils/common';
import { getObjectPropSafely } from '../../utils.3rd';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { makeSelectJourneyChannelActive } from '../../../../../modules/Dashboard/MarketingHub/Journey/selectors';
import { updateValue } from '../../../../../redux/actions';
import { MODULE_CONFIG } from '../../../../../modules/Dashboard/MarketingHub/Journey/config';
import { getChannelIdByCode } from '../../../../../modules/Dashboard/MarketingHub/Journey/utils';
import { getUrlCreateJourneyByTemplate } from '../../../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/utils';
import { TemplateBlock } from './styles';
import { UI_DETAIL_DRAWER } from '../../../../../modules/Dashboard/MarketingHub/Journey/constant';
import { MediaGalleryIcon } from '@antscorp/antsomi-ui/es/components/icons';

const MAP_TITLE = {
  actEdit: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit'),
  actChangeTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Change Template',
  ),
  actUseTemplate: getTranslateMessage(TRANSLATE_KEY._, 'Use Template'),
  actPreview: getTranslateMessage(TRANSLATE_KEY._ACT_PREVIEW_RESULT, 'Preview'),
  templateType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Template type'),
  deviceType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Device Type'),
  all: getTranslateMessage(TRANSLATE_KEY.TITL_ALL, 'All'),
  noTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'There is no template'),
  createNewTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Create New Template',
  ),
  noPermission: getTranslateMessage(
    TRANSLATE_KEY._TITL_NO_PERMISSION,
    "you don't have permission to perform this action",
  ),
  blankTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Blank Template'),
  actCreateCampaign: getTranslateMessage(TRANSLATE_KEY._, 'Create Campaign'),
};

const MediaTemplateEditor = props => {
  // const { typesList = [] } = props;
  const { portalId } = useParams();
  const [state, setState] = useImmer({
    isOpenDrawer: false,
    isLoadingTypesList: false,
    isLoadingTemplateType: false,
    isLoadingSavedBlock: false,
    selectedTypes: [TYPE_ALL],
    selectedDevices: [1, 2],
    typesList: [],
    mediaTemplateList: [],
    // template after select
    selectedTemplate: {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
    },
    isSaved: false,
    tabActive: MY_TEMPLATE,
    postMessageId: null,
    version: null,
    isUseTemplate: false,
    templateName: null,
    isBlank: false,
  });

  const history = useHistory();

  const searchParams = new URLSearchParams(window.location.search);

  // Theo breadcrumb user_id
  // const hasPermisisonCreate = validateAction(
  //   MENU_CODE.MEDIA_TEMPLATE,
  //   APP_ACTION.CREATE,
  //   props.activeRow.c_user_id || getCurrentAccessUserId(),
  // );

  // Theo current user
  const hasPermisisonCreate = checkingRoleScope(
    MENU_CODE.MEDIA_TEMPLATE,
    APP_ACTION.CREATE,
    APP_ROLE_SCOPE.CREATED_BY_USER,
  );

  useEffect(() => {
    const initValue = props.variantExtraData;
    const isSavedTemplate =
      initValue.template_settings && initValue.template_settings.id;
    // || (initValue.properties && initValue.properties.id);
    const isSavedVariant = initValue.fe_config_id;
    if (isSavedTemplate && !isSavedVariant) {
      // Nếu là variant sau khi select chứ chưa save changes(API) thì mới chạy vào đây

      const selectedTemplate = {
        properties: initValue.properties || {},
        template_setting: initValue.template_settings,
        template_name: initValue.template_settings.name,
        template_type: initValue.template_settings.type,
      };
      setState(draft => {
        draft.isSaved = true;
        draft.selectedTemplate = selectedTemplate;
      });
    } else {
      // new Variant
      const selectedTemplate = {
        properties: {},
        template_name: '',
        template_setting: {
          rulesets: [],
        },
        template_type: null,
      };
      setState(draft => {
        draft.isSaved = false;
        // draft.selectedTypes = '';
        draft.selectedTemplate = selectedTemplate;
      });
    }
  }, [props.componentKey]);

  useEffect(() => {
    const createFromTemplate = !isEmpty(
      getObjectPropSafely(() => props.creatingJourneyInfo.data, {}),
    );

    const selectedTemplateJourneyInfo = !isEmpty(
      getObjectPropSafely(
        () => props.creatingJourneyInfo.selectedTemplateTemp,
        {},
      ),
    );

    if (createFromTemplate) {
      const selectedTemplate = { ...props.creatingJourneyInfo.data };

      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        draft.isSaved = true;
        draft.isUseTemplate = false;
        draft.isBlank = false;
      });

      props.onChangeOthers(JSON_TEMPLATE, selectedTemplate, {
        isUseTemplate: state.isUseTemplate,
        templateName: state.templateName,
        isBlank: state.isBlank,
        resetCreatingJourneyInfo: true,
      });
    }

    if (selectedTemplateJourneyInfo) {
      const selectedTemplate = {
        ...props.creatingJourneyInfo.selectedTemplateTemp,
      };
      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        if (selectedTemplate.template_id) {
          draft.isUseTemplate = true;
          draft.templateName = selectedTemplate.template_name;
        } else {
          draft.isBlank = true;
        }
      });
      props.updateCreatingJourneyInfo({
        selectedTemplateTemp: {},
      });
      props.updateLoadingOnStepTwo(false);
      onOpenDrawer();
    }
  }, [props.componentKey, props.creatingJourneyInfo]);

  useEffect(() => {
    let isNullRes = false;
    const { fe_config_id, template_settings, copy_id } = props.variantExtraData;
    const fetchData = async (id, isCopy) => {
      props.setIsFetchData(true);
      setState(draft => {
        draft.isSaved = true;
        draft.isLoadingSavedBlock = true;
      });
      let res = await UserAttrServices.settings.get(id);

      if (!res.data[0] && props.isViewMode) {
        // use case khi use template ở khác portal thì cần lấy properties trong variantExtraData
        isNullRes = true;
        res = {
          data: [
            {
              properties: props.variantExtraData.properties,
            },
          ],
        };
      }

      props.setIsFetchData(false);
      const { properties = {} } = res.data[0];
      const selectedTemplate = {
        properties,
        template_setting: template_settings,
        template_name: safeParse(template_settings, {}).name,
        template_type: safeParse(template_settings, {}).type,
        // Nếu là case clone => set fe_config_id = null
        fe_config_id: isCopy ? null : id || null,
      };

      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        draft.isLoadingSavedBlock = false;
      });

      if (!isNullRes) {
        props.onChangeOthers(JSON_TEMPLATE, selectedTemplate, {
          isSavedVariant: true,
        });
      }
    };
    // case Clone
    if (copy_id) {
      fetchData(copy_id, true);
    }
    // case update variant đã save API || case clone
    else if (fe_config_id) {
      fetchData(fe_config_id, false);
    }
  }, [props.variantExtraData.fe_config_id, props.variantExtraData.copy_id]);

  // useEffect(() => {
  //   setState(draft => {
  //     draft.isLoadingTypesList = true;
  //   });
  //   const fetchData = async () => {
  //     const res = await JourneyServices.emailTemplate.getListType({
  //       data: {},
  //     });
  //     setState(draft => {
  //       // draft.selectedTypes = res.data[0].template_type_id;
  //       draft.typesList = res.data || [];
  //       draft.isLoadingTypesList = false;
  //     });
  //   };
  //   const fetchVersion = async () => {
  //     const res = await JourneyServices.emailTemplate.getVersion();
  //     setState(draft => {
  //       draft.version = res.data.version || '';
  //     });
  //   };

  //   fetchData();
  //   fetchVersion();
  // }, []);

  useEffect(() => {
    if (state.selectedTypes.length && state.selectedDevices && !state.isSaved) {
      setState(draft => {
        draft.isLoadingTemplateType = true;
      });
      const fetchMediaTemplates = async (types, devices) => {
        const filter = [
          // {
          //   TEMPLATE_TYPE: {
          //     matches_any: types.includes(TYPE_ALL)
          //       ? // type all => matches_any all types
          //         state.typesList.map(template => template.template_type_id)
          //       : types,
          //   },
          // },
          {
            DEVICE_TYPE: { matches_any: devices },
          },
        ];
        const params = `?page=${1}&columns=["properties","template_setting","thumbnail"]&filter=${JSON.stringify(
          filter,
        )}`;
        const res =
          state.tabActive === MY_TEMPLATE
            ? await JourneyServices.jsonTemplate.getListByType(params)
            : await JourneyServices.jsonTemplate.getListGallery(params);

        const data =
          state.tabActive === MY_TEMPLATE
            ? res.data.body || []
            : (res.data.body || []).map(item => ({
                ...item,
                template_id: item.media_gallery_id,
                template_name: item.media_gallery_name,
              }));

        setState(draft => {
          draft.mediaTemplateList = data;
          draft.isLoadingTemplateType = false;
        });
      };
      fetchMediaTemplates(state.selectedTypes, state.selectedDevices);
    }
  }, [
    state.selectedTypes,
    state.selectedDevices,
    state.tabActive,
    state.typesList,
    state.isSaved,
  ]);

  useEffect(() => {
    const listener = event => {
      const { actionType, data } = event.data;
      switch (actionType) {
        case 'onCancelMediaJson': {
          onCloseDrawer();
          break;
        }
        case 'onSaveMediaJson': {
          const selectedTemplate = { ...data };
          setState(draft => {
            draft.selectedTemplate = selectedTemplate;
            draft.isSaved = true;
            draft.isUseTemplate = false;
            draft.isBlank = false;
          });
          props.onChangeOthers(JSON_TEMPLATE, selectedTemplate, {
            isUseTemplate: state.isUseTemplate,
            templateName: state.templateName,
            isBlank: state.isBlank,
          });
          onCloseDrawer();
          break;
        }
        case 'setMediaJsonDetailSuccess': {
          stopPostMessage();
          break;
        }
        default: {
          console.log('default');
        }
      }
    };

    window.addEventListener('message', listener);

    return () => window.removeEventListener('message', listener);
  }, [props.componentKey, state.postMessageId]);

  const onSelectType = value => {
    const index = state.selectedTypes.findIndex(type => type === value);
    if (index === -1) {
      const indexTypeAll = state.selectedTypes.findIndex(
        type => type === TYPE_ALL,
      );

      // select All => uncheck others
      if (value === TYPE_ALL && indexTypeAll === -1) {
        setState(draft => {
          draft.selectedTypes = [0];
        });
      } else if (value !== TYPE_ALL && indexTypeAll > -1) {
        // select others type & type all selected => select new type & uncheck type all
        setState(draft => {
          draft.selectedTypes.splice(indexTypeAll, 1, value);
        });
      } else {
        // select others type
        setState(draft => {
          draft.selectedTypes.push(value);
        });
      }
    } else if (state.selectedTypes.length > 1) {
      // Prevent delete last item
      setState(draft => {
        draft.selectedTypes.splice(index, 1);
      });
    }
  };

  const onSelectDevice = value => {
    const index = state.selectedDevices.findIndex(type => type === value);
    if (index === -1) {
      setState(draft => {
        draft.selectedDevices.push(value);
      });
    } else if (state.selectedDevices.length > 1) {
      // Prevent delete last item
      setState(draft => {
        draft.selectedDevices.splice(index, 1);
      });
    }
  };

  const onOpenDrawer = () => {
    setState(draft => {
      draft.isOpenDrawer = true;
    });
    props.updateIsOpenSubDrawer(true);

    if (searchParams.get('ui') === UI_DETAIL_DRAWER) {
      props.updateFullScreen(true);
    }
  };

  const onCloseDrawer = (_event, reason) => {
    props.updateIsOpenSubDrawer(false);
    setState(draft => {
      draft.isOpenDrawer = false;
    });
    props.updateFullScreen(false);
    // if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
    // }
  };

  const onSelectTemplate = value => {
    setState(draft => {
      // draft.isOpenDrawer = true;
      draft.selectedTemplate = value;
      if (value.template_id) {
        draft.isUseTemplate = true;
        draft.templateName = value.template_name;
      } else {
        draft.isBlank = true;
      }
    });
    // handle pre-select template blast campaign
    if (props.templateType) {
      const channelCode = props.channelActive.code;
      const channelId = getChannelIdByCode(channelCode);
      const redirectUrl = getUrlCreateJourneyByTemplate(
        channelId,
        props.templateType,
      );

      if (props.isCreateJourneyV2) {
        props.onChange('USE_TEMPLATE', {
          templateType: props.templateType,
          model: value,
        });
        return;
      }

      props.updateCreatingJourneyInfo({
        channelCode,
        templateType: props.templateType,
        data: {},
        selectedTemplateTemp: value,
      });

      if (redirectUrl) history.push(redirectUrl);
    } else {
      onOpenDrawer();
    }
  };

  const onPostMessage = () => {
    const mediaTemplateIframe = document.getElementById(
      'media-template-iframe',
    );
    // remove Previews
    window.postMessage(
      { messageType: 'preview-antsomi-cdp-campaign-close-all' },
      '*',
    );

    if (mediaTemplateIframe) {
      const { journeySettings = {} } = props;

      const data = {
        ...state.selectedTemplate.properties,
        journeySettings,
      };

      const postMessage = () => {
        if (mediaTemplateIframe.contentWindow) {
          mediaTemplateIframe.contentWindow.postMessage(
            {
              actionType: 'setMediaJsonDetail',
              data,
            },
            '*',
          );
        }
      };
      startPostMessage(postMessage);
    }
  };

  const startPostMessage = postMessage => {
    let timer = 0;
    const interval = 200;

    const postMessageId = setInterval(() => {
      if (typeof postMessage === 'function' && timer <= 5000) {
        postMessage();
        timer += interval;
      }
    }, interval);

    setState(draft => {
      draft.postMessageId = postMessageId;
    });
  };

  const stopPostMessage = () => {
    clearInterval(state.postMessageId);
  };

  const onEditTemplate = () => {
    onOpenDrawer();
    onPostMessage();
  };

  const onResetSelectedTemplate = () => {
    const selectedTemplate = {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
    };
    setState(draft => {
      draft.isSaved = false;
      // draft.selectedTypes = '';
      draft.selectedTemplate = selectedTemplate;
    });

    props.onChangeOthers(JSON_TEMPLATE, selectedTemplate);
  };

  const handleChangeTab = value => {
    setState(draft => {
      draft.tabActive = value;
    });
  };

  const renderSavedBlock = () => {
    return (
      <WrapperSavedBlock>
        <UILoading isLoading={state.isLoadingSavedBlock} />
        <SavedBlock>
          {state.selectedTemplate.properties.thumbnail ? (
            <img
              src={state.selectedTemplate.properties.thumbnail}
              alt={state.selectedTemplate.properties.name}
              width="100%"
              className="img-thumbnail"
              // height="100%"
            />
          ) : null}
          {!props.isViewMode && (
            <div className="btn-group">
              <UIButton theme="primary" onClick={onEditTemplate}>
                {MAP_TITLE.actEdit}
              </UIButton>
              <UIButton
                theme="outline"
                onClick={onResetSelectedTemplate}
                className="m-left-3 bg-white"
              >
                {MAP_TITLE.actChangeTemplate}
              </UIButton>
            </div>
          )}
        </SavedBlock>
      </WrapperSavedBlock>
    );
  };

  const renderBlankTemplate = () => {
    const blankTemplate = {
      template_name: '',
      template_type: 4,
      device_type: 1,
      template_setting: {},
      properties: {
        deviceType: 1,
      },
      thumbnail: '',
    };

    return (
      <StyledGridItem item xs="auto">
        <TemplateBlock className="blank-template">
          <div className="d-flex flex-column align-items-center justify-content-center">
            <AddButton
              className="add-button"
              onClick={() => onSelectTemplate(blankTemplate)}
            />
            <p className="blank-template-text">{MAP_TITLE.blankTemplate}</p>
          </div>
        </TemplateBlock>
        <p className="p-all-0 text-center" />
      </StyledGridItem>
    );
  };

  const renderDisplayBlock = () => {
    return (
      <>
        {renderUITabs()}
        <WrapperMediaTemplate
          className="wrapper-media-template"
          style={{ height: '100%' }}
        >
          <StyledGridContainer container>
            <UILoading isLoading={state.isLoadingTemplateType} />
            {/* {state.tabActive === GALLERY ? renderBlankTemplate() : null} */}
            {state.tabActive === MY_TEMPLATE && props.isDisplayUseTemplate
              ? renderBlankTemplate()
              : null}
            {state.mediaTemplateList.length ? (
              state.mediaTemplateList.map(item => (
                <StyledGridItem key={item.template_id} item xs="auto">
                  <TemplateBlock className="template-block">
                    <div className="layer" />
                    <img src={item.thumbnail} alt={item.thumbnail} />
                    <div className="template-buttons">
                      <UIButton
                        theme="primary"
                        // onClick={() => onOpenDrawer(true)}
                        onClick={() => onSelectTemplate(item)}
                        className="btn-template"
                      >
                        {props.isDisplayUseTemplate
                          ? MAP_TITLE.actUseTemplate
                          : MAP_TITLE.actCreateCampaign}
                      </UIButton>
                    </div>
                  </TemplateBlock>
                  <p className="p-all-0 text-center">{item.template_name}</p>
                </StyledGridItem>
              ))
            ) : (
              <WrapperNoData>
                <div className="icon-nodata">
                  <MediaGalleryIcon color="#a2a2a2" />
                </div>
                <p className="text-no-template">{MAP_TITLE.noTemplate}.</p>
              </WrapperNoData>
            )}
          </StyledGridContainer>
        </WrapperMediaTemplate>
      </>
    );
  };
  const renderDrawer = () => {
    return (
      <Drawer
        anchor="right"
        open={state.isOpenDrawer}
        onClose={(_, reason) => onCloseDrawer(_, reason)}
        PaperProps={{
          style: {
            // width: '90vw',
            width: 'calc(100vw - 48px)',
          },
        }}
      >
        <Iframe
          id="media-template-iframe"
          title="media-template-iframe"
          src={`${
            PORTAL_CONFIG.URL_MEDIA_TEMPLATE
          }/${getPortalId()}#/${getCurrentUserId()}/media-json/embed`}
          allowFullScreen
          width="100%"
          height="100%"
          onLoad={onPostMessage}
        />
      </Drawer>
    );
  };

  const renderUITabs = () => {
    return (
      <StyledUITabs
        height="40px"
        activeTab={state.tabActive}
        onChange={handleChangeTab}
        classNameTabNav={`p-x-3 ui-tabs ${props.classNameTabNav ||
          ''} ${props.isDisplayUseTemplate && 'mode-use'} json-template`}
        tippy
        isBoxShadow={false}
      >
        {/* <TabPanel
          label={TEMPLATE_TYPE.gallery.label}
          eventKey={TEMPLATE_TYPE.gallery.value}
        /> */}
        <TabPanel
          label={TEMPLATE_TYPE.my_template.label}
          eventKey={TEMPLATE_TYPE.my_template.value}
        />
      </StyledUITabs>
    );
  };
  return (
    <>
      {state.isSaved ? renderSavedBlock() : renderDisplayBlock()}
      {renderDrawer()}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => ({
  updateCreatingJourneyInfo: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`, value));
  },
  updateIsOpenSubDrawer: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_OPEN_SUB_DRAWER`, value));
  },
  updateLoadingOnStepTwo: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@LOADING_ON_STEP_TWO`, params));
  },
  updateFullScreen: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`, params));
  },
});

MediaTemplateEditor.propTypes = {
  variantExtraData: PropTypes.object,
  setIsFetchData: PropTypes.func,
  onChangeOthers: PropTypes.func,
  // eslint-disable-next-line react/no-unused-prop-types
  isViewMode: PropTypes.bool,
  componentKey: PropTypes.string,
  creatingJourneyInfo: PropTypes.object,
};

MediaTemplateEditor.defaultProps = {
  variantExtraData: {
    design: 'json-template',
    type: 'json-template',
    template_settings: {
      rulesets: [],
    },
    properties: {},
    fe_config_id: null,
    views: {},
  },
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(MediaTemplateEditor);

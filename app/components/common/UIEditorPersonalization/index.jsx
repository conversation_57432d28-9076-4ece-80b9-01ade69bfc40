/* eslint-disable no-lonely-if */
/* eslint-disable no-useless-escape */
/* eslint-disable prefer-destructuring */
/* eslint-disable object-shorthand */
/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
/* eslint-disable indent */
/* eslint-disable no-lone-blocks */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-shadow */
/* eslint-disable no-unused-expressions */
/* eslint-disable func-names */
/* eslint-disable react/button-has-type */
/* eslint-disable no-param-reassign */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-multi-str */
// Libraries
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import produce from 'immer';
import { useImmer } from 'use-immer';
import { UILoading as Loading } from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';
import styled from 'styled-components';
// Components

// Assets
import './styles.scss';
import queryString from 'query-string';
import styles from './editor.scss';
import { getObjectPropSafely, random } from './utils.3rd';
import {
  safeParse,
  generateKey,
  replacePersonalizeTag,
} from '../../../utils/common';
import { defaultProps, propTypes } from './propTypes';
import {
  convertHtmlText,
  getInsertPattern,
  getInsertPatternAddBody,
  handleUpdateInputPersonalization,
  regexContent,
  STATUS_CREATE,
  STATUS_UPDATE,
} from './utils';
import PopupPersonalization from './components/PopupPersonalization/ModalV2';
import UIPopupEditorMultiTab from '../UIPopupEditorMultiTab';
import UIPopupObjectWidget from './components/PopupObjectWidget';
import { renderDocumentContent } from '../UIPopupEditorMultiTab/utils';
import UIPopupPreview from './components/PopupPreview';
import { getPortalId, getToken } from '../../../utils/web/cookie';
import { addMessageToQueue } from '../../../utils/web/queue';
import useDebounce from '../../../hooks/useDebounce';
import { detectContentEdit, getInitialSelectedNotFound } from './utils.extends';
import { getInitialSelected } from './components/PopupPersonalization/utils';

const PATH = 'app/components/common/UIEditorPersonalization/index.jsx';

export const TinymceEditor = props => {
  const { isViewMode } = props;
  const [state, setState] = useImmer({
    css: '',
    js: '',
    html: '',
    isRendering: true,
    design: STATUS_CREATE,
    isDisabledButtonPersonalization: false,
    onChangeKey: '',
  });
  const [textValue, setTextValue] = useState('');
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [isOpenPopupMultiTab, setIsOpenPopupMultiTab] = useState(false);
  const [isOpenPopupPreview, setIsOpenPopupPreview] = useState(false);
  const [stateObjectWidget, setStateObjectWidget] = useImmer({
    isOpen: false,
    activeWidget: {},
  });

  const [initSelectedProperties, setInitSelectedProperties] = useState({});
  const querySearch = queryString.parse(location.search).design;
  const debounceText = useDebounce(textValue, 500);
  const wrapperRef = useRef(null);
  const editorRef = useRef(null);
  const myTagRef = useRef({});
  const loadDataViewModeRef = useRef();

  // get index of data textHTML into body tag of textValue

  const currentTagActive = useRef('');

  useLayoutEffect(() => {
    // get button Els [x] to create hover and style it
    const contentIframe = Array.from(
      wrapperRef &&
        wrapperRef.current &&
        wrapperRef.current.getElementsByTagName('iframe'),
    )[0];

    const iconRemoveEls =
      contentIframe &&
      contentIframe.contentWindow.document.querySelectorAll(
        '.editor-icon-remove',
      );
    const listIconRemove = iconRemoveEls && Array.from(iconRemoveEls);
    // get button elements
    const buttonElement =
      contentIframe &&
      contentIframe.contentWindow.document.querySelectorAll('.insert-word');
    const listBtnElement = buttonElement && Array.from(buttonElement);

    if (listIconRemove && listBtnElement) {
      handleUpdateInputPersonalization(
        listIconRemove,
        listBtnElement,
        'advance',
      );
    }
  }, [
    textValue,
    props.dataState.isLoadDataDone,
    props.initData,
    props.componentKey,
    state.isRendering,
  ]);

  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  const resetTextValue = initData => {
    // console.log('convertHtmlText', initData);
    let convertTextToHTML = '';
    // console.log('UIEditorPersonalization initData', initData);
    if (typeof safeParse(initData, '') === 'string') {
      convertTextToHTML = safeParse(initData, '');
      setTextValue(
        convertHtmlText({
          htmlText: convertTextToHTML,
          format: 'html',
          state: props.dataState,
          objectWidgetInput: props.objectWidgetInput,
          type: props.type,
        }),
      );
      setStateCommon({
        html: initData,
        css: '',
        js: '',
      });
    } else if (typeof safeParse(initData, {}) === 'object' && initData.html) {
      setTextValue(
        convertHtmlText({
          htmlText: initData.html,
          format: 'html',
          state: props.dataState,
          objectWidgetInput: props.objectWidgetInput,
          type: props.type,
        }),
      );
      setStateCommon({
        html: initData.html,
        css: initData.css,
        js: initData.js,
      });
    }
    //
  };

  useEffect(() => {
    if (props.dataState.isLoadDataDone) {
      // console.log('props.initData', props.initData, props.componentKey);
      resetTextValue(props.initData);
    }
  }, [props.dataState.isLoadDataDone, props.initData, props.componentKey]);

  useEffect(() => {
    try {
      resetEditorState();
      if (typeof props.onChange === 'function') {
        if (props.type === 'editor') {
          const valueRaw = convertHtmlText({
            htmlText: textValue,
            format: 'raw',
            state: props.dataState,
            objectWidgetInput: props.objectWidgetInput,
            type: props.type,
          });
          props.onChange(valueRaw);
        } else {
          const valueRaw = convertHtmlText({
            htmlText: textValue,
            format: 'raw',
            state: props.dataState,
            objectWidgetInput: props.objectWidgetInput,
            type: props.type,
          });
          console.log('valueRaw', { valueRaw, textValue });
          const data = regexContent(valueRaw);
          // props.onChange({ html: data.html, css: state.css, js: state.js });
          console.log('{ html: data.html, css: state.css, js: state.js }', {
            html: data.html,
            css: state.css,
            js: state.js,
          });
          props.onChange({ html: data.html, css: state.css, js: state.js });
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      console.log(error);
    }
  }, [debounceText, state.onChangeKey]);

  const handleEditorChange = (content, editor) => {
    try {
      setTextValue(content);
      if (isViewMode && loadDataViewModeRef.current && editor) {
        loadDataViewModeRef.current.innerHTML = editor.getBody().outerHTML;
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleEditorChange',
        data: error.stack,
      });
      console.log(error);
    }
  };

  const togglePopover = () => {
    setPopoverOpen(!popoverOpen);
    if (state.design === STATUS_UPDATE) {
      setStateCommon({ design: STATUS_CREATE });
    }

    setInitSelectedProperties(getInitialSelected(props.dataState));
    currentTagActive.current = '';
  };

  const togglePopupMultiTab = () => {
    setIsOpenPopupMultiTab(!isOpenPopupMultiTab);
  };

  const togglePopupObjectWidget = () => {
    if (state.design === STATUS_UPDATE) {
      setStateCommon({ design: STATUS_CREATE });
    }
    setStateObjectWidget(draft => {
      draft.isOpen = !stateObjectWidget.isOpen;
    });
  };

  const togglePopupPreview = () => {
    setIsOpenPopupPreview(!isOpenPopupPreview);
  };
  const onChangePopupEditor = data => {
    const { dataState, dataRender } = data;
    setTextValue(dataState.html);
    setStateCommon({
      css: dataState.css,
      js: dataState.js,
      html: dataState.html,
      onChangeKey: generateKey(),
    });
  };

  const onCallbackObjectWidgetInput = data => {
    const { objectWidgetInput = {} } = props;
    objectWidgetInput[data.code] = data;
    props.onChangeOthers('objectWidgetInput', objectWidgetInput);
  };

  const onChangePopupObjectWidget = dataComming => {
    // console.log('dataComming', dataComming);
    const data = dataComming;
    // const data = {
    //   objectWidgetId: 'dataObjectWidgetId',
    //   name: 'Thành test',
    //   code: 'thanh_test',
    //   customFilters: { OR: [{ AND: [] }] },
    //   settings: {
    //     data_type_integer: 'data data_type_integer',
    //     data_type_double: 'data data_type_double',
    //   },
    // };

    const selectedProperties = {
      personalType: {
        value: 'objectWidget',
        label: 'Object Widget',
      },
      attribute: {
        value: `custom_${data.code}`,
        id: data.objectWidgetId,
        label: data.name,
        bracketed_code: `custom_${data.code}`,
      },
      value: '',
    };

    try {
      const bgColor = '#1890F0';
      const personalizationCodeCustom = `#{objectWidget.${
        selectedProperties.attribute.bracketed_code
      }}`;
      if (state.design === STATUS_CREATE) {
        const id = random(5);

        if (currentTagActive.current) {
          editorRef.current.editor.dom.remove(`${currentTagActive.current}`);
          currentTagActive.current = '';
        }

        editorRef.current.editor.insertContent(
          getInsertPattern(id, personalizationCodeCustom, data.name, bgColor),
        );

        myTagRef.current = {
          ...myTagRef.current,
          [id]: selectedProperties,
        };
      } else {
        const regex = new RegExp(
          `<button id="${
            currentTagActive.current
          }" .*?class="insert-word".*? value=(.*?)>(.*?)<\\/button>`,
          'gim',
        );

        let tempTextValue = `${textValue}`;

        const newButton = getInsertPattern(
          currentTagActive.current,
          personalizationCodeCustom,
          data.name,
          bgColor,
        );

        tempTextValue = tempTextValue.replace(regex, newButton);

        setTextValue(
          renderDocumentContent({
            html: tempTextValue,
            css: state.css,
            js: state.js,
          }),
        );
      }

      currentTagActive.current = '';
      setPopoverOpen(false);
      // setSelectedProperties(getInitialSelected(props.dataState));

      if (state.design === STATUS_UPDATE) {
        setStateCommon({ design: STATUS_CREATE });
      }

      onCallbackObjectWidgetInput(data);

      togglePopupObjectWidget();
      editorRef.current.editor.focus();
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleInsertText',
        data: error.stack,
      });
      console.log(error);
    }
  };

  const handleInsertText = (
    selectedProperties,
    personalizationCode,
    classNameTag,
  ) => {
    try {
      // console.log('editorRef.current.editor', editorRef.current.editor);
      let bgColor = '#009000';

      // console.log('selectedProperties', selectedProperties);
      // console.log('classNameTag', classNameTag);
      // console.log('personalizationCode', personalizationCode);

      classNameTag === 'blue' && (bgColor = '#3C39BA');
      classNameTag === 'orange' && (bgColor = '#FBAC00');
      classNameTag === 'disabled' && (bgColor = '#ccc');
      classNameTag === 'sky-blue' && (bgColor = '#24abb9');
      classNameTag === 'violet' && (bgColor = '#9238b5');
      classNameTag === 'violet-pc' && (bgColor = '#6750A7');

      if (state.design === STATUS_CREATE) {
        const id = random(5);

        if (currentTagActive.current) {
          editorRef.current.editor.dom.remove(`${currentTagActive.current}`);
          currentTagActive.current = '';
        }

        editorRef.current.editor.insertContent(
          getInsertPatternAddBody(
            id,
            personalizationCode,
            getObjectPropSafely(() => selectedProperties.attribute.label),
            bgColor,
          ),
        );
        myTagRef.current = {
          ...myTagRef.current,
          [id]: selectedProperties,
        };
      } else {
        const regex = new RegExp(
          `<button id="${
            currentTagActive.current
          }" .*?class="insert-word".*? value=(.*?)>(.*?)<\\/button>`,
          'gim',
        );

        let tempTextValue = `${textValue}`;

        const newButton = getInsertPatternAddBody(
          currentTagActive.current,
          personalizationCode,
          getObjectPropSafely(() => selectedProperties.attribute.label),
          bgColor,
        );

        tempTextValue = tempTextValue.replace(regex, newButton);

        setTextValue(
          renderDocumentContent({
            html: tempTextValue,
            css: state.css,
            js: state.js,
          }),
        );
      }

      currentTagActive.current = '';
      setPopoverOpen(false);
      // setInitSelectedProperties(getInitialSelected(props.dataState));

      if (state.design === STATUS_UPDATE) {
        setStateCommon({ design: STATUS_CREATE });
      }

      editorRef.current.editor.focus();
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleInsertText',
        data: error.stack,
      });
      console.log(error);
    }
  };

  const editText = event => {
    try {
      setStateCommon({ design: STATUS_UPDATE });
      if (event.target.id) {
        const { value } = event.target;
        const {
          personalType,
          personalTypeCode = '',
          attributeType,
          attributeTypeCode = '',
          valueString,
          typeItem,
          attributePromotionCode,
        } = detectContentEdit(
          value,
          props.dataState.personalizationType,
          props.dataState.personalizationData,
          props.dataState.promotionCodeAttr,
        );

        // console.log({
        //   personalType,
        //   personalTypeCode,
        //   attributeType,
        //   attributeTypeCode,
        //   valueString,
        //   typeItem,
        // });

        if (personalType && attributeType) {
          // case cũ, show personalization attribute
          setInitSelectedProperties({
            personalType,
            attribute: attributeType,
            value: valueString,
            promotionCodeAttr: attributePromotionCode,
          });

          setPopoverOpen(true);
        } else if (
          personalTypeCode === 'objectWidget' &&
          attributeTypeCode.startsWith('custom_')
        ) {
          // object widget
          setStateObjectWidget(draft => {
            draft.isOpen = true;
            draft.activeWidget = {
              personalType,
              personalTypeCode,
              attributeType,
              attributeTypeCode,
              valueString,
              typeItem,
            };
          });
        } else {
          // personalization not found
          setInitSelectedProperties(
            getInitialSelectedNotFound(props.dataState, {
              personalTypeCode,
              attributeTypeCode,
              value: valueString,
              promotionCodeAttr: attributePromotionCode,
            }),
          );
          setPopoverOpen(true);
        }
        currentTagActive.current = event.target.id;
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'editText',
        data: error.stack,
      });
      console.log(error);
    }
  };

  const onInit = () => {
    setStateCommon({ isRendering: false });
  };

  const handleResetDisable = (elementsTool, status) => {
    if (elementsTool !== undefined && props.showPersonalization) {
      elementsTool && elementsTool.setAttribute('aria-disabled', status);
    }
  };

  const resetEditorState = (isRenderDocument = false) => {
    if (!editorRef.current) {
      return;
    }
    let contentEditor =
      editorRef.current.editor && editorRef.current.editor.getContent();
    let isChangeContent = false;
    if (contentEditor) {
      // xóa các tag html dư thừa từ editor trả ra (nếu có)
      if (
        contentEditor.match(/<div id="gtx-trans".*?>/gim) ||
        contentEditor.match(/<div class="gtx-trans-icon".*?>.*?<\/div>/gim)
      ) {
        contentEditor = contentEditor
          .replace(/<div id="gtx-trans".*?>/gim, '')
          .replace(/<div class="gtx-trans-icon".*?>.*?<\/div>/gim, '')
          .trim();

        isChangeContent = true;
      }

      if (isRenderDocument && props.type !== 'editor') {
        setTextValue(
          renderDocumentContent({
            html: contentEditor,
            css: state.css,
            js: state.js,
          }),
        );
      } else {
        if (isChangeContent) {
          setTextValue(contentEditor);
        }
      }
    }

    const checkPersonalizationTag =
      contentEditor && contentEditor.match(/<button/gim);
    // const elementsTool = document.getElementsByClassName('tox-tbtn');
    // const elementsTool = document.getElementsByClassName(
    //   'icon-xlab-person-alt',
    // )[0].parentElement.parentElement;
    let elementsTool;
    const tmpElement = document.getElementsByClassName(
      'icon-xlab-person-alt',
    )[0];
    if (tmpElement !== undefined && tmpElement.parentElement !== undefined) {
      elementsTool = tmpElement.parentElement.parentElement;
    }
    // console.log('elementsTool', elementsTool);
    if (checkPersonalizationTag && Array.isArray(checkPersonalizationTag)) {
      if (checkPersonalizationTag.length >= 15) {
        // NOTE: Lưu ý thứ tự sắp xếp personalTag trong file propTypes.js dòng 101

        if (state.isDisabledButtonPersonalization === false) {
          setStateCommon({ isDisabledButtonPersonalization: true });
          handleResetDisable(elementsTool, true);
        }
      } else {
        setStateCommon({ isDisabledButtonPersonalization: false });
        handleResetDisable(elementsTool, false);
      }
    } else if (
      checkPersonalizationTag === null &&
      state.isDisabledButtonPersonalization
    ) {
      setStateCommon({ isDisabledButtonPersonalization: false });
      handleResetDisable(elementsTool, false);
    }
  };

  const onOpenPopupPersonalization = () => {
    setPopoverOpen(true);
  };

  // const dataRenderPopup = useMemo(
  //   () =>
  //     textValue && textValue.includes('<!DOCTYPE html>')
  //       ? textValue
  //       : renderDocumentContent({
  //           html: textValue,
  //           css: state.css,
  //           js: state.js,
  //         }),
  //   [textValue, state.css, state.js],
  // );

  return (
    <ErrorBoundary path="app/components/common/UIEditorPersonalization/index.jsx">
      <div
        className={`ants-tinymce-editor${querySearch ? `-update` : ''} ${
          state.isDisabledButtonPersonalization
            ? 'disabled-button-personalization'
            : ''
        }`}
        style={{ position: 'relative', maxWidth: '100% !important' }}
        ref={wrapperRef}
      >
        {/* wating for isViewMode */}
        <Loading isLoading={state.isRendering} />
        {/* {!isViewMode ? ( */}
        <div style={{ display: !isViewMode ? 'block' : 'none' }}>
          <Editor
            ref={editorRef}
            // apiKey={'pj3be4r7csm282qh8gsssq1cywkvjh2j7iic281901rof8aw'}
            // tinymceScriptSrc='./tinymce.min.js'
            apiKey="scyw71pj8619analvxs56ppc2w2fj2kpy5vnmflhhc300y35"
            // inline={true}
            value={textValue}
            onInit={onInit}
            init={{
              min_height: props.minHeight,
              max_height: props.maxHeight,
              max_width: props.maxWidth,
              min_width: props.minWidth,
              menubar: props.menubar || false,
              plugins:
                props.type === 'htmlEditor'
                  ? props.plugins || []
                  : props.pluginsOnlyHtmlEditor || [],
              toolbar: props.toolbar,
              font_formats:
                'Andale Mono=andale mono,times; Arial=arial,helvetica,sans-serif; Arial Black=arial black,avant garde; Book Antiqua=book antiqua,palatino; Comic Sans MS=comic sans ms,sans-serif; Courier New=courier new,courier; Georgia=georgia,palatino; Helvetica=helvetica; Impact=impact,chicago; Open Sans=Open Sans, sans-serif; Symbol=symbol; Tahoma=tahoma,arial,helvetica,sans-serif; Terminal=terminal,monaco; Times New Roman=times new roman,times; Trebuchet MS=trebuchet ms,geneva; Verdana=verdana,geneva; Webdings=webdings; Wingdings=wingdings,zapf dingbats',
              fontsize_formats:
                '8px 9px 10px 11px 12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 48px 72px',
              // skin: 'snow',
              toolbar_mode: 'wrap',
              branding: false,
              body_class: 'antsomi-editor-body',
              content_style: 'body { font-size: 12px; }',
              tinydrive_token_provider: `${
                PORTAL_CONFIG.URL_API
              }/thirdparty-services/v2.0/tinymce?portalId=${getPortalId()}&token=${getToken()}`,
              setup: editor => {
                editor.on('init', function(e) {
                  // editor.getBody().style.fontSize = '12px';
                  // editorRef.current.editor = editor;
                  if (isViewMode && loadDataViewModeRef.current) {
                    loadDataViewModeRef.current.innerHTML = editor.getBody().outerHTML;
                  }
                });

                props.showPersonalization &&
                  editor.ui.registry.addButton('personalTag', {
                    text:
                      '<span class="icon-xlab-person-alt" style="font-size: 20px"></span>',
                    onAction(_) {
                      const isCheckPersonalTag = document.querySelector(
                        '.disabled-button-personalization',
                      );

                      isCheckPersonalTag === null &&
                        onOpenPopupPersonalization();
                    },
                  });

                props.showObjectWidget &&
                  editor.ui.registry.addButton('objectWidget', {
                    // icon: 'sourcecode',
                    // tooltip: 'Source code',
                    text:
                      '<span class="icon-xlab-people-alt" style="font-size: 20px"></span>',
                    onAction: function(_) {
                      togglePopupObjectWidget();
                    },
                  });

                props.showPopupEditorHTML &&
                  editor.ui.registry.addButton('source', {
                    icon: 'sourcecode',
                    tooltip: 'Source code',
                    onAction: function(_) {
                      togglePopupMultiTab();
                    },
                  });

                editor.ui.registry.addButton('previewCustom', {
                  icon: 'preview',
                  tooltip: 'Preview',
                  onAction: function(_) {
                    togglePopupPreview();
                  },
                });
              },
              init_instance_callback(editor) {
                editor.on('keydown', function(event) {
                  if (event.keyCode === 27) {
                    const { dom } = editor;
                    const parentBlock = editor.selection.getSelectedBlocks()[0];

                    if (parentBlock) {
                      const containerBlock =
                        getObjectPropSafely(
                          () => parentBlock.parentNode.nodeName,
                        ) === 'BODY'
                          ? dom.getParent(parentBlock, dom.isBlock)
                          : dom.getParent(
                              parentBlock && parentBlock.parentNode,
                              dom.isBlock,
                            );
                      const newBlock = editor.dom.create('p');

                      newBlock.innerHTML = '<br data-mce-bogus="1">';
                      dom.insertAfter(newBlock, containerBlock);
                      const rng = dom.createRng();

                      newBlock.normalize();
                      rng.setStart(newBlock, 0);
                      rng.setEnd(newBlock, 0);
                      editor.selection.setRng(rng);
                    }
                  }
                });
                editor.on('click', function(e) {
                  if (
                    e.target.nodeName === 'EM' &&
                    e.target.className === 'editor-icon-remove'
                  ) {
                    e.target.parentElement.remove();
                    setTextValue(editor.getContent());
                    resetEditorState(true);
                  }

                  if (e.target.className === 'insert-word' && e.target.id) {
                    editText(e);
                  }
                });
              },
              resize: true,
              entity_encoding: 'raw',
              forced_root_block: false,
              force_br_newlines: true, // tried with and without
              force_p_newlines: false, //
            }}
            // outputFormat='text'
            onEditorChange={handleEditorChange}
          />
        </div>

        <div
          ref={loadDataViewModeRef}
          style={{ display: isViewMode ? 'block' : 'none', margin: '10px 0 0' }}
        />

        {props.showPersonalization && (
          <PopupPersonalization
            initSelectedProperties={initSelectedProperties}
            popoverOpen={popoverOpen}
            togglePopover={togglePopover}
            handleInsertText={handleInsertText}
            design={state.design}
            dataState={props.dataState}
          />
        )}
        {props.showPopupEditorHTML && isOpenPopupMultiTab ? (
          <UIPopupEditorMultiTab
            isOpen={isOpenPopupMultiTab}
            toggle={togglePopupMultiTab}
            onChange={onChangePopupEditor}
            // initData={dataRenderPopup}
            type={props.type}
            initHtml={textValue}
            initCss={state.css}
            initJs={state.js}
          />
        ) : null}
        {props.showObjectWidget && stateObjectWidget.isOpen ? (
          <UIPopupObjectWidget
            initSelectedWidget={stateObjectWidget.activeWidget}
            isOpen={stateObjectWidget.isOpen}
            toggle={togglePopupObjectWidget}
            onChange={onChangePopupObjectWidget}
            objectWidgetInput={props.objectWidgetInput}
            type={props.type}
            design={state.design}
          />
        ) : null}
        <UIPopupPreview
          isOpen={isOpenPopupPreview}
          toggle={togglePopupPreview}
          // initData={dataRenderPopup}
          initHtml={textValue}
          initCss={state.css}
          initJs={state.js}
        />
      </div>
    </ErrorBoundary>
  );
};

TinymceEditor.propTypes = propTypes;

TinymceEditor.defaultProps = defaultProps;

export default TinymceEditor;

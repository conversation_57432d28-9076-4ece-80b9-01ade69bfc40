/* eslint-disable prefer-destructuring */
/* eslint-disable no-plusplus */
/* eslint-disable react/no-danger */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-param-reassign */
/* eslint-disable react/button-has-type */
/* eslint-disable no-lone-blocks */
/* eslint-disable no-prototype-builtins */
/* eslint-disable react/prop-types */
/* eslint-disable no-cond-assign */
/* eslint-disable one-var */
/* eslint-disable prefer-const */
/* eslint-disable indent */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/jsx-curly-brace-presence */
// Libraries
import React, {
  useState,
  useLayoutEffect,
  useEffect,
  useRef,
  useMemo,
} from 'react';
import classnames from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import _ from 'lodash';
import { useImmer } from 'use-immer';
// import UIDropdownAction from 'components/common/UIDropdownAction';
import { Popover } from '@material-ui/core';
import ErrorBoundary from 'components/common/ErrorBoundary';
// import { useParams } from 'react-router';
import JourneyServices from 'services/Journey';
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';
import PopupPersonalization from '../components/PopupPersonalization/ModalV2';
// Components
import { getObjectPropSafely, random } from '../utils.3rd';
import CaretPositioning from './EditCaretPositioning';
import { WrapperInputStyle, SpanIcon, StylePopover } from './styled';
import { defaultProps } from './propsType';
import {
  convertHtmlText,
  escapeHtml,
  getInsertLinkPatternWithAppendSpaceEnd,
  // getInsertPattern,
  getInsertPatternWithAppendSpaceEnd,
  handleResetInputPersonalizationViewMode,
  handleUpdateInputPersonalization,
  handleUpdateInputPersonalizationViewMode,
  regexAttrFormat,
  STATUS_CREATE,
  STATUS_UPDATE,
} from '../utils';
import {
  removeHtmlTag,
  pasteHtmlAtCaret,
  createElement,
  detectContenEditTracking,
  spacingNonTracking,
  validateHTMLTags,
  convertBreakTag,
  convertDivToBreakTag,
  getAddDynamicContentOptions,
  ADD_DYNAMIC_OPTION,
  genShortLinkValue,
  mapDataCustomFucntion,
  ExcludeCustom,
  mapDataFormatAttribute,
  ExcludeFormatAttr,
} from './utils';
import { addMessageToQueue } from '../../../../utils/web/queue';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import {
  DATA_ATTR_WITH_TYPE,
  getInitialSelected,
  toEntryAPIList,
} from '../components/PopupPersonalization/utils';
import { detectContentEdit } from '../utils.extends';
import { safeParse } from '../../../../utils/common';
// import PopupNonTrackingLink from '../components/PopupNonTrackingLink/ModalV2';
// import PopupShortLink from '../components/PopupShortLink/ModalV2';
import { MenuItemStyled } from '../../UIDropdownAction/styles';
import PopupShortLink from '../components/PopupShortLinkV2';
// Hooks
const PATH = 'app/components/common/UIEditorPersonalization/Input/index.jsx';

const PersonalizationInput = props => {
  const {
    initData = '',
    isViewMode,
    isChannelEmail,
    isChannelLine = false,
    isUsePairKeyValue = false,
    errors,
    enableShortLink,
    hiddenDynamicOption = [],
    otherData = {},
    canMultipleLine = false,
    isSimplifyUI = false,
    isLoadingPersonalizations,
    customFnPersonalization = { list: [], map: {} },
    shouldEscapeHtml,
  } = props;

  const [textValue, setTextValue] = useState(
    convertHtmlText({
      htmlText: initData,
      format: shouldEscapeHtml ? 'raw' : 'html',
      state: props.dataState,
      objectWidgetInput: {},
      type: 'input',
      init: '',
      customFunction:
        otherData &&
        otherData.variantExtraData &&
        otherData.variantExtraData.customFunction,
    }),
  );
  const [initSelectedProperties, setInitSelectedProperties] = useState({});
  // const { channelId } = useParams();
  const [stateCustomPersonal, setStateCustomPersonal] = useImmer({
    map: {},
    templateCustom: {
      list: [],
      map: {},
    },
  });

  const [stateFormatAttr, setstateFormatAttr] = useImmer({
    formatAttributes: {},
  });
  useDeepCompareEffect(() => {
    if (
      otherData &&
      otherData.variantExtraData &&
      (otherData.variantExtraData.formatAttributes &&
        Object.keys(otherData.variantExtraData.formatAttributes).length > 0)
    ) {
      setstateFormatAttr(draft => {
        draft.formatAttributes = otherData.variantExtraData.formatAttributes;
      });
    }
  }, [
    otherData &&
      otherData.variantExtraData &&
      otherData.variantExtraData.formatAttributes,
  ]);
  useDeepCompareEffect(() => {
    if (
      otherData &&
      otherData.variantExtraData &&
      otherData.variantExtraData.customFunction &&
      Object.keys(otherData.variantExtraData.customFunction).length > 0
    ) {
      const objTmp = mapDataCustomFucntion(
        otherData.variantExtraData.customFunction,
      );
      setStateCustomPersonal(draft => {
        draft.map = { ...objTmp };
      });
    }
  }, [
    otherData &&
      otherData.variantExtraData &&
      otherData.variantExtraData.customFunction,
  ]);
  useEffect(() => {
    try {
      const newTextValue = convertHtmlText({
        htmlText: isViewMode ? props.value : initData,
        format: shouldEscapeHtml ? 'raw' : 'html',
        state: props.dataState,
        objectWidgetInput: {},
        type: 'input',
        init: '',
        customFunction:
          otherData &&
          otherData.variantExtraData &&
          otherData.variantExtraData.customFunction,
        isInit: true,
      });
      if (newTextValue) {
        setTextValue(
          newTextValue.endsWith('&nbsp;')
            ? newTextValue
            : `${newTextValue}&nbsp;`,
        );
      } else {
        setTextValue(newTextValue);
      }
    } catch (e) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: e.stack,
      });
    }
  }, [
    props.initData,
    props.componentKey,
    isViewMode,
    props.catalogCode,
    props.dataState,
  ]);

  useDeepCompareEffect(() => {
    setStateCustomPersonal(draft => {
      draft.templateCustom = customFnPersonalization;
    });
  }, [customFnPersonalization]);

  // useEffect(() => {
  //   try {
  //     JourneyServices.personalization
  //       .getListCustom()
  //       .then(res => {
  //         if (res.code === 200 && !_isEmpty(res.data)) {
  //           const dataTmp = toEntryAPIList(res.data);
  //           setStateCustomPersonal(draft => {
  //             draft.templateCustom.list = dataTmp.list;
  //             draft.templateCustom.map = dataTmp.map;
  //           });
  //         }
  //       })
  //       .catch(err => {
  //         if (!err.isCanceled) {
  //           addMessageToQueue({
  //             path: 'appcomponentscommonUIEditorPersonalizationInputindex.jsx',
  //             func: 'createTemplatePersonalization',
  //             data: err.stack,
  //           });
  //           console.warn('err', err);
  //         }
  //       });
  //   } catch (e) {
  //     addMessageToQueue({
  //       path: PATH,
  //       func: 'useEffect',
  //       data: e.stack,
  //     });
  //   }
  // }, [stateCustomPersonal.map]);
  const [state, setState] = useImmer({
    isDisabledButtonPersonalization: false,
  });

  const [design, setDesign] = useState(STATUS_CREATE);
  const [popoverOpenShortLink, setPopoverOpenShortLink] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [popoverOpenTracking, setPopoverTrackingOpen] = useState(false);
  const personalizationInputRef = useRef(null);
  const currentTagActive = useRef('');
  const caretPositionRef = useRef(null);
  const [anchorElURL, setAnchorElURL] = useState(null);

  const addDynamicOptions = useMemo(
    () =>
      getAddDynamicContentOptions({
        enableShortLink,
      }).filter(option => !hiddenDynamicOption.includes(option.value)),
    [enableShortLink],
  );
  const listPersonalizeType = useMemo(() => {
    if (_.has(props.dataState, 'personalizationType')) {
      return _.get(props.dataState, 'personalizationType.list', []);
    }

    return null;
  }, [props.dataState && props.dataState.personalizationType]);
  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  const handleFocusInput = () => {
    try {
      if (personalizationInputRef.current instanceof HTMLElement) {
        const childNodes = _.get(
          personalizationInputRef.current,
          'childNodes',
          [],
        );
        const childNodesText = _.filter(
          childNodes,
          child => child.nodeName === '#text',
        );
        const lastChild = _.last(childNodesText);
        const selection = window.getSelection();

        if (
          lastChild instanceof Text &&
          lastChild.length &&
          selection.anchorOffset === 0 &&
          selection.focusOffset === 0
        ) {
          const range = new Range();
          range.setStart(lastChild, lastChild.length);
          range.collapse();
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleFocusInput',
        data: {
          err: err.stack,
        },
      });
    }
  };

  useLayoutEffect(() => {
    // get button Els [x] to create hover and style it
    const iconRemoveEls = document.querySelectorAll('.editor-icon-remove');
    const listIconRemove = iconRemoveEls && Array.from(iconRemoveEls);
    // get button elements
    const buttonElement = document.querySelectorAll(
      '.insert-word:not(.insert-emoji)',
    );
    const listBtnElement = buttonElement && Array.from(buttonElement);

    handleUpdateInputPersonalization(listIconRemove, listBtnElement);
  }, [
    textValue,
    props.initData,
    props.componentKey,
    getObjectPropSafely(() => personalizationInputRef.current.innerHTML),
  ]);

  useEffect(() => {
    const iconRemoveEls = document.querySelectorAll('.editor-icon-remove');
    const listIconRemove = iconRemoveEls && Array.from(iconRemoveEls);
    // get specific element btn .insert-word for styling
    const buttonElement = document.querySelectorAll(
      '.insert-word:not(.insert-emoji)',
    );
    const listBtnElement = buttonElement && Array.from(buttonElement);
    if (isViewMode) {
      handleUpdateInputPersonalizationViewMode(
        listIconRemove,
        listBtnElement,
        isUsePairKeyValue,
        'input',
      );
    } else {
      handleResetInputPersonalizationViewMode(
        listIconRemove,
        listBtnElement,
        'input',
      );
    }
  }, [
    textValue,
    props.initData,
    props.componentKey,
    isViewMode,
    getObjectPropSafely(() => personalizationInputRef.current.innerHTML),
  ]);
  useLayoutEffect(() => {
    try {
      // save caret position
      if (!personalizationInputRef.current) {
        return;
      }
      if (props.name === 'email') {
        const newEmail = convertHtmlText({
          htmlText: personalizationInputRef.current.innerHTML,
          format: 'raw',
          state: props.dataState,
          objectWidgetInput: {},
          type: 'input',
        });
        const textConverted = convertBreakTag(newEmail);
        props.onChange(removeHtmlTag(textConverted));
      }

      // Perhaps React (whose VDOM gets outdated because we often prevent
      // rerendering) did not update the DOM. So we update it manually now.
      // if (props.value !== personalizationInputRef.current.innerHTML) {
      //     personalizationInputRef.current.innerHTML = props.value;
      // }
      // replaceCaret(personalizationInputRef.current);
      if (caretPositionRef && caretPositionRef.current) {
        CaretPositioning.restoreSelection(
          personalizationInputRef.current,
          caretPositionRef.current,
        );
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useLayoutEffect',
        data: error.stack,
      });
      if (typeof props.onError === 'function') {
        props.onError(error, {
          component: PATH,
          action: 'useLayoutEffect saved caret position',
          args: {},
        });
      }
    }
  }, [
    textValue,
    // getObjectPropSafely(() => caretPositionRef.current),
    // selectedProperties,
  ]);

  useEffect(() => {
    if (typeof props.setCaretPosition === 'function') {
      props.setCaretPosition({
        CaretPositioning,
        personalizationInputRef,
        caretPositionRef,
        dataState: props.dataState,
      });
    }
  }, []);

  useEffect(() => {
    if (enableShortLink) {
      onTriggerChange();
    }
    // handle display icon remove on tag personaliztion input when edit mode
    if (!isViewMode) {
      const iconRemoveEls = document.querySelectorAll('.editor-icon-remove');
      const listIconRemove = iconRemoveEls && Array.from(iconRemoveEls);

      if (listIconRemove.length) {
        listIconRemove.forEach(removeIcon => {
          removeIcon.style.display = 'unset';
        });
      }
    }
  }, [isViewMode]);

  useEffect(() => {
    try {
      if (personalizationInputRef.current) {
        personalizationInputRef.current.addEventListener('click', event => {
          caretPositionRef.current = CaretPositioning.saveSelection(
            event.currentTarget,
          );
          // console.log(
          //   'event.target.nodeName',
          //   event.target.nodeName,
          //   event.target.className,
          // );
          // event.preventDefault();
          if (
            event.target.nodeName === 'EM' &&
            event.target.className === 'editor-icon-remove'
          ) {
            // console.log({ personalizationInputRef });
            event.target.parentElement.remove();
            setTextValue(
              personalizationInputRef &&
                personalizationInputRef.current.innerHTML,
            );
          } else if (
            (event.target.className.includes('insert-word') &&
              event.target.id) ||
            event.target.className.includes('insert-text')
          ) {
            editText(event);
          }
        });
      }
    } catch (error) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'useEffectLast',
        data: error.stack,
      });
      if (typeof props.onError === 'function') {
        props.onError(error, {
          component: PATH,
          action: '',
          args: {},
        });
      }
    }
  }, [
    getObjectPropSafely(() => personalizationInputRef.current),
    stateCustomPersonal.map,
    stateFormatAttr.formatAttributes,
    listPersonalizeType,
  ]);

  useLayoutEffect(() => {
    try {
      if (
        getObjectPropSafely(() =>
          convertHtmlText({
            htmlText: personalizationInputRef.current.innerHTML,
            format: 'raw',
            state: props.dataState,
            objectWidgetInput: {},
            type: 'input',
          }),
        ) !==
        getObjectPropSafely(() =>
          convertHtmlText({
            htmlText: textValue,
            format: 'raw',
            state: props.dataState,
            objectWidgetInput: {},
            type: 'input',
          }),
        )
      ) {
        setTextValue(personalizationInputRef.current.innerHTML);
      }
    } catch (error) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'useLayoutEffect',
        data: error.stack,
      });
      if (typeof props.onError === 'function') {
        props.onError(error, {
          component: PATH,
          action: 'useLayoutEffect check innerHTML',
          args: {},
        });
      }
    }
  }, [getObjectPropSafely(() => personalizationInputRef.current.innerHTML)]);

  const onTriggerChange = (event = null, dataAttrFormat) => {
    try {
      let tmp = personalizationInputRef.current.innerHTML;
      if (event !== null) {
        caretPositionRef.current = CaretPositioning.saveSelection(
          event.currentTarget,
        );
        tmp = event.currentTarget.innerHTML;
      }
      if (enableShortLink) {
        tmp = tmp
          .replace(
            // replace old non tracking url to detect url
            /<button(?=[^>]*class="insert-word")[^>]*value=['"]#(?:{nontrackurl\()?(https?:\/\/[0-9a-zA-Z-{}|"\./?_=&%:#*@;]+)(?:\)})?['"][^>]*>.*?<\/button>/g,
            match => {
              // Check if the matched button element also has the "insert-emoji" class
              if (match.includes('class="insert-emoji"')) {
                return match; // Do not replace if it has both classes
              }

              const url = match.match(
                /(https?:\/\/[0-9a-zA-Z-\./?_=&%:#*@;]+)/,
              );
              if (url) {
                return `<span id="${random(5)}" class="detect-url">${
                  url[1]
                }</span>`;
              }

              return match; // If no match found, return the original string
            },
          )
          .replace(
            // replace url to detect url
            /^(?!.*#{shortlink_static\(|.*class="detect-url">|.*<span style="line-height: 20px;margin-right: 8px;pointer-events:none;">).*\bhttps*:\/\/[0-9a-zA-Z-{}|"./?_=&%:#*@;]+/g,
            match =>
              match.replace(
                /\bhttps*:\/\/[0-9a-zA-Z-{}|"./?_=&%:#*@;]+/g,
                match1 => {
                  // Check if the matched URL contains ?detect-emoji=line
                  if (match1.includes('?detect-emoji=line-message')) {
                    return match1; // Do not replace URLs with this parameter
                  }

                  return `<span id="${random(
                    5,
                  )}" class="detect-url">${match1}</span>`;
                },
              ),
          );
      }

      tmp = convertDivToBreakTag(tmp);
      setTextValue(tmp);
      const text = convertHtmlText({
        htmlText: tmp,
        format: 'raw',
        state: props.dataState,
        objectWidgetInput: {},
        type: 'input',
        init: 'add',
      });
      const textConverted = convertBreakTag(text);
      // const found = removeHtmlTag(textConverted)
      //   .replace(/#|{|}|/g, '')
      //   .split('.');
      // console.log(found);
      const string = removeHtmlTag(textConverted);
      props.onChange(removeHtmlTag(textConverted));

      if (typeof props.onChangeOthers === 'function') {
        props.onChangeOthers('otherData', {
          customFunction: ExcludeCustom(stateCustomPersonal, string),
          formatAttributes: ExcludeFormatAttr(
            dataAttrFormat || stateFormatAttr,
            string,
          ),
          // formatAttributes:
        });
      }
    } catch (error) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'onTriggerChange',
        data: error.stack,
      });
      console.error(error);
    }
  };
  const handleShowPopupConvert = listElement => {
    listElement.forEach(element => {
      if (!element.listener) {
        element.addEventListener('mouseover', e => {
          if (e.currentTarget === element) {
            setAnchorElURL(e.currentTarget);
          }
        });
        element.addEventListener('click', e => {
          if (e.currentTarget === element) {
            handleOpenShortLinkModal(
              e.currentTarget.textContent,
              e.currentTarget,
            );
          }
        });
        element.addEventListener('mouseleave', e => {
          setAnchorElURL(null);
        });
        element.listener = true;
      }
    });
  };

  useEffect(() => {
    if (enableShortLink && !isViewMode) {
      const elements = document.querySelectorAll('.detect-url');
      const listElement = Array.from(elements);
      if (listElement.length) {
        setAnchorElURL(null);
        handleShowPopupConvert(listElement);
      }
    }
  }, [textValue, isViewMode]);

  const handleOnCloseConvert = () => {
    setAnchorElURL(null);
  };

  const handleOpenShortLinkModal = (url = '', elementDetectedUrl = '') => {
    toggleShortLinkModal();
    handleOnCloseConvert();
    setInitSelectedProperties(prev => ({
      ...prev,
      link: url,
      elementDetectedUrl,
    }));
  };

  const callback = (type, data) => {
    switch (type) {
      case 'REPLACE_DETECTED_URL':
        const innerHTML = getObjectPropSafely(
          () => personalizationInputRef.current.innerHTML,
          null,
        );
        if (innerHTML) {
          // remove span url và focus vào vị trí remove để add tag shortlink vào vị trí này.
          const index = Array.from(
            personalizationInputRef.current.childNodes,
          ).findIndex(element => element.id === data.id);

          const newDataUpdate = innerHTML.replace(data.outerHTML, '');
          personalizationInputRef.current.focus();

          let range = document.createRange(); // Tạo một đối tượng Range
          let sel = window.getSelection(); // Lấy đối tượng Selection hiện tại
          range.setStart(personalizationInputRef.current.childNodes[index], 0); // Đặt điểm bắt đầu của Range
          range.collapse(true); // Thu gọn Range để tập trung vào điểm bắt đầu
          sel.removeAllRanges(); // Xóa tất cả các Range hiện có
          sel.addRange(range); // Thêm Range mới vào Selection

          personalizationInputRef.current.innerHTML = newDataUpdate;
        }
        break;

      default:
        break;
    }
  };

  const handleOnKeyPress = (event, type = '') => {
    // check is on click Enter
    const isOnClickEnter = +event.charCode === 13;
    handleChangeInput(isOnClickEnter);

    if (
      ['content', 'message', 'text', 'description', 'ticketComment'].includes(
        type,
      ) ||
      canMultipleLine
    )
      return;

    if (+event.charCode === 13) {
      event.preventDefault();
    }
  };

  const onPaste = e => {
    // cancel paste
    e.preventDefault();

    // get text representation of clipboard
    let text = (e.originalEvent || e).clipboardData.getData('text/plain');

    if (shouldEscapeHtml) {
      text = escapeHtml(text);
    }

    // insert text manually
    document.execCommand('insertHTML', false, text);
  };

  const handleInsertSortLink = (url, shortlinkType) => {
    try {
      const { id, value: linkValue, label: linkLabel } = genShortLinkValue(
        url,
        shortlinkType,
      );

      setPopoverOpenShortLink(false);

      if (design === STATUS_UPDATE) {
        setDesign(STATUS_CREATE);
      }

      if (personalizationInputRef.current instanceof HTMLElement) {
        if (currentTagActive.current) {
          const btnLinkEl = document.getElementById(currentTagActive.current);
          const linkLabelEl = btnLinkEl.querySelector('span');

          if (btnLinkEl && linkLabelEl) {
            btnLinkEl.setAttribute('value', linkValue);
            linkLabelEl.innerText = linkLabel;
          }

          currentTagActive.current = '';

          onTriggerChange();
          return;
        }

        CaretPositioning.restoreSelection(
          personalizationInputRef.current,
          caretPositionRef.current,
        );

        pasteHtmlAtCaret(
          getInsertLinkPatternWithAppendSpaceEnd(id, linkValue, linkLabel),
          null,
          () => {
            caretPositionRef.current = CaretPositioning.saveSelection(
              personalizationInputRef.current,
            );
            onTriggerChange();
          },
        );
      }

      if (design === STATUS_UPDATE) {
        setDesign(STATUS_CREATE);
      }
    } catch (err) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'handleInsertText',
        data: err.stack,
      });
    }
  };

  const handleInsertText = (
    selectedProperties,
    personalizationCode,
    classNameTag,
    customFunction,
  ) => {
    try {
      const id = random(5);
      let bgColor = '#E8FECA';
      const { personalType } = selectedProperties;
      classNameTag === 'blue' && (bgColor = '#CAE5FE');
      classNameTag === 'orange' && (bgColor = '#FECACA');
      classNameTag === 'disabled' && (bgColor = '#ccc');
      classNameTag === 'sky-blue' && (bgColor = '#CAFEDD');
      classNameTag === 'violet' && (bgColor = '#9238b5');
      // promotionCode colors
      classNameTag === 'violet-pc' && (bgColor = '#D8CAFE');
      classNameTag === 'content-source-tag' && (bgColor = '#FFDD9F');
      personalType.value === 'custom' && (bgColor = '#BBEFBE');
      setPopoverOpen(false);
      if (design === STATUS_UPDATE) {
        setDesign(STATUS_CREATE);
      }

      if (personalizationInputRef.current instanceof HTMLElement) {
        // console.log('step 1');
        if (currentTagActive.current) {
          // for case update
          // console.log('step 2');

          const element = document.getElementById(
            `${currentTagActive.current}`,
          );
          const child = createElement(
            'em',
            {
              class: 'editor-icon-remove',
              style: 'color: white;font-style: normal;',
            },
            ['x'],
          );
          const codeValue = `${getObjectPropSafely(() =>
            personalType.value === 'custom'
              ? customFunction.personalizationName
              : selectedProperties.attribute.label,
          )} `;
          const spanEle = createElement('span', {
            style: 'line-height: 20px;margin-right: 8px;pointer-events:none;',
          });
          spanEle.innerText = codeValue;
          const newElement = createElement(
            'button',
            {
              id,
              class: 'insert-word',
              style: `color: white; font-size: inherit; outline: none;border: 1px solid transparent; border-radius: 1.2rem; max-height: 30px; cursor: pointer; backgroundColor: ${bgColor}`,
              contentEditable: 'false',
              value: `${personalizationCode.replace(/\s/gm, '&nbsp;')}`,
            },
            [spanEle, child],
          );

          if (element && element.parentNode) {
            element.parentNode.replaceChild(newElement, element);
          }
          currentTagActive.current = '';
          onTriggerChange();
          if (personalType.value === 'custom') {
            const found = personalizationCode.replace(/#|{|}|/g, '').split('.');
            setStateCustomPersonal(draft => {
              // draft.list.push(customFunction);
              draft.map[found[1]] = customFunction;
            });
          } else if (
            selectedProperties.displayFormat.dataType &&
            DATA_ATTR_WITH_TYPE.includes(
              selectedProperties.displayFormat.dataType,
            )
          ) {
            const key = personalizationCode.replace(regexAttrFormat, '');
            const dataAttrFormat = mapDataFormatAttribute(
              selectedProperties.displayFormat,
            );
            setstateFormatAttr(draft => {
              draft.formatAttributes[key] = dataAttrFormat;
              onTriggerChange(null, draft);
            });
          }
          return;
        }
        // personalizationInputRef.current.focus();
        CaretPositioning.restoreSelection(
          personalizationInputRef.current,
          caretPositionRef.current,
        );
        pasteHtmlAtCaret(
          getInsertPatternWithAppendSpaceEnd(
            id,
            personalizationCode,
            getObjectPropSafely(() =>
              personalType.value === 'custom'
                ? customFunction.personalizationName
                : selectedProperties.attribute.label,
            ),
            bgColor,
          ),
          null,
          () => {
            caretPositionRef.current = CaretPositioning.saveSelection(
              personalizationInputRef.current,
            );
            onTriggerChange();
          },
        );
      }

      if (design === STATUS_UPDATE) {
        setDesign(STATUS_CREATE);
      }
      if (personalType.value === 'custom') {
        const found = personalizationCode.replace(/#|{|}|/g, '').split('.');
        setStateCustomPersonal(draft => {
          // draft.list.push(customFunction);
          draft.map[found[1]] = customFunction;
        });
      } else if (
        selectedProperties.displayFormat.dataType &&
        DATA_ATTR_WITH_TYPE.includes(selectedProperties.displayFormat.dataType)
      ) {
        const key = personalizationCode.replace(regexAttrFormat, '');
        setstateFormatAttr(draft => {
          draft.formatAttributes[key] = mapDataFormatAttribute(
            selectedProperties.displayFormat,
          );
        });
      }
      // if(personalType.value === 'custom'){

      // }
    } catch (error) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'handleInsertText',
        data: error.stack,
      });
      console.log(error);
    }
  };
  const toggleShortLinkModal = () => {
    setPopoverOpenShortLink(!popoverOpenShortLink);

    handleAfterToggleModal();
  };

  const togglePersonalModal = () => {
    setPopoverOpen(!popoverOpen);

    handleAfterToggleModal();
  };

  const toggleTrackingModal = () => {
    setPopoverTrackingOpen(!popoverOpenTracking);

    handleAfterToggleModal();
  };

  const handleAfterToggleModal = () => {
    if (design === STATUS_UPDATE) {
      setDesign(STATUS_CREATE);
    }
    setInitSelectedProperties(getInitialSelected(props.dataState));
    currentTagActive.current = '';
    CaretPositioning.restoreSelection(
      personalizationInputRef.current,
      caretPositionRef.current,
    );
    personalizationInputRef.current.focus();
  };
  const editText = event => {
    try {
      setDesign(STATUS_UPDATE);
      if (event.target.id) {
        const { value } = event.target;
        const found = value.replace(/#|{|}|/g, '').split('.');
        const foundTracking = value.replace(/#|{|}|/g, '').split('(');
        const shortLink = value.replace(/#|{|}|/g, '').split('(');
        if (found[0] === 'custom') {
          setInitSelectedProperties({
            personalType: {
              value: 'custom',
              label: 'Custom',
            },
            customFunction: stateCustomPersonal.map[found[1]],
          });
        } else {
          let personalType;
          let attributeType;
          let valueString = '';
          let typeItem;
          let contentSourceGroupId = '';

          let personalTypeCode;
          let attributeTypeCode;
          const detectContent = safeParse(
            detectContentEdit(
              value,
              props.dataState.personalizationType,
              props.dataState.personalizationData,
              props.dataState.promotionCodeAttr,
              stateFormatAttr,
            ),
            {},
          );
          if (foundTracking[0] === 'nontrackurl') {
            const content = detectContenEditTracking(value);
            setInitSelectedProperties({
              promotionCodeAttr: content.code,
              url: content.url,
            });
            currentTagActive.current = event.target.id;
            setPopoverTrackingOpen(true);
          }
          if (
            shortLink[0] === 'shortlink' ||
            shortLink[0] === 'shortlink_static'
          ) {
            const link = shortLink[1].substring(0, shortLink[1].length - 1);
            setInitSelectedProperties({ link, keyTag: shortLink[0] });
            currentTagActive.current = event.target.id;
            setPopoverOpenShortLink(true);
          }
          if (detectContent.personalType && detectContent.attributeType) {
            setInitSelectedProperties({
              personalType: detectContent.personalType,
              attribute: detectContent.attributeType,
              value: detectContent.valueString,
              promotionCodeAttr: detectContent.attributePromotionCode,
              displayFormat: detectContent.displayFormat,
              contentSources: props.dataState.contentSources || [],
              csIndexAttribute: detectContent.csIndexAttribute,
            });
            // case cũ, show personalization attribute
            // console.log({ fake, personalType, attributeType });
            // setPopoverOpen(true);
          }
          if (found && found.length) {
            [personalTypeCode] = found;

            const hasAppendBOContentSource = found[0] === 'groups';
            if (hasAppendBOContentSource) {
              contentSourceGroupId = (found[1] || '').replace(/\[.*?\]/g, '');
            }

            for (const index in found) {
              if (+index === 0) {
                let predicate = found[0];

                if (hasAppendBOContentSource) {
                  predicate = contentSourceGroupId;
                }

                typeItem = props.dataState.personalizationType.list.find(
                  item => item.value === predicate,
                );

                if (typeItem) {
                  personalType = {
                    value: typeItem.value,
                    label: typeItem.label,
                  };
                } else {
                  return;
                }
              }
              if (
                +index === 1 &&
                typeItem.value &&
                props.dataState.personalizationData &&
                props.dataState.personalizationData.hasOwnProperty(
                  typeItem.value,
                )
              ) {
                const groupAttribute =
                  props.dataState.personalizationData[typeItem.value];

                let attribute = '';
                if (found.length === 2) {
                  attribute =
                    groupAttribute &&
                    groupAttribute.map[found[1].split('||')[0]];
                } else if (found.length === 3) {
                  const splitString = found[2].split('||');
                  let propertyCode = `${found[1]}.${splitString[0]}`;

                  if (hasAppendBOContentSource && groupAttribute) {
                    propertyCode = splitString[0];
                  }

                  attribute =
                    groupAttribute && groupAttribute.map[propertyCode];
                }
                attribute && (attributeType = attribute);
              }

              if (+index === 1) {
                let tempValue = '';

                if (found.length === 2) {
                  tempValue = found[1].split('||')[1];
                  attributeTypeCode = found[1].split('||')[0];
                } else if (found.length === 3) {
                  const splitString = found[2].split('||');
                  tempValue = splitString[1];
                  attributeTypeCode = `${found[1]}.${splitString[0]}`;

                  if (hasAppendBOContentSource) {
                    attributeTypeCode = splitString[0];
                  }
                }

                valueString =
                  (tempValue &&
                    tempValue.replace(/"/g, '').replace(/&nbsp;/gm, ' ')) ||
                  '';
              }
            }
          }
        }

        currentTagActive.current = event.target.id;
        setPopoverOpen(true);
        setDesign(STATUS_UPDATE);
      }
    } catch (error) {
      addMessageToQueue({
        path: 'app/containers/UIDev/UIEditorPersonalization/Input/index.jsx',
        func: 'editText',
        data: error.stack,
      });
      if (typeof props.onError === 'function') {
        props.onError(error, {
          component: PATH,
          action: '',
          args: {},
        });
      }
    }
  };

  const checkPersonalizationTag =
    textValue &&
    textValue.match(
      /<button\b(?=[^>]*\bclass="insert-word\b(?!.*\bclass="insert-emoji\b"))[^>]*>/gim,
    );

  if (checkPersonalizationTag && Array.isArray(checkPersonalizationTag)) {
    if (checkPersonalizationTag.length >= 15) {
      if (state.isDisabledButtonPersonalization === false) {
        setStateCommon({ isDisabledButtonPersonalization: true });
      }
    } else if (state.isDisabledButtonPersonalization) {
      setStateCommon({ isDisabledButtonPersonalization: false });
    }
  } else if (
    checkPersonalizationTag === null &&
    state.isDisabledButtonPersonalization
  ) {
    setStateCommon({ isDisabledButtonPersonalization: false });
  }

  // console.log('personalizationInputRef', personalizationInputRef);
  const onClickAddDynamicOption = option => {
    handleClose();

    if (state.isDisabledButtonPersonalization) return;

    // if (option.value === ADD_DYNAMIC_OPTION.non.value) {
    //   toggleTrackingModal();
    // }

    if (option.value === ADD_DYNAMIC_OPTION.addPer.value) {
      togglePersonalModal();
    }

    if (option.value === ADD_DYNAMIC_OPTION.shortLink.value) {
      toggleShortLinkModal();
    }
  };
  const [anchorEl, setAnchorEl] = React.useState(null);

  const onOpenPopoverAddDynamicContent = event => {
    CaretPositioning.restoreSelection(
      personalizationInputRef.current,
      caretPositionRef.current,
    );
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const renderAddDynamiOptions = list =>
    list.map(option => (
      <MenuItemStyled
        key={`${option.value}`}
        onClick={() => onClickAddDynamicOption(option)}
        disabled={option.disabled}
      >
        <div className="m-right-2" />
        {getTranslateMessage(option.labelTranslateCode, option.label) || '--'}
      </MenuItemStyled>
    ));

  const getDangerouslySetInnerHTML = text => {
    // const tags = text.match(/<[^<>]+>/g);
    if (text) {
      let regex = /<[^<>]+>/g;
      let tag;
      while ((tag = regex.exec(text))) {
        // console.log({ tag });
        if (!validateHTMLTags.isValidTag(tag[0])) {
          const finalTag = tag[0].replace('<', '&#60').replace('>', '&#62');
          text = text.replace(tag[0], finalTag);
        }
      }
      // if (tags && tags.length) {
      //   for (const tag of tags) {
      //     if (validateHTMLTags.isValidTag(tag)) {
      //       return tag;
      //     }
      //     return tag.replace('<', 'abc').replace('>', 'abc');
      //   }
      // }

      text = text.replaceAll('\n', '<br>');

      if (spacingNonTracking(text)) {
        return text;
      }
      return safeParse(text, '').concat('<br />');
    }

    return '';
  };

  const renderPersonalIcon = () => (
    <>
      {isViewMode || props.isForceHideBtnPersonalization ? null : (
        <>
          {addDynamicOptions.length === 1 ? (
            <SpanIcon
              data-test="personal-icon"
              className={`icon-xlab-person-alt ${
                isUsePairKeyValue
                  ? 'customize-antsomi-profile-outter'
                  : 'customize-antsomi-profile'
              } ${isSimplifyUI ? 'icon-customize-position' : ''}`}
              aria-describedby={id}
              variant="contained"
              // onClick={handleClick}
              onClick={
                state.isDisabledButtonPersonalization
                  ? () => {}
                  : () => onClickAddDynamicOption(addDynamicOptions[0])
              }
              // id={'antsomi-popover-input'}
              isDisabled={
                state.isDisabledButtonPersonalization ||
                isLoadingPersonalizations
              }
              title={
                state.isDisabledButtonPersonalization
                  ? getTranslateMessage(
                      TRANSLATE_KEY._NOTI_MAXIMUM_PERSONALIZATION_TAG,
                      'Can only add up to 15 personalization tag',
                    )
                  : ''
              }
            />
          ) : (
            <SpanIcon
              className={`icon-xlab-person-alt ${
                isUsePairKeyValue
                  ? 'customize-antsomi-profile-outter'
                  : 'customize-antsomi-profile'
              } ${
                props.isBlastCampaign && !props.isCenterBlast
                  ? 'blast-person-icon'
                  : ''
              } ${isSimplifyUI ? 'icon-customize-position' : ''}`}
              aria-describedby={id}
              variant="contained"
              onClick={onOpenPopoverAddDynamicContent}
              // onClick={
              //   state.isDisabledButtonPersonalization
              //     ? () => {}
              //     : togglePersonalModal
              // }
              // id={'antsomi-popover-input'}
              isDisabled={
                state.isDisabledButtonPersonalization ||
                isLoadingPersonalizations
              }
              title={
                state.isDisabledButtonPersonalization
                  ? getTranslateMessage(
                      TRANSLATE_KEY._NOTI_MAXIMUM_PERSONALIZATION_TAG,
                      'Can only add up to 15 personalization tag',
                    )
                  : ''
              }
            />
          )}
        </>
      )}
    </>
  );

  const timerRef = useRef(null);

  const handleChangeInput = innerHTML => {
    if (enableShortLink || props.isSmartInbox) {
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        caretPositionRef.current = CaretPositioning.saveSelection(
          personalizationInputRef.current,
        );
        onTriggerChange();
      }, 2000);
    }
  };

  const handleOnInput = element => {
    if (element) {
      const content = element.textContent;
      // console.log(element);
      if (!content) {
        element.innerHTML = '';
      }
    }
  };

  const handleClick = () => {
    personalizationInputRef.current.focus();

    // const endOfLine = getObjectPropSafely(() => {
    //   const text = personalizationInputRef.current.innerText;

    //   if (text.endsWith(' \n')) {
    //     return personalizationInputRef.current.innerText.length - 2;
    //   }

    //   return personalizationInputRef.current.innerText.length - 1;
    // }, 0);

    // CaretPositioning.restoreSelection(personalizationInputRef.current, {
    //   start: endOfLine,
    //   end: endOfLine,
    // });
  };

  return (
    <ErrorBoundary path="app/containers/UIDev/UIEditorPersonalization/Input/index.jsx">
      <WrapperInputStyle>
        <div
          style={{
            position: 'relative',
            width: props.width ? props.width : '100%',
          }}
        >
          <div
            data-test="personalization-input-wrapper"
            className={classnames({
              'styles-input-wrapper': !isViewMode,
              'input-email-wrapper': isChannelEmail,
              'input-wrapper-outter': !isChannelEmail && isUsePairKeyValue,
              'input-wrapper': !isChannelEmail && !isUsePairKeyValue,
              'input-error': errors.length > 0,
              'input-has-emoji': props.hasEmoji,
              'input-height-sm': props.isInputHeightSM,
              'input-full-width': isSimplifyUI,
              'padding-right-28': isSimplifyUI,
            })}
            // className={`${isViewMode ? '' : 'styles-input-wrapper'} ${
            //   props.isChannelEmail
            //     ? 'input-email-wrapper'
            //     : isUsePairKeyValue
            //     ? 'input-wrapper-outter'
            //     : 'input-wrapper'
            // }`}
            tabIndex="1"
            style={{
              ...(isViewMode && {
                padding: 0,
                minHeight: 0,
                fontSize: 12,
                border: 'none',
              }),
              ...(props.styledComponentBlast || {}),
            }}
            onClick={handleClick}
          >
            <div
              id={props.inputPersonalId}
              className="input-personal"
              style={{
                display: textValue && textValue !== '<br>' && 'inline-block',
              }}
              contentEditable={isViewMode ? 'false' : 'true'}
              onKeyPress={event => handleOnKeyPress(event, props.name)}
              onSelect={event => handleOnInput(event.target)}
              onBlur={event => onTriggerChange(event)}
              onFocus={handleFocusInput}
              // onChange={event => handleOnInput(event)}
              ref={personalizationInputRef}
              // spellCheck={false}
              dangerouslySetInnerHTML={{
                __html: getDangerouslySetInnerHTML(textValue),
              }}
              data-placeholder={props.placeHolder || ''}
              // innerHTML={{ __html: textValue }}
              onPaste={onPaste}
            />
            {/* {isUsePairKeyValue || isBroadCast ? null : renderPersonalIcon()} */}
            <StylePopover
              id={id}
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
            >
              {renderAddDynamiOptions(addDynamicOptions)}
            </StylePopover>
            {/* <UIDropdownAction
            isShowAddIcon={false}
            PopoverAnchor={PopoverAnchor}
            options={Object.values(MAP_SMS_TARGET)}
            callback={onClick}
          /> */}
          </div>
          {renderPersonalIcon()}
        </div>
        {/* {isUsePairKeyValue ? renderPersonalIcon() : null} */}
        {popoverOpenShortLink && (
          <PopupShortLink
            open={popoverOpenShortLink}
            onOk={handleInsertSortLink}
            onClose={toggleShortLinkModal}
            initSelectedProperties={initSelectedProperties}
            callback={callback}
          />
        )}

        <PopupPersonalization
          popoverOpen={popoverOpen}
          togglePopover={togglePersonalModal}
          handleInsertText={handleInsertText}
          design={design}
          dataState={props.dataState}
          initSelectedProperties={initSelectedProperties}
          listTemplateCustom={stateCustomPersonal.templateCustom}
        />

        {enableShortLink && (
          <Popover
            id={anchorElURL && anchorElURL.id}
            open={Boolean(anchorElURL)}
            anchorEl={anchorElURL}
            onClose={handleOnCloseConvert}
            style={{
              pointerEvents: 'none',
            }}
            anchorOrigin={{
              vertical: -30,
              horizontal: 45,
            }}
            PaperProps={{
              style: {
                backgroundColor: '#0000009e',
                padding: '5px 10px',
              },
            }}
          >
            <span style={{ color: '#fff', fontSize: '12px' }}>
              {getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SHORTLINK_CONVERT,
                'Click on the link to convert it to a shortlink',
              )}
            </span>
          </Popover>
        )}
        {/* <PopupNonTrackingLink */}
        {/*   popoverOpen={popoverOpenTracking} */}
        {/*   togglePopover={toggleTrackingModal} */}
        {/*   handleInsertText={handleInsertTextTracking} */}
        {/*   design={design} */}
        {/*   dataState={props.dataState} */}
        {/*   initSelectedProperties={initSelectedProperties} */}
        {/* /> */}
      </WrapperInputStyle>
    </ErrorBoundary>
  );
};

PersonalizationInput.defaultProps = defaultProps;

export default PersonalizationInput;

/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React from 'react';
import { connect } from 'react-redux';
import ServiceNotify from 'services/Notify';
import Bowser from 'bowser';

// Components
import UIIconXlabColor from 'components/common/UIIconXlabColor';
import { UIButton } from '@xlab-team/ui-components';
import { Button, Icon } from '@antscorp/antsomi-ui';
import SupportButton from './SupportButton';

// Styles
import { ErrorBoundaryWrapper } from './styles';

// Constants
import { BROWSER_NAME, getFeature } from './constants';

// Assets
import chromeLogo from 'assets/images/browser/chrome.png';
import firefoxLogo from 'assets/images/browser/firefox.png';
import edgeLogo from 'assets/images/browser/edge.png';
import safariLogo from 'assets/images/browser/safari.png';

// Utils
import {
  makeSelectUser,
  makeSelectPortal,
} from '../../../modules/Dashboard/selector';
import { addMessageToQueue } from '../../../utils/web/queue';
import {
  getToken,
  getCurrentOwnerId,
  getWindowParam,
} from '../../../utils/web/cookie';
import { getObjectPropSafely } from '@antscorp/antsomi-ui/es/utils';

const CACHE_VERSION_KEY = '_browser_version';
const ImageLogo = ({ src }) => <img src={src} alt="logo" />;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);

    const browser = Bowser.parse(window.navigator.userAgent);

    this.cachedVersion = localStorage.getItem(CACHE_VERSION_KEY);

    if (!this.cachedVersion) {
      localStorage.setItem(CACHE_VERSION_KEY, browser.browser.version);
    }

    this.state = {
      // latestVersion: '',
      hasError: !!props.debug,
      browser,
    };
  }

  logErrorToMyService = error => {
    const { portal, user, path = '...' } = this.props;

    console.log('ErrorBoundary', { errorBoundaryProps: this.props }, { error });

    try {
      if (portal === null || user === null) {
        ServiceNotify.create(
          'ErrorBoundary',
          {
            portal: '',
            user: '',
            location: window.location.href,
            token: getToken(),
            userAgent: window.navigator.userAgent,
          },
          { path, mes: error.stack },
        ).then(result => {});
      } else {
        ServiceNotify.create(
          'ErrorBoundary',
          {
            portal: `${portal.portalName} - ${portal.portalId}`,
            user: `${user.email} - ${user.userId}`,
            path,
            location: window.location.href,
            token: getToken(),
            userAgent: window.navigator.userAgent,
          },
          { path, mes: error.stack },
        ).then(result => {});
      }
    } catch (e) {
      addMessageToQueue({
        path: 'app/components/common/ErrorBoundary/index.jsx',
        func: 'logErrorToMyService',
        data: {
          stacktrace: e.stack,
          message: e.message,
        },
      });
    }
  };

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, info) {
    // You can also log the error to an error reporting service
    this.logErrorToMyService(error);
  }

  // async getChromeLatestVersion() {
  //   fetch(
  //     'https://chromiumdash.appspot.com/fetch_releases?channel=Extended&platform=Windows&num=1',
  //   ).then(async res => {
  //     const data = await res.json();
  //     console.log('latest chrome', data);
  //     if (data && data.length && data[0].version) {
  //       this.setState({
  //         latestVersion: data[0].version,
  //       });
  //     }
  //   });
  // }

  // async getFirefoxLatestVersion() {
  //   return 'Firefox 123.0';
  // }

  // async getEdgeLatestVersion() {
  //   fetch('http://edgeupdates.microsoft.com/api/products').then(async x => {
  //     const data = await x.json();

  //     if (data && data.data) {
  //       const stableVersion = data.data.find(item => item.Product === 'Stable')

  //       if (stableVersion && stableVersion.Releases) {
  //         this.setState({
  //           latestVersion: data,
  //         });
  //       }
  //     }
  //   });
  // }

  // async getSafariLatestVersion() {
  //   return 'Safari 16.0';
  // }

  // async fetchLatestVersion() {
  //   let latestVersion = '';

  //   switch (this.state.browser.browser.name) {
  //     case BROWSER_NAME.CHROME:
  //       latestVersion = await this.getChromeLatestVersion();
  //       break;
  //     case BROWSER_NAME.FIREFOX:
  //       latestVersion = await this.getFirefoxLatestVersion();
  //       break;
  //     case BROWSER_NAME.EDGE:
  //       latestVersion = await this.getEdgeLatestVersion();
  //       break;
  //     case BROWSER_NAME.SAFARI:
  //       latestVersion = await this.getSafariLatestVersion();
  //       break;
  //     default:
  //       latestVersion = '';
  //   }

  //   this.setState({
  //     latestVersion,
  //   });
  // }

  renderLogo() {
    switch (this.state.browser.browser.name) {
      case BROWSER_NAME.CHROME:
        return <ImageLogo src={chromeLogo} />;
      case BROWSER_NAME.FIREFOX:
        return <ImageLogo src={firefoxLogo} />;
      case BROWSER_NAME.EDGE:
        return <ImageLogo src={edgeLogo} />;
      case BROWSER_NAME.SAFARI:
        return <ImageLogo src={safariLogo} />;
      default:
        return <ImageLogo src={chromeLogo} />;
    }
  }

  // handleGoUpdate() {
  //   window.location.href = {
  //     [BROWSER_NAME.CHROME]: 'chrome://settings/help',
  //     [BROWSER_NAME.EDGE]: 'edge://settings/help',
  //     [BROWSER_NAME.FIREFOX]: 'about:preferences#general',
  //     [BROWSER_NAME.SAFARI]: 'https://support.apple.com/en-us/108382',
  //   }[this.state.browser.browser.name];
  // }

  handleGoUpdate() {
    const updateUrl = {
      [BROWSER_NAME.CHROME]:
        'https://support.google.com/chrome/answer/95414?hl=en&ref_topic=7437521&sjid=14596928818355436947-AP',
      [BROWSER_NAME.EDGE]:
        'https://support.microsoft.com/en-au/topic/microsoft-edge-update-settings-af8aaca2-1b69-4870-94fe-18822dbb7ef1#:~:text=Update%20once,Download%20and%20install%20to%20proceed.',
      [BROWSER_NAME.FIREFOX]:
        'https://support.mozilla.org/en-US/kb/update-firefox-latest-release',
      [BROWSER_NAME.SAFARI]: 'https://support.apple.com/en-us/108382',
    }[this.state.browser.browser.name];

    window.open(updateUrl);
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI

      if (typeof this.props.errorCallback === 'function') {
        return this.props.errorCallback();
      }

      if (this.props.fallbackUI) {
        return this.props.fallbackUI;
      }

      if (!this.props.useVersionError) {
        return this.props.inline ? (
          <h4
            className="d-flex full-width align-items-start justify-content-center"
            style={{ height: this.props.inline ? 'auto' : '100vh' }}
          >
            {`We're sorry! CDP365 is not available right now. Please try again later.`}
          </h4>
        ) : (
          <ErrorBoundaryWrapper>
            <div className="error-content">
              <Icon
                type="icon-ants-warning-circle-filled"
                size="30"
                color="#F44336"
              />
              <h2 className="error-title near-top">Unexpected Issue</h2>
              <p className="error-description">
                Sorry for the inconvenience. We encountered a problem processing
                <br />
                your request due to a temporary glitch. Please try again.
              </p>
              <Button onClick={() => window.location.reload()}>
                Reload This Page
              </Button>
              <p className="contact-txt">
                Already retried but still having problems?
                <br />
                Contact our support team{' '}
                <SupportButton feature={getFeature()} doCapture />.
              </p>
            </div>
          </ErrorBoundaryWrapper>
        );
      }
      // return
      return (
        <ErrorBoundaryWrapper>
          <div className="error-content">
            <UIIconXlabColor name="warning" fontSize="23px" />
            <h2 className="error-title">
              Browser requirements not met for this feature
            </h2>
            <p className="error-description">
              This feature requires an up-to-date browser. Kindly update your
              browser to access it.
            </p>
            <div className="browser-info">
              <div className="logo">{this.renderLogo()}</div>
              <div className="right-info">
                <span className="browser-name">
                  {this.state.browser.browser.name}
                </span>
                <span className="browser-version">
                  Current version {this.state.browser.browser.version}
                </span>
                {/* {
                  !!this.state.latestVersion && (
                    <span className="browser-version">
                      Latest version {this.state.latestVersion}
                    </span>
                  )
                } */}
              </div>
            </div>
            <UIButton
              theme="outline"
              className="upgrade-btn"
              onClick={this.handleGoUpdate.bind(this)}
            >
              Upgrade Now
            </UIButton>
            <p className="contact-txt">
              Already upgraded but still having problems?
              <br />
              Contact our support team{' '}
              <SupportButton
                feature={getFeature()}
                browserName={this.state.browser.browser.name}
                prevVersion={
                  this.cachedVersion ||
                  getObjectPropSafely(() => this.state.browser.browser.version)
                }
                updatedVersion={getObjectPropSafely(
                  () => this.state.browser.browser.version,
                )}
              />
              .
            </p>
          </div>
        </ErrorBoundaryWrapper>
      );
    }

    return this.props.children;
  }
}

const mapStateToProps = state => {
  const dashboard = state.get('dashboard');
  if (dashboard) {
    return {
      portal: makeSelectPortal(state),
      user: makeSelectUser(state),
    };
  }
  return {
    portal: null,
    user: null,
  };
};
export default connect(
  mapStateToProps,
  null,
)(ErrorBoundary);

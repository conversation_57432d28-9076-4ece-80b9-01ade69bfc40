/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import Grid from '@material-ui/core/Grid';
import { useImmer } from 'use-immer';
import Radio from '@material-ui/core/Radio';
import { FormControlLabel } from '@material-ui/core';
import {
  UIInputTime,
  UIInputCalendar,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import AccessTimeIcon from '@material-ui/icons/AccessTime';

import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';

import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import {
  useSty<PERSON>,
  WrapperChildContent,
  WrapperRadio,
  WrapperShowTime,
} from '../../styled';
import {
  DateOrTimeWrapper,
  DateWrapper,
  TextViewMode,
  TimeWrapper,
} from './styled';
import { safeParse } from '../../../../../utils/common';
import SelectDaysOfWeekCustom from '../../../UISchedulerTrigger/_UI/SelectDaysOfWeekCustom';
import { TIME_UNIT_TYPES } from '../../../../../utils/constants';
import { DisplayTimeZone } from '../../../../../modules/Dashboard/Profile/Segment/Create/styles';
import { getLabelPortalTimeZone } from '../../../../../modules/Dashboard/utils';
import { PortalDate } from '../../../../../utils/date';

const initDataDateOrTime = () => ({
  timeUnit: 'DATE',
  isShowTime: false,
  timeOfDay: new PortalDate().getTime(),
  date: new PortalDate().getTime(),
  daysOfWeek: [],
  errors: [],
});

const DelayDateOrTime = props => {
  const classes = useStyles();
  const { initData, isViewMode } = props;
  const [state, setState] = useImmer(initDataDateOrTime());

  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  // const commonValidate = (key, value) => {
  //   const temp = {
  //     ...state,
  //     [key]: value,
  //   };
  //   const { errors } = commonValidateNodeDelay(temp, 'amount_of_time');
  //   setState(draft => {
  //     draft.errors = errors;
  //   });
  // };

  const handleChange = e => {
    setStateCommon({ timeUnit: e.target.value, isShowTime: false });
  };

  const onChangeDate = data => {
    setStateCommon({ date: data });
  };

  // const onClick = () => {
  //   setStateCommon({ isShowTime: true });
  // };

  // const deleteItem = () => {
  //   setStateCommon({ isShowTime: false });
  // };

  const onChangeDaysOfWeek = data => {
    setStateCommon({ daysOfWeek: data });
  };

  const onChangeTime = data => {
    setStateCommon({ timeOfDay: Date.parse(data) });
  };

  useEffect(() => {
    props.onChange('date_or_time', state);
  }, [state]);

  // useEffect(() => {
  //   if (didMountRef.current === true) {
  //     const { errors } = commonValidateNodeDelay(state, 'date_or_time');
  //     setState(draft => {
  //       draft.errors = errors;
  //     });
  //   } else didMountRef.current = true;
  // }, [props.validateKey]);

  useEffect(() => {
    if (Object.keys(safeParse(initData, {})).length === 0) {
      return;
    } else {
      const tempInitData = { ...initData };

      if (initData.timeUnit === TIME_UNIT_TYPES.DAYS_OF_WEEK) {
        tempInitData.timeUnit = TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM;
      }

      const temp = {
        ...tempInitData,
        isShowTime: initData.isShowTime,
      };
      setState(() => temp);
    }

    return () => {
      setState(() => initDataDateOrTime());
    };
  }, [props.componentId]);

  return (
    <>
      <Grid container item sm={12}>
        <Grid item sm={10}>
          <WrapperDisable disabled={props.disabled}>
            {!isViewMode && (
              <WrapperRadio>
                <FormControlLabel
                  value="DATE"
                  name="DATE"
                  onChange={handleChange}
                  control={
                    <Radio
                      color="primary"
                      size="small"
                      checked={state.timeUnit === 'DATE'}
                    />
                  }
                  label={getTranslateMessage(
                    TRANSLATE_KEY._OPT_SPECIFIC_DATE,
                    'At a specific date',
                  )}
                  labelPlacement="end"
                />
              </WrapperRadio>
            )}
            {isViewMode && state.timeUnit === 'DATE' && (
              <TextViewMode className={classes.wrapperTextViewMode}>
                {getTranslateMessage(
                  TRANSLATE_KEY._OPT_SPECIFIC_DATE,
                  'At a specific date & time',
                )}
              </TextViewMode>
            )}
            {state.timeUnit === 'DATE' && (
              <WrapperChildContent
                className={`${!isViewMode && 'p-left-5'} m-top-2`}
              >
                <div className={`${classes.textSmall} ${classes.colorGray}`}>
                  {getTranslateMessage(
                    TRANSLATE_KEY._INFO_STORY_START_DATE,
                    'Start date',
                  )}
                </div>
                <DateOrTimeWrapper className={classes.wrapperDateOrTime}>
                  <DateWrapper>
                    <UIInputCalendar
                      value={state.date}
                      onChange={onChangeDate}
                      isShowLabel={false}
                      isViewMode={isViewMode}
                      placeholder="MM/DD/YYYY"
                    />
                  </DateWrapper>
                  <span className="p-x-4">at</span>
                  <TimeWrapper>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <UIInputTime
                        isViewMode={isViewMode}
                        keyboardIcon={<AccessTimeIcon />}
                        margin="normal"
                        id="time-picker"
                        value={state.timeOfDay}
                        onChange={onChangeTime}
                        KeyboardButtonProps={{
                          'aria-label': 'change time',
                        }}
                      />
                    </MuiPickersUtilsProvider>
                  </TimeWrapper>
                  <DisplayTimeZone>{getLabelPortalTimeZone()}</DisplayTimeZone>
                </DateOrTimeWrapper>

                {/* {!state.isShowTime && (
                  <div className="m-top-2">
                    <UIButton
                      style={noPadding}
                      theme="text-link"
                      onClick={onClick}
                    >
                      {`+ ${getTranslateMessage(
                        TRANSLATE_KEY._ACT_ADD_DELAY_TIME,
                        'Delay until time of the day',
                      )}`}
                    </UIButton>
                  </div>
                )} */}

                {/* {state.isShowTime && (
                  <WrapperShowTime className="m-top-2">
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <KeyboardTimePicker
                        keyboardIcon={<AccessAlarmIcon />}
                        margin="normal"
                        id="time-picker"
                        //   label="Time picker"
                        value={state.timeOfDay}
                        onChange={onChangeTime}
                        KeyboardButtonProps={{
                          'aria-label': 'change time',
                        }}
                      />
                    </MuiPickersUtilsProvider>
                    <WrapperIcon onClick={deleteItem} className="m-left-1">
                      <Icon>cancel</Icon>
                    </WrapperIcon>
                  </WrapperShowTime>
                )} */}
              </WrapperChildContent>
            )}
          </WrapperDisable>
        </Grid>
      </Grid>

      <Grid container item sm={12}>
        <Grid item sm={3}>
          <WrapperDisable disabled={props.disabled}>
            {!isViewMode && (
              <WrapperRadio>
                <FormControlLabel
                  value={TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM}
                  name={TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM}
                  onChange={handleChange}
                  control={
                    <Radio
                      color="primary"
                      size="small"
                      checked={
                        state.timeUnit === TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM
                      }
                    />
                  }
                  label={getTranslateMessage(
                    TRANSLATE_KEY._STORY_DELAY_WEEK_DAY,
                    'Days of week',
                  )}
                  labelPlacement="end"
                />
              </WrapperRadio>
            )}
            {isViewMode &&
              state.timeUnit === TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM && (
                <TextViewMode className={classes.wrapperTextViewMode}>
                  {getTranslateMessage(
                    TRANSLATE_KEY._STORY_DELAY_WEEK_DAY,
                    'Days of week',
                  )}
                </TextViewMode>
              )}
          </WrapperDisable>
        </Grid>
      </Grid>
      {state.timeUnit === TIME_UNIT_TYPES.DAYS_OF_WEEK_CUSTOM && (
        <>
          <Grid container item sm={12}>
            <WrapperChildContent>
              <WrapperDisable disabled={props.disabled}>
                <WrapperChildContent>
                  <WrapperShowTime
                    className={`${
                      isViewMode ? 'p-left-0' : 'p-left-10'
                    } m-bottom-2`}
                  >
                    {!isViewMode && (
                      <div
                        className={classes.textView}
                        style={{ color: '#595959' }}
                      >
                        {getTranslateMessage(
                          TRANSLATE_KEY._TITL_TRIGGER_TIME,
                          'Trigger time',
                        )}
                      </div>
                    )}
                    {!isViewMode && (
                      <UIInputTime
                        keyboardIcon={<AccessTimeIcon />}
                        margin="normal"
                        id="time-picker"
                        // label="Trigger time"
                        value={state.timeOfDay}
                        onChange={onChangeTime}
                        KeyboardButtonProps={{
                          'aria-label': 'change time',
                        }}
                      />
                    )}
                    <DisplayTimeZone style={{ lineHeight: '32px' }}>
                      {getLabelPortalTimeZone()}
                    </DisplayTimeZone>
                    {/* <WrapperIcon onClick={deleteItem} className="m-left-1">
                      <Icon>cancel</Icon>
                    </WrapperIcon> */}
                    <SelectDaysOfWeekCustom
                      onChange={onChangeDaysOfWeek}
                      initData={state.daysOfWeek}
                      timeOfDay={state.timeOfDay}
                      validateKey={props.validateKey}
                      disabled={props.disabled}
                      isViewMode={props.isViewMode}
                      componentId={props.componentId}
                    />
                  </WrapperShowTime>
                </WrapperChildContent>
                {/* {state.errors.length > 0 && (
                <div className={classes.wrapperError}>{state.errors}</div>
              )} */}
                {/* {!state.isShowTime && (
                  <div className="m-top-2">
                    <UIButton
                      style={noPadding}
                      theme="text-link"
                      onClick={onClick}
                    >
                      {`+ ${getTranslateMessage(
                        TRANSLATE_KEY._ACT_ADD_DELAY_TIME,
                        'Delay until time of the day',
                      )}`}
                    </UIButton>
                  </div>
                )} */}
              </WrapperDisable>
            </WrapperChildContent>
          </Grid>
          {/* <Grid container item sm={3}>
            <WrapperDisable disabled={props.disabled}>
              {state.isShowTime && (
                <WrapperChildContent>
                  <WrapperShowTime className="m-bottom-2">
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <KeyboardTimePicker
                        keyboardIcon={<AccessAlarmIcon />}
                        margin="normal"
                        id="time-picker"
                        //   label="Time picker"
                        value={state.timeOfDay}
                        onChange={onChangeTime}
                        KeyboardButtonProps={{
                          'aria-label': 'change time',
                        }}
                      />
                    </MuiPickersUtilsProvider>
                    <WrapperIcon onClick={deleteItem} className="m-left-1">
                      <Icon>cancel</Icon>
                    </WrapperIcon>
                  </WrapperShowTime>
                </WrapperChildContent>
              )}
            </WrapperDisable>
          </Grid> */}
        </>
      )}

      <Grid container item sm={12}>
        <Grid item sm={8}>
          <WrapperDisable disabled={props.disabled}>
            <WrapperRadio>
              {!isViewMode && (
                <FormControlLabel
                  value="SAME_DAY"
                  name="SAME_DAY"
                  onChange={handleChange}
                  control={
                    <Radio
                      color="primary"
                      size="small"
                      checked={state.timeUnit === 'SAME_DAY'}
                    />
                  }
                  label={getTranslateMessage(
                    TRANSLATE_KEY._STORY_DELAY_ENTER_DAY,
                    'Same day audience enters node',
                  )}
                  labelPlacement="end"
                />
              )}
              {isViewMode && state.timeUnit === 'SAME_DAY' && (
                <TextViewMode className={classes.wrapperTextViewMode}>
                  {getTranslateMessage(
                    TRANSLATE_KEY._STORY_DELAY_ENTER_DAY,
                    'Same day audience enters node',
                  )}
                </TextViewMode>
              )}
            </WrapperRadio>
            {state.timeUnit === 'SAME_DAY' && (
              <WrapperChildContent>
                <WrapperShowTime className={`${isViewMode && 'p-left-0'}`}>
                  <div
                    className={`${classes.textView} ${!isViewMode &&
                      'm-top-0'}`}
                  >
                    {getTranslateMessage(
                      TRANSLATE_KEY._INFO_STORY_START_TIME,
                      'Start time',
                    )}
                  </div>
                  <MuiPickersUtilsProvider utils={DateFnsUtils}>
                    {isViewMode ? (
                      <div className="d-flex">
                        <div className={classes.noneWidth}>
                          <UIInputTime
                            isViewMode={isViewMode}
                            keyboardIcon={<AccessTimeIcon />}
                            margin="normal"
                            id="time-picker"
                            // label={getTranslateMessage(
                            //   TRANSLATE_KEY._INFO_STORY_START_TIME,
                            //   'Start time',
                            // )}
                            value={state.timeOfDay}
                            onChange={onChangeTime}
                            KeyboardButtonProps={{
                              'aria-label': 'change time',
                            }}
                          />
                        </div>
                        <DisplayTimeZone style={{ lineHeight: '24px' }}>
                          {getLabelPortalTimeZone()}
                        </DisplayTimeZone>
                      </div>
                    ) : (
                      <>
                        <UIInputTime
                          isViewMode={isViewMode}
                          keyboardIcon={<AccessTimeIcon />}
                          margin="normal"
                          id="time-picker"
                          // label={getTranslateMessage(
                          //   TRANSLATE_KEY._INFO_STORY_START_TIME,
                          //   'Start time',
                          // )}
                          value={state.timeOfDay}
                          onChange={onChangeTime}
                          KeyboardButtonProps={{
                            'aria-label': 'change time',
                          }}
                        />
                        <DisplayTimeZone style={{ lineHeight: '32px' }}>
                          {getLabelPortalTimeZone()}
                        </DisplayTimeZone>
                      </>
                    )}
                  </MuiPickersUtilsProvider>
                </WrapperShowTime>
              </WrapperChildContent>
            )}
          </WrapperDisable>
        </Grid>
      </Grid>
    </>
  );
};

DelayDateOrTime.defaultProps = {
  onChange: () => {},
  initData: {},
  disabled: false,
};

export default DelayDateOrTime;

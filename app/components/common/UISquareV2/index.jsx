/* eslint-disable react/prop-types */
import React from 'react';
import classNames from 'classnames';
import PropType from 'prop-types';
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import { makeStyles } from '@material-ui/core/styles';
import {
  UIButton,
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import {
  Wrapper,
  WrapperIcon,
  Head,
  Text,
  WrapperBody,
  WrapperIconAndLabel,
  Tag,
  WrapperImage,
  IconImage,
  CoverBlock,
} from './styled';
import UIIconXlab from '../UIIconXlab'; // fake commit
import { CATALOG_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

const useStyles = makeStyles({
  active: {
    position: 'absolute',
    top: '-10px',
    right: '-10px',
    color: 'rgb(0, 94, 184)',
    backgroundColor: '#fff',
  },
  wrapperIconXlab: {
    height: '100%',
    flexDirection: 'column',
    justifyContent: 'space-around',
  },
  xlabIcon: {
    color: '#005EB8 !important',
    width: '50px',
    height: '50px',
    fontSize: '50px !important',
  },
  xlabIconSM: {
    color: '#005EB8 !important',
    width: '28px',
    height: '28px',
    fontSize: '28px !important',
  },
});

function UISquare(props) {
  const classes = useStyles();
  const {
    active,
    size,
    disabled,
    id,
    iconBackgroundColor,
    tag,
    hasImage,
    imageUrl,
    isActive,
    catalogCode,
    styles,
    btnLabel,
    isXlabIconSM,
    isShowCover = true,
  } = props;

  const {
    wrapperIconLabel = {},
    wrapperCard = {},
    wrapperImage = {},
    head = {},
  } = styles;
  const handleClick = event => {
    if (props.disabled) {
      event.preventDefault();
      return;
    }

    if (props.onClick) {
      props.onClick(props.item, event);
    }
  };

  if (props.CustomSquare) {
    const { CustomSquare, ...restOf } = props;

    return <CustomSquare {...restOf} />;
  }

  return (
    <WrapperDisable disalbed={props.disabled} key={props.key}>
      <Wrapper
        active={active}
        size={size}
        onClick={event => (props.isNewLayout ? {} : handleClick(event))}
        disabled={disabled}
        aria-describedby={id}
        className={`${isActive ? 'active-box' : ''} wrapperUISquare`}
        backgroundColorCard={props.backgroundColorCard}
        style={wrapperCard}
        isNewLayout={props.isNewLayout}
      >
        {tag && <Tag>{tag}</Tag>}
        {active &&
          ![
            CATALOG_CODE.ZNS,
            CATALOG_CODE.SMS_FPT,
            CATALOG_CODE.ZALO_OA,
          ].includes(catalogCode) && (
            <CheckCircleIcon className={classes.active} />
          )}
        <WrapperBody>
          <WrapperIconAndLabel
            style={wrapperIconLabel}
            className={props.isDisplayIconXlab ? classes.wrapperIconXlab : ''}
          >
            {hasImage ? (
              <WrapperImage style={wrapperImage}>
                <IconImage src={imageUrl} alt="image preview" />
              </WrapperImage>
            ) : (
              <>
                {props.isDisplayIconXlab ? (
                  <UIIconXlab
                    className={classNames({
                      [classes.xlabIcon]: !isXlabIconSM,
                      [classes.xlabIconSM]: isXlabIconSM,
                    })}
                    name={props.icon}
                  />
                ) : (
                  <WrapperIcon $backgroundColor={iconBackgroundColor}>
                    {props.icon}
                  </WrapperIcon>
                )}
              </>
            )}
            {props.isNewLayout ? null : (
              <Head
                className="head-label"
                style={
                  hasImage
                    ? { fontSize: '12px', fontWeight: '700' }
                    : props.styledLabel || {}
                }
              >
                {props.label}
              </Head>
            )}
          </WrapperIconAndLabel>

          {!hasImage && props.useTippy ? (
            <UITippy arrow distance={10} content={props.description}>
              <Text>{props.description}</Text>
            </UITippy>
          ) : (
            <Text>{props.description}</Text>
          )}
        </WrapperBody>
        {props.isNewLayout && (
          <>
            <Head
              className="head-label"
              style={
                hasImage
                  ? {
                      fontSize: '12px',
                      fontWeight: 400,
                      textAlign: 'left',
                      position: 'relative',
                      left: '-10px',
                      ...head,
                    }
                  : { ...(props.styledLabel || {}), ...head } || {}
              }
            >
              {props.label}
            </Head>
            <CoverBlock isShowCover={isShowCover} disabled={disabled}>
              <UIButton
                onClick={e => {
                  if (!isShowCover || disabled) return;

                  handleClick(e);
                }}
                theme="primary"
              >
                {btnLabel}
              </UIButton>
            </CoverBlock>
          </>
        )}
      </Wrapper>
    </WrapperDisable>
  );
}

UISquare.defaultProps = {
  useTippy: false,
  disabled: false,
  isXlabIconSM: false,
  styles: {
    wrapperIconLabel: {},
    wrapperCard: {},
    wrapperImage: {},
    head: {},
  },
  backgroundColorCard: '#ffffff',
  btnLabel: getTranslateMessage(TRANSLATE_KEY._, 'Create Campaign'),
};

UISquare.propTypes = {
  icon: PropType.any,
  label: PropType.string,
  description: PropType.string,
  hasImage: PropType.bool,
  imageUrl: PropType.string,
  isActive: PropType.bool,
  active: PropType.bool,
  size: PropType.string,
  onClick: PropType.func,
  useTippy: PropType.bool,
  disabled: PropType.bool,
  styledLabel: PropType.any,
  btnLabel: PropType.string,
  backgroundColorCard: PropType.string,
  isXlabIconSM: PropType.bool,
  styles: PropType.shape({
    wrapperIconLabel: PropType.object,
    wrapperCard: PropType.object,
    wrapperImage: PropType.object,
    head: PropType.object,
  }),
};
UISquare.deaultProps = {
  size: '200px',
  hasImage: false,
  imageUrl: '',
  isActive: false,
};
export default UISquare;

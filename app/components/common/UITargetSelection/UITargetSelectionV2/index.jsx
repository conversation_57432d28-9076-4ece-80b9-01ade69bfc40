/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React, { useState, useEffect, useRef, memo, useMemo } from 'react';
// import produce from 'immer';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useImmer } from 'use-immer';
import SelectTree from 'components/form/UISelectCondition';
import {
  UITextSearch as TextSearch,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UITippy,
  UIButton,
} from '@xlab-team/ui-components';
import {
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Icon,
  TableCell,
  makeStyles,
  Popover,
  // Drawer,
  // Step,
  Tab,
  Tabs,
} from '@material-ui/core';
import _isEmpty from 'lodash/isEmpty';
// import { StyleWrapper } from 'modules/Dashboard/Profile/Segment/Create/styles';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import { Alert } from '@antscorp/antsomi-ui';
import { ViewDetailsInformationIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import {
  ContainerTargetAudience,
  HeaderTargetAudience,
  ContainerSelectAudience,
  WrapperSelectItem,
  WrapperItemsSelect,
  WrapperItemsPreview,
  WrapperHeaderSelect,
  DivError,
  WrapperStyleLoadingBottom,
  RemoveAllButton,
  StyledTable,
  TextTableLeft,
  TextTableRight,
  TextTableVisible,
  WrapperButtonFooter,
  WrapperLabelFooter,
  TextLabel,
  // StepWrapper,
  // StyledStepper,
  // StyledStepButton,
  // DisplayContent,
  // StyleHeader,
  // TextHeader,
  // WrapperButton,
  TextLink,
  TextTableLeftLink,
} from '../styled';
import {
  MAP_TRANSLATE,
  OPTIONS_SELECT_SEGMENTS,
  getAudienceTabs,
  serializeData,
  validate,
} from '../utils';
import { SEGMENT_STATUS } from '../../constant';
import LabeledSelection from '../_UI/LabelSelection';
import ColumnPreview from '../_UI/ColumnPreview';
import { Nodata } from '../../../form/UISelect/DropdownAction/Tree/styled';
import { useFetchSelection, useFetchSelected } from '../useFetchData';
import { TableCellStyle } from '../../../../containers/UIDev/UIDocument/styled';
import { TextSearchWrapper } from '../../../form/AutoComplete/Dropdown/styled';
import { UISelectConditionWrapper } from '../../../form/UISelectCondition/styled';
import {
  WrapperIcon,
  WrapperTextLeftTable,
} from '../../UITargetUpdateSegment/styled';
import UIIconXlabColor from '../../UIIconXlabColor';
import { makeSelectConfigureMainCreateWorkflow } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';
import { updateValue, addNotification } from '../../../../redux/actions';
import { AUDIENCE_TYPE, SEGMENT } from '../../contants';
// import Design from '../../../../modules/Dashboard/Profile/Segment/Create/Desgin';
// import Content from '../../../../modules/Dashboard/Settings/Uploads/BO/Uploads/ContentV0';
// import UILinearProgress from '../../UILinearProgress';
import { makeSelectMainCreateSegment } from '../../../../modules/Dashboard/Profile/Segment/Create/selectors';
import {
  StyledButtonClearAll,
  TabsWrapper,
  WrapperSelectedItemV2,
} from './styled';
import UIIconXlab from '../../UIIconXlab';
import useUpdateEffect from '../../../../hooks/useUpdateEffect';
import Checkbox from '../../../Molecules/CheckBox';
import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';
import APP from '../../../../appConfig';
import { getCurrentOwnerIds, getPortalId } from '../../../../utils/web/cookie';
import { useCreateSegmentInJourney } from '../../../../containers/Drawer/DrawerSegment/hooks';
import DropdownViewAccess from '../../DropdownViewAccess';
import SegmentServices from 'services/Segment';

const LIMIT = 10;
const initState = () => ({
  dataOptions: { list: [], map: {}, options: [] },
  dataSelected: [],
  mapDataSelected: {},
  search: '',
  viewId: undefined,
  viewObject: undefined,
  limit: LIMIT,
  page: 1,
  total: 0,
  isLoadMore: false,
  errors: [],
  triggerOut: 1,
  triggerFetch: 0,
  isInitDone: false,
  dataTypeBelongToSegment: OPTIONS_SELECT_SEGMENTS[0],
  hasNextPage: true,
});

const useStyles = makeStyles(() => ({
  styleTextLeft: {
    width: '266px',
  },
  styleTextRight: {
    width: '92px',
  },
}));

const alertMessage = {
  [SEGMENT_STATUS.ARCHIVE]: getTranslateMessage(
    TRANSLATE_KEY._ARCHIVED_SEGMENT,
    `This segment is archived`,
  ),
  [SEGMENT_STATUS.REMOVE]: getTranslateMessage(
    TRANSLATE_KEY._,
    'This segment does not exist',
  ),
};

const UITargetAudienceV2 = props => {
  const {
    itemTypeId,
    audienceTypes,
    titleSelect,
    titleHeader,
    id,
    initData,
    disabledRemove,
    isViewMode,
    isHiddentListViewMode,
    isShowAlert,
    use,
    // main,
    activeRow,
    viewObject = {},
  } = props;

  const ownerId = getCurrentOwnerIds();

  const isUseTargetSelectionV2 = true; // đang dùng component này...
  const [openPopover, setOpenPopover] = useState(false);
  // const [openDrawer, setOpenDrawer] = useState(false);
  // const [nameSegment, setNameSegment] = useState('');
  const [initDataDone, setInitDataDone] = useState(false);
  const isVisitorCustomer = audienceTypes === 'specificAudiences';
  const [state, setState] = useImmer(initState());

  const [isLoadedDataAccess, setIsLoadedDataAccess] = useState(false);
  const [selectedDataView, setSelectedDataView] = useState(
    () => initData?.viewObject,
  );
  const [dataAccessList, setDataAccessList] = useState();

  const isEmptyDataAccessList = dataAccessList
    ? _isEmpty(dataAccessList?.businessObject) &&
      _isEmpty(dataAccessList?.owner) &&
      _isEmpty(dataAccessList?.shareWithMe)
    : true;

  const selectedViewId = selectedDataView && selectedDataView?.item?.viewId;

  const viewIdList = dataAccessList
    ? [
        ...dataAccessList?.owner?.map(item => item.viewId),
        ...dataAccessList?.shareWithMe?.map(item => item.viewId),
      ]
    : [];
  // /** The current data view selected is in viewAccessInfo */
  const isSelectedDataViewExist = viewIdList.includes(selectedViewId);
  const isSelectedDataObjectExist =
    selectedDataView?.item?.type === 'BO' &&
    selectedDataView?.item?.itemTypeDisplay ===
      dataAccessList?.businessObject?.itemTypeDisplay;

  const isHasPermissionWithCurrentValue =
    isSelectedDataViewExist || isSelectedDataObjectExist;

  const btnRef = useRef(null);
  // const [width, setWidth] = React.useState(window.innerWidth * 0.7);
  // const [activeStep, setActiveStep] = useState(0);
  // const [isNumSave, setIsNumSave] = useState(0);
  // const childRef = useRef(null);
  // const [files, setFiles] = useState([]);
  // const [completedStep, setCompletedStep] = useState({
  //   isCompleted: false,
  // });
  // const [boUploadData, setBoUploadData] = useState({
  //   importId: '',
  //   mappingAttributes: [],
  // });
  // const [importType, setInportType] = useState('');
  const [checkedAll, setStateCheckedAll] = useState(false);

  useEffect(() => {
    // if (selectedViewId && typeof selectedViewId === 'number')
    setState(draft => {
      draft.viewId = selectedViewId;
      draft.viewObject = selectedDataView;
      draft.triggerFetch += 1;
      draft.isLoadMore = false;
      draft.page = 1;
      draft.hasNextPage = true;
    });
  }, [selectedViewId]);

  useEffect(() => {
    const getListViewAccess = async () => {
      try {
        const data = await SegmentServices.fetch.getListViewAccess({
          body: {
            itemTypeId: +itemTypeId,
            ownerId,
          },
        });

        setDataAccessList(data.data);
        setIsLoadedDataAccess(true);
        const { owner, shareWithMe, businessObject } = data.data || {};
        const dataList = [].concat(owner, shareWithMe);

        const isHaveBO = businessObject && !_isEmpty(businessObject);

        const isTheSameItemType =
          Number(selectedDataView?.item?.itemTypeId) === itemTypeId;

        if (
          (dataList.length > 0 || isHaveBO) &&
          ((selectedDataView && !isTheSameItemType) ||
            !selectedDataView ||
            _isEmpty(selectedDataView))
        ) {
          setSelectedDataView(
            isHaveBO
              ? { item: { ...businessObject, type: 'BO' } }
              : { item: dataList[0] },
          );
        }
      } catch (error) {
        // console.log('🚀 ~ getListViewAccess error:', error);
      }
    };

    getListViewAccess();
  }, [itemTypeId]);

  // const MAP_TITLE = {
  //   navBar: {
  //     titleCustomerSegment: getTranslateMessage(
  //       TRANSLATE_KEY._TITL_CUSTOMER_SEGMENT,
  //       'Customers and Segment',
  //     ),
  //     titleVisitorSegment: getTranslateMessage(
  //       TRANSLATE_KEY._TITL_VISTOR_SEGMENT,
  //       'Visitors and Segment',
  //     ),
  //   },
  // };

  // const mapTranslateMessage = {
  //   createCustomer: getTranslateMessage(
  //     TRANSLATE_KEY._ACT_CREATE_CUSTOMER,
  //     'Create Customer',
  //   ),
  //   createVisitor: getTranslateMessage(
  //     TRANSLATE_KEY._ACT_CREATE_VISITOR,
  //     'Create Visitor',
  //   ),
  //   createSegment: getTranslateMessage(
  //     TRANSLATE_KEY._ACT_CREATE_SEGMENT,
  //     'Create Segment',
  //   ),
  // };

  // const steps = [
  //   nameSegment === MAP_TITLE.navBar.titleCustomerSegment
  //     ? mapTranslateMessage.createCustomer
  //     : mapTranslateMessage.createVisitor,
  //   mapTranslateMessage.createSegment,
  // ];

  const [activeTab, setActiveTab] = useState(props.audienceTypes);

  const { dispatchOpenWorkspaceSegment } = useCreateSegmentInJourney();

  const audienceTabs = useMemo(() => {
    const result = getAudienceTabs(props.itemTypeId);
    return result;
  }, [props.itemTypeId]);

  useEffect(() => {
    if (!activeTab) {
      setActiveTab(props.audienceTypes);
    }
  }, [props.audienceTypes, audienceTabs]);

  useUpdateEffect(() => {
    if (props.isBlastCampaign) {
      const {
        canSwitch = false,
        use = [],
        switchToTab = [],
        itemTypeId = '',
      } = props.dataValidateSwitchTabAudiences;

      if (canSwitch && itemTypeId) {
        if (state.dataSelected.length === 0) {
          // Nếu không chọn audience nào và chuyển sang tab khác thì sẽ xóa TargetAudience đó đi.
          use.forEach(item => {
            if (props.use === item) {
              const currentTab =
                switchToTab[item] === 'segmentAudiences'
                  ? 'specificAudiences'
                  : 'segmentAudiences';
              const currentId = `${item}${itemTypeId}@@${currentTab}`;
              props.callback('ON_REMOVE_ITEM', currentId);
            }
          });
        }

        use.forEach(useItem => {
          if (props.use === useItem) {
            const valueTemp = switchToTab[useItem];

            setActiveTab(valueTemp);
            if (useItem === 'include') {
              props.callback('ON_CHANGE_TYPE_INCLUDE', valueTemp);
            }
            if (useItem === 'exclude') {
              props.callback('ON_CHANGE_TYPE_EXCLUDE', valueTemp);
            }
          }
        });
      }
    }
  }, [props.refreshKeyValidateAudiences]);

  const onChangeTab = (event, value) => {
    if (state.dataSelected.length === 0) {
      // Nếu không chọn audience nào và chuyển sang tab khác thì sẽ xóa TargetAudience đó đi.
      const currentTab =
        value === 'segmentAudiences' ? 'specificAudiences' : 'segmentAudiences';
      const currentId = `${props.use}${props.itemTypeId}@@${currentTab}`;
      props.callback('ON_REMOVE_ITEM', currentId);
    }
    setActiveTab(value);
    if (props.use === 'include') {
      props.callback('ON_CHANGE_TYPE_INCLUDE', value);
    }
    if (props.use === 'exclude') {
      props.callback('ON_CHANGE_TYPE_EXCLUDE', value);
    }
  };

  // const handleStep = step => {
  //   if (step === 1) {
  //     if (childRef.current.onValidate()) {
  //       setActiveStep(step);
  //       setCompletedStep(prev => ({ ...prev, isCompleted: true }));
  //     }
  //   } else {
  //     setActiveStep(step);
  //   }
  // };

  const [dataAPI, isLoading, total] = useFetchSelection({
    itemTypeId,
    audienceTypes,
    search: state.search,
    viewId: selectedViewId || state.viewId,
    limit: state.limit,
    page: state.page,
    triggerFetch: state.triggerFetch,
    // dependencies: [state.triggerFetch, props.componentId],
    componentId: props.componentId,
    isUseTargetSelectionV2,
  });
  const [dataLookupAPI, isLoadingLookup] = useFetchSelected(
    {
      itemTypeId,
      audienceTypes,
      search: state.search,
      limit: state.limit,
      page: state.page,
      audienceIds: initData.audienceIds || [],
      dependencies: [props.componentId],
    },
    [],
  );

  const classes = useStyles();

  // console.log('render', props.componentId, initData.audienceIds, {
  //   isLoading,
  //   isLoadingLookup,
  // });

  // useEffect(() => {
  //   if (state.isInitDone) {
  //     const { errors } = validate(state);
  //     setState(draft => {
  //       draft.errors = errors;
  //     });
  //   }
  // }, [props.componentId]);
  const handleSegment = title => {
    dispatchOpenWorkspaceSegment({ title });
    setOpenPopover(false);
  };

  const renderLabel = typeId => {
    if (typeId === `${use}-${AUDIENCE_TYPE.visitorSegmentAudiences}`) {
      return (
        <WrapperLabelFooter>
          <TextLabel onClick={() => handleSegment(SEGMENT.VISITOR_SEGMENT)}>
            {SEGMENT.VISITOR_SEGMENT}
          </TextLabel>
          <TextLabel
            onClick={() => handleSegment(SEGMENT.VISITORS_AND_SEGMENT)}
          >
            {SEGMENT.VISITORS_AND_SEGMENT}
          </TextLabel>
        </WrapperLabelFooter>
      );
    }
    if (typeId === `${use}-${AUDIENCE_TYPE.customerSegmentAudiences}`) {
      return (
        <WrapperLabelFooter>
          <TextLabel onClick={() => handleSegment(SEGMENT.CUSTOMER_SEGMENT)}>
            {SEGMENT.CUSTOMER_SEGMENT}
          </TextLabel>
          <TextLabel
            onClick={() => handleSegment(SEGMENT.CUSTOMERS_AND_SEGMENT)}
          >
            {SEGMENT.CUSTOMERS_AND_SEGMENT}
          </TextLabel>
        </WrapperLabelFooter>
      );
    }
    return null;
  };

  useEffect(() => {
    if (state.isInitDone) {
      const { errors } = validate(state);
      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [props.validateKey, props.validateKeyBlast]);

  useEffect(() => {
    if (!isLoading && !isLoadingLookup) {
      setState(draft => {
        const dataOptions = serializeData(dataAPI, itemTypeId, audienceTypes);
        // console.log('dataLookupAPI', dataLookupAPI, '--', dataOptions);
        draft.hasNextPage = dataOptions.list.length > 0;
        if (state.page === 1 && dataOptions.list.length < state.limit) {
          // for case page = 1 data is small, and trigger scroll not action because height is not scroll to bottom
          draft.hasNextPage = false;
        }
        if (!state.isInitDone) {
          const dataLookup = serializeData(
            dataLookupAPI,
            itemTypeId,
            audienceTypes,
          );
          draft.dataOptions.map = {
            ...state.dataOptions.map,
            ...dataOptions.map,
            ...dataLookup.map,
          };
          if (Object.keys(initData).length > 0) {
            //
            (initData.audienceIds || []).forEach(each => {
              const dataSelected = draft.dataOptions.map[each];

              const item = { ...dataSelected };
              if (!_isEmpty(item)) {
                draft.dataSelected.push(item);
                draft.mapDataSelected[each] = true;
              }
            });
            draft.dataTypeBelongToSegment = initData.dataTypeBelongToSegment;
          }
          draft.search = '';
          draft.isInitDone = true;
          draft.triggerOut += 1;
          setInitDataDone(true);
        } else {
          draft.dataOptions.map = {
            ...state.dataOptions.map,
            ...dataOptions.map,
          };
        }
        draft.total = total > 0 ? total : draft.dataOptions.list.length; // case fetch data lần cuối khi chạm đáy, không có data nên total = 0
        if (state.page > 1) {
          draft.dataOptions.list = state.dataOptions.list.concat(
            dataOptions.list,
          );
        } else {
          draft.dataOptions.list = dataOptions.list;
        }
      });
    }
  }, [isLoading, isLoadingLookup]);

  useEffect(() => {
    if (state.isInitDone) {
      onChange({ initDataDone });
      setInitDataDone(false);
    }
  }, [state.triggerOut]);
  useEffect(() => {
    if (state.dataSelected.length === 0) {
      setStateCheckedAll(false);
    } else {
      setStateCheckedAll(true);
    }
  }, [state.dataSelected]);
  const isSegment = useMemo(() => audienceTypes === 'segmentAudiences', [
    audienceTypes,
  ]);
  const operatorLabel = useMemo(
    () =>
      state.dataTypeBelongToSegment.value === OPTIONS_SELECT_SEGMENTS[0].value
        ? MAP_TRANSLATE.or
        : MAP_TRANSLATE.and,
    [state.dataTypeBelongToSegment],
  );
  const onDragEnd = item => {
    const indexOfItem = state.dataSelected.findIndex(
      each => `${each.value}` === `${item.draggableId}`,
    );

    if (!item.destination || indexOfItem < 0) {
      return;
    }
    setState(draft => {
      const splicedItem = state.dataSelected[indexOfItem];
      draft.dataSelected.splice(indexOfItem, 1);
      draft.dataSelected.splice(item.destination.index, 0, splicedItem);
      draft.triggerOut += 1;
    });
  };

  const onRemoveItem = index => {
    setState(draft => {
      const item = state.dataSelected[index];
      draft.dataSelected.splice(index, 1);
      delete draft.mapDataSelected[item.value];
      draft.triggerOut += 1;
    });
  };

  const onChangeCheckbox = item => {
    setState(draft => {
      const indexOfItem = state.dataSelected.findIndex(
        each => `${each.value}` === `${item.value}`,
      );
      if (indexOfItem < 0) {
        draft.dataSelected.push(item);
        draft.mapDataSelected[item.value] = true;
      } else {
        draft.dataSelected.splice(indexOfItem, 1);
        delete draft.mapDataSelected[item.value];
      }
      draft.triggerOut += 1;
      const { errors } = validate(draft);
      draft.errors = errors;
    });
  };

  const onSearch = value => {
    // if (value !== state.search) {
    //   const el = document.getElementById(`target-selection-${audienceTypes}`);
    //   el.scrollTo({
    //     top: 0,
    //     left: 0,
    //     behavior: 'smooth', // or can get `auto` variable
    //   });
    // }
    setState(draft => {
      draft.search = value;
      draft.triggerFetch += 1;
      draft.isLoadMore = false;
      draft.page = 1;
      draft.hasNextPage = true;
    });
  };

  const onClearAll = () => {
    setState(draft => {
      draft.dataSelected = [];
      draft.mapDataSelected = {};
      draft.triggerOut += 1;
    });
  };

  const onChangeSelectTree = item => {
    setState(draft => {
      draft.dataTypeBelongToSegment = item;
      draft.triggerOut += 1;
    });
  };
  const onChange = ({ initDataDone = false }) => {
    let output = {
      id,
      data: {
        viewId: selectedViewId,
        viewObject: selectedDataView,
        audienceIds: Object.keys(state.mapDataSelected),
        listAudience: state.dataSelected,
        dataTypeBelongToSegment: state.dataTypeBelongToSegment,
      },
    };
    if (props.isBlastCampaign) {
      output = {
        ...output,
        initDataDone,
      };
    }
    props.onChange(output);
  };

  const onLoadMore = () => {
    // if (state.total > state.dataOptions.list.length) {
    if (state.hasNextPage) {
      setState(draft => {
        draft.page += 1;
        draft.triggerFetch += 1;
        draft.isLoadMore = true;
      });
    }
  };
  const handleClickCheckboxAll = () => {
    if (checkedAll) {
      onClearAll();
    } else {
      setState(draft => {
        draft.dataSelected = state.dataOptions.list;
        draft.triggerOut += 1;
      });
      state.dataOptions.list.forEach(each => {
        if (each.value) {
          setState(draft => {
            draft.mapDataSelected[each.value] = true;
          });
        }
      });
    }
    setStateCheckedAll(!checkedAll);
  };
  const onScroll = e => {
    const bottom =
      // e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
      e.target.scrollHeight - e.target.scrollTop < e.target.clientHeight + 70;
    if (bottom) {
      onLoadMore();
    }
  };
  const onClickRemoveItem = () => {
    props.callback('ON_REMOVE_AUDIENCES', id);
  };
  const hiddenErrorBlast = useMemo(() => {
    let isHidden = false;
    if (props.isBlastCampaign) {
      [...(props.cluster || [])].forEach(clusterItem => {
        const { initData: initDataTemp = {} } = clusterItem;
        if (!_isEmpty(initDataTemp.audienceIds)) {
          isHidden = true;
        }
      });
    }
    return isHidden;
  }, [props.cluster]);

  const renderErrorMessage = () => {
    if (
      (props.isBlastCampaign && hiddenErrorBlast) ||
      state.errors.length === 0
    )
      return null;

    return <DivError className="m-top-1">{state.errors}</DivError>;
  };

  const renderAlert = () => {
    if (!isShowAlert || props.use !== 'include') return null;

    return (
      <Alert
        message={getTranslateMessage(
          TRANSLATE_KEY._3RD_CAMPAIGN_INFO_AUDIENCE_SIZE,
          'Recommend targeting an audience size of less than 100K to ensure the message is delivered on time.',
        )}
        type="info"
        showIcon
        closable={false}
        icon={
          <ViewDetailsInformationIcon
            size={20}
            style={{ color: globalToken?.colorPrimary, marginRight: 0 }}
          />
        }
        style={{
          borderColor: '#B8CFE6',
          backgroundColor: globalToken?.bw0,
          marginBottom: 15,
          minWidth: 608,
        }}
      />
    );
  };

  if (isViewMode) {
    if (isLoading) {
      return <Loading isLoading={isLoading} isWhite />;
    }

    return (
      <>
        {renderAlert()}
        {!isHiddentListViewMode && (
          <UISelectConditionWrapper bsNone>
            <SelectTree
              options={OPTIONS_SELECT_SEGMENTS}
              onlyParent={props.onlyParent}
              use="tree"
              label={capitalize(titleHeader)}
              isSearchable={false}
              isParentOpen={props.isParentOpen}
              value={state.dataTypeBelongToSegment}
              onChange={onChangeSelectTree}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              isViewMode={isViewMode}
            />
          </UISelectConditionWrapper>
        )}
        <TableContainer
          className="no-margin-bottom -margin-top-10"
          style={{ marginBottom: '20px', marginTop: '10px', width: '358px' }}
        >
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell align="left" className={classes.styleTextLeft}>
                  {isVisitorCustomer ? (
                    <TextTableVisible>{titleSelect}</TextTableVisible>
                  ) : (
                    <TextTableLeft>{titleSelect}</TextTableLeft>
                  )}
                </TableCell>
                {!isVisitorCustomer && (
                  <TableCell align="right" className={classes.styleTextRight}>
                    <TextTableRight>{MAP_TRANSLATE.members}</TextTableRight>
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {state.dataSelected &&
                state.dataSelected.map(data => (
                  <TableRow key={data.value}>
                    <TableCellStyle
                      align="left"
                      className={classes.styleTextLeft}
                    >
                      {isVisitorCustomer ? (
                        <TextTableVisible>{data.label}</TextTableVisible>
                      ) : (
                        <>
                          {[
                            SEGMENT_STATUS.ARCHIVE,
                            SEGMENT_STATUS.REMOVE,
                          ].includes(data.status) ? (
                            <WrapperTextLeftTable>
                              <UITippy
                                content={data.label}
                                placement="top-start"
                                distance={0}
                                arrow={false}
                              >
                                <TextLink
                                  onClick={() => {
                                    window.open(
                                      `${
                                        APP.PREFIX
                                      }/${getPortalId()}/profile/segments?tab=configure&segment-id=${data.value ||
                                        {}}&use-segment-into=segment-menu&design=update`,
                                      '_blank',
                                    );
                                  }}
                                >
                                  {data.label}
                                </TextLink>
                              </UITippy>
                              <UITippy
                                content={alertMessage[data.status]}
                                placement="top-start"
                                distance={0}
                                arrow={false}
                              >
                                <WrapperIcon>
                                  <UIIconXlabColor
                                    name="warning"
                                    fontSize="14px"
                                    style={{ marginRight: '-10px' }}
                                  />
                                </WrapperIcon>
                              </UITippy>
                            </WrapperTextLeftTable>
                          ) : (
                            <UITippy
                              content={data.label}
                              placement="top-start"
                              distance={0}
                              arrow={false}
                            >
                              {/* <StyleLink
                              target="_blank"
                              to={`${
                                APP.PREFIX
                              }/${getPortalId()}/profile/segments/${data.value ||
                                {}}/detail`}
                            > */}
                              <TextTableLeftLink
                                onClick={() => {
                                  window.open(
                                    `${
                                      APP.PREFIX
                                    }/${getPortalId()}/profile/segments?tab=configure&segment-id=${data.value ||
                                      {}}&use-segment-into=segment-menu&design=update`,
                                    '_blank',
                                  );
                                }}
                              >
                                {data.label}
                              </TextTableLeftLink>
                              {/* </StyleLink> */}
                            </UITippy>
                          )}
                        </>
                      )}
                    </TableCellStyle>
                    {!isVisitorCustomer && (
                      <TableCellStyle
                        align="right"
                        className={classes.styleTextRight}
                      >
                        <TextTableRight>{data.size}</TextTableRight>
                      </TableCellStyle>
                    )}
                  </TableRow>
                ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      </>
    );
  }

  return (
    <>
      {renderAlert()}
      <ContainerTargetAudience
        errors={
          props.isBlastCampaign
            ? !hiddenErrorBlast && state.errors.length > 0
            : state.errors.length > 0
        }
      >
        <HeaderTargetAudience className="header-target-audience wrapper-center">
          {String(titleHeader).toUpperCase()}
          <WrapperDisable disabled={disabledRemove}>
            <div className="delete-button wrapper-center">
              <Icon onClick={onClickRemoveItem} fontSize="small">
                close
              </Icon>
            </div>
          </WrapperDisable>
        </HeaderTargetAudience>
        <ContainerSelectAudience>
          <WrapperHeaderSelect>
            <TabsWrapper>
              <Tabs
                value={activeTab}
                onChange={onChangeTab}
                indicatorColor="primary"
                textColor="primary"
                aria-label="scrollable auto tabs example"
              >
                {audienceTabs.map(audience => (
                  <Tab value={audience.value} label={audience.label} />
                ))}
              </Tabs>
            </TabsWrapper>
          </WrapperHeaderSelect>
          <WrapperHeaderSelect borderLeft={false} padding="5px 20px">
            <div
              style={{ height: !isSegment && '32px' }}
              className="wrapper-center"
            >
              <div className="select-dropdown">
                {isSegment && (
                  <UISelectConditionWrapper bsNone>
                    <SelectTree
                      options={OPTIONS_SELECT_SEGMENTS}
                      onlyParent={props.onlyParent}
                      use="tree"
                      isSearchable={false}
                      isParentOpen={props.isParentOpen}
                      value={state.dataTypeBelongToSegment}
                      onChange={onChangeSelectTree}
                      placeholder={getTranslateMessage(
                        TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                        'Select an item',
                      )}
                      fullWidthPopover
                    />
                  </UISelectConditionWrapper>
                )}
              </div>
              <div>
                <RemoveAllButton onClick={onClearAll}>
                  {String(
                    getTranslateMessage(
                      TRANSLATE_KEY._ACT_REMOVE_ALL,
                      'CLEAR ALL',
                    ),
                  ).toUpperCase()}
                </RemoveAllButton>
              </div>
            </div>
          </WrapperHeaderSelect>
        </ContainerSelectAudience>

        <ContainerSelectAudience>
          <WrapperHeaderSelect
            isBlastCampaign={props.isBlastCampaign}
            borderLeft
            padding="5px 20px"
          >
            {isVisitorCustomer && (
              <DropdownViewAccess
                label="Data access:"
                useGridColumn={false}
                isCheckPermission={isLoadedDataAccess}
                isRequired={false}
                value={selectedDataView && selectedDataView.item}
                callback={(eventType, item) => {
                  if (eventType === 'DATA_ACCESS_ITEM')
                    setSelectedDataView(item);
                  setState(draft => {
                    draft.triggerOut += 1;
                  });
                }}
                viewAccessInfo={dataAccessList}
                classNameContainer="data-access_selected"
                adjustColumn={{ left: 4, right: 8 }}
                isShowLabelLeft
                containerStyles={{
                  marginBottom: '10px',
                }}
              />
            )}
            <TextSearchWrapper className="no-icon-padding">
              <TextSearch
                classDiv="search-filter"
                placeholder={
                  isSegment
                    ? `Search ${props.titleSelect.toLowerCase()}`
                    : getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'Search')
                }
                translateCode={TRANSLATE_KEY._ACT_SEARCH}
                isTimeout
                onChange={onSearch}
                value={state.search}
                type="text"
              />
            </TextSearchWrapper>
          </WrapperHeaderSelect>
        </ContainerSelectAudience>
        <ContainerSelectAudience position="relative">
          <WrapperSelectItem
            // id="target-selection-select-item"
            id={`target-selection-${audienceTypes}`}
            borderLeft
            padding="14px 16px"
            isLoading={isLoading}
            onScroll={onScroll}
          >
            <div
              className="wrapper-center"
              style={{ fontSize: '12px', fontWeight: 'bold' }}
            >
              <div style={{ display: 'flex' }}>
                {isSegment && (
                  <Checkbox
                    checked={checkedAll}
                    onClick={handleClickCheckboxAll}
                    // name={item.value.toString()}
                    margin="0px 10px 0px 0"
                    // disabled={disabled}
                    checkedIcon={<IndeterminateCheckBoxIcon />}
                  />
                )}
                {titleSelect}&nbsp;
                <span style={{ fontWeight: 'bold' }}>
                  {isSegment && ` (${state.total})`}
                </span>
              </div>
              {isSegment && <div>{MAP_TRANSLATE.members}</div>}
            </div>
            <WrapperItemsSelect>
              {/* Not show options archive (status = 4) */}
              {state.dataOptions.list.filter(
                ({ status }) => status !== SEGMENT_STATUS.ARCHIVE,
              ).length === 0 &&
                !state.hasNextPage && (
                  <Nodata>
                    {getTranslateMessage(
                      TRANSLATE_KEY._INFO_NO_DATA,
                      'No data',
                    )}
                  </Nodata>
                )}
              {(!isEmptyDataAccessList &&
                isVisitorCustomer &&
                isHasPermissionWithCurrentValue) ||
              isSegment ? (
                state.dataOptions.list
                  .filter(({ status }) => status !== SEGMENT_STATUS.ARCHIVE)
                  .map(item => (
                    <LabeledSelection
                      isBlastCampaign={props.isBlastCampaign}
                      item={item}
                      name={item.value}
                      checked={state.mapDataSelected[item.value] !== undefined}
                      onClick={onChangeCheckbox}
                      handleChange={() => {}}
                      label={item.label}
                      key={item.value}
                      isSegment={isSegment}
                      audienceTypes={audienceTypes}
                      isTargetSelectionV2
                    />
                  ))
              ) : (
                <Nodata>
                  {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
                </Nodata>
              )}
              {(isEmptyDataAccessList && isVisitorCustomer) ||
              !isHasPermissionWithCurrentValue
                ? null
                : state.hasNextPage && (
                    <WrapperStyleLoadingBottom>
                      <Loading isLoading isWhite />
                    </WrapperStyleLoadingBottom>
                  )}
            </WrapperItemsSelect>
          </WrapperSelectItem>
          <WrapperSelectedItemV2
            borderLeft={false}
            padding="14px 16px"
            isVisitorCustomer={isVisitorCustomer}
          >
            <div
              className="wrapper-center"
              style={{
                fontSize: '12px',
                fontWeight: 'bold',
                marginBottom: '15px',
              }}
            >
              <div>
                {`Selected ${
                  isSegment
                    ? `${props.titleSelect.toLowerCase()}`
                    : props.titleSelect
                }`}
                &nbsp;
                <span style={{ fontWeight: 'bold' }}>
                  {isSegment && ` (${state.dataSelected.length})`}
                </span>
              </div>

              {state.dataSelected.length ? (
                <>
                  {isSegment ? (
                    <div>
                      {/* <span style={{ marginRight: '17.5px' }}>
                        {MAP_TRANSLATE.members}
                      </span>
                      <StyledButtonClearAll onClick={onClearAll}>
                        &#10006;
                      </StyledButtonClearAll> */}
                    </div>
                  ) : (
                    <StyledButtonClearAll onClick={onClearAll}>
                      &#10006;
                    </StyledButtonClearAll>
                  )}
                </>
              ) : null}
            </div>
            <WrapperItemsPreview>
              <ColumnPreview
                dataSelecteds={state.dataSelected}
                onRemoveItem={onRemoveItem}
                onDragEnd={onDragEnd}
                prefixLabel={isSegment ? operatorLabel : ''}
                isSegment={isSegment}
                audienceTypes={audienceTypes}
                isUseTargetSelectionV2={isUseTargetSelectionV2}
                isBlastCampaign={props.isBlastCampaign}
              />
            </WrapperItemsPreview>
          </WrapperSelectedItemV2>
        </ContainerSelectAudience>
        {[
          `${use}-${AUDIENCE_TYPE.customerSegmentAudiences}`,
          `${use}-${AUDIENCE_TYPE.visitorSegmentAudiences}`,
        ].includes(id) && (
          <WrapperButtonFooter
            style={{ paddingTop: props.isBlastCampaign && '7px' }}
            ref={btnRef}
          >
            <UIButton
              className="m-bottom-1"
              iconName="add"
              theme="text-link"
              reverse
              iconSize="24px"
              style={{
                fontSize: '12px',
                marginLeft: '4px',
                color: '#1F5FAC',
                marginTop: '-6px',
              }}
              onClick={() => setOpenPopover(true)}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#1F5FAC', fontSize: '12px' }}>
                  CREATE NEW AUDIENCE
                </span>
                <UIIconXlab color="#1F5FAC" name="arrow-down" fontSize="16px" />
              </div>
              <div
                style={{
                  fontSize: '11px',
                  color: '#595959',
                  position: 'absolute',
                  whiteSpace: 'nowrap',
                  bottom: '-12px',
                  left: '39px',
                  fontFamily: 'Roboto',
                  fontWeight: 'normal',
                }}
              >
                {getTranslateMessage(
                  TRANSLATE_KEY._BLAST_CAMPAIGN_SEGMENT_LABEL,
                  'Create a new or lookalike audience',
                )}
              </div>
            </UIButton>
            <Popover
              open={openPopover}
              anchorEl={btnRef.current}
              onClose={() => setOpenPopover(false)}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              // transformOrigin={{
              //   vertical: 'top',
              //   horizontal: 'left',
              // }}
            >
              {renderLabel(id)}
              {/* {renderDrawer(nameSegment)} */}
            </Popover>
          </WrapperButtonFooter>
        )}
      </ContainerTargetAudience>
      {renderErrorMessage()}
    </>
  );
};

function capitalize(string) {
  return string.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase());
}

UITargetAudienceV2.defaultProps = {
  initData: {},
  onChange: () => {},
  validateKey: 1,
  disabledRemove: false,
  isShowAlert: false,
  isHiddentListViewMode: false,
};

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectConfigureMainCreateWorkflow(),
  main: makeSelectMainCreateSegment(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig && props.moduleConfig.key;

  return {
    onUpdateFlattenNode: value =>
      dispatch(updateValue(`${prefix}@@FLATTEN_NODES@@`, value)),
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(memo(UITargetAudienceV2));

import styled from 'styled-components';
import colors from 'utils/colors';
import ModalFooter from 'components/Molecules/ModalFooter/index';
import Button from 'components/Atoms/Button/index';
import { UITextSearch as TextSearch } from '@xlab-team/ui-components';

export const WrapperChipSelect = styled.div`
  display: flex;
  align-items: center;
  button {
    height: 24px;
    padding: 0;
    margin: 0;
  }
`;

export const WapperStyleHeader = styled.div`
  padding: 1rem;
  height: 50px;
  // border-bottom: 1px solid #e6e6e6;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .text {
    color: #005eb8;
    font-weight: 600;
  }
`;
export const WrapperDrawer = styled.div`
  width: ${props => props.width};
`;
export const ContainerCustomize = styled.div`
  height: calc(100vh - 110px);
  overflow: hidden;
  display: flex;
`;
export const WrapperContentCustomize = styled.div`
  /* max-width: 40.0625rem; */
  flex-grow: 1;
  border-right: 1px solid ${colors.platinum};
  width: 100%;
`;
export const WrapperNoti = styled.div`
  display: flex;
  width: 33%;
  border-radius: 3px;
  border: 1px solid #f29100;
  padding: 10px;
  margin: 15px 0px 10px 15px;
`;
export const NotiContent = styled.div`
  line-height: 1.5;
  font-size: 12px;
  margin-left: 10px;
`;
export const FooterExtend = styled(ModalFooter)`
  padding: 0 1.5rem;
  height: 64px;
`;
export const WrapperButton = styled.div`
  /* margin-left: 56rem; */
  width: 100%;
  display: flex;
  justify-content: flex-start;
`;
export const WrapperLimit = styled.div`
  /* margin-left: 56rem; */
  width: 100%;
  display: flex;
  justify-content: flex-start;
`;
export const ButtonCancelExtend = styled(Button)`
  margin-left: 0.75rem;
  font-size: 12px !important;
  padding: 5px 7px 5px 7px;
  /* padding: 0.375rem 0;
  font-size: 12px !important;
  width: 6.375rem;
  min-width: 6.375rem; */
`;

export const ButtonSaveExtend = styled(Button)`
  margin-left: 0.75rem;
  font-size: 12px !important;
  padding: 5px 7px 5px 7px;
  /* padding: 0.375rem 0;
  width: 8.875rem; */
`;
export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  margin-top: 8px;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  // height: 235px;
  height: 100%;
  // min-height: 145px;
  // max-height: 235px;
  top: 0;
  left: 0;
  .table {
    .header {
      .tr {
        .th:first-child {
          border-right: 2px solid ${colors.platinum} !important;
        }
        .th {
          font-weight: bold;
          &.status {
            text-indent: 8px;
          }
        }
      }
    }
    .body {
      .tr {
        .td:first-child {
          border-right: 2px solid ${colors.platinum};
        }
        .td {
          &.align-right {
            text-align: right;
          }
        }
      }
    }
  }
`;
export const WrapperSearch = styled.div`
  padding: 0px 15px 0px 15px;
  width: 388px;
`;
export const LabelLimit = styled.div`
  display: flex;
  align-items: center;
  color: #000000;
  font-size: 12px;
`;
export const WrapperContent = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 10px 5px 0px 20px;
`;
export const TextSearchCustom = styled(TextSearch)`
  .MuiInputBase-root.MuiInput-underline {
    height: 30px;
  }
`;

/* eslint-disable indent */
import React from 'react';
import PropTypes from 'prop-types';
import { isValid as isValidDate } from 'date-fns';
import { get } from 'lodash';
import moment from 'moment';

// Components
import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup as MuiRadioGroup,
  Grid,
} from '@material-ui/core';
import AccessTimeIcon from '@material-ui/icons/AccessTime';
import {
  UINumber,
  UIInputTime,
  UIInputCalendar,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import {
  Title,
  List,
  ListItem,
  RadioGroupWrapper,
  WrapperItem,
  DisplayTimeZone,
  SelectTimeWrapper,
  WrapperInputTime,
  Wrapper,
  Text,
} from './styles';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';

// Utils
import DateFnsUtils from '@date-io/date-fns';
import { addMessageToQueue } from '../../../utils/web/queue';
import { getLabelPortalTimeZone } from '../../../modules/Dashboard/utils';
import { deserializeDateTimeToUI, serializeDateTimeToAPI } from './utils';

// Constants
import { CATALOG_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';

const RadioGroup = ({
  name,
  isViewMode,
  row,
  label,
  options,
  value,
  enterValue,
  useEnterValueMode,
  onChange,
  defaultValue,
  radioGroupClass,
  fs,
  isHiddenTitleViewMode,
  isHiddenListStyle,
  catalogCode,
  styleGroup,
  disabled,
}) => {
  const handleChangeExtraEffect = data => {
    try {
      if (typeof onChange !== 'function') return;

      switch (catalogCode) {
        case CATALOG_CODE.LINE_RICH_MENU: {
          if (data === 'automation') {
            onChange(null, {
              value: data,
              enterValue: {
                ...(enterValue || {}),
                [data]: {
                  ...(enterValue[data] || {}),
                  date: new Date().getTime(),
                  time: new Date().getTime(),
                },
              },
            });
          }
          break;
        }
        default: {
          break;
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: 'app/components/Molecules/RadioGroup/index.jsx',
        func: 'handleChangeExtraEffect',
        data: {
          err: err.stack,
          data,
        },
      });
    }
  };

  const handleChange = (event, data) => {
    try {
      if (typeof onChange !== 'function') return;

      if (!useEnterValueMode) {
        onChange(event, data);
        return;
      }

      const temp = {
        value: data,
        enterValue: { ...enterValue, [data]: enterValue[data] || 0 },
      };

      onChange(event, temp);
      handleChangeExtraEffect(data);
    } catch (error) {
      addMessageToQueue({
        path: 'app/components/Molecules/RadioGroup/index.jsx',
        func: 'handleChange',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeDate = ({ inputName, valueItem, valueDate, currentTime }) => {
    try {
      const { date = new Date().getTime() } = serializeDateTimeToAPI({
        date: valueDate,
        time: currentTime,
      });
      const newValueItem = {
        ...valueItem,
        [inputName]: {
          ...((valueItem && valueItem[inputName]) || {}),
          date,
        },
      };

      if (typeof onChange === 'function') {
        onChange(null, {
          value,
          enterValue: newValueItem,
        });
      }

      const newStartTime = moment().set({
        hour: 24,
        minute: 1,
        second: 0,
        millisecond: 0,
      });

      onChangeTime({
        inputName,
        valueItem: newValueItem,
        valueTime: new Date(newStartTime),
        currentDate: date,
      });
    } catch (err) {
      addMessageToQueue({
        path: 'app/components/Molecules/RadioGroup/index.jsx',
        func: 'onChangeDate',
      });
    }
  };

  const onChangeTime = ({ inputName, valueItem, valueTime, currentDate }) => {
    try {
      let timeTmp = valueTime;

      if (isValidDate(valueTime)) {
        timeTmp = valueTime.getTime();
      } else {
        timeTmp = '';
      }

      const { time = new Date().getTime() } = serializeDateTimeToAPI({
        date: currentDate,
        time: timeTmp,
      });

      if (typeof onChange === 'function') {
        onChange(null, {
          value,
          enterValue: {
            ...valueItem,
            [inputName]: {
              ...((valueItem && valueItem[inputName]) || {}),
              time,
            },
          },
        });
      }
    } catch (err) {
      addMessageToQueue({
        path: '',
        func: 'onChangeTime',
        data: {
          err: err.stack,
          inputName,
          valueItem,
          valueTime,
        },
      });
    }
  };

  const renderEnterValueNode = ({ option = {}, valueItem = {} }) => {
    const { isEnterValue = false, enterValueType = '' } = option;
    if (!isEnterValue) return null;

    switch (enterValueType) {
      case 'datetime': {
        if (value !== option.value) return null;
        const disabledPastDate =
          ['displayType'].includes(name) &&
          [CATALOG_CODE.LINE_RICH_MENU].includes(catalogCode);

        /*
         * Sample format following portal timezone:
         *
         * Before serialize:
         *  {
         *    "date": "2024-03-29", -> string date
         *    "time": 1711590840000 -> milliseconds of date time,
         *  }
         *
         * After serialize:
         * {
         *  "date": 1711594440000,
         *  "time": 1711594440000
         * }
         * */
        const date = get(valueItem, `${option.value}.date`);
        const time = get(valueItem, `${option.value}.time`);
        const {
          date: newDate = new Date().getTime(),
          time: newTime = new Date().getTime(),
        } = deserializeDateTimeToUI({ dateTime: time });

        return (
          <Grid container>
            <Grid item sm="auto" className="mr-30">
              <SelectTimeWrapper style={{ marginBottom: 10 }}>
                <Wrapper>
                  <WrapperDisable
                    disabled={isViewMode || disabled}
                    className="min-w-170"
                  >
                    <UIInputCalendar
                      minDate={disabledPastDate && new Date()}
                      placeholder="MM/DD/YYYY"
                      isViewMode={isViewMode}
                      value={newDate}
                      onChange={output =>
                        onChangeDate({
                          inputName: option.value,
                          valueItem,
                          valueDate: output,
                          currentTime: time,
                        })
                      }
                      isShowLabel={false}
                    />
                  </WrapperDisable>
                </Wrapper>
                <Text style={{ marginTop: '10px' }}>at</Text>
                <WrapperInputTime>
                  <WrapperDisable disabled={isViewMode || disabled}>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <UIInputTime
                        margin="normal"
                        id="time-picker"
                        value={newTime}
                        isViewMode={isViewMode}
                        onChange={output =>
                          onChangeTime({
                            inputName: option.value,
                            valueItem,
                            valueTime: output,
                            currentDate: date,
                          })
                        }
                        KeyboardButtonProps={{
                          'aria-label': 'change time',
                        }}
                        keyboardIcon={<AccessTimeIcon />}
                      />
                    </MuiPickersUtilsProvider>
                  </WrapperDisable>
                </WrapperInputTime>
                <DisplayTimeZone style={{ marginTop: '10px' }}>
                  {getLabelPortalTimeZone()}
                </DisplayTimeZone>
              </SelectTimeWrapper>
            </Grid>
          </Grid>
        );
      }
      default: {
        return (
          <WrapperDisable disabled={option.disabled || disabled}>
            <UINumber
              type="number"
              min={0}
              value={valueItem[option.value] || 0}
              onChange={_value =>
                onChange(null, {
                  value,
                  enterValue: {
                    ...valueItem,
                    [option.value]: _value,
                  },
                })
              }
            />
          </WrapperDisable>
        );
      }
    }
  };

  const renderRadioGroup = () => {
    if (options.length) {
      return (
        <RadioGroupWrapper key={value} className="mb-0" fontSize={fs}>
          <MuiRadioGroup
            row={row}
            name={name}
            value={value}
            defaultValue={defaultValue}
            onChange={handleChange}
            style={{
              gap: useEnterValueMode ? '5px' : '0px',
              ...(styleGroup || {}),
            }}
            className={radioGroupClass}
          >
            {options.map(option => (
              <React.Fragment key={option.key}>
                {useEnterValueMode ? (
                  <WrapperItem $type={option.enterValueType}>
                    <FormControlLabel
                      style={{
                        marginLeft: '-8px',
                        color: '#000000',
                        ...(option.style || {}),
                      }}
                      key={option.key || Math.random()}
                      control={
                        <Radio
                          disabled={option.disabled || disabled}
                          size={option.size || 'small'}
                          color={option.color || 'primary'}
                          checked={option.checked}
                        />
                      }
                      label={option.label}
                      value={option.value}
                      data-test={option['data-test'] || 'radio'}
                    />
                    {renderEnterValueNode({ option, valueItem: enterValue })}
                  </WrapperItem>
                ) : (
                  <FormControlLabel
                    style={{
                      marginLeft: '-8px',
                      color: '#000000',
                      ...(option.style || {}),
                    }}
                    key={option.key || Math.random()}
                    control={
                      <Radio
                        disabled={option.disabled || disabled}
                        size={option.size || 'small'}
                        color={option.color || 'primary'}
                        checked={option.checked}
                      />
                    }
                    label={option.label}
                    value={option.value}
                    data-test={option['data-test'] || 'radio'}
                  />
                )}
              </React.Fragment>
            ))}
          </MuiRadioGroup>
        </RadioGroupWrapper>
      );
    }

    return null;
  };

  if (isViewMode) {
    const optionCheckedIndex = options.findIndex(
      option => option.value === value,
    );

    let optionCheckedLabel = '';

    if (optionCheckedIndex !== -1)
      optionCheckedLabel = options[optionCheckedIndex].label;

    return label ? (
      <>
        {!isHiddenTitleViewMode && <Title>{label}</Title>}
        {!isHiddenListStyle ? (
          <List style={{ paddingLeft: 4, margin: '2px 0' }}>
            <ListItem style={{ listStylePosition: 'inside' }}>
              <span style={{ color: '#000', fontSize: '12px' }}>
                {optionCheckedLabel}
              </span>
            </ListItem>
          </List>
        ) : (
          <span style={{ color: '#000', fontSize: '12px' }}>
            {optionCheckedLabel}
          </span>
        )}
      </>
    ) : (
      <span style={{ color: '#000', fontSize: '12px' }}>
        {optionCheckedLabel}
      </span>
    );
  }

  return label ? (
    <>
      <FormControl component="fieldset" data-test="radio-group">
        <Title>{label}</Title>
        {renderRadioGroup()}
      </FormControl>
    </>
  ) : (
    renderRadioGroup()
  );
};

RadioGroup.defaultProps = {
  label: '',
  options: [],
  value: '',
  enterValue: {},
  useEnterValueMode: false,
  onChange: () => {},
  radioGroupClass: '',
  isViewMode: false,
  isHiddenTitleViewMode: false,
  isHiddenListStyle: false,
  styleGroup: {},
  catalogCode: '',
};

RadioGroup.propTypes = {
  name: PropTypes.string,
  isViewMode: PropTypes.bool,
  enterValue: PropTypes.object,
  useEnterValueMode: PropTypes.bool,
  row: PropTypes.bool,
  label: PropTypes.string,
  styleGroup: PropTypes.any,
  catalogCode: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.any,
      label: PropTypes.string,
      checked: PropTypes.bool,
      disabled: PropTypes.bool,
      color: PropTypes.string,
      size: PropTypes.string,
    }),
  ),
  isHiddenTitleViewMode: PropTypes.bool,
  isHiddenListStyle: PropTypes.bool,
  defaultValue: PropTypes.any,
  value: PropTypes.any,
  onChange: PropTypes.func,
  fs: PropTypes.string,
  disabled: PropTypes.bool,
  radioGroupClass: PropTypes.string,
};

export default RadioGroup;

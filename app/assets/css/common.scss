@import './loading.scss';
@import './dropdown.scss';
@import './modal.scss';
@import './material.scss';
// @import './gird.scss';
@import './margin-padding.scss';
@import './alert.scss';
@import './theme.scss';

/** Demo use boostrap */
@import './bootstrap/style.scss';

:root {
  --rounded: 10px;
}

.style-container {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: auto;
}

.full-width,
.width-100 {
  width: 100% !important;
}
.width-430 {
  width: 430px !important;
}

.width-90 {
  width: 90% !important;
}
.full-height,
.height-100 {
  height: 100% !important;
}

.height-0 {
  height: 0 !important;
}
.height-fit {
  height: fit-content !important;
}
.color-blue {
  .MuiInputBase-input {
    color: rgb(0, 94, 184) !important;
  }
}
.min-width-100 {
  min-width: 100% !important;
}

.no-wrap {
  white-space: nowrap !important;
}
.txt-right {
  justify-content: flex-end !important;
}
.txt-right-format {
  text-align: right;
}
.txt-left {
  justify-content: flex-start !important;
}
.txt-center {
  justify-content: center !important;
}
.text-align-center {
  text-align: center !important;
}
.jc-center {
  justify-content: center;
}
.ai-center {
  align-items: center;
}
.box-margin-padding {
  padding: 12px !important;
  margin: -12px !important;
}

.tbl-border-left {
  // border-left: 1px solid #e6e6e6;
}
*:focus {
  outline: none;
}

.footer > button {
  font-weight: 600 !important;
}

a.title-have-url {
  color: rgb(0, 94, 184);
  font-family: Roboto-Bold;
  text-decoration: none;
}

a.title-have-url:hover {
  text-decoration: underline;
}

span.title-no-url {
  font-family: Roboto-Bold;
}

.MuiPopover-root.head-menu {
  .MuiPopover-paper {
    left: 0 !important;
  }
}

.padding-left-right-10 {
  padding: 0 10px !important;
}

.item-inline-deactive {
  color: inherit;
  text-decoration: line-through;
}

.item-removed {
  text-decoration: line-through;
  opacity: 0.5;
}

.is--text--error {
  font-size: 0.688rem;
  color: #f44336;
  margin-top: 4px;
}

.is--text--warn {
  font-size: 0.688rem;
  color: rgb(243, 163, 3);
}

// .d-flex {
//   display: flex;
// }
.pos-relative {
  position: relative;
}
.align-items-center {
  align-items: center;
}
.align-items-start {
  align-items: start !important;
}

.justify-content-start {
  justify-content: start;
}

.modal .modal-dialog {
  margin-bottom: 10%;
}

.modal-modify-column {
  .MuiDialog-paper {
    margin-bottom: 32px !important;
  }
  // .MuiDialog-paperScrollBody {
  //   margin-top: 32px !important;
  // }
}

.modal {
  display: flex !important;
  align-items: center;
}

.tox-tinymce-aux {
  .tox-notifications-container {
    .tox-notification--warning {
      display: none !important;
    }
  }
}

// .bs-top {
//   box-shadow: rgba(0, 0, 0, 0.2) 0 -0.375em 0.375em -0.375em !important;
// }

// .bs-right {
//   box-shadow: rgba(0, 0, 0, 0.2) 0.375em 0 0.375em -0.375em !important;
// }

// .bs-bottom {
//   box-shadow: rgba(0, 0, 0, 0.2) 0 0.375em 0.375em 0.375em !important;
// }

// .bs-left {
//   box-shadow: rgba(0, 0, 0, 0.2) -0.375em 0 0.375em -0.375em !important;
// }

.bs-all {
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0.375em 0px !important;
}
.bs-none {
  box-shadow: none !important;
}

// box-shadow: rgba(0,0,0,0.2) 0px 0px 0.375em 0px;

.border-top {
  border-top: 1px solid #e5e5e5 !important;
}
.border-left {
  border-left: 1px solid #e5e5e5 !important;
}
.border-right {
  border-right: 1px solid #e5e5e5 !important;
}
.border-bottom {
  border-bottom: 1px solid #e5e5e5 !important;
}

.border-all {
  border: 1px solid #e5e5e5 !important;
}

.overflow-auto {
  overflow: auto !important;
}
.overflow-hidden {
  overflow: hidden !important;
}
.height-form {
  height: 32px;
}

.bg-form {
  background-color: #fff;
}
.lowercase {
  text-transform: lowercase !important;
}

.uppercase {
  text-transform: uppercase !important;
}
.position-relative {
  position: relative;
}

.wrapper-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-right {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
}

.wrapper-text-overflow {
  width: 100%;
  white-space: nowrap;
}
$i: 5;
@while $i < 100 {
  $unit: '%';
  .width-#{$i} {
    width: $i#{$unit};
  }
  .height-#{$i} {
    height: $i#{$unit};
  }
  $i: $i + 5;
}

@each $type in (stretch, center, start, end) {
  .alg-#{$type} {
    align-items: $type;
  }
}
@each $type in (flex-start, flex-end, center, space-between) {
  .jus-con-#{$type} {
    justify-content: $type;
  }
}
.grid {
  display: grid;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.bottom-align {
  display: inline-block;
  height: 100%;
  vertical-align: bottom;
}

.small-font {
  font-size: 0.813rem !important;
}

.xlab-no-permission {
  opacity: 0.5;
  position: relative;
  button {
    cursor: not-allowed;
    pointer-events: none;
  }
}

.xlab-custom-linear-progress-with-parent {
  position: absolute !important;
  top: 0;
  width: 100%;
}

#app.hidden-menu-sidebar {
  .xlab-warpper-menu-sidebar {
    display: none !important;
  }
}

.icon-line-vertical {
  margin: 0 16px !important;
  font-size: 28px !important;
  color: #ccc !important;
  &.no-space {
    margin: 0 !important;
  }
  &.space-15 {
    margin: 0 15px !important;
  }
}

.journey-editing {
  aside.sidebar {
    display: none;
  }
  #main-content {
    margin: 0;

    .tab-box-shadow {
      nav.tab-box-shadow {
        display: none;
      }
      > div {
        > div {
          > div {
            margin-top: 0;
            > div.style-wrapper {
              height: calc(100vh - 81px);
            }
          }
        }
      }
    }
  }
}

#customized-menu-popover-dropdown {
  z-index: 100 !important;
}

.scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none; /* Chrome and Safari */
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.antsomi-modal-layer-2 {
  .antsomi-modal-mask {
    z-index: 3002 !important;
  }
  .antsomi-modal-wrap {
    z-index: 3003 !important;
  }
}

.antsomi-modal-root .antsomi-modal-wrap:has(.modal-custom) {
  z-index: 1300 !important;
}

iframe {
  border: none;
}

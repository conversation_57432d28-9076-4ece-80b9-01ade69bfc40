/* eslint-disable react/no-unused-prop-types */
/* eslint-disable spaced-comment */
/* eslint-disable no-useless-escape */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { useImmer } from 'use-immer';
import { connect } from 'react-redux';
import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
  UICheckbox,
  // UIButton as Button,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UITextField,
} from '@xlab-team/ui-components';

import parse from 'html-react-parser';

import UISelect from 'components/form/UISelectCondition';

import Grid from '@material-ui/core/Grid';

import BusinessObject from 'services/BusinessObject';
import { addNotification } from 'redux/actions';
import { getTranslateMessage } from 'containers/Translate/util';
import { MODULE_CONFIG as SEGMENT_MEMBER_LIST_MODULE_CONFIG } from 'modules/Dashboard/Profile/Segment/Detail/MemberList/config';

import { Radio, FormControlLabel } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';

import {
  ButtonStyle,
  ContainerCheckbox,
  ContainerCustomize,
  RadioGroupStyle,
  StyleModal,
  TitleWarnDownload,
  TitleWarnExport,
  WrapperLayout,
  WrapperUIModal,
  WrraperTextSmall,
} from './styled';
import TRANSLATE_KEY from '../../../messages/constant';

import {
  initState,
  NOTI,
  toEntryAPI,
  DatimeDefault,
  getColumnExport,
  getPerColumn,
  formatOptions,
} from './utils';
import { getItemAttributeDecryptFields } from '../../../utils/web/attribute';
import useToggle from '../../../hooks/useToggle';
import APP from '../../../appConfig';
import { getPortalId } from '../../../utils/web/cookie';
import XMLExportField from '../../XMLExportField';
import { Button, Checkbox, Flex, Modal, Select } from '@antscorp/antsomi-ui';
// import { LeftSide } from '../../ModifyColumn/CustomizeTable/_UI/ColumnSelection/styles';
// import { SignalCellularNullTwoTone } from '@material-ui/icons';

const MAP_TRANSLATE = {
  xmlExportedColTitle: getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'Include attribute available in CDP 365',
  ),
  titleSchema: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Schema'),
  actExportList: getTranslateMessage(
    TRANSLATE_KEY._BOX_TITL_EXPORT_DATA,
    'Export Data',
  ),
  actCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'Export'),
  titleExport: getTranslateMessage(
    TRANSLATE_KEY._TITL_PREPARE_YOUR_EXPORT_FILE,
    'Prepare your export file',
  ),
  titleDelimiter: getTranslateMessage(
    TRANSLATE_KEY._TITL_DELIMITER_TYPE,
    'Delimiter type',
  ),
  titleFormat: getTranslateMessage(
    TRANSLATE_KEY._TITL_FILE_FORMAT,
    'File format',
  ),
  titleFileName: getTranslateMessage(
    TRANSLATE_KEY._TITL_FILE_NAME,
    'File name',
  ),
  titlePageDownload: getTranslateMessage(
    TRANSLATE_KEY._TITL_PAGE_DOWNLOAD,
    'Page download',
  ),
  titleExportRow: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXPORTED_ROWS,
    'Exported rows',
  ),
  titleExportCol: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXPORTED_COLUMNS,
    'Exported columns',
  ),
  titleCompressFile: getTranslateMessage(
    TRANSLATE_KEY._TITL_COMPRESS_FILE,
    'Compress file',
  ),
  titleExportHistory: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_EXPORT_HISTORY,
    'Export History',
  ),
  noteFileName: getTranslateMessage(
    TRANSLATE_KEY._TITL_NOTE_EXPORT_FILE_NAME,
    'Use aiphabet letters (a-Z) & number (0-9) only',
  ),
  close: getTranslateMessage(TRANSLATE_KEY._ACT_CLOSE, 'Close'),
  titleIncludeRow: x =>
    getTranslateMessage(
      TRANSLATE_KEY._TITL_EXPORTED_INCLUDE_ROWS,
      `Include ${x} row(s) in your list`,
      {
        x,
      },
    ),
  titleFromPage: getTranslateMessage(
    TRANSLATE_KEY._TITL_FROM_PAGE,
    'From page',
  ),
  titleToPage: getTranslateMessage(TRANSLATE_KEY._TITL_TO_PAGE, 'to page'),
  exportOnly: getTranslateMessage(
    TRANSLATE_KEY._EXPORTED_COLUMNS_ONLY_INFO,
    'Only info being displayed',
  ),
};
const styleHeader = {
  color: '#005eb8',
  boxShadow: '0px 0px 3px grey',
};

function ModalExport(props) {
  const {
    isOpen,
    toggle,
    paging,
    sort,
    filters,
    perf_columns,
    durations,
    filterSegments,
    // decryptFields,
    itemTypeId,
    viewId,
    columns,
    // properties,
    object_type,
    // feService,
    // otherData,
    channelId,
    objectName,
    storyId,
    exportType,
    destinationId,
    moduleConfig = {},
    processId,
    sortDefault,
    id,
    ServiceFn = BusinessObject.dataTable.export,
  } = props;
  const [state, setState] = useImmer(initState({}));
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);

  useEffect(() => {
    if (isOpen) {
      const today = new Date();
      const placeHolderName = `${objectName}${
        exportType ? `_${exportType}` : ''
      }_${DatimeDefault(today)}`;

      let options = [...formatOptions];
      if (
        object_type !== 'BO_DATA_TABLE' &&
        (+itemTypeId === -1003 || +itemTypeId === -1007)
      ) {
        options = [
          ...formatOptions,
          {
            label: '.xml',
            value: 'xml',
            display: 'Extensible markup language (.xml)',
          },
        ];
      }

      if (
        [
          'ACTION_HISTORY',
          'SCHEDULE_HISTORY_PROCESS',
          'DELIVERY_LOG',
          'SCHEDULE_LOG_HISTORY_PROCESS',
          'ACTION_LOG_HISTORY_PROCESS',
        ].includes(object_type)
      ) {
        options = options.filter(option => option.value !== 'xlsx');
      }

      setState(draft => initState({ placeHolderName, options }));
      if (placeHolderName) {
        setState(draft => {
          const { isValidate } = draft.name.validate(placeHolderName);
          draft.name.isValidate = isValidate;
          draft.isValidate = draft.inputFields.every(
            each => draft[each].isValidate,
          );
        });
      }

      // console.log(state);
    }
  }, [isOpen]);

  const onCancel = () => {
    toggle(false);
  };

  const onClose = () => {
    toggleModalDownload();
  };

  const onApply = () => {
    setState(draft => {
      draft.isLoading = true;
    });

    const data = {
      fileName: state.name.value,
      fileFormat: state.fileFormat,
      deliterType: state.deliterType,
      compressFile: state.compressFile,
      exportedRows: paging.totalRecord,
      exportColumns: state.type,
      itemTypeId,
      filterSegments,
      paging,
      decryptFields: getItemAttributeDecryptFields(itemTypeId),
      sort,
      filters,
      durations,
      channelId,
      storyId,
      destinationId,
      // properties,
      columns: Array.isArray(columns)
        ? columns
        : getColumnExport(columns.column, columns.main, columns.type),
      object_type,
      perf_columns: perf_columns
        ? getPerColumn(perf_columns.column, perf_columns.main)
        : perf_columns,
      XMLExportField: state.exportXMLFlow,
      processId,
      sortDefault,
      viewId,
    };
    const params = { data: toEntryAPI(data) };
    if (id) {
      params.id = id;
    }
    ServiceFn(params).then(res => {
      setState(draft => {
        draft.isLoading = false;
      });
      if (res.data.code === 200) {
        toggle(false);
        toggleModalDownload();
      } else {
        const notification = NOTI.fail(res);
        props.addNotification(notification);
      }
    });
  };

  const onChangeFormat = value => {
    setState(draft => {
      draft.fileFormat = state.formatOptions.find(each => each.value === value);
    });
  };
  const onChangeDelimiter = value => {
    setState(draft => {
      draft.deliterType = state.optionDeliter.find(
        each => each.value === value,
      );
    });
  };
  const onChangeCompress = e => {
    setState(draft => {
      draft.compressFile = e.target.checked;
    });
  };
  const onChangeGroupType = e => {
    setState(draft => {
      draft.type = e.target.value;
    });
  };

  const onChangeValue = value => {
    let valueTmp = value;

    // Check first character === space, convert delete space
    if (valueTmp.charAt(0) === ' ') {
      valueTmp = `${valueTmp}`.replace(/[ ]/g, '');
    }

    // Delete character special to ''
    const replaceCharacterSpecial = `${valueTmp}`.replace(
      /[-|`*;&\/\\#,+()$~%.'":*?<>{}@^=![]|]/g,
      '',
    );

    // Conver space to '_'
    const replaceSpace = `${replaceCharacterSpecial}`.replace(/[ ]/g, '_');

    setState(draft => {
      draft.name.value = replaceSpace;
      const { error, isValidate } = draft.name.validate(replaceSpace);
      draft.name.isValidate = isValidate;
      draft.name.error = error;
      draft.isValidate = draft.inputFields.every(
        each => draft[each].isValidate,
      );
    });
    // console.log(state);
  };

  return (
    <>
      <StyleModal
        open={isOpen}
        onCancel={toggle}
        // style={{ overflow: 'hidden', zIndex: 1400 }}
        title={MAP_TRANSLATE.actExportList}
        okText={MAP_TRANSLATE.actExport}
        onOk={onApply}
        okButtonProps={{
          disabled:
            state.isLoading ||
            paging.totalRecord === 0 ||
            !state.isValidate ||
            (state.fileFormat.value === 'xml' &&
              !state.exportXMLFlow.isValidXMLSchema),
        }}
        data-test="modal-export"
      >
        <WrapperDisable disabled={paging.totalRecord === 0}>
          <Loading isLoading={state.isLoading} />
          <ContainerCustomize>
            <Grid container alignItems="center">
              <Grid item xs={4} className="title-name">
                <TitleWarnDownload>
                  {MAP_TRANSLATE.titleFileName}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8}>
                <UITextField
                  textFieldProps={{
                    size: 'small',
                    multiline: false,
                    rowsMax: 1,
                    className: 'width-100',
                    id: 'standard-basic',
                    error: !!state.name.error[0],
                    // label: 'Use aiphabet letters (a-Z) & number (0-9) only',
                  }}
                  value={state.name.value}
                  onChange={onChangeValue}
                  // maxLength={state.name.maxLength}
                  firstText={state.name.error}
                />
                <WrraperTextSmall
                  style={{ display: !state.isValidate ? 'none' : '' }}
                >
                  {MAP_TRANSLATE.noteFileName}
                </WrraperTextSmall>
              </Grid>
              <Grid item xs={4}>
                <TitleWarnDownload>
                  {MAP_TRANSLATE.titleFormat}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8}>
                <Select
                  options={state.formatOptions}
                  value={state.fileFormat}
                  onChange={onChangeFormat}
                />

                {state.fileFormat.value === 'xlsx' ? (
                  <WrraperTextSmall>
                    {getTranslateMessage(
                      TRANSLATE_KEY._TITL_NOTE_MAX_EXPORT,
                      '.xlsx file only allows exporting a maximum of 50,000 rows',
                    )}
                  </WrraperTextSmall>
                ) : (
                  <></>
                )}
              </Grid>

              {state.fileFormat.value === 'csv' ? (
                <>
                  <Grid item xs={4}>
                    <TitleWarnDownload>
                      {MAP_TRANSLATE.titleDelimiter}
                    </TitleWarnDownload>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      options={state.optionDeliter}
                      onChange={onChangeDelimiter}
                      value={state.deliterType}
                    />
                    {/* <UISelect
                      isSearchable={false}
                      use="tree"
                      options={state.optionDeliter}
                      value={state.deliterType}
                      onChange={onChangeDelimiter}
                    /> */}
                  </Grid>
                </>
              ) : null}

              {state.fileFormat.value === 'xml' ? (
                <>
                  <Grid item xs={4} className="title-schema">
                    <TitleWarnDownload>
                      {MAP_TRANSLATE.titleSchema}
                    </TitleWarnDownload>
                  </Grid>
                  <Grid item xs={8}>
                    <XMLExportField
                      objectName={objectName}
                      onChange={values =>
                        setState(draft => {
                          draft.exportXMLFlow = values;
                        })
                      }
                      // use case only for app/modules/Dashboard/Profile/Segment/Detail/MemberList/index.jsx
                      {...moduleConfig.key ===
                        SEGMENT_MEMBER_LIST_MODULE_CONFIG.key && {
                        customAttributes: [
                          {
                            name: 'Segment Id',
                            apply: props.segmentId,
                            value: props.segmentId,
                            status: 1,
                          },
                          {
                            name: 'Segment Name',
                            apply: props.segmentName,
                            value: props.segmentId,
                            status: 1,
                          },
                        ],
                      }}
                    />
                  </Grid>
                </>
              ) : null}

              <Grid item xs={4} />
              <Grid item xs={8}>
                <ContainerCheckbox>
                  <Checkbox
                    checked={state.compressFile}
                    onChange={onChangeCompress}
                  >
                    {MAP_TRANSLATE.titleCompressFile}
                  </Checkbox>
                </ContainerCheckbox>
              </Grid>
              <Grid item xs={4}>
                <TitleWarnDownload>
                  {MAP_TRANSLATE.titleExportRow}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8}>
                <TitleWarnDownload>
                  {MAP_TRANSLATE.titleIncludeRow(paging.totalRecord)}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={4} style={{ marginTop: '6px' }}>
                <TitleWarnDownload>
                  {MAP_TRANSLATE.titleExportCol}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8} style={{ marginTop: '6px' }}>
                {state.fileFormat.value === 'xml' ? (
                  <TitleWarnDownload>
                    {MAP_TRANSLATE.xmlExportedColTitle}
                  </TitleWarnDownload>
                ) : (
                  // <WrapperLayout>
                  //   <FormControl>
                  //     <RadioGroupStyle
                  //       onChange={onChangeGroupType}
                  //       value={state.type}
                  //       name="trigger-type"
                  //     >
                  //       <FormControlLabel
                  //         value="only_info"
                  //         control={
                  //           <Radio
                  //             color="primary"
                  //             size="small"
                  //             checked={state.type === 'only_info'}
                  //           />
                  //         }
                  //         label={
                  //           <span style={{ fontSize: 12 }}>
                  //             {getTranslateMessage(
                  //               TRANSLATE_KEY._EXPORTED_COLUMNS_ONLY_INFO,
                  //               'Only info being displayed',
                  //             )}
                  //           </span>
                  //         }
                  //       />
                  //     </RadioGroupStyle>
                  //   </FormControl>
                  // </WrapperLayout>
                  <TitleWarnDownload>
                    {MAP_TRANSLATE.exportOnly}
                  </TitleWarnDownload>
                )}
              </Grid>
            </Grid>
          </ContainerCustomize>
        </WrapperDisable>
      </StyleModal>

      <Modal
        data-test="modal-export-noti"
        open={isOpenModalDownload}
        title={getTranslateMessage(
          TRANSLATE_KEY._TITL_PROCESSING_YOUR_FILE,
          'Processing your file',
        )}
        footer={null}
        onCancel={onClose}
      >
        <Flex vertical gap={20}>
          <div>
            {parse(
              getTranslateMessage(
                TRANSLATE_KEY._WARN_EXPORT_PROCESSING,
                `The system is processing your file, please visit <a href ="${
                  APP.PREFIX
                }/${getPortalId()}/api-hub/export-history" target="_blank">Export History </a> to check the exporting status and download the file.<br><br>Also, a notification will be sent to you when the file exported`,
                {
                  x: `<a href="${
                    APP.PREFIX
                  }/${getPortalId()}/api-hub/export-history" target="_blank"  >${
                    MAP_TRANSLATE.titleExportHistory
                  }</a>`,
                },
              ),
            )}
          </div>
          <div>
            <Button data-test="btn-close-export-noti" onClick={onClose}>
              {MAP_TRANSLATE.close}
            </Button>
          </div>
        </Flex>
      </Modal>
    </>
  );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalExport.defaultProps = {
  isOpen: false,
  otherData: {},
};

ModalExport.propTypes = {
  isOpen: PropTypes.bool,
  otherData: PropTypes.object,
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalExport);

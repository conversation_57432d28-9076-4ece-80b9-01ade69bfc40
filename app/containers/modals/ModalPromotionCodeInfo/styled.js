import styled from 'styled-components';
import colors from 'utils/colors';
import { UIModal } from '@xlab-team/ui-components';

export const WrapperInfo = styled.div`
  margin: 15px 15px;
  :first-child {
    margin-top: 10px;
  }
`;

export const WrapperContent = styled.main`
  padding: 20px;
  height: 100%;

  display: flex;
  flex-direction: column;
  gap: 15px;
`;

export const InfoTitle = styled.p`
  font-size: 16px;
  font-weight: bold;
  color: ${colors.primary};
  margin: 0;
`;

export const TableRelative = styled.div`
  position: relative;
  flex-grow: 1;
  margin-top: 8px;
`;

export const TableWrapper = styled.div`
  position: absolute;
  width: 100%;
  // height: 235px;
  /* height: 325px; */
  // min-height: 145px;
  // max-height: 235px;
  top: 0;
  left: 0;
  .table {
    .header {
      .tr {
        .th:first-child {
          border-right: 2px solid ${colors.platinum} !important;
        }
        .th {
          font-weight: bold;
          &.status {
            text-indent: 8px;
          }
        }
      }
    }
    .body {
      .tr {
        .td:first-child {
          border-right: 2px solid ${colors.platinum};
        }
        .td {
          &.align-right {
            text-align: right;
          }
        }
      }
    }
  }
`;

export const WrapperHeader = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 18px 16px;
`;

export const WrapperIconCancel = styled.div`
  display: flex;
  align-items: center;
  margin-right: 5px;
  cursor: pointer;
  color: #7f7f7f;
`;

export const TitlePreview = styled.div`
  font-size: 16px;
  font-weight: 600;
`;

export const UIModalStyle = styled(UIModal)`
  .MuiDialog-paper {
    margin: 0px;
  }
`;

/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';
import parse from 'html-react-parser';

import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
  UIButton as Button,
} from '@xlab-team/ui-components';

// import ModalHeader from 'components/Molecules/ModalHeader/index';
import { UITextField } from '@xlab-team/ui-components';
import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter/index';
import { addNotification } from '../../../redux/actions';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { initState, NOTI } from './utils';
import { MAX_LNAME } from '../../../utils/common';
import {
  getUntitledCopyName,
  getUntitledName,
} from '../../../utils/web/properties';
import { useHistory } from 'react-router-dom';
import APP from '../../../appConfig';
import { getPortalId } from '../../../utils/web/cookie';
import { ModalV2 } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  actClone: getTranslateMessage(TRANSLATE_KEY._ACT_MAKE_A_COPY, 'Make a copy'),
  actCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  actApply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Apply'),
  untitledSegment: getTranslateMessage(
    TRANSLATE_KEY._UNTITLED_SEGMENT,
    'Untitled Segment',
  ),
};

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;
export const ContainerCustomize = styled.div`
  display: flex;
  flex-direction: column;
  /* padding: 1rem 2rem; */
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;

function ModalClone(props) {
  const [state, setState] = useImmer(initState());
  const {
    label,
    placeHolderName,
    activeRow,
    isOpenModal,
    nameKey,
    mapParamsFn,
    ObjectServicesFn,
    cloneUrl,
    onAfterApply,
    // defaultName,
  } = props;
  const history = useHistory();

  useEffect(() => {
    if (isOpenModal) {
      const defaultName = getUntitledCopyName(activeRow[nameKey]);
      // console.log('ModalClone', props);
      setState(draft => {
        // const extendString = ' - cloned';
        // const extendStringLength = extendString.length; // TH: origin name == 255 + clone > 255
        // draft.name.value = `${activeRow[nameKey].substring(
        //   0,
        //   MAX_LNAME - extendStringLength,
        // )}${extendString}`;
        draft.name.value = defaultName;
        draft.isValidate = true;
      });
    }
  }, [isOpenModal]);

  const onChangeValue = value => {
    setState(draft => {
      draft.name.value = value;
      const { error, isValidate } = draft.name.validate(value);
      draft.name.isValidate = isValidate;
      draft.name.error = error;
      draft.isValidate = draft.inputFields.every(
        each => draft[each].isValidate,
      );
    });
  };

  const onCancel = () => {
    setState(() => initState());
    props.setOpenModal(false);
  };
  // console.log({ activeRow });

  const onApply = () => {
    // history.push(
    //   `${APP.PREFIX}/${getPortalId()}/profile/segments/create/${
    //     activeRow.item_type_id
    //   }?copyId=${activeRow.id}&name=${window.encodeURIComponent(
    //     state.name.value,
    //   )}`,
    // );
    history.push(
      `${cloneUrl}&name=${window.encodeURIComponent(state.name.value)}`,
    );

    props.setOpenModal(false);

    onAfterApply();

    // if (!ObjectServicesFn) {
    //   const notification = NOTI.serviceNotFound();
    //   props.addNotification(notification);
    // } else {
    //   setState(draft => {
    //     draft.isLoading = true;
    //   });
    //   const params = mapParamsFn(activeRow, state) || { data: {} };
    //   ObjectServicesFn(params).then(res => {
    //     setState(draft => {
    //       draft.isLoading = false;
    //     });
    //     if (res.code === 200) {
    //       const notification = NOTI.success();
    //       props.addNotification(notification);
    //       // const itemTypeId = res.data[0];
    //       // ${itemTypeId}
    //       history.push(
    //         `${APP.PREFIX}/${getPortalId()}/profile/segments/create/${
    //           activeRow.item_type_id
    //         }?copyId=${res.data[0]}`,
    //         // &name=${window.encodeURIComponent(
    //         //   state.name.value,
    //         // )}`,
    //       );
    //       // props.fetchData();
    //       props.setOpenModal(false);
    //     } else {
    //       const notification = NOTI.fail(res);
    //       props.addNotification(notification);
    //     }
    //   });
    // }
  };

  return (
    <ModalV2
      open={props.isOpenModal}
      title={label}
      okText={MAP_TITLE.actClone}
      cancelText={MAP_TITLE.actCancel}
      onOk={onApply}
      okButtonProps={{ disabled: !state.isValidate || state.isLoading }}
      onCancel={onCancel}
      zIndex={1400}
      styles={{ header: { marginBottom: 0 }, footer: { marginTop: 0 } }}
      centered
      // closeIcon={null}
    >
      <ContainerCustomize>
        <div style={{ paddingBottom: '1rem' }}>
          {parse(
            getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_CLONE,
              `A new item, inheriting setups of the original item, will be created.
            This impact nothing on the original one Please name the new item and
            click save.`,
            ),
          )}
        </div>
        <UITextField
          textFieldProps={{
            autoFocus: true,
            size: 'small',
            multiline: true,
            rowsMax: 4,
            className: 'width-100',
            id: 'standard-basic',
            error: !!state.name.error[0],
            label: placeHolderName,
          }}
          value={state.name.value}
          onChange={onChangeValue}
          maxLength={state.name.maxLength}
          firstText={state.name.error}
        />
      </ContainerCustomize>
    </ModalV2>
  );

  // return (
  //   <ModalContext
  //     isOpenModal={props.isOpenModal}
  //     handleCloseModal={props.setOpenModal}
  //     size="normal"
  //   >
  //     <ModalHeader title={label} />
  //     <ContainerCustomize>
  //       <div style={{ paddingBottom: '1rem' }}>
  //         {getTranslateMessage(
  //           TRANSLATE_KEY._USER_GUIDE_CLONE,
  //           `A new item, inheriting setups of the original item, will be created.
  //           This impact nothing on the original one Please name the new item and
  //           click save.`,
  //         )}
  //       </div>
  //       <UITextField
  //         textFieldProps={{
  //           autoFocus: true,
  //           size: 'small',
  //           multiline: true,
  //           rowsMax: 4,
  //           className: 'width-100',
  //           id: 'standard-basic',
  //           error: !!state.name.error[0],
  //           label: placeHolderName,
  //         }}
  //         value={state.name.value}
  //         onChange={onChangeValue}
  //         maxLength={state.name.maxLength}
  //         firstText={state.name.error}
  //       />
  //     </ContainerCustomize>
  //     <ModalFooterStyle className="p-y-4">
  //       <ButtonStyle  theme="outline" onClick={onCancel}>
  //         {MAP_TITLE.actCancel}
  //       </ButtonStyle>
  //       <ButtonStyle
  //
  //         // color="secondary"
  //         onClick={onApply}
  //         disabled={!state.isValidate || state.isLoading}
  //       >
  //         {MAP_TITLE.actClone}
  //       </ButtonStyle>
  //     </ModalFooterStyle>
  //   </ModalContext>
  // );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    // cloneSegment: params => {
    //   dispatch()
    // }
  };
}

ModalClone.propTypes = {
  isOpenModal: PropTypes.bool,
  setOpenModal: PropTypes.func,
  label: PropTypes.string,
  placeHolderName: PropTypes.string,
  ObjectServicesFn: PropTypes.any,
  nameKey: PropTypes.string,
  addNotification: PropTypes.func,
  fetchData: PropTypes.func,
  activeRow: PropTypes.object,
  mapParamsFn: PropTypes.func,
  onAfterApply: PropTypes.func,
};
ModalClone.defaultProps = {
  isOpenModal: false,
  setOpenModal: () => {},
  label: 'Label',
  placeHolderName: 'new name',
  fetchData: () => {},
  ObjectServicesFn: () => {},
  nameKey: '',
  activeRow: {},
  onAfterApply: () => {},
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalClone);

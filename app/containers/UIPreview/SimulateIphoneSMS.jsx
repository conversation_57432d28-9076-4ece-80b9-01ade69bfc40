/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React from 'react';
import styled from 'styled-components';
import iphoneImg from 'assets/images/simulator/simulate-sms.png';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { replaceNbsps } from './utils';
import {
  makeSelectDashboardNetworkInfo,
  makeSelectPersonalizations,
} from '../../modules/Dashboard/selector';
import { CATALOG_CODE } from './constants';

const P = styled.pre`
  font-family: 'Roboto', 'Open Sans', 'Helvetica Neue', 'Helvetica', 'Arial',
    'sans-serif';
  font-size: 12px;
  white-space: pre-line;
  margin: 0rem;
`;

const Wrapper = styled.div`
  position: absolute;
  top: 0;
  left: 50px;
  padding: 7px 15px;
  width: 310px;
  min-height: 50px;
  background-color: #ebebeb;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  box-shadow: 2px 3px 8px -5px #000;
  &:before {
    bottom: -2px;
    width: 17px;
    height: 17px;
    left: -10px;
    position: absolute;
    background-color: #ebebeb;
    display: inline-block;
    vertical-align: text-top;
    content: '';
    clip-path: polygon(99% 0%, 0% 93%, 3% 96%, 7% 98%, 11% 99%, 100% 85%);
  }
`;

const WrapperSMSIphone = styled.div`
  position: relative;
  padding: 15px 0;
  overflow: hidden;
  height: 700px;
  width: fit-content;
  margin: auto;

  .phone-background {
    position: relative;
    width: 424px;
    /* z-index: 5; */
  }
`;

const ContainTextWrapper = styled.div`
  height: 520px;
  width: 100%;
  top: 180px;
  position: absolute;
  overflow: auto;
  z-index: 1;
`;

const ContainTextWrapperBackground = styled.div`
  position: absolute;
  top: 150px;
  left: 10px;
  right: 10px;
  bottom: 0;
  background: #fff;
`;

const ContainInfoNetwork = styled.div`
  position: absolute;
  width: 100%;
  top: 95px;
  z-index: 9;

  .logo-cotain {
    width: 100%;
    display: flex;
    justify-content: center;
    .logo {
      width: 30px;
      border-radius: 50%;
      background-color: #fff;
      border: 1px solid #dddd;
    }
  }

  .portal-name {
    font-size: 12px;
    text-align: center;
    font-weight: bold;
    margin-top: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

const SimulateIphoneSMS = props => {
  const { catalogCode, data, networkInfo } = props;
  let { _CHANNEL_SMS_MESSAGE, _CHANNEL_SMS_CONTENT } = data;
  const { logo, networkName, subLogo } = networkInfo;
  const {
    settings: { personalizationData = {} },
  } = props.personalizations || {};

  if (!_CHANNEL_SMS_MESSAGE && !_CHANNEL_SMS_CONTENT) {
    Object.keys(data).forEach(key => {
      if (data[key] && data[key].value && typeof data[key].value === 'string') {
        _CHANNEL_SMS_MESSAGE = data[key].value;
      }
    });
  }

  return (
    <WrapperSMSIphone>
      <ContainInfoNetwork>
        <div className="logo-cotain">
          <img alt="img" className="logo" src={subLogo} />
        </div>
        <div className="portal-name">
          {networkName}
          <span
            style={{ fontSize: 16, color: '#8888' }}
            className="icon-xlab-arrow-right"
          />
        </div>
      </ContainInfoNetwork>
      <ContainTextWrapperBackground />
      <ContainTextWrapper className="scrollbar-hidden">
        <Wrapper>
          <P>
            <P>
              {catalogCode === CATALOG_CODE.ONEWAY_SMS ? 'RM0.00 ' : ''}
              {_CHANNEL_SMS_MESSAGE
                ? replaceNbsps(_CHANNEL_SMS_MESSAGE, personalizationData)
                : replaceNbsps(_CHANNEL_SMS_CONTENT, personalizationData)}
            </P>
          </P>
        </Wrapper>
      </ContainTextWrapper>
      <img className="phone-background" src={iphoneImg} />
    </WrapperSMSIphone>
  );
};

const mapStateToProps = createStructuredSelector({
  networkInfo: makeSelectDashboardNetworkInfo(),
  personalizations: makeSelectPersonalizations(),
});

export default connect(
  mapStateToProps,
  null,
)(SimulateIphoneSMS);

/* eslint-disable react/prop-types */
// import Grid from '@material-ui/core/Grid';
import React, { useMemo, useEffect } from 'react';
import { Button, ModalV2, Space } from '@antscorp/antsomi-ui';
import { getTranslateMessage } from 'containers/Translate/util';
import _isEqual from 'lodash/isEqual';
import TRANSLATE_KEY from 'messages/constant';
import { getPortalId } from 'utils/web/cookie';
import { useHistory } from 'react-router-dom';
import { buildDisableHours } from 'components/Organisms/CalendarTable/utils';
import { validateComputeScheduleInHour } from 'components/Organisms/ComputationSchedule/utils';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { MODULE_CONFIG } from 'modules/Dashboard/Profile/Segment/Create/config';
import APP from '../../../../appConfig';
import useToggle from '../../../../hooks/useToggle';
import { StyleFooter } from './styled';
import makeSelectCreateSegment from '../../../../modules/Dashboard/Profile/Segment/Create/selectors';
import { makeSelectUseSegmentIntoAnother } from '../../../Drawer/DrawerSegment/selectors';
import { toAPI } from '../../../../modules/Dashboard/Profile/Segment/Create/utils';
import { isModifiedRules } from '../../util';
import DrawerInsightExplore from '../../../Drawer/DrawerInsightExplore';
import { safeParse } from '../../../../utils/common';
import useUpdateEffect from '../../../../hooks/useUpdateEffect';
import { BUTTON_COLLECTION_SAVE_ID } from '../../../Drawer/DrawerCreateCollection/constants';
import { createPortal } from 'react-dom';
import { JOURNEY } from '../../../Drawer/DrawerSegment/constants';
import { useSegmentJourneyDone } from '../../../Drawer/DrawerSegment/hooks';
import { updateDone } from '../../../../redux/actions';

const labelCancel = getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel');
const labelBuild = getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save');
const labelExplore = getTranslateMessage(
  TRANSLATE_KEY._ACT_EXPLORER,
  'Explore (beta)',
);

const PREFIX_SEG_CREATE = MODULE_CONFIG.key;

function Footer(props) {
  const MAP_TITLE = {
    titleWarningSegment: getTranslateMessage(
      TRANSLATE_KEY._WARN_IMPORT_SEGMENT,
      `Data of the segment will be ready in up to 3 computed hours`,
    ),
    actClose: getTranslateMessage(TRANSLATE_KEY._ACT_CLOSE, 'Close'),
    editSegmentEnabled:
      'The segment has edit success. Do you want to computed immediately?',
    editSegmentDisabled:
      'If you click on Yes button, this segment will be enabled',
  };

  const {
    use = 'segment',
    computeSchedule,
    cacheComputeSchedule,
    mappingAttributes,
    isSegment,
    isSegmentOption1,
    type,
    setIsOpenSegmentJourney,
    boUploadData,
    isJourneySuccess,
    importType,
    onSaveFirstStep,
    segment,
    reducer,
    isEditSegment,
    isShowCancel = true,
    isDisplayExplore = false,
    hasEditPermission = false,
    activeRowCurrentVersion = {},
    modalCalendarTable = {},
    activeRow,
    onChangeErrors = () => {},
    styleFooter = {},
    useSegmentInAnother = '',
  } = props;

  const { main, configure } = reducer;
  const { design } = main;
  const dataToAPI = toAPI(
    main,
    configure,
    design,
    'segment',
    true,
    true,
    mappingAttributes,
    undefined,
    computeSchedule,
    importType,
  );
  const excludeCheckedModified = [
    'activeId',
    'isBuild',
    'itemTypeId',
    'description',
    'alertSetting',
    'segmentDisplay',
    'status',
    'importId',
    'importType',
  ];
  const isModified = isModifiedRules(
    activeRow,
    Object.keys(activeRowCurrentVersion).length > 0
      ? activeRowCurrentVersion
      : dataToAPI,
    {
      excludeProperty: excludeCheckedModified,
    },
  );
  // console.log({ isSegment, activeRow, use });

  const [isOpenModalProcessing, toggleModalProcessing] = useToggle(false);
  const [isOpenModalEditSegment, toggleModalEditSegment] = useToggle(false);
  const isCheckCustomerOrUserIdAttribute = useMemo(
    () =>
      mappingAttributes &&
      mappingAttributes.some(item =>
        ['user_id', 'customer_id'].includes(item.currentValue.itemPropertyName),
      ),
    [mappingAttributes],
  );
  const history = useHistory();
  const { updateSegmentDone } = useSegmentJourneyDone();

  useEffect(() => {
    if (!props.disabled && isJourneySuccess) {
      if (
        mappingAttributes &&
        mappingAttributes.length === 1 &&
        isCheckCustomerOrUserIdAttribute &&
        isSegment
      ) {
        history.push(`${APP.PREFIX}/${getPortalId()}/profile/segments`);
      } else {
        toggleModalProcessing();
      }
    }
  }, [isJourneySuccess, props.disabled]);

  useUpdateEffect(() => {
    if (props.disabled) return;

    if (props.isValidTotalLimit) {
      onSave();
    }
  }, [props.isValidTotalLimit, props.disabled]);

  const onSave = () => {
    if (!props.isValidTotalLimit && props.design === 'create') {
      props.callback('VALIDATE_TOTAL_LIMIT', {
        type: safeParse(computeSchedule.type, ''),
      });
      return;
    }

    // validate computeSchedule
    if (
      (props.design === 'create' && computeSchedule.type === 'dynamic') ||
      (props.design === 'update' &&
        computeSchedule.type === 'dynamic' &&
        !_isEqual(cacheComputeSchedule, computeSchedule))
    ) {
      const {
        dataEventsGroupByDate,
        timeRange,
        limitHour,
      } = modalCalendarTable;

      // rebuild disableHours for current settings
      const disableTimes = buildDisableHours({
        computeSchedule,
        dataEventsGroupByDate,
        timeRange,
        limitHour,
      });

      const scheduleValidate = validateComputeScheduleInHour({
        computeSchedule,
        disableTimes,
        timeRange,
      });

      if (scheduleValidate === false) {
        onChangeErrors({
          name: 'limit_schedule',
          message: getTranslateMessage(
            TRANSLATE_KEY._004,
            `In this hour, the limit on the number of ${
              use === 'collection' ? 'collections' : 'segments'
            } to be built has been reached.`,
          ),
        });
        return;
      }
    }

    if (
      (mappingAttributes &&
        mappingAttributes.length !== 0 &&
        mappingAttributes.findIndex(
          item => item.headerValue.headerIndex === -1,
        ) === -1) ||
      type !== 2
    ) {
      if (isSegmentOption1) {
        if (
          computeSchedule &&
          computeSchedule.type === 'dynamic' &&
          computeSchedule &&
          computeSchedule.repeatType !== 'none'
        ) {
          if (setIsOpenSegmentJourney) {
            setIsOpenSegmentJourney(true);
          }
        } else {
          props.callback('SAVE_SEGMENT', {
            design: props.design,
            isBuild: true,
          });
        }
      } else if (
        computeSchedule.type === 'static' &&
        props.design === 'create'
      ) {
        props.callback('SAVE_SEGMENT', {
          design: props.design,
          importId: boUploadData && boUploadData.importId,
          mappingAttributes: boUploadData && boUploadData.mappingAttributes,
          isBuild: true,
          isTypeApiV21: true,
          importType,
        });
      } else if (
        mappingAttributes &&
        mappingAttributes.length === 1 &&
        isCheckCustomerOrUserIdAttribute &&
        isSegment
      ) {
        if (type === 2) {
          props.callback('SAVE_SEGMENT', {
            design: props.design,
            importId: boUploadData && boUploadData.importId,
            mappingAttributes: boUploadData && boUploadData.mappingAttributes,
            isBuild: true,
            isTypeApiV21: true,
            importType,
          });
        } else {
          // eslint-disable-next-line no-unused-expressions
          onSaveFirstStep && onSaveFirstStep();
          props.callback('SAVE_SEGMENT', {
            design: props.design,
            importId: boUploadData && boUploadData.importId,
            // mappingAttributes: boUploadData && boUploadData.mappingAttributes,
            isBuild: true,
            // importType,
          });
        }
      } else {
        // eslint-disable-next-line no-lonely-if
        if (type === 2) {
          if (
            ((computeSchedule && computeSchedule.type === 'static') ||
              (computeSchedule && computeSchedule.repeatType === 'none')) &&
            props.design === 'update'
          ) {
            props.callback('SAVE_SEGMENT', {
              design: props.design,
              isTypeApiV21: true,
              importType,
              isBuild: true,
            });
          } else if (isModified && props.design === 'update' && isEditSegment) {
            toggleModalEditSegment();
          } else {
            props.callback('SAVE_SEGMENT', {
              design: props.design,
              importId: boUploadData && boUploadData.importId,
              mappingAttributes: boUploadData && boUploadData.mappingAttributes,
              isBuild:
                computeSchedule.type === 'dynamic' &&
                computeSchedule.repeatType === 'none',
              isTypeApiV21: true,
              importType,
            });
          }
        } else if (
          ((computeSchedule && computeSchedule.type === 'static') ||
            (computeSchedule && computeSchedule.repeatType === 'none')) &&
          props.design === 'update'
        ) {
          props.callback('SAVE_SEGMENT', {
            design: props.design,
            isTypeApiV21: true,
            importType,
            isBuild: true,
          });
        } else if (isModified && props.design === 'update' && isEditSegment) {
          toggleModalEditSegment();
        } else {
          // eslint-disable-next-line no-unused-expressions
          onSaveFirstStep && onSaveFirstStep();
          props.callback('SAVE_SEGMENT', {
            design: props.design,
            importId: boUploadData && boUploadData.importId,
            // mappingAttributes: boUploadData && boUploadData.mappingAttributes,
            isBuild:
              computeSchedule.type === 'dynamic' &&
              computeSchedule.repeatType === 'none',
            // importType,
          });
        }
        // toggleModalProcessing()
      }
    } else {
      props.callback('SAVE_SEGMENT', { design: props.design });
    }
  };

  const onCancel = () => {
    props.callback('ON_CANCEL', true);
  };

  const onCloseModalStepMode = () => {
    toggleModalProcessing();

    if (useSegmentInAnother === JOURNEY) {
      updateSegmentDone();
    } else {
      props.dispatchActionSegmentDone({ design: props.design });
    }
  };

  const handleEditSegment = (isConfirrm = false) => {
    if (type === 2) {
      props.callback('SAVE_SEGMENT', {
        design: props.design,
        isTypeApiV21: true,
        importType,
        isBuild: isConfirrm,
      });
    } else {
      props.callback('SAVE_SEGMENT', {
        design: props.design,
        isBuild: isConfirrm,
      });
    }
    toggleModalEditSegment();
  };

  const onCancleEditSegment = () => {
    handleEditSegment();
  };

  const onConfirmEditSegment = () => {
    handleEditSegment(true);
  };

  const renderButtonSave = () => {
    const buttonSaveEle = document.getElementById(BUTTON_COLLECTION_SAVE_ID);

    if (buttonSaveEle) {
      return createPortal(
        <Button
          data-test="save-segment-button"
          disabled={
            props.disabledFooter ||
            props.disabled ||
            props.isDoing ||
            parseInt(activeRow.status) === 4 ||
            (design !== 'create' && !hasEditPermission)
          }
          type="primary"
          onClick={onSave}
        >
          <span className="">{labelBuild}</span>
        </Button>,
        buttonSaveEle,
      );
    }
    return null;
  };

  if (props.isUIV2) {
    return renderButtonSave();
  }

  return (
    <>
      <StyleFooter style={styleFooter}>
        {isDisplayExplore && (
          <DrawerInsightExplore
            use="button"
            labelExplore={labelExplore}
            activeRow={props.activeRow}
            segment={props.segment}
            configure={props.configure}
            design={props.design}
          />
        )}
        {isShowCancel && (
          <Button type="outline" onClick={onCancel} className="m-x-2">
            <span className="" style={{ color: '#005fb8', fontSize: '12px' }}>
              {labelCancel}
            </span>
          </Button>
        )}

        <Button
          disabled={
            props.disabledFooter ||
            props.disabled ||
            props.isDoing ||
            parseInt(activeRow.status) === 4 ||
            (design !== 'create' && !hasEditPermission)
          }
          data-test="save-segment-button"
          type="primary"
          onClick={onSave}
        >
          <span className="">{labelBuild}</span>
        </Button>
      </StyleFooter>
      <ModalV2
        keyboard={false}
        destroyOnClose
        open={isSegment && isOpenModalProcessing}
        title="Warning"
        cancelText={MAP_TITLE.actClose}
        onCancel={onCloseModalStepMode}
        footer={
          <Button onClick={onCloseModalStepMode}>{MAP_TITLE.actClose}</Button>
        }
      >
        {MAP_TITLE.titleWarningSegment}
      </ModalV2>
      <ModalV2
        open={isOpenModalEditSegment}
        onCancel={toggleModalEditSegment}
        title="Edit success"
        footer={
          <Space>
            <Button onClick={onCancleEditSegment}>No</Button>
            <Button type="primary" onClick={onConfirmEditSegment}>
              Yes
            </Button>
          </Space>
        }
      >
        {MAP_TITLE.editSegmentEnabled}
        {segment && segment.status === 2 && (
          <div>{MAP_TITLE.editSegmentDisabled}</div>
        )}
      </ModalV2>
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  reducer: makeSelectCreateSegment(),
  useSegmentInAnother: makeSelectUseSegmentIntoAnother(),
});
const mapDispatchToProps = dispatch => {
  return {
    dispatchActionSegmentDone: payload => {
      dispatch(
        updateDone(`${PREFIX_SEG_CREATE}@@ACTION_SEGMENT_DONE@@`, payload),
      );
    },
  };
};

Footer.defaultProps = {
  design: 'create',
  styleFooter: {},
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Footer);

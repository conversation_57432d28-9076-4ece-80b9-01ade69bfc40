/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-param-reassign */
import { useEffect, useCallback } from 'react';
// import _isEmpty from 'lodash/isEmpty';
import { useImmer } from 'use-immer';
import { OrderedMap } from 'immutable';

import SegmentServices from 'services/Segment';
import MetaDataServices from 'services/MetaData';
import {
  toEntryFE,
  getLookupRefineWithProperties,
  toRefinePropertiesUI,
} from './utils';
import { isEmpty } from 'lodash';
import { useDeepCompareEffect } from '../../../../../../../hooks/useDeepCompareEffect';
import { mapItemEventProperty } from '../../../../../../../utils/web/map';

// import { initState, toEntryFE, NO_DATA } from '../ItemRule/Preview/utils';
// const NO_DATA = 'No data.';

const initState = {
  isLoading: true,
  group: {
    list: [],
    map: {},
  },
  conditions: OrderedMap(),
  dataLookup: {
    list: [],
    map: {}
  }
};

export const useFetchDataByEvent = (
  eventValue = null,
  isInit,
  backupRefineWithProperties,
  moduleConfig = {},
  sourcesSelected = [],
) => {
  const [state, setState] = useImmer(initState);
  const setStateCommon = object => {
    setState(draft => {
      Object.keys(object).forEach(key => {
        draft[key] = object[key];
      });
    });
  };

  const fetchDataAPI = useCallback(
    (type, value, dataSources) => {
      setState(draft => {
        draft.isLoading = true;
      });

      SegmentServices.fetch[type](
        value,
        moduleConfig.objectType,
        dataSources,
      ).then(response => {
        let data = {
          list: [],
          map: {},
        };
        let dataLookup = {
          list: [],
          map: {},
        }
        if (response.code === 200) {
          data = toEntryFE(response.data, type);
          dataLookup = mapItemEventProperty(response.data);
        }

        // const isInit = false;
        if (isInit) {
          const eventIds = getLookupRefineWithProperties(
            backupRefineWithProperties,
          );
          const dataSourceID = '-1';
          const params = {
            inputUrl: `${dataSourceID}/${eventValue.eventCategoryId}/${eventValue.eventActionId
              }`,
            dataPost: {
              eventIds,
            },
          };
          MetaDataServices.eventProperty.lookupByIds(params).then(res2 => {
            const conditions = toRefinePropertiesUI(
              backupRefineWithProperties,
              data.map,
              res2.map,
              dataLookup.map,
            );
            setStateCommon({
              group: data,
              isLoading: false,
              conditions,
              dataLookup,
            });
            // setState(draft => {
            //   draft.group = data;
            //   draft.isLoading = false;
            //   draft.conditions = conditions;
            // });
          });
        } else {
          const newConditions = isEmpty(backupRefineWithProperties)
            ? OrderedMap()
            : state.conditions;
          setStateCommon({
            group: data,
            isLoading: false,
            conditions: newConditions,
          });
          // setState(draft => {
          //   draft.group = data;
          //   draft.isLoading = false;
          // });
        }
      });
    },
    [eventValue],
  );

  useDeepCompareEffect(() => {
    if (eventValue !== null) {
      // console.log('eventValue', eventValue);
      fetchDataAPI('eventProperties', eventValue, sourcesSelected);
    }
  }, [eventValue, sourcesSelected]); // no more warning
  return { ...state };
};

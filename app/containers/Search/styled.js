import Popover from '@material-ui/core/Popover';
import styled, { css } from 'styled-components';
import colors from 'utils/colors';
import { fonts } from 'utils/variables';

export const PopoverWrapper = styled(Popover)`
  .MuiPopover-paper {
    overflow-y: hidden;
    max-width: 300px;
    /* max-height: 262px; */
    min-width: 300px;
    border-radius: 10px;
    /* min-height: 262px; */
  }
`;

export const ContainerSearchColumn = styled.div`
  position: relative;
`;

export const WrapperSearchColumn = styled.div`
  height: 100%;
`;

export const WrapperInputSearchColumn = styled.div`
  padding: ${props => (props.isGoTo ? '0' : '8px 0rem 8px 0rem')};

  .MuiInputBase-root .MuiInputBase-input {
    padding: 0.5rem 0 0.5rem 0.313rem;
    padding-bottom: 8px;
  }
  .MuiTextField-root {
    padding: 8px 10px !important;
  }
`;

const LineItem = css`
  padding-left: 10px;
  display: flex;
  align-items: center;
`;

export const LabelSearchColumn = styled.div`
  font-size: 12px;
  cursor: pointer;
`;

export const ValueSearchColumn = styled.span`
  font-family: ${fonts.roMedium};
`;

export const WrapperFilterTextSearch = styled.div`
  ${LineItem};
  margin-top: ${props => (props.noMarginTop ? 0 : '0.5rem')};
  margin-bottom: ${props => (props.noMarginBottom ? 0 : '0.5rem')};
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  cursor: pointer;
  color: ${colors.primary};
`;

export const AnotherSearchColumn = styled.div`
  padding: 8px 0 0 0;
  font-size: 11px;
  color: ${colors.tertiary};
  ${LineItem};
`;

export const WrapperSuggestion = styled.div`
  /* min-height: 100px; */
  position: relative;
  ${({ isBorder }) => isBorder && `border-top: 1px solid ${colors.platinum};`}
`;

export const WrapperSuggestionList = styled.div`
  position: relative;
  max-height: 200px;
  overflow-y: auto;
`;

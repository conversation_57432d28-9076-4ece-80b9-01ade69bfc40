/* eslint-disable react/prop-types */
import React, { useImperativeHandle, useRef } from 'react';
import {
  UIAvatar as Avatar,
  UINodata as Nodata,
} from '@xlab-team/ui-components';

import {
  WrapperFilterTextSearch,
  LabelSearchColumn,
  ValueSearchColumn,
  AnotherSearchColumn,
  WrapperSuggestionList,
} from '../styled';
import { getAvatarLabel } from '../../../utils/common';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { useInView } from 'react-intersection-observer';

const labelOrGoTo = getTranslateMessage(TRANSLATE_KEY._INFO_GO_TO, 'Or go to');

// const labelNodata = getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data');

function SuggestionItem(props) {
  const { itemRef } = props;

  const onClick = () => {
    // console.log('onClick: ', props.item);
    props.onClick(props.item);
  };

  return (
    <WrapperFilterTextSearch
      ref={itemRef}
      key={props.item.value}
      noMarginTop
      noMarginBottom
      onClick={onClick}
    >
      {props.isShowAvatar && (
        <Avatar
          label={getAvatarLabel(props.item.label)}
          size={24}
          url=""
          className="m-right-1"
        />
      )}
      <LabelSearchColumn>
        <ValueSearchColumn>{props.item.label}</ValueSearchColumn>
      </LabelSearchColumn>
    </WrapperFilterTextSearch>
  );
}

const SuggestionList = React.forwardRef((props, ref) => {
  const listRef = useRef(null);

  const { ref: lastOptionRef } = useInView({
    threshold: 0.1,
    onChange: inView => {
      if (inView && props.onScrollToEnd) {
        props.onScrollToEnd();
      }
    },
  });

  useImperativeHandle(ref, () => ({
    scrollToTop: () => {
      if (listRef.current) {
        listRef.current.scrollTop = 0;
      }
    },
  }));

  if (props.data.length === 0) {
    return (
      <Nodata height="170px">
        {props.labelNodata ||
          getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
      </Nodata>
    );
  }
  return (
    <>
      {props.header}

      {props.isGoTo && <AnotherSearchColumn>{labelOrGoTo}</AnotherSearchColumn>}

      <WrapperSuggestionList ref={listRef} data-testid="suggestion-list">
        {props.data.map((item, idx) => {
          return (
            <SuggestionItem
              itemRef={node => {
                if (!node || idx !== props.data.length - 1) return;

                lastOptionRef(node);
              }}
              key={`${item.value}@@${item.id}@@${idx}`}
              item={item}
              onClick={props.onClick}
              isShowAvatar={props.isShowAvatar}
            />
          );
        })}
      </WrapperSuggestionList>
    </>
  );
});

export default SuggestionList;

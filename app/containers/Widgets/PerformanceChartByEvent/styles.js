import styled, { css } from 'styled-components';

import { UIIconButton as IconButton } from '@xlab-team/ui-components';
// import Pulldown from 'components/Molecules/Pulldown';
import colors from 'utils/colors';
import { fonts, transition, breakdownMd } from 'utils/variables';

const LineChartContainer = styled.div`
  background-color: ${colors.white};
  margin-top: 0.5rem;
  border-bottom: solid 1px #E5E5E5;
  /* box-shadow: 0 0 0.5rem 0 ${colors.blackBlur}; */
  transition: all ${transition} ease-in-out;
  transform: translateY(0);
  visibility: visible;
  height: ${props => (props.isExpend ? '100%' : 'calc((100% - 120px)/2)')};
  max-height: ${props => (props.isExpend ? '100%' : '272px')};
  min-height: ${props => props.showChart && '272px'};
  z-index: 3;
  overflow: hidden;
  ${breakdownMd(css`
    /* height: 268px; */
  `)}
  .chart {
    width: '100%' !important;
    height: 200px;
  }
  ${props =>
    !props.showChart &&
    css`
      height: 0;
      margin-top: unset;
      transform: translateY(-100%);
      visibility: hidden;
      ${breakdownMd(css`
        height: 0;
      `)}
    `}
`;

export const Flexbox = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem 1.5rem;
`;

export const WrapperLabel = styled.div`
  display: flex;
  margin-right: auto;
`;

export const WrapperPulldown = styled.div`
  &:not(:first-of-type) {
    margin-left: 4rem;
  }
  display: flex;
  align-items: center;
`;

export const SquareBox = styled.div`
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.5rem;
  background-color: ${props => props.color || colors.primary};
`;

// export const PulldownExtend = styled(Pulldown)`
//   margin-left: 1rem;
//   > label {
//     border-bottom: none !important;
//     font-family: ${fonts.roMedium};
//   }
// `;

export const WrapperOption = styled.div`
  display: flex;
`;

export const IconButtonExtend = styled(IconButton)`
  margin-left: 2rem;
  &:not(:first-of-type) {
    margin-left: 1.5rem;
  }
  &:last-child {
    margin-left: 0px;
  }
`;

export const WrraperLoading = styled.div`
  position: relative;
  height: calc(100% - 64px);
  width: 100%;
`;

export default LineChartContainer;

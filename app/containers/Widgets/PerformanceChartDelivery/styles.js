import styled, { css } from 'styled-components';

import { UIIconButton as IconButton } from '@xlab-team/ui-components';
// import Pulldown from 'components/Molecules/Pulldown';
import colors from 'utils/colors';
import { fonts, transition, breakdownMd } from 'utils/variables';

const LineChartContainer = styled.div`
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background-color: ${colors.white};
  margin-top: 0.5rem;
  /* box-shadow: 0 0 0.5rem 0 ${colors.blackBlur}; */
  transition: all ${transition} ease-in-out;
  transform: translateY(0);
  visibility: visible;
  height: ${props => (props.isExpend ? '100%' : 'calc((100% - 120px)/2)')};
  max-height: ${props =>
    props.isExpend || props.isShowDrawer ? '100%' : '272px'};
  min-height: ${props => props.showChart && '272px'};
  z-index: 3;
  overflow: hidden;
  border-bottom: 1px solid #e5e5e5;
  ${breakdownMd(css`
    /* height: 268px; */
  `)}
  .chart {
    width: '100%' !important;
    height: 200px;
  }
  ${props =>
    !props.showChart &&
    css`
      height: 0;
      margin-top: unset;
      transform: translateY(-100%);
      visibility: hidden;
      ${breakdownMd(css`
        height: 0;
      `)}
    `}
`;

export const Flexbox = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
`;

export const WrapperLabel = styled.div`
  display: flex;
  /* margin-right: 750px; */
`;

export const WrapperPulldown = styled.div`
  &:not(:first-of-type) {
    margin-left: 40px;
  }
  display: flex;
  align-items: center;

  button.style-btn-dropdown {
    border: none;
    height: 14px;

    &:hover {
      background-color: unset;
    }

    > span {
      line-height: 14px;
    }

    .MuiSvgIcon-root {
      font-size: 16px !important;
      margin-left: 8px !important;
      margin-right: 0 !important;
      color: ${colors.iconGrey} !important;
    }
  }
`;

export const SquareBox = styled.div`
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-right: 3px;
  background-color: ${props => props.color || colors.primary};
  border-radius: 5px;
`;

// export const PulldownExtend = styled(Pulldown)`
//   margin-left: 1rem;
//   > label {
//     border-bottom: none !important;
//     font-family: ${fonts.roMedium};
//   }
// `;

export const WrapperOption = styled.div`
  display: flex;
`;

export const IconButtonExtend = styled(IconButton)`
  &:not(:first-of-type) {
    margin-left: 10px;
  }
  color: #7f7f7f;
`;

export const WrraperLoading = styled.div`
  position: relative;
  height: calc(100% - 64px);
  width: 100%;
`;

export default LineChartContainer;

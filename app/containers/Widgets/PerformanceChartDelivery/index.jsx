/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
import PropTypes from 'prop-types';
import React, { memo, useState } from 'react';
import UISelect from 'components/form/UISelectCondition';
import {
  UILoading as Loading,
  UINodata as Nodata,
} from '@xlab-team/ui-components';
import UIDropdownSimple from 'components/common/UIDropdownSimple';
import { useImmer } from 'use-immer';
import { defaultColor } from './utils';
import { LIST_CHART_TYPE, useFetchData } from './useFetchData';
import ChartData from '../../UIDev/Chart';
import LineChartContainer, {
  Flexbox,
  IconButtonExtend,
  SquareBox,
  WrapperLabel,
  WrapperOption,
  WrapperPulldown,
  WrraperLoading,
} from './styles';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import Scorecard from '../../../components/common/Scorecard';

const PopoverAnchor = props => (
  <div {...props}>
    <IconButtonExtend color="#595959" iconName="tune" size="20px" isVertical>
      {/* ADJUST */}
      {getTranslateMessage(TRANSLATE_KEY._ACT_ADJUST, 'ADJUST')}
    </IconButtonExtend>
  </div>
);
const PopoverAnchorScore = props => (
  <div {...props}>
    <IconButtonExtend
      color="#595959"
      iconName="show-chart"
      size="20px"
      isVertical
    >
      {/* ADJUST */}
      {getTranslateMessage(TRANSLATE_KEY._ACT_CHART_TYPE, 'Chart type')}
    </IconButtonExtend>
  </div>
);
const NUM_OF_LINE = 2;
// - 'daily'
// - 'weekly'
// - 'monthly'
// - 'quarterly'
// - 'yearly'

function PerformanceChart(props) {
  const {
    listSelection,
    dateRange,
    showChart,
    ServiceFn,
    defaultMetric,
    // objectId,
    // channelId,
    rules,
    isParentInitDone,
    storyType,
    storyId,
    destination_id,
    objectType,
    optionScoreCard,
    isScoreCard = false,
    defaultOptionScoreCard,
    dataRangeType,
    processId,
    chartTypeDefault,
    isShowDrawer,
    isShowButtonAdjust = true,
  } = props;
  //
  // useWhyDidYouUpdate('PerformanceChart', props);
  const [isExpend, setIsExpend] = useState(false);

  const [
    {
      listSelectionValue = [],
      data,
      layout,
      chartUnitSel,
      isLoading,
      isNodata,
      chartType,
      dataScoreCard,
      scoreCardState,
      isLoadingScore,
    },
    { setListSelectionValue, setChartUnitSel, setCharType },
  ] = useFetchData({
    numOfLine: NUM_OF_LINE,
    ServiceFn,
    listSelection,
    dateRange,
    destination_id,
    // objectId,
    defaultMetric,
    rules,
    isParentInitDone,
    storyType,
    storyId,
    objectType,
    dataRangeType,
    optionScoreCard,
    defaultOptionScoreCard,
    processId,
    chartTypeDefault,
  });
  // console.log('listSelection', { listSelection, listSelectionValue });
  const onChange = index => value => {
    setListSelectionValue(draft => {
      draft[index] = value;
      const out = draft.map(each => each.value);
      props.callback('UPDATE_METRICS', out);
    });
  };

  // const useMemoData = useMemo(() => data, [data]);
  const onChangeChartType = value => {
    setCharType(draft => {
      draft.value = value;
    });
    props.callback('CHANGE_CHART_TYPE', value);
  };
  const renderContent = () => {
    if (isLoading) {
      return (
        <WrraperLoading>
          <Loading isLoading />
        </WrraperLoading>
      );
    }

    if (isNodata && chartType.value.value === 'lineChart') {
      return (
        <Nodata height="150px">
          {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
        </Nodata>
      );
    }
    if (chartType.value.value === 'lineChart') {
      return (
        <ChartData
          columns={listSelectionValue}
          linesChart={layout}
          data={data}
        />
      );
    }
    return (
      <Scorecard
        defaultOptionScoreCard={scoreCardState}
        optionScoreCard={optionScoreCard}
        data={dataScoreCard}
        objectType={objectType}
        isLoading={isLoadingScore}
      />
    );

    // return (
    //   // <ChartData columns={listSelectionValue} linesChart={layout} data={data} />
    // );
  };
  const renderTopLayout = () => (
    <Flexbox>
      <WrapperLabel>
        {chartType.value.value === 'lineChart' &&
          listSelectionValue.map((each, index) => (
            <WrapperPulldown key={`${index}${each.value}`}>
              <SquareBox color={defaultColor[index]} />
              <UISelect
                onlyParent
                use="tree"
                isSearchable={false}
                options={listSelection}
                value={each}
                onChange={onChange(index)}
                placeholder={getTranslateMessage(
                  TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                  'Select an item',
                )}
                fullWidthPopover={false}
              />
            </WrapperPulldown>
          ))}
      </WrapperLabel>
      <WrapperOption>
        {isScoreCard ? (
          <UIDropdownSimple
            isShowAddIcon={false}
            PopoverAnchor={PopoverAnchorScore}
            options={chartType.options}
            style={{ marginLeft: 10 }}
            value={chartType.value}
            callback={value => onChangeChartType(value)}
          />
        ) : (
          // <IconButtonExtend
          //   iconName="show-chart"
          //   size="20px"
          //   onClick={() => {}}
          //   isVertical
          //   disabled
          // >
          //   {/* CHART TYPE */}
          //   {getTranslateMessage(TRANSLATE_KEY._ACT_CHART_TYPE, 'Chart type')}
          // </IconButtonExtend>
          <></>
        )}

        <IconButtonExtend
          style={{ minWidth: '50px' }}
          iconName={`${isExpend ? 'fullscreen-exit' : 'fullscreen'}`}
          size="20px"
          color="#595959"
          onClick={() => setIsExpend(prev => !prev)}
          isVertical
          // disabled
        >
          {/* EXPAND */}
          {isExpend
            ? getTranslateMessage(TRANSLATE_KEY._ACT_COLLAPSE, 'collapse')
            : getTranslateMessage(TRANSLATE_KEY._ACT_EXPAND, 'expand')}
        </IconButtonExtend>
        {chartType.value.value === 'lineChart' ? (
          <UIDropdownSimple
            isShowAddIcon={false}
            PopoverAnchor={PopoverAnchor}
            style={{ marginLeft: 10 }}
            options={chartUnitSel.options}
            value={chartUnitSel.value}
            callback={value =>
              setChartUnitSel(draft => {
                draft.value = value;
              })
            }
          />
        ) : (
          <>
            {isShowButtonAdjust && (
              <IconButtonExtend
                color="#595959"
                disabled
                iconName="tune"
                size="20px"
                isVertical
              >
                {/* ADJUST */}
                {getTranslateMessage(TRANSLATE_KEY._ACT_ADJUST, 'Interval')}
              </IconButtonExtend>
            )}
          </>
        )}
      </WrapperOption>
    </Flexbox>
  );

  return (
    <LineChartContainer
      showChart={showChart}
      isExpend={isExpend}
      style={props.style}
      isShowDrawer={isShowDrawer}
    >
      {renderTopLayout()}
      {renderContent()}
    </LineChartContainer>
  );
}

PerformanceChart.propTypes = {
  data: PropTypes.array,
  layout: PropTypes.object,
  stylesChart: PropTypes.object,
  showChart: PropTypes.bool,
  defaultMetric: PropTypes.any,
  isParentInitDone: PropTypes.bool,
  isScoreCard: PropTypes.bool,
};
PerformanceChart.defaultProps = {
  isParentInitDone: true,
};

export default memo(PerformanceChart);

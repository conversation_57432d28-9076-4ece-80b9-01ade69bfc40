/* eslint-disable no-param-reassign */
import { useCallback, useEffect } from 'react';
import _isEmpty from 'lodash/isEmpty';
import { useImmer } from 'use-immer';
import { differenceInCalendarDays } from 'date-fns';
import { defaultLayout, toEntryFE } from './utils';
import { useCancellablePromise } from '../../../utils/web/useHooks';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { toConditionAPI } from '../../Filters/utils';
import { addMessageToQueue } from '../../../utils/web/queue';

const MAP_OPTIONS_DISABLED = {
  1: ['daily'],
  2: ['daily', 'weekly'],
  3: ['daily', 'weekly', 'monthly'],
  4: ['daily', 'weekly', 'monthly', 'quarterly'],
  5: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
};
const getDisableOption = timeRange => {
  if (timeRange <= 7) {
    return 1;
  }
  if (timeRange > 7 && timeRange <= 30) {
    return 2;
  }
  if (timeRange > 31 && timeRange <= 90) {
    return 3;
  }
  if (timeRange > 90 && timeRange <= 365) {
    return 4;
  }
  if (timeRange > 365) {
    return 5;
  }
};
const LIST_UNIT_CHART = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._ADJUST_DAILY, 'daily'),
    value: 'daily',
    disabled: false,
    // labelTranslateCode: '_ADJUST_DAILY',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._ADJUST_WEEKLY, 'weekly'),
    value: 'weekly',
    disabled: true,
    // labelTranslateCode: '_ADJUST_WEEKLY',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._ADJUST_MONTHLY, 'monthly'),
    value: 'monthly',
    disabled: true,
    // labelTranslateCode: '_ADJUST_MONTHLY',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._ADJUST_QUARTERLY, 'quarterly'),
    value: 'quarterly',
    disabled: true,
    // labelTranslateCode: '_ADJUST_QUARTERLY',
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._ADJUST_YEARLY, 'yearly'),
    value: 'yearly',
    disabled: true,
    // labelTranslateCode: '_ADJUST_YEARLY',
  },
];

export const useFetchData = ({
  dateRange,
  ServiceFn,
  numOfLine,
  listSelection,
  defaultMetric,
  rules,
  isParentInitDone,
  storyType,
  storyId,
  objectType,
  callback = () => {},
}) => {
  const [state, setState] = useImmer({
    data: [],
    layout: defaultLayout,
    isNodata: true,
    isLoading: true,
    // isInitDone: false,
  });

  const [listSelectionValue, setListSelectionValue] = useImmer([]);
  const [chartUnitSel, setChartUnitSel] = useImmer({
    options: [...LIST_UNIT_CHART],
    value: { ...LIST_UNIT_CHART[0] },
  });
  const { cancellablePromise } = useCancellablePromise();
  // console.log('defaultMetric', defaultMetric);
  useEffect(() => {
    const timeRange = differenceInCalendarDays(
      dateRange.toDate,
      dateRange.fromDate,
    );
    const AcceptedArray =
      MAP_OPTIONS_DISABLED[getDisableOption(timeRange)] || [];
    LIST_UNIT_CHART.forEach(element => {
      if (AcceptedArray.includes(element.value)) {
        element.disabled = false;
      } else {
        element.disabled = true;
      }
    });
  }, [dateRange]);

  useEffect(() => {
    if (!isParentInitDone) {
      setState(draft => {
        draft.isLoading = true;
      });
    }
  }, [isParentInitDone]);

  useEffect(() => {
    if (_isEmpty(listSelection)) return;
    setListSelectionValue(draft => {
      const propertyCode = [];
      const channelId = storyType === null ? 0 : storyType;
      listSelection.forEach(element => {
        propertyCode.push(element.propertyCode);
        // console.log('Metric', {
        //   defaultMetric,
        //   element,
        //   listSelection,
        //   listSelectionValue,
        // });
        if (defaultMetric[channelId]) {
          if (element.propertyCode === defaultMetric[channelId][0]) {
            draft[0] = element;
          }
          if (element.propertyCode === defaultMetric[channelId][1]) {
            draft[1] = element;
          }
        }
        // Trường hợp mới vô chưa có localstorage
        if (draft[0] === undefined) {
          draft[0] = listSelection[0] || {};
        }
        if (draft[1] === undefined) {
          draft[1] = listSelection[1] || listSelection[0] || {};
        }
      });
      // Trường hợp mà chuyển đổi giữa các story
      // Nếu draft không có trong list propertyCode
      if (
        !propertyCode.includes(draft[0].propertyCode) ||
        !propertyCode.includes(draft[1].propertyCode)
      ) {
        draft[0] = listSelection[0] || {};
        draft[1] = listSelection[1] || listSelection[0] || {};
      }
    });
  }, [listSelection]);

  const fetchDataAPI = useCallback(() => {
    if (
      _isEmpty(listSelection) ||
      _isEmpty(listSelectionValue) ||
      !isParentInitDone
    )
      return;
    // console.log('call API');
    const perf_columns = listSelectionValue.map(each => each.value);
    const perf_columns_label = listSelectionValue.map(each => each.label);
    const params = {
      objectId: Number(storyId),
      data: {
        object_type: objectType,
        // story_id: channelId,
        story_id: storyId,
        story_type: storyType,
        // channel_id: channelId,
        filters: toConditionAPI(rules) || {
          OR: [
            {
              AND: [],
            },
          ],
        },
        durations: {
          fromDate: dateRange.fromDate,
          toDate: dateRange.toDate,
        },
        chart_unit: chartUnitSel.value.value || 'daily',
        perf_columns,
      },
    };

    if (typeof callback === 'function') {
      callback('SET_LOADING_SELECTION_CHART', true);
    }
    setState(draft => {
      draft.isLoading = true;
    });
    cancellablePromise(ServiceFn(params))
      .then(res => {
        if (res.code === 200 && !_isEmpty(res.data)) {
          const { data, layout } = toEntryFE(
            res.data,
            perf_columns,
            perf_columns_label,
          );
          setState(draft => {
            draft.data = res.data;
            draft.layout = layout;
            draft.isNodata = false;
            draft.isLoading = false;
          });
        } else {
          setState(draft => {
            draft.data = [];
            draft.layout = defaultLayout;
            draft.isLoading = false;
            draft.isNodata = true;
          });
        }
      })
      .catch(err => {
        if (!err.isCanceled) {
          addMessageToQueue({
            path: 'app/containers/Widgets/PerformanceChart/useFetchData.js',
            func: 'cancellablePromise',
            data: err.stack,
          });
          console.warn('err', err);
        }
      })
      .finally(() => {
        if (typeof callback === 'function') {
          callback('SET_LOADING_SELECTION_CHART', false);
        }
      });
  }, [
    dateRange,
    chartUnitSel.value,
    listSelectionValue,
    rules,
    isParentInitDone,
  ]);

  useEffect(() => {
    fetchDataAPI({});
  }, [
    dateRange,
    chartUnitSel.value,
    listSelectionValue,
    rules,
    isParentInitDone,
  ]); // no more warning

  return [
    {
      isLoading: state.isLoading,
      data: state.data,
      layout: state.layout,
      listSelectionValue,
      chartUnitSel,
      isNodata: state.isNodata,
    },
    { setListSelectionValue, setChartUnitSel },
  ];
};

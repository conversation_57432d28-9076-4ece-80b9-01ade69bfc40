/* eslint-disable react/prop-types */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
import PropTypes from 'prop-types';
import React, { memo, useState } from 'react';
import cls from 'classnames';
import UISelect from 'components/form/UISelectCondition';
import {
  UILoading as Loading,
  UINodata as Nodata,
} from '@xlab-team/ui-components';
import UIDropdownSimple from 'components/common/UIDropdownSimple';
import { defaultColor } from './utils';
import { useFetchData } from './useFetchData';
import ChartData from '../../UIDev/Chart';
import LineChartContainer, {
  Flexbox,
  IconButtonExtend,
  SquareBox,
  WrapperLabel,
  WrapperOption,
  WrapperPulldown,
  WrraperLoading,
} from './styles';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

const PopoverAnchor = props => {
  return (
    <div {...props} style={{ cursor: 'pointer' }}>
      <IconButtonExtend
        iconName="tune"
        size="20px"
        isVertical
        className={cls({
          '--selected': props.isOpen,
        })}
      >
        {getTranslateMessage(TRANSLATE_KEY._, 'INTERVAL')}
      </IconButtonExtend>
    </div>
  );
};
const NUM_OF_LINE = 2;
// - 'daily'
// - 'weekly'
// - 'monthly'
// - 'quarterly'
// - 'yearly'

function PerformanceChart(props) {
  const {
    listSelection,
    dateRange,
    showChart,
    ServiceFn,
    defaultMetric,
    // objectId,
    // channelId,
    rules,
    isParentInitDone,
    storyType,
    storyId,
    objectType,
  } = props;
  // useWhyDidYouUpdate('PerformanceChart', props);
  // console.log('listSelection: ', listSelection);
  const [isExpend, setIsExpend] = useState(false);
  const [
    {
      listSelectionValue = [],
      data,
      layout,
      chartUnitSel,
      isLoading,
      isNodata,
    },
    { setListSelectionValue, setChartUnitSel },
  ] = useFetchData({
    numOfLine: NUM_OF_LINE,
    ServiceFn,
    listSelection,
    dateRange,
    // objectId,
    defaultMetric,
    rules,
    isParentInitDone,
    storyType,
    storyId,
    objectType,
    callback: props.callback,
  });
  // console.log('listSelection', { listSelection, listSelectionValue });
  const onChange = index => value => {
    setListSelectionValue(draft => {
      draft[index] = value;
      const out = draft.map(each => each.value);
      props.callback('UPDATE_METRICS', out);
    });
  };

  // const useMemoData = useMemo(() => data, [data]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <WrraperLoading>
          <Loading isLoading />
        </WrraperLoading>
      );
    }

    if (isNodata) {
      return (
        <Nodata height="150px">
          {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
        </Nodata>
      );
    }

    // console.log('listSelectionValue', { listSelectionValue, layout, data });
    return (
      <ChartData columns={listSelectionValue} linesChart={layout} data={data} />
    );
  };
  const renderTopLayout = () => (
    <Flexbox>
      <WrapperLabel>
        {listSelectionValue.map((each, index) => (
          <WrapperPulldown key={`${index}${each.value}`}>
            <SquareBox color={defaultColor[index]} />
            <UISelect
              onlyParent
              use="tree"
              isSearchable={false}
              options={listSelection}
              value={each}
              onChange={onChange(index)}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover={false}
            />
          </WrapperPulldown>
        ))}
      </WrapperLabel>
      <WrapperOption>
        {/* <IconButtonExtend
          iconName="show-chart"
          size="20px"
          onClick={() => {}}
          isVertical
          disabled
        >
          {getTranslateMessage(TRANSLATE_KEY._ACT_CHART_TYPE, 'Chart type')}
        </IconButtonExtend> */}
        <IconButtonExtend
          style={{ minWidth: '50px' }}
          iconName={`${isExpend ? 'fullscreen-exit' : 'fullscreen'}`}
          size="20px"
          onClick={() => setIsExpend(prev => !prev)}
          isVertical
          // disabled
        >
          {/* EXPAND */}
          {isExpend
            ? getTranslateMessage(TRANSLATE_KEY._ACT_COLLAPSE, 'collapse')
            : getTranslateMessage(TRANSLATE_KEY._ACT_EXPAND, 'expand')}
        </IconButtonExtend>
        <UIDropdownSimple
          isShowAddIcon={false}
          PopoverAnchor={PopoverAnchor}
          options={chartUnitSel.options}
          value={chartUnitSel.value}
          style={{ marginLeft: 0 }}
          callback={value =>
            setChartUnitSel(draft => {
              draft.value = value;
            })
          }
        />
      </WrapperOption>
    </Flexbox>
  );

  return (
    <LineChartContainer
      showChart={showChart}
      isExpend={isExpend}
      style={props.style}
    >
      {renderTopLayout()}
      {renderContent()}
    </LineChartContainer>
  );
}

PerformanceChart.propTypes = {
  data: PropTypes.array,
  layout: PropTypes.object,
  stylesChart: PropTypes.object,
  showChart: PropTypes.bool,
  defaultMetric: PropTypes.any,
  isParentInitDone: PropTypes.bool,
};
PerformanceChart.defaultProps = {
  isParentInitDone: true,
};

export default memo(PerformanceChart);

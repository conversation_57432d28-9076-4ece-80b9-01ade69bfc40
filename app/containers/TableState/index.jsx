/* eslint-disable react/prop-types */
import React, { memo, useEffect } from 'react';
import PropTypes from 'prop-types';
import Table from '../Table/Table';
import { COLUMNS_WIDTH } from '../Table/constants';

const WrapperTable = props => {
  const { dispatchAction, limitDefault } = props;
  // useEffect(() => {
  //   // fetchDataAPI();
  //   props.callback('FETCH_DATA');
  // }, [props.paging, props.sort]);

  useEffect(() => {
    if (limitDefault) {
      dispatchAction.onTablePaging({ limit: limitDefault, page: 1 });
    }
  }, []);

  const onSort = sortBy => {
    if (sortBy.length === 1) {
      dispatchAction.onTableSort({
        key: sortBy[0].id,
        by: sortBy[0].desc ? 'desc' : 'asc',
      });
      // props.callback('FETCH_DATA');
    } else {
      dispatchAction.onTableSort({
        key: '',
        by: '',
      });
      // props.callback('FETCH_DATA');
    }
  };

  const setSelectedRow = data => {
    dispatchAction.onTableSelectRow({ item: data, data: props.data });
  };

  const onPaging = params => {
    dispatchAction.onTablePaging(params);
    props.callback('ON_PAGING_TABLE', params);
  };

  const onSelectAll = params => {
    dispatchAction.onSelectAll(params);
    // props.callback('SELECT_ALL', params);
  };

  const onSelectAllPage = params => {
    dispatchAction.onSelectAllPage(params);
    // props.callback('SELECT_ALL_IN_PAGE', params);
  };
  const onTableSubRow = params => {
    dispatchAction.onTableSubRow(params);
    // props.callback('SELECT_ALL_IN_PAGE', params);
  };
  return (
    <Table
      noCheckboxAction={props.noCheckboxAction}
      moduleConfig={props.moduleConfig}
      isLoading={props.isLoading}
      isShowFooter={props.isShowFooter}
      mapDataFooter={props.mapDataFooter}
      paging={props.paging}
      limitDefault={props.limitDefault}
      table={props.table}
      sort={props.sort}
      selectedRows={props.selectedRows}
      isSelectedAll={props.isSelectedAll}
      disabledSelectedAll={props.disabledSelectedAll}
      isSelectedAllPage={props.isSelectedAllPage}
      onSubRow={onTableSubRow}
      onSelectAll={onSelectAll}
      onSelectAllPage={onSelectAllPage}
      columns={props.columns}
      data={props.data}
      onSort={onSort}
      setSelectedRow={setSelectedRow}
      onTablePaging={onPaging}
      noDataText={props.noDataText}
      sizeCheckbox={COLUMNS_WIDTH.CHECKBOX}
      initialWidthColumns={props.initialWidthColumns}
      resizeColName={props.resizeColName}
      widthFirstColumns={props.widthFirstColumns}
      ComponentControlTable={props.ComponentControlTable}
      callback={props.callback}
      maxSelectedRows={props.maxSelectedRows}
      isShowPagination={props.isShowPagination}
      isExpandCell={props.isExpandCell}
      expanded={props.expanded}
      global={props.global}
      isOpenNewTab={props.isOpenNewTab}
      isSplitTable={props.isSplitTable}
      leftColumn ={props.leftColumn}
    >
      {props.children}
    </Table>
  );
};

WrapperTable.defaultProps = {
  resizeColName: 'name',
  widthFirstColumns: 0,
  selectedIds: new Set([]),
  ComponentControlTable: () => <div />,
  isExpandCell: false,
};
WrapperTable.propTypes = {
  moduleConfig: PropTypes.object.isRequired,
  resizeColName: PropTypes.string,
  selectedIds: PropTypes.object,
  widthFirstColumns: PropTypes.number,
  ComponentControlTable: PropTypes.any,
  isExpandCell: PropTypes.bool,
};

export default memo(WrapperTable);

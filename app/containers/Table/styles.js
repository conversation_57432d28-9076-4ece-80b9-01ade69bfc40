/* eslint-disable indent */
import styled, { css } from 'styled-components';

import colors, { COLOR } from '../../utils/colors';
import { transition, fonts, TABLE_HEIGHT_FILTER } from 'utils/variables';
import { createResizerHoverCss } from './utils';
import { Flex, Scrollbars } from '@antscorp/antsomi-ui';

const ROW_HEIGHT = '34px';

const StyledTable = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  .table-wrapper {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;
    overflow: auto;
    max-height: unset;
    /* padding: 0 12px; */
    padding-left: 12px;
    padding-right: 12px;
    margin: 0 -12px;
    >.uiOverlay-backdrop:first-child {
      margin: 0 12px;
    }
  }
  .table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-spacing: 0;
    background-color: ${colors.white};

    .table-is-loading {
      width: '100%'; position: 'relative'; overflow: 'hidden';
    }
    .hidden-expanded-style {
      position: sticky !important;
      left: 0;
      z-index: 4;
      justify-content: center;

      &.sticky-expand{
        left: 50px;
      }
    }
    .expanded-style {
      position: sticky !important;
      left: 0;
      z-index: 4;
      justify-content: center;
    }
    .header {
      .tr {
        .th {
          position: relative;
          background-color: #f9f9f9;
          color: #000;
          font-size: 12px;
          font-weight: 700;
          line-height: 14px;

          &:not(.hidden-expanded-style) {
            .th-txt {
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          &:first-child,
          &:nth-child(2) {
            flex-grow: 0 !important;
            /* border-right: 1px solid ${colors.tertiary250} !important; */
          }
          &.number {
            justify-content: flex-end;
          }
          &.txt-status {
            /* padding-left: 27px; */
            justify-content: flex-start;
          }
          &.txt-right {
            justify-content: flex-end;
          }
          &.txt-left {
            justify-content: flex-start;
          }
          &.txt-center {
            justify-content: center;
          }
          /*
            IMPORTANT
            c_user_id vs u_user_id
            class này có nhiều ở các màn hình + datatype = number nên luôn cần overview lại css
            => mình mới mang ta table
            => Không tùy tiện mang các property khác ra ngoài này
          */
          &.c_user_id {
            justify-content: flex-start;
          }
          &.u_user_id {
            justify-content: flex-start;
          }
          &:after {
            content: '';
            position: absolute;
            width: calc(100% + 1px);
            height: 1px;
            background-color: ${colors.tertiary250};
            bottom: -1px;
            left: 0;
          }
        }
      }
    }
    /* .thead {
      flex-shrink: 0;
      .tr {
        cursor: unset;
        &:hover {
          background-color: unset;
        }
        .th {
          font-family: ${fonts.roBold};
          &.txt-status {
            padding-left: 27px;
          }
        }
      }
    } */
    .tbody-group {
      flex-grow: 1;
      overflow-y: auto;
    }
    .tr {
      min-width: calc(100% - 1px) !important;
      width: fit-content !important;
      cursor: pointer;
      &.selected {
        .td {
          /* :first-child,
          :nth-child(2) {
            background-color: ${colors.primary50};
          } */
          .expand-content {
            background-color: ${colors.primary50} !important;
          }
          background-color: #DEEFFE !important;
        }
        .td.main-expanded-style {
          background-color: ${colors.primary50} !important;
        }
      }
      :last-child {
        .td::after {
          border-bottom: none !important;
        }
      }
      &:hover {
        .td {
          .expand-content {
            background-color: ${colors.primary50} !important;
          }
          background-color: ${colors.primary50} !important;
        }
        .tr-action {
          &.tr-no-action {
            display: none !important;
          }
          display: block;
          color: #000;
        }
      }
      &.row-expanded-empty {
        pointer-events: none;
        cursor: default; /* đổi lại con trỏ để người dùng không tưởng là có thể tương tác */
      
  }

    }


    .th,
    .td {
      padding: 0px 10px;
      min-height: ${ROW_HEIGHT};
      /* border-bottom: 1px solid ${colors.tertiary250}; */
      /* border-right: 1px solid ${colors.tertiary250}; */
      box-shadow: 0px 0px 0 1px ${colors.tertiary250};
      position: relative;
      display: flex !important;
      align-items: center;
      cursor: text;
      font-size: 12px;
      background-color: #fff;

  

      ::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        // fix border index when hover resizer
        /* border-bottom: 1px solid ${colors.tertiary250}; */
        
        
      }
      &.expand::after {
        border-bottom: none !important;
      }
      &.last-cell {
        /* border-top: 1px solid ${colors.tertiary250} !important; */
      }
      &:last-child {
          border-right: none !important;
      }

      .td-action:not(.on-action) {
        display: none;
      }

      &:hover {
        .td-action {
          &.td-no-action {
            display: none !important;
          }

          display: block;
          color: #000;
        }
      }
      :first-child {
        flex-grow: 0 !important;
        background-color: ${colors.white};
        position: sticky !important;
        /* position: ${props =>
    props.isSplitTable ? 'relative' : 'sticky'} !important; */
        left: 0;
        top: 0;
        z-index: 1;
        border-right: none;
      }
      :nth-child(2) {
        flex-grow: 0 !important;
      }
      .sorting {
        display: inline-block;
        margin-left: 8px;
        font-size: 18px;
      }
      .icon-data-action {
        display: inline-block;
        /* margin-left: 8px; */
        padding: 0px 4px;
        font-size: 18px;
        cursor: pointer;
      }
      .icon-disabled {
        opacity: 0.5;
        cursor: default;
      }

      .resizer {
        display: inline-block;
        width: 10px;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        transform: translateX(50%);
        z-index: 1;
        touch-action: none;
        cursor: e-resize !important;
        &.isResizing {
        }
      }
      :last-child {
        .resizer {
          display: none;
        }
      }
    }
    .tr.row-expanded {
      .td.main-expanded-style {
        padding: 0px;
        background-color: #ffffff;

        .td-main-expanded:not(.td-can-expanded) {
          .expand-content {
            border-left: 1px solid ${colors.tertiary250};
            padding: 5px 10px;
          }
        }
        .td-can-expanded-child {
        }
        .td-main-expanded {
          .expand-content {
            height: 100%;
            width: 100%;
          }
        }
      }
    }

    .th {
      font-family: ${fonts.roBold};
      border-bottom: 0;
    }
    &.sticky {
      /* position: relative;
      overflow: ${props => !props.isExpandCell && 'auto'}; */
      /* ${props =>
    props.isExpandCell && `overflow-x : hidden; max-height:500px;`} */
      &.overflow-hidden {
        overflow: hidden !important;
      }
      .header,
      .footer {
        position: sticky;
        z-index: 1;
        width: fit-content;
      }
      /* &::-webkit-scrollbar {
        position: absolute;
        -webkit-appearance: none;
        width: 7px;
        height: 7px;
        -webkit-overflow-scrolling: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: rgba(0, 0, 0, 0.5);
      }
      &::-webkit-scrollbar:vertical {
        width: 7px;
        -webkit-overflow-scrolling: auto;
      } */
      &::-webkit-scrollbar-track:vertical {
        margin-top: calc(${ROW_HEIGHT} + 4px);
      }

      .header {
        width: auto;
        top: 0;
        flex-shrink: 0;
        .th {
          &.d-none {
            display: none !important;
          }
        }
      }

      .footer {
        bottom: 0;
        /* box-shadow: 0px -3px 3px #ccc; */
        .tr-footer {
          height : ${ROW_HEIGHT};
          border-top: 1px solid ${colors.tertiary250};
          .left {
            position: sticky;
            left: 0px;
            z-index: 3;
          }
          .right {
            z-index: 1;
          }
          /* z-index: 3; */
          [data-sticky-td]{
            position:sticky;
            left: 0px;
            z-index: 3;
          }
          [data-sticky-last-left-td="true"] {
            border-right: 1px solid ${colors.tertiary250};
          }
        }
      }
      .footer {
        width: auto !important;
        box-shadow: none !important;
        .tr-footer {
          .left .td {
            background-color: ${colors.whitesmoke};
            border-right: 1px solid ${colors.tertiary250};
            justify-content: flex-end;
            font-family: ${fonts.roMedium};
            font-weight: 500;

            &:last-child {
              border-right: 2px solid ${colors.tertiary250};
              position: sticky !important;
              z-index: 3;
              justify-content: flex-start;
            }
          }
          .td {
            background-color: #F9F9F9;
            color: #000;
            font-size: 12px;
            font-weight: bold;
            border-right: 1px solid ${colors.tertiary250};
            justify-content: flex-end;
            font-family: ${fonts.roBold};
            /* &:nth-child(3) {
              border-right: 2px solid ${colors.tertiary250};
            } */
          }
        }
      }

      .body {
        position: relative;
        z-index: 0;
        flex-grow: 1;
      }

      [data-sticky-td] {
        position: sticky;
      }

      [data-sticky-last-left-td=true] {
        background-color: ${colors.white};
        border-right: 1px solid ${colors.tertiary250} !important;
      }

      ${createResizerHoverCss()}
    }
  }
  .pagination {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #000000;
    /* height: 60px;
    padding: 18px; */
    padding-top: 10px;
    .index-paging {
      font-family: unset;
      font-size: 13px;
      margin: 0px 10px;
    }
    .rowPageSize {
      display: flex;
      align-items: center;
      span {
        flex-shrink: 0;
        font-family: ${fonts.roBold};
        font-size: 12px;
        font-family: unset;
      }

      .select-rowshow-label {
        margin-right: 10px
      }

      .select-rowshow {
        width: 47px;
        position: relative;

        button.style-btn-dropdown {
          /* border-bottom: 1px solid #E0E0E0; */
          box-shadow: unset;
          margin-left: 0px;
          padding-left: 0px;
        }

        label {
            line-height: 1.3;
            padding-right: 1rem;
          span {
            margin: 0;
            padding-left: 0;
          }

        }
        svg {
            position: absolute;
            right: 0;
            color: #666666;
            margin-right: 4px !important;
        }
      }
    }
    button.table-arrow {
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      outline: none;
      cursor: pointer;
      width: 1.5rem;
      height: 1.5rem;
      &.disabled {
        color: #BFBFBF;
      }

      &:not(:last-child) {
        margin-right: 10px;
      }

      /* &:first-of-type {
        margin-right: 10px;
      }
      &.goPrevPage {
        margin-right: 10px;
      }
      &.goNextPage {
        margin-right: 10px;
      } */
      /* &:first-of-type {
        margin-right: 10px;
      }
      &.goPrevPage {
        margin-right: 10px;
        color: #666666;
      } */
      /* &:nth-of-type(2) {
        margin-right: 10px;
      }
      &.goNextPage {
        margin-right: 10px;
      } */
    }
  }
`;

export const StyleControlTable = styled.div`
  z-index: 901;
  position: 'relative';
  flex-shrink: 0;
  padding: 0;
  background-color: ${props =>
    props.isSelected ? colors.primary : colors.white};
  // box-shadow: 0 -3px 6px 0 ${colors.blackBlur};
  //border-top: 1px solid #D4D4D4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .filterTable {
    display: flex;
    align-items: center;
    flex-grow: 1;
    .addFill {
      width: 7.313rem;
      position: relative;

      &-wrapper {
        display: none;
        position: absolute;
        top: 0.5rem;
        left: 0.125rem;
        width: 24rem;
        z-index: 2;
        // box-shadow: 0 0 0.5625rem 0 ${colors.blackBlur};
          // border: 1px solid ${colors.platinum};
        background-color: ${colors.white};
        &-show {
          display: block;
        }
        &_input {
          padding: 1rem 0.75rem 0;
        }
      }
      &-recents_wrapper {
        padding: 0.5rem 0;
        border-bottom: 1px solid ${colors.platinum};
      }
      &-filter_wrapper {
        padding: 0.5rem 0;
        max-height: 336px;
        overflow: auto;
        /* ::-webkit-scrollbar {
          -webkit-appearance: none;
          width: 7px;
          height: 7px;
          -webkit-overflow-scrolling: auto;
        }
        ::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background-color: rgba(0, 0, 0, 0.45);
          -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
        } */
      }
      &-filter_collapse {
        &_inner {
            /* transition: all ${transition} ease-in-out; */
          height: 0;
          visibility: hidden;
          opacity: 0;
          &.show {
            height: auto;
            visibility: visible;
            opacity: 1;
          }
        }
        &__item {
          height: 2rem;
          display: flex;
          align-items: center;
          padding-left: 3.5rem;
          font-size: 0.75rem;
          cursor: pointer;
          &:hover {
            background-color: ${colors.primary50};
          }
        }
      }
      &-line {
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all ${transition} ease-in-out;
        &:hover {
          background-color: ${colors.primary50};
        }
        &-pointer {
          cursor: pointer;
        }
      }
      &-label {
        padding-left: 2rem;
        padding-right: 0.5rem;
        font-size: 0.75rem;
      }
      &-value {
        padding-left: 3.5rem;
        font-size: 0.75rem;
      }
      ul {
        margin-top: 0;
        margin-bottom: 0;
        padding-left: 0;
        li {
          list-style: none;
        }
      }
    }
  }

`;

export const WrapActionTable = styled.div`
  ${props => css`
    display: ${() => (props.show ? 'flex' : 'none')};
  `}
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  margin-left: 1rem;
  /* gap: 2px; */

  .actionTable__inner {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 2px;
  }

  .downBlock {
    padding-left: 1rem;
    border-left: 2px solid ${colors.blackBlur};
    padding: 4px 0 4px 16px;
    margin-left: 1rem;
  }
`;

export const RowSelectedControl = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  height: ${TABLE_HEIGHT_FILTER};
  font-size: 0.875rem;
  color: ${colors.white};
  background-color: ${colors.primary};
  span {
    flex-shrink: 0;
  }
  button {
    display: inline-block;
    color: ${colors.white};
    padding: 0;
    min-width: unset;
    margin-left: 1rem;
    font-size: 0.875rem;
    border: none;
    width: 3.75rem;
    div {
      font-size: 0.875rem;
    }
  }
`;

export const VerticleLine = styled.div`
  height: 100%;
  border-right: 1px solid ${colors.white};
  margin-left: 1rem;
`;

export const EmptyTable = styled.div`
  padding: 1rem;
  background-color: ${colors.white};
  font-size: 0.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
  border-bottom: 1px solid ${colors.tertiary250};
  // box-shadow: 1px 0px 6px 0 rgba(0, 0, 0, 0.09);
`;

export const LabelTotal = styled.div`
  height: 60px;
  display: flex;
  justify-content: ${props =>
    props.isShowLabelTotal ? 'space-between' : 'flex-end'};
  align-items: center;
  background: #fff;
  .label-total {
    font-family: unset;
    font-size: 0.813rem;
    margin-left: 12px;
    flex: 1;
    font-weight: bold;
    min-width: 60px;
  }
  .label-archive {
    font-family: unset;
    font-size: 12px;
    margin-left: 12px;
    flex: 1;
    min-width: 60px;
  }
`;

export const StyledScrollBar = styled(Scrollbars)`
  border-top: 1px solid ${colors.tertiary250};
  border-bottom: 1px solid ${colors.tertiary250};

  .scrollbar-track {
    z-index: 1;
    display: ${props => (props.isSplitTable ? 'none' : 'block')} !important;
  }

  .scrollbar-view {
    margin-bottom: 0 !important;
    margin-right: ${props => !props.isSplitTable && 0} !important;
    max-height: ${props => props.isSplitTable && 'calc(100vh - 240px)'} !important;
  }
`;

export default StyledTable;
export const GroupByExpandButton = styled(Flex)`
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;
export const WrapperTableResize = styled.div`
  display: 'flex';
  max-height: calc(100vh - 240px);
  .resizable {
    position: relative;
    flex-shrink: 1 !important;
    border-right: 1px solid #e5e5e5;
    /* z-index === #wrapper-drawer for zoom drawer */
    z-index: 1000;
    height: 100% !important;
    /* min-width: 425px; */
    // overflow: auto hidden;
    border-right: 2px solid ${COLOR.primary60};
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      background-color: ${COLOR.white};
      border: 1px solid ${COLOR.primary60};
      border-radius: 8px;
      position: absolute;
      top: 20px;
      right: 0;
      transform: translate(55%, -50%);
      z-index: 4;
      pointer-events: none;
    }
  }
`;

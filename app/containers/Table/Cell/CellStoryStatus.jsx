import React from 'react';
import PropTypes from 'prop-types';
import UIStatusTag from 'components/cdp/UIStatusTag';
import styled from 'styled-components';
import { Tooltip } from '@material-ui/core';
import { getLabelDescriptionJourney } from '../../../services/Abstract.data';

const WrapperStatus = styled.div`
  width: 100%;
  display: flex;
  justify-content: start;
  cursor: help;
  overflow: hidden;
`;

function CellStatus(props) {
  const row = props.row.original;
  return (
    <Tooltip
      interactive
      disableFocusListener
      title={
        <>
          {getLabelDescriptionJourney(row.status.id)}
          <br />
        </>
      }
      placement="top-start"
    >
      <WrapperStatus width={row.width}>
        <UIStatusTag
          pulse={row.status.pulse}
          label={row.status.label}
          use={row.status.use}
          translateCode={row.status.translateCode}
        />
      </WrapperStatus>
    </Tooltip>
  );
}

CellStatus.propTypes = {
  row: PropTypes.object,
};

export default CellStatus;

/* eslint-disable no-else-return */
/* eslint-disable prefer-template */
import { css } from 'styled-components';
import {
  safeParse,
  makeArrayToLabel,
  safeParseInt,
  safeParseNumber,
} from '../../utils/web/utils';
import { getDateTimeFormatTranslate } from '../../utils/web/translate';
import {
  safeParseDisplayFormatNumber,
  safeParseDisplayFormat,
  safeParseDisplayFormatCurrency,
  safeParseDisplayFormatPercentage,
  initDateWithoutTime,
} from '../../utils/web/portalSetting';
import { serializePropertyValues } from '../../utils/web/properties';
import {
  formatDate,
  formatDateTZ,
  formatPortalDate,
  safeParseDate,
} from '../../utils/date';
import colors from '../../utils/colors';

export function buildClassNameTh(column) {
  let className =
    column.accessor || column.id === 'selection' || column.id === 'expand'
      ? 'th'
      : 'th d-none';
  // if (
  //   leftColumn &&
  //   leftColumn.length > 0 &&
  //   ((!leftColumn.includes(column.id) && !rightSplit) ||
  //     (rightSplit && leftColumn.includes(column.id))) &&
  //   isSplitTable
  // ) {
  //   className = 'th d-none';
  // }
  // let className = 'th';
  if (column.placement === 'right') {
    className += ' txt-right';
  } else if (column.placement === 'center') {
    className += ' txt-center';
  } else if (column.placement === 'left') {
    className += ' txt-left';
  } else if (column.placement === 'status') {
    className += ' txt-status';
  }

  if (column.className) {
    className += ` ${column.className}`;
  }
  return `${className} ${safeParse(column.dataType, '')}`;
}

export function buildIdNameTh(column) {
  if (column.idName) {
    return column.idName;
  } else if (column.id) {
    // using id not idName
    return column.id;
  }
  return '';
}

export function buildClassNameTd(
  column,
  isHideBorder = false,
  isLastCell = false,
) {
  let className = 'td';
  if (column.placement === 'right') {
    className += ' txt-right';
  } else if (column.placement === 'center') {
    className += ' txt-center';
  } else if (column.placement === 'left') {
    className += ' txt-left';
  }

  if (column.className) {
    className += ` ${column.className}`;
  }
  if (isHideBorder) {
    className += ' expand';
  }
  if (isLastCell) {
    className += ' last-cell';
  }
  return className;
}

export function getWidth(mapColumnSize = null, colName, colWidth = 200) {
  if (mapColumnSize === null) {
    return colWidth;
  }
  let width = mapColumnSize[colName];
  if (width === undefined) {
    // eslint-disable-next-line prefer-destructuring
    width = colWidth;
  }
  return width;
}

export function getCellDisplayText(column, value) {
  return safeParse(value, '--');
}

export function getCellDisplayEnummration(column, tmpValue) {
  if (column.mapPropertyValues) {
    const value = safeParse(tmpValue, '--');
    if (value !== '--') {
      if (typeof value !== 'string' || value.indexOf(';') < 0) {
        return safeParse(
          column.mapPropertyValues.get(value),
          'Option has removed',
        );
      }
      const temptArr = value.split(';');
      const arr = [];
      temptArr.forEach(item => {
        const t = column.mapPropertyValues.get(item);

        if (t !== undefined) {
          arr.push(t.label);
        }
      });

      if (arr.length > 0) {
        return makeArrayToLabel(arr, 'and');
      }
    }
    return value;
  }
  return safeParse(tmpValue, '--');
}

export function getCellDisplayArrayWithLabel(column, tmpValue) {
  if (column.mapPropertyValues) {
    const value = safeParse(tmpValue, '--');
    if (value !== '--') {
      if (Array.isArray(value)) {
        const arr = [];
        value.forEach(item => {
          const t = column.mapPropertyValues.get(`${item}`);

          if (t !== undefined) {
            arr.push(t.label);
          } else {
            arr.push('Undefined');
          }
        });

        if (arr.length > 0) {
          return makeArrayToLabel(arr, 'and');
        }
      } else {
        const t = column.mapPropertyValues.get(`${value}`);
        if (t !== undefined) {
          return t.label;
        }
      }
    }
    return value;
  }
  return safeParse(tmpValue, '--');
}

export function getCellDisplayRadioSelect(column, tmpValue) {
  if (column.mapPropertyValues) {
    const value = safeParse(tmpValue, '--');
    if (value !== '--') {
      if (typeof value !== 'string' || value.indexOf(';') < 0) {
        const t = column.mapPropertyValues.get(value);
        if (t !== undefined) {
          return t.label;
        }
        return 'Option has removed';
      }
      return column.mapPropertyValues.get(value, value);
    }
    return value;
  }
  return safeParse(tmpValue, '--');
}

export function getCellDisplayArray(column, value, spliChar = 'and') {
  let type = '';
  if (column.id === 'segment_ids') {
    type = 'segment';
  }
  const arr = safeParse(value, '--');
  // console.log('arr', arr);
  if (arr !== '--') {
    if (Array.isArray(arr)) {
      if (arr.length > 0) {
        if (type === 'segment' && typeof arr[0] === 'object') {
          return makeArrayToLabel(
            arr.map(item => item.segment_display),
            spliChar,
          );
        }
        return makeArrayToLabel(arr, spliChar);
        // return arr;
      }
    }
    if (typeof value !== 'object') {
      return arr;
    }
    // return arr;
  }
  return '--';
}

// Return Array for lable, using for cellArrayBackground
export function getDataDisplayArray(column, value, spliChar = 'and') {
  // console.log('value', value);
  // console.log('column', column);
  let type = '';
  if (column.id === 'segment_ids') {
    type = 'segment';
  }
  const arr = safeParse(value, []);
  // console.log('arr', arr);
  if (Array.isArray(arr)) {
    if (arr.length > 0) {
      if (type === 'segment' && typeof arr[0] === 'object') {
        // return makeArrayToLabel(
        //   arr.map(item => item.segment_display),
        //   spliChar,
        // );
        return arr.map(item => item.segment_display);
      }
      // return makeArrayToLabel(arr, spliChar);
      return arr;
    }
  }
  // if (typeof value !== 'object') {
  //   return arr;
  // }
  // return arr;
  return [];
}

export function getCellDisplayNumber(column, value) {
  const number = safeParse(value, '--');
  if (number === '--' || number === 'N/A') {
    return number;
  }

  const temp = safeParseNumber(number, '--');
  if (temp === '--') {
    // return chính value của nó
    return number;
  }

  // if (Number.isInteger(temp)) {
  //   return numberWithCommas(parseFloat(temp));
  // }
  // return numberWithCommas(parseFloat(temp.toFixed(2)));

  // console.log('column.displayFormat: ', column.displayFormat, column, value);
  const displayFormat = safeParseDisplayFormat(column.displayFormat, {
    dataType: 'number',
    isCurrency: 0,
  });

  // if (Object.keys(displayFormat.config).length === 0) {
  //   displayFormat.config = getPortalFormatNumber();
  // }

  if (displayFormat.type === 'CURRENCY') {
    return safeParseDisplayFormatCurrency(temp + '', displayFormat.config);
  } else if (displayFormat.type === 'PERCENTAGE') {
    return safeParseDisplayFormatPercentage(temp + '', displayFormat.config);
  }
  return safeParseDisplayFormatNumber(temp + '', displayFormat.config);
}

export function getCellDisplayRevenue(column, value) {
  const data = safeParseInt(value, '--');
  if (data === '--') {
    return data;
  }
  // return numberWithCommas(parseInt(data));
  // return `${numberWithCommas(
  //   parseFloat(data),
  // )} ${getAppCookieSessionCurrency()}`;
  const displayFormat = safeParseDisplayFormat(column.displayFormat, {
    dataType: 'number',
    isCurrency: 1,
  });

  return safeParseDisplayFormatCurrency(data + '', displayFormat.config);
}

export function getCellDisplayPortalDate(column, value) {
  const data = safeParse(value, '');
  if (data !== '') {
    const displayFormat = safeParseDisplayFormat(column.displayFormat, {
      dataType: 'datetime',
    });

    return formatPortalDateCol(data, displayFormat.config);
  }
  return '--';
}

export function getCellDisplayUTCDate(column, value) {
  const data = safeParse(value, '');

  if (data !== '') {
    const displayFormat = safeParseDisplayFormat(column.displayFormat, {
      dataType: 'datetime',
    });

    return formatUTCDateCol(data, displayFormat.config);
  }

  return '--';
}

export function getCellDisplayCustomFunction(column, value) {
  const data = safeParse(value, '#N/A');
  if (data === '#N/A') {
    return '#N/A';
  }

  const { property } = column;
  const { customLabel, dataType } = property;
  const { fieldType, isShow } = customLabel;
  if (isShow === 0) {
    if (dataType === 'string') {
      return getCellDisplayText(column, value);
    } else if (dataType === 'number') {
      return getCellDisplayNumber(column, value);
    } else if (dataType === 'datetime') {
      return getCellDisplayUTCDate(column, value);
    }
    return data;
    // eslint-disable-next-line no-else-return
  } else {
    const mapPropertyValues = serializePropertyValues(customLabel.options);
    const temp = column;
    temp.mapPropertyValues = mapPropertyValues;

    //   'getCellDisplayCustomFunction',
    //   mapPropertyValues,
    //   customLabel.options,
    // );

    return getCellDisplayArrayWithLabel(temp, value);
  }
}

export function getCellDisplayOwner(column, value) {
  const obj = safeParse(value, '--');
  if (obj === '--') {
    return {
      id: 0,
      name: '',
      status: 0,
    };
  }
  const versions = safeParse(obj.versions, []);
  if (versions.length > 0) {
    if (versions[0].user_name && versions[0].email) {
      let name = `${versions[0].user_name} (${versions[0].email})`;
      if (versions[0].status === 0) {
        name = `Deactivated User (${name})`;
      }
      return {
        id: versions[0].user_id,
        name,
        status: versions[0].status,
      };
    } else if (versions[0].user_name) {
      let name = `${versions[0].user_name}`;
      if (versions[0].status === 0) {
        name = `Deactivated User (${name})`;
      }
      return {
        id: versions[0].user_id,
        name,
        status: versions[0].status,
      };
    }
  }
  // Deactivated User
  return {
    id: 0,
    name: '',
    status: 0,
  };
}

export function getCellDisplayAssociatedContacts(column, value) {
  const obj = safeParse(value, '--');
  const versions = safeParse(obj.versions, []);
  if (versions.length > 0) {
    return {
      id: versions[0].object_id,
      name: versions[0].object_name,
    };
  }
  return {
    id: 0,
    name: '',
  };
}

export function getCellDisplayObject(column, value) {
  const obj = safeParse(value, null);
  if (obj !== null && typeof obj === 'object') {
    return JSON.stringify(obj);
  }
  return '--';
}

// ** PRIVATE FUNCTION **/
export function formatDateCol(timestamp, shortTime, onlyTime = false) {
  try {
    const isValid = !Number.isNaN(new Date(timestamp).getTime());
    if (!isValid) {
      return timestamp;
    }
    if (onlyTime) {
      return formatDate(new Date(timestamp), 'hh:mm:ss a');
    }

    // if (shortTime) {
    //   return formatDate(new Date(timestamp), "LLL dd',' yyyy");
    // }
    const format = getDateTimeFormatTranslate();
    return formatDate(new Date(timestamp), format, 'en-US');

    // return formatDate(new Date(timestamp), "LLL dd',' yyyy p");
    // return formatDate(new Date(timestamp), "LLL do',' yyyy p");
  } catch (error) {
    return timestamp;
  }
}

// ** PRIVATE FUNCTION **/
function formatPortalDateCol(timestamp, displayFormat) {
  try {
    const isValid = !Number.isNaN(new Date(timestamp).getTime());
    if (!isValid) {
      return timestamp;
    }

    const format = getDateTimeFormatTranslate(displayFormat);
    return formatPortalDate(safeParseDate(timestamp), format, 'en-US');
  } catch (error) {
    return timestamp;
  }
}

function formatUTCDateCol(timestamp, displayFormat) {
  try {
    const isValid = !Number.isNaN(new Date(timestamp).getTime());
    if (!isValid) {
      return timestamp;
    }

    const format = getDateTimeFormatTranslate(displayFormat);
    return formatDateTZ(timestamp, format, 'en-US');
  } catch (error) {
    return timestamp;
  }
}

// ** FUNCTION **/
export function displayOnlyDate(timestamp, displayFormatTmp = {}) {
  try {
    // eslint-disable-next-line no-restricted-globals
    const isValid = !isNaN(new Date(timestamp).getTime());
    if (!isValid) {
      return 'N/A';
    }
    const displayFormat =
      displayFormatTmp && Object.keys(displayFormatTmp).length > 0
        ? displayFormatTmp
        : initDateWithoutTime();
    const format = getDateTimeFormatTranslate(displayFormat);
    return formatDate(safeParseDate(timestamp), format, 'en-US');
  } catch (error) {
    return 'N/A';
  }
}

export const getTextWidth = (text, font) => {
  // canvas is a method generating fast, dynamic graphics using js
  // re-use canvas object for better performance
  const canvas =
    getTextWidth.canvas ||
    (getTextWidth.canvas = document.createElement('canvas'));
  const context = canvas.getContext('2d');
  context.font = font;
  const metrics = context.measureText(text);
  return metrics.width;
};

export const createResizerHoverCss = () => {
  let styles = '';

  for (let i = 1; i < 100; i += 1) {
    styles += `
      &:has(
        .th:nth-child(${i})[role='menuitem'] .resizer:hover,
        .th:nth-child(${i})[role='menuitem'] .isResizing
      ) {
        .header,
        .body,
        .footer {
          .th:nth-child(${i}),
          .td:nth-child(${i}) {
            border-color: ${colors.primary60} !important;
          }

          .th:nth-child(${i}) {
            &::before {
              content: '';
              width: 8px;
              height: 8px;
              background-color: ${colors.white};
              border: 1px solid ${colors.primary60};
              border-radius: 8px;
              position: absolute;
              top: 50%;
              right: 0;
              transform: translate(55%, -50%);
              z-index: 4;
              pointer-events: none;
            }

            &[data-sticky-last-left-td='true']::before {
              transform: translate(60%, -50%);
            }
          }
        }
      }
    `;
  }

  return css`
    ${styles}
  `;
};

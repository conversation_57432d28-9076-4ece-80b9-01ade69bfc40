import React, { useState } from 'react';
import { Route, Switch, with<PERSON><PERSON><PERSON> } from 'react-router-dom';
import styled from 'styled-components';

import { MuiThemeProvider, createMuiTheme } from '@material-ui/core/styles';

import Menu, { MenuItem } from 'components/Molecules/Menu';
import Header from 'components/Organisms/CustomHeader';
import MainContent from 'components/Organisms/MainContent';
import SideBar from 'components/Organisms/SideBar';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { compose } from 'redux';
import routes from './routes';

import 'assets/css/common.scss';

APP_CACHE_PARAMS = {
  user_id: '**********',
  p_currency: 'VND',
  p_f_datetime: {
    date: { check: true, value: 'long' },
    time: { check: true, value: 'medium', timeFormat: '24' },
    format: 'MM/DD/YYYY',
  },
  p_f_currency: {
    group: 'none',
    decimal: '.',
    currency: 'VND',
    decimalPlace: 11,
  },
  p_f_number: { group: ',', decimal: '.', decimalPlace: 0 },
  p_f_percentage: { group: ',', decimal: '.', decimalPlace: 5 },
  p_language: 'vi',
  p_timezone: 'Asia/Ho_Chi_Minh',
  undefined: "MMMM dd, yyyy 'at' HH:mm:ss OO",
  user_language: 'en',
  api_pid: 33167,
  // api_pid: 554926187,
};

const theme = createMuiTheme({
  palette: {
    primary: {
      main: '#005eb8',
    },
  },
});

const Wrapper = styled.div`
  overflow: hidden;
  aside {
    padding-top: 20px !important;
  }

  ul {
    height: calc(100% - 50px);
    overflow-y: auto;
  }
`;

const StyleMainContent = styled.div`
  padding: 1rem;
  height: 100%;
`;

function Layout() {
  const [isCollapse, setIsCollapse] = useState(false);
  const handleSideBarCollapse = collapse => {
    setIsCollapse(collapse);
  };

  const showContentMain = () => {
    // console.log('this.props.isLoading', this.props.isLoading);
    let result = null;
    if (routes.length > 0) {
      result = routes.map(route => (
        <Route
          key={route.path}
          path={route.path}
          exact={route.exact}
          component={route.main}
        />
      ));
    }
    return <Switch>{result}</Switch>;
  };
  return (
    <Wrapper>
      <MuiThemeProvider theme={theme}>
        {/* <Header /> */}
        <SideBar defaultToggle={false} onCollapse={handleSideBarCollapse}>
          <Menu collapse={isCollapse}>
            {routes.map(item => (
              <MenuItem key={item.path} iconName="viewAlt" href={item.path}>
                {item.label}
              </MenuItem>
            ))}
          </Menu>
        </SideBar>

        <MainContent collapse={isCollapse}>
          <ErrorBoundary path="app/containers/UIDev/index.jsx">
            <StyleMainContent>{showContentMain()}</StyleMainContent>
          </ErrorBoundary>
        </MainContent>
      </MuiThemeProvider>
    </Wrapper>
  );
}

export default compose(withRouter)(Layout);

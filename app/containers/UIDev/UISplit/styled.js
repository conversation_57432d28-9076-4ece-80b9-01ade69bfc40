import styled from 'styled-components';
import UIButton from '@material-ui/core/Button';
import { UINumber } from '@xlab-team/ui-components';
import { Switch } from '@antscorp/antsomi-ui';

export const InputNumner = styled(UINumber)`
  color: red;
`;
export const Button = styled(UIButton)`
  text-transform: none;
  font-weight: 700;
  margin-top: 14px;
`;
export const WrapperButton = styled.div`
  margin-top: 14px;
`;
export const WrapperTextOver = styled.div`
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 25px;
`;

export const WrapperControlGroup = styled.div``;

export const StyledSwitch = styled(Switch)`
  /* .MuiSwitch-colorPrimary.Mui-disabled {
    color: #005eb8;
  }
  .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track {
    background-color: #005eb8;
    opacity: 0.5;
  } */
`;

import React, { memo, useEffect, useState } from 'react';
import ModalConfirm from 'containers/modals/ModalConfirmExit';
import { useImmer } from 'use-immer';
import { UIButton as Button } from '@xlab-team/ui-components';
import PropTypes from 'prop-types';

import Item from './Item';
import { handleSplit } from './utils';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { generateKey } from '../../../utils/web/utils';
import { StyledSwitch, WrapperButton, WrapperControlGroup } from './styled';
import { NODE_TYPE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { Flex } from '@antscorp/antsomi-ui';

const MAP_TRANSLATE = {
  modalTitle: getTranslateMessage(
    TRANSLATE_KEY._BOX_TITL_REMOVE_BRANCH,
    'Remove branch',
  ),
  lableTotle: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_TOTAL_BRANCH,
    'Note: Total 100%',
  ),
  lableAddOption: getTranslateMessage(
    TRANSLATE_KEY._ACT_ADD_BRANCH,
    'Add option',
  ),
  modalContent: label =>
    getTranslateMessage(
      TRANSLATE_KEY._WARN_REMOVE_BRANCH_SPLIT,
      'Do you want to remove the branch',
      { branch_name: label },
    ),
};

function Split(props) {
  console.log('props', props);
  const {
    maxBranch,
    callbackData,
    initData,
    isAddOption,
    isViewMode,
    activeNode = {},
  } = props;

  const dataDefault = [
    {
      id: generateKey(),
      label: 'Group 1',
      value: 50,
      disabled: false,
      isDelete: false,
    },
    {
      id: generateKey(),
      label: 'Group 2',
      value: 50,
      disabled: true,
      isDelete: false,
    },
  ];

  const [dataSplit, setDataSplit] = useState([]);
  const [isActiveControlGroup, setIsActiveControlGroup] = useState(false);

  const [modal, setModal] = useImmer({
    isOpen: false,
    data: {},
  });
  // console.log('dataSplit', dataSplit);
  // DidMount
  useEffect(() => {
    let tmp = [];
    console.log('initData', initData);
    if (initData.length > 0) {
      tmp = [...initData];
      let totalNormalNode = tmp.length;
      tmp.forEach(each => {
        each.disabled = false;
        each.isDelete = true;

        if (each.isControlGroup) {
          each.isDelete = false;
        }
      });

      if (tmp.some(each => each.isControlGroup)) {
        totalNormalNode -= 1;
      }
      tmp[totalNormalNode - 1].disabled = true;
      tmp[totalNormalNode - 1].isDelete = true;

      if (
        tmp.some(each => each.isControlGroup)
          ? totalNormalNode === 1
          : totalNormalNode === 2
      ) {
        tmp[0].isDelete = false;
        tmp[1].isDelete = false;
      }
    }
    // else {
    //   tmp = dataDefault;
    // }

    setDataSplit(tmp);
    callbackData('UPDATE_LIST_DATA', { tmp });

    const branchControlGroup =
      initData && initData.find(branch => branch.isControlGroup);

    setIsActiveControlGroup(Boolean(branchControlGroup));
    return () => {
      setDataSplit(dataDefault);
    };
  }, [activeNode.nodeId, initData.length]);

  // Add Branch
  const handleAddData = (moreInfoGroup = {}) => {
    const tmp = [...dataSplit];
    let maxValue = 100;
    let valueNumberRecycle = 0;
    const id = generateKey();
    // Push element to array
    tmp.push({ id, ...moreInfoGroup });
    let divideTmp = tmp.length;
    const controlGroupNode = tmp.find(each => each.isControlGroup);
    if (controlGroupNode) {
      maxValue -= controlGroupNode.value;
      divideTmp -= 1;
    }

    // Set value for array. Total 100%
    tmp.forEach((each, index) => {
      each.disabled = false;
      each.isDelete = true;

      if (each.isControlGroup) {
        each.value = each.value;
        each.isDelete = false;
      } else if (maxValue % divideTmp === 0) {
        each.value = maxValue / Number(divideTmp);
      } else {
        valueNumberRecycle = maxValue % divideTmp;
        each.value = Math.floor(maxValue / divideTmp);
      }

      if (!each.label) {
        each.label = `${getTranslateMessage(
          TRANSLATE_KEY._TITL_GROUP,
          `Group ${Number(index) + 1}`,
          {
            number_group: Number(index) + 1,
          },
        )} `;
      }
    });

    if (tmp.some(el => el.isControlGroup)) {
      tmp.sort((el1, el2) => {
        if (el1.isControlGroup && !el2.isControlGroup) {
          return 1; // Đẩy phần tử có isControlGroup về sau
        }
        if (!el1.isControlGroup && el2.isControlGroup) {
          return -1; // Đẩy phần tử có isControlGroup về trước
        }
        return 0; // Giữ nguyên vị trí của các phần tử khác
      });
    }

    tmp[divideTmp - 1].value = tmp[divideTmp - 1].value + valueNumberRecycle;

    // set Max Value
    // if (tmp.length > 3) {
    //   tmp.forEach((each, index) => {
    //     each.maxValue = each.value;
    //   });
    // } else {
    //   tmp.forEach((each, index) => {
    //     each.maxValue = 100;
    //   });
    // }

    // If total < 100, push value to last element
    // if (valueNumber < 100) {
    //   tmp[tmp.length - 1].value =
    //     100 - Number(valueNumber) + Number(tmp[tmp.length - 1].value);
    // }

    // Set disable for last element
    tmp[divideTmp - 1].disabled = true;
    tmp[divideTmp - 1].isDelete = true;

    if (
      tmp.some(each => each.isControlGroup) ? divideTmp === 1 : divideTmp === 2
    ) {
      tmp[0].isDelete = false;
      tmp[1].isDelete = false;
    }

    // set tmp for state
    callbackData('UPDATE_LIST_DATA', { tmp, id });
    setDataSplit(tmp);
  };

  const callback = (type, data) => {
    if (type === 'ON_CHANGE_INPUT') {
      const tmp = handleSplit(data, dataSplit);

      // set Max Value
      if (tmp.length > 3) {
        tmp.forEach((each, index) => {
          each.maxValue = each.value;
        });
      } else {
        tmp.forEach((each, index) => {
          each.maxValue = 100;
        });
      }

      setDataSplit(tmp);
      callbackData('UPDATE_VALUE_DATA', { tmp });
    }
    if (type === 'DELETE_BRANCH') {
      setModal(draft => {
        draft.isOpen = true;
        draft.data = data;
      });
    }
    if (type === 'ON_CONFIRM') {
      let valueNumberRecycle = 0;
      let maxValue = 100;
      const tmpData = [...dataSplit];
      tmpData.forEach((each, index) => {
        if (each.id === data.id) {
          tmpData.splice(index, 1);
        }
      });

      let divideTmp = tmpData.length;
      const controlGroupNode = tmpData.find(each => each.isControlGroup);
      if (controlGroupNode) {
        maxValue -= controlGroupNode.value;
        divideTmp -= 1;
      }

      // Set value for array. Total 100%
      tmpData.forEach((each, index) => {
        if (each.isControlGroup) {
          each.value = each.value;
        } else if (maxValue % divideTmp === 0) {
          each.value = maxValue / Number(divideTmp);
        } else {
          valueNumberRecycle = maxValue % divideTmp;
          each.value = Math.floor(maxValue / divideTmp);
        }
      });

      // Push value to last element
      tmpData[divideTmp - 1].value =
        tmpData[divideTmp - 1].value + valueNumberRecycle;

      // Set disable for last element
      tmpData[divideTmp - 1].disabled = true;
      tmpData[divideTmp - 1].isDelete = true;

      if (
        tmpData.some(each => each.isControlGroup)
          ? divideTmp === 1
          : divideTmp === 2
      ) {
        tmpData[0].isDelete = false;
        tmpData[1].isDelete = false;
      }

      // Change label
      // tmpData.forEach((each, index) => {
      //   console.log('index', index);
      //   // console.log('each', each);
      //   // console.log(
      //   //   "startsWith('Branch')",
      //   //   each.label.startsWith(`Branch ${index}`),
      //   // );
      //   if (each.label.startsWith('Branch') === true) {
      //     each.label = `Branch ${Number(index) + 1}`;
      //   }
      //   if (each.label.startsWith('Nhánh') === true) {
      //     each.label = `Branch ${Number(index) + 1}`;
      //   } else {
      //     each.label = each.label;
      //   }
      // });

      // set Max Value
      // if (tmpData.length > 3) {
      //   tmpData.forEach((each, index) => {
      //     each.maxValue = each.value;
      //   });
      // } else {
      //   tmpData.forEach((each, index) => {
      //     each.maxValue = 100;
      //   });
      // }

      callbackData('UPDATE_LIST_DATA', { tmp: tmpData });
      setDataSplit(tmpData);
      setModal(draft => {
        draft.isOpen = false;
        // draft.data = {};
      });
    }
  };

  const handleActiveControlGroup = isActive => {
    setIsActiveControlGroup(isActive);
    if (isActive && !initData.some(data => data.isControlGroup)) {
      const infoControlGroup = {
        label: getTranslateMessage(
          TRANSLATE_KEY._TITL_CONTROL_GROUP,
          'Control Group',
        ),
        isControlGroup: true,
        value: 0,
      };
      handleAddData(infoControlGroup);
    } else {
      const controlGroupSplit = dataSplit.find(ele => ele.isControlGroup);
      if (controlGroupSplit) {
        const { id, value, label } = controlGroupSplit;
        callback('ON_CONFIRM', { id, value, label });
      }
    }
  };

  return (
    <>
      {dataSplit
        .filter(group => !group.isControlGroup)
        .map((each, index) => {
          const key = `@@${index}`;
          return (
            <Item
              {...each}
              key={key}
              index={index}
              isViewMode={isViewMode}
              callback={callback}
            />
          );
        })}
      {isAddOption && !isViewMode && (
        <WrapperButton>
          <Button
            style={{ padding: 0, marginTop: 0 }}
            theme="text-link"
            // color="primary"
            onClick={() => handleAddData()}
            disabled={dataSplit.length === maxBranch && true}
          >
            + {getTranslateMessage(TRANSLATE_KEY._ACT_ADD_GROUP, 'Add a group')}
          </Button>
        </WrapperButton>
      )}

      {activeNode.type === NODE_TYPE.SPLIT_BRANCH && (
        <WrapperControlGroup>
          <Flex gap="10px">
            <StyledSwitch
              color="primary"
              onChange={e => handleActiveControlGroup(!isActiveControlGroup)}
              value={isActiveControlGroup}
              checked={isActiveControlGroup}
              disabled={
                isViewMode || (isActiveControlGroup && initData.length === 2)
              }
              data-test="switch-control-group"
            />
            <span>
              {getTranslateMessage(
                TRANSLATE_KEY._ACT_ENABLE_CONTROL_GROUP,
                'Enable Control Group',
              )}
            </span>
          </Flex>
          <div>
            {dataSplit
              .filter(group => group.isControlGroup)
              .map((each, index) => {
                const key = `@@${index}`;
                return (
                  <Item
                    {...each}
                    key={key}
                    isViewMode={isViewMode}
                    callback={callback}
                  />
                );
              })}
          </div>
        </WrapperControlGroup>
      )}

      <ModalConfirm
        title={MAP_TRANSLATE.modalTitle}
        children={MAP_TRANSLATE.modalContent(modal.data.label)}
        isOpen={modal.isOpen}
        toggle={() =>
          setModal(draft => {
            draft.isOpen = !modal.isOpen;
          })
        }
        onConfirm={() => callback('ON_CONFIRM', modal.data)}
      />
    </>
  );
}

Split.defaultProps = {
  maxBranch: 10,
  isAddOption: true,
};
Split.prototype = {
  isAddOption: PropTypes.bool,
  maxBranch: PropTypes.number,
};
export default memo(Split);

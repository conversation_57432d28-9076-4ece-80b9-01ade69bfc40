import React, { memo, useEffect, useState } from 'react';
import { Grid, Icon, Slider } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { UITippy } from '@xlab-team/ui-components';
import UIStatusTag from 'components/cdp/UIStatusTag';

import { InputNumner, WrapperTextOver } from './styled';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

const useStyles = makeStyles(props => ({
  textFlexEnd: {
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    display: 'flex',
    width: '100%',
    opacity: 1,
  },
  hoverIcon: {
    '&:hover': {
      cursor: 'pointer',
    },
  },
  fullWidth: {
    width: '100%',
  },
  root: {
    alignItems: 'baseline',
    position: 'relative',
  },
  slider: {
    '&.MuiSlider-root': {
      padding: '2px 0',
      height: '4px',
    },
    '& > .MuiSlider-track': {
      height: '4px',
    },
    '&.slider-view-mode > .MuiSlider-track': {
      color: '#005eb8',
    },
    '& > .MuiSlider-thumb.Mui-disabled': {
      marginTop: '-2px',
    },
    '& > .MuiSlider-thumb': {
      marginTop: '-4px',
    },
    '&.slider-view-mode > .MuiSlider-thumb.Mui-disabled': {
      display: 'none',
    },
    '& > .MuiSlider-rail': {
      height: '4px',
    },
  },
  sliderCenter: {
    display: 'flex',
    alignItems: 'center',
  },
  wrapperIcon: {
    position: 'absolute',
    top: '50%',
    right: '10px',
    transform: 'translateY(-50%)',
    height: '24px',
  },
  opacityBlur: {
    opacity: '0.6',
  },
  textFlexEndOpacityBlur: {
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    display: 'flex',
    width: '100%',
    opacity: '0.6',
  },
  textViewMode: {
    fontWeight: 600,
    color: '#000000',
    fontSize: '12px',
    width: '22px',
    display: 'block',
    textAlign: 'center',
  },
  percent: {
    display: 'flex',
    justifyContent: 'center',
  },
  labelText: {
    color: '#666',
    fontSize: '12px',
  },
}));

const rootSplit = {
  alignItems: 'baseline',
  position: 'relative',
  marginBottom: '10px',
};
function Item(props) {
  const classes = useStyles(props);
  const {
    id,
    value,
    callback,
    disabled,
    isDelete,
    label,
    maxValue,
    isUseStatus,
    isEnableRemove,
    isViewMode,
    index,
    isControlGroup = false,
  } = props;
  // console.log('label', label);
  const [labelState, setLabelState] = useState();

  const onChange = (e, val) => {
    callback('ON_CHANGE_INPUT', { id, value: val });
  };
  const onChangeInput = e => {
    callback('ON_CHANGE_INPUT', { id, value: e });
  };

  useEffect(() => {
    // let labelTmp = label || '';
    // if (labelTmp.length > 5) {
    //   const labelSubString = labelTmp.substring(0, 3);
    //   labelTmp = `${labelSubString}...`;
    // }
    // setLabelState(labelTmp);
  }, []);
  return (
    <Grid
      container
      className="containerSplit"
      style={{ ...rootSplit, ...(isViewMode && { alignItems: 'center' }) }}
    >
      <Grid
        item
        xs={3}
        style={{
          flexBasis: 'unset',
          width: '175px',
          marginRight: '10px',
        }}
        className={
          disabled && !isViewMode
            ? classes.textFlexEndOpacityBlur
            : classes.textFlexEnd
        }
      >
        <UITippy
          title={`${label}`}
          arrow
          distance={10}
          className="tippy-lable-branch"
        >
          <WrapperTextOver className={classes.labelText}>
            {/* <UIStatusTag
              // pulse={row.story_status.pulse}
              use={1}
            /> */}

            {isUseStatus && (
              <status-indicator use={`${props.status && 'positive'}`} />
            )}
            {label}
          </WrapperTextOver>
        </UITippy>
      </Grid>
      {isViewMode ? (
        <span className={classes.textViewMode}>{Math.round(value) || 0}</span>
      ) : (
        <Grid
          item
          xs={2}
          style={{ flexBasis: 'unset' }}
          className={disabled ? classes.opacityBlur : ''}
        >
          <InputNumner
            // usePropsValue
            className={classes.fullWidth}
            value={Math.round(value)}
            onChange={e => onChangeInput(e)}
            disabledInput={disabled}
            disabledupto={disabled}
            min={0}
            max={maxValue}
            width="4.25rem"
            inputProps={{
              style: {
                textAlign: 'center',
              },
            }}
          />
        </Grid>
      )}
      <Grid
        item
        xs={1}
        style={{
          display: 'flex',
          justifyContent: 'center',
          flexBasis: 'unset',
          marginRight: 20,
          marginLeft: 10,
          color: '#000',
          fontSize: '13px',
          right: 0,
          ...(isViewMode && {
            marginLeft: 5,
            right: 0,
          }),
        }}
        // className={classes.percent}
        className="splitPercent"
      >
        %
      </Grid>
      <Grid item xs={5} className={isViewMode && classes.sliderCenter}>
        <Slider
          className={`${classes.slider} ${isViewMode && 'slider-view-mode'}`}
          value={value}
          min={0}
          step={1}
          max={100}
          onChange={(e, val) => onChange(e, val)}
          disabled={isViewMode || disabled}
        />
      </Grid>
      {isEnableRemove && !isViewMode && (
        <Grid
          item
          xs={2}
          className={classes.wrapperIcon}
          style={{ flexBasic: 'unset' }}
        >
          {isDelete && (
            <Icon
              style={{ marginLeft: '30px', color: '#7f7f7f' }}
              className={classes.hoverIcon}
              onClick={() => callback('DELETE_BRANCH', { id, value, label })}
            >
              closeOutlined
            </Icon>
          )}
        </Grid>
      )}
    </Grid>
  );
}

Item.defaultProps = {
  isEnableRemove: true,
  isUseStatus: false,
};

export default memo(Item);

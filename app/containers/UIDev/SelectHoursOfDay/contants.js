import _ from 'lodash';
import TRANSLATE_KEY from '../../../messages/constant';
import { getTranslateMessage } from '../../Translate/util';

/* eslint-disable no-plusplus */
const cache = [];
const cacheEveryDay = [];

export const NUMBER_DAY = 7;
const MAP_TRANSLATE = {
  mon: getTranslateMessage(TRANSLATE_KEY._TITL_MON, 'Mon'),
  tue: getTranslateMessage(TRANSLATE_KEY._TITL_TUE, 'Tue'),
  wed: getTranslateMessage(TRANSLATE_KEY._TITL_WED, 'Wed'),
  thur: getTranslateMessage(TRANSLATE_KEY._TITL_THU, 'Thu'),
  fri: getTranslateMessage(TRANSLATE_KEY._TITL_FRI, 'Fri'),
  sat: getTranslateMessage(TRANSLATE_KEY._TITL_SAT, 'Sat'),
  sun: getTranslateMessage(TRANSLATE_KEY._TITL_SUN, 'Sun'),
  every: getTranslateMessage(TRANSLATE_KEY._TITL_EVERYDAY, 'Everyday'),
};

const DAY_OF_WEEKS = ['MON', 'TUS', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
// const DAY_OF_WEEKS = ['0', '1', '2', '3', '4', '5', '6'];
const LABLE_DAY_OF_WEEKS = [
  `${MAP_TRANSLATE.mon}`,
  `${MAP_TRANSLATE.tue}`,
  `${MAP_TRANSLATE.wed}`,
  `${MAP_TRANSLATE.thur}`,
  `${MAP_TRANSLATE.fri}`,
  `${MAP_TRANSLATE.sat}`,
  `${MAP_TRANSLATE.sun}`,
];
export const LABLE_TIME_OF_DAY = [
  '12AM',
  '3AM',
  '6AM',
  '9AM',
  '12PM',
  '3PM',
  '6PM',
  '9PM',
];

export function getDataDayOfWeek() {
  const data = [];

  if (data.length === 0) {
    DAY_OF_WEEKS.forEach((day, index) => {
      for (let i = 0; i < 24; i++) {
        const value = i + 1 + index * 24 - 1;
        data.push({ x: `${i}`, y: day, value, isSelected: false });
      }
    });
  }
  data.push(LABLE_DAY_OF_WEEKS);
  data.push(LABLE_TIME_OF_DAY);
  // const arrayData = [...data];
  // for (let i = 0; i < 167; i++) {
  //   for (let j = 0; j <= data.length; j++) {
  //     if (arrayData[i].value === data[j]) {
  //       arrayData[i].isSelected = true;
  //     }
  //   }
  // }

  // console.log('data', data);
  return data;
}

export function getDataDayOfWeekSelected() {
  if (cache.length === 0) {
    DAY_OF_WEEKS.forEach((day, index) => {
      for (let i = 0; i < 24; i++) {
        const value = i + 1 + index * 24 - 1;
        cache.push({ x: `${i}`, y: day, value, isSelected: true });
      }
    });
  }
  cache.push(LABLE_DAY_OF_WEEKS);
  cache.push(LABLE_TIME_OF_DAY);
  // const arrayData = [...cache];
  // for (let i = 0; i < 167; i++) {
  //   for (let j = 0; j <= data.length; j++) {
  //     if (arrayData[i].value === data[j]) {
  //       arrayData[i].isSelected = true;
  //     }
  //   }
  // }

  // console.log('cache', cache);
  return cache;
}

export function getDataEveryDay() {
  const dataOut = [];
  if (dataOut.length === 0) {
    for (let i = 0; i < 24; i++) {
      dataOut.push({ x: `${i}`, isSelected: true });
    }
  }
  return dataOut;
}

export const mapValueToFe = data => {
  const arrayData = JSON.parse(JSON.stringify(getDataEveryDay()));
  arrayData.map(each => {
    if (!data.includes(Number(each.x))) {
      each.isSelected = false;
    }
  });
  return arrayData;
};

export const mapValueToFeEveryDay = data => {
  // const arrayData = [...getDataDayOfWeek()];
  const arrayData = JSON.parse(JSON.stringify(getDataDayOfWeek()));
  const mapTempt = {};
  const mapEveryDay = {};

  data.forEach(child => {
    // console.log('arrayData[child]', arrayData[child]);
    // if(arrayData[child] )
    if (mapTempt[arrayData[child].x] === undefined) {
      mapTempt[arrayData[child].x] = 1;
    } else {
      mapTempt[arrayData[child].x]++;
    }
    Object.keys(mapTempt).forEach(key => {
      if (mapTempt[key] === 7) {
        mapEveryDay[key] = true;
      }
    });
  });

  return mapEveryDay;
};

export const mapEntryToAPI = data => {
  // const arrayData = [...cache];
  for (let i = 0; i <= data; i++) {
    console.log(data[i]);
    // for (let j = 0; j <= data.length; j++) {
    //   if (arrayData[i].value === data[j]) {
    //     arrayData[i].isSelected = true;
    //   }
    // }
  }
};

/* eslint-disable func-names */
const shell = require('shelljs');

// var obj = config.modules[config.module];
// shell.rm('-rf', 'build/'+obj.path+'/'+obj.version+'/*');
shell.rm('-rf', 'build/prod*');

// var obj = config.modules[config.module]


console.log('Start build file message translate');

shell.exec('node internals/scripts/translate.js', function() {
  console.log(`End build file message translate`);

  console.log('Start build Staging');

  const t1 = new Date().getTime();
  shell.exec(
    'cross-env NODE_ENV=staging webpack --config internals/webpack/webpack.prod.babel.js --color --mode production --progress',
    function() {
      const t2 = new Date().getTime();
      console.log(`End build Staging with time: ${t2 - t1} (ms)`);
    },
  );

});


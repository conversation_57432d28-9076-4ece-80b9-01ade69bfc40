/**
 * COMMON WEBPACK CONFIGURATION
 */

const path = require('path');
const webpack = require('webpack');

const WorkboxWebpackPlugin = require('workbox-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
// const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');

// const smp = new SpeedMeasurePlugin();

module.exports = options => ({
  mode: options.mode,
  entry: options.entry,
  output: Object.assign(
    {
      // Compile into js/build.js
      path: path.resolve(process.cwd(), `build`),
      publicPath: `/`,
    },
    options.output,
  ), // Merge with env dependent settings
  optimization: options.optimization,
  module: {
    rules: [
      {
        test: /\.jsx?$/, // Transform all .js and .jsx files required somewhere with Babel
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            cacheCompression: false,
            cacheDirectory: true,
          },
        },
      },
      {
        test: /\.m?js$/,
        resolve: {
          fullySpecified: false,
        },
      },
      {
        // Preprocess our own .css files
        // This is the place to add your own loaders (e.g. sass/less etc.)
        // for a list of loaders, see https://webpack.js.org/loaders/#styling
        test: /\.css$/,
        exclude: /node_modules/,
        use: ['style-loader', 'css-loader'],
      },
      {
        // Preprocess 3rd party .css files located in node_modules
        test: /\.css$/,
        include: /node_modules/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.scss$/,
        use: [
          'style-loader', // creates style nodes from JS strings
          'css-loader', // translates CSS into CommonJS
          'sass-loader', // compiles Sass to CSS, using Node Sass by default
        ],
      },

      // Fonts (EOT, OTF, TTF, WOFF, WOFF2)
      {
        test: /\.(eot|otf|ttf|woff|woff2)$/i,
        type: 'asset/resource', // Xuất file ra dist, trả về URL
        generator: {
          filename: 'fonts/[path]__[name][ext]',
        },
      },

      // SVG: inline nếu nhỏ, còn lại emit ra file
      {
        test: /\.svg$/i,
        type: 'asset', // inline nếu nhỏ hơn limit, còn lại là file
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024, // 10KB
          },
        },
        generator: {
          filename: 'images/[path]__[name][ext]',
        },
      },

      // Images (JPG, PNG, GIF): inline nếu nhỏ, dùng plugin tối ưu hóa
      {
        test: /\.(jpe?g|png|gif)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024,
          },
        },
        generator: {
          filename: 'images/[path]__[name][ext]',
        },
      },

      // HTML
      {
        test: /\.html$/i,
        use: 'html-loader',
      },

      // Videos
      {
        test: /\.(mp4|webm)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024,
          },
        },
        generator: {
          filename: 'videos/[path]__[name][ext]',
        },
      },
    ],
  },
  plugins: options.plugins.concat([
    // Always expose NODE_ENV to webpack, in order to use `process.env.NODE_ENV`
    // inside your code for any environment checks; Terser will automatically
    // drop any unreachable code.
    new webpack.EnvironmentPlugin({
      NODE_ENV: 'development',
    }),

    new CopyPlugin({
      patterns: [
        { from: './app/metadata/favicon.ico', to: '' },
        { from: './app/metadata/manifest.json', to: '' },
        { from: './app/metadata/logo192.png', to: '' },
        { from: './app/metadata/logo512.png', to: '' },
      ],
    }),
    new WorkboxWebpackPlugin.InjectManifest({
      swSrc: './app/src-sw.js',
      swDest: 'sw.js',
    }),
  ]),
  resolve: {
    modules: ['node_modules', 'app'],
    extensions: ['.js', '.jsx', '.react.js'],
    mainFields: ['browser', 'jsnext:main', 'main'],
    alias: {
      PredictiveModel: path.resolve(
        __dirname,
        '../../app/modules/Dashboard/Profile/PredictiveModel',
      ),
      '@codemirror': path.resolve(__dirname, '../../node_modules/@codemirror/'),
    },
    fallback: {
      domain: false,
    },
  },
  devtool: options.devtool,
  target: 'web', // Make web variables accessible to webpack, e.g. window
  performance: options.performance || {},
});

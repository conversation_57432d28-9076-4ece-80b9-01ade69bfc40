/**
 * DEVELOPMENT WEBPACK CONFIGURATION
 */

const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
// const CircularDependencyPlugin = require('circular-dependency-plugin');

module.exports = require('./webpack.base.babel')({
  mode: 'development',

  // Add hot reloading in development
  entry: [
    require.resolve('react-app-polyfill/ie11'),
    path.join(process.cwd(), 'app/app.js'), // Start with js/app.js
  ],
  resolve: {
    fallback: {
      domain: false,
      process: require.resolve('process'),
      'process/browser': require.resolve('process/browser'),
    },
  },

  // Don't use hashes in dev mode for better performance
  output: {
    filename: '[name].js',
    chunkFilename: '[name].chunk.js',
  },

  optimization: {
    moduleIds: 'deterministic',
    runtimeChunk: 'single',
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },

  // Add development plugins
  plugins: [
    new HtmlWebpackPlugin({
      inject: true, // Inject all files that are generated by webpack, e.g. bundle.js
      template: 'app/index.development.html',
    }),
    // TODO: Temporary fix Uncaught ReferenceError: process is not defined when use uniqid package
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
    // new CircularDependencyPlugin({
    //   exclude: /a\.js|node_modules/, // exclude node_modules
    //   failOnError: false, // show a warning when there is a circular dependency
    // }),
  ],

  // Webpack Dev Server configuration
  devServer: {
    port: 3000,
    host: 'localhost',
    hot: true,
    open: true,
    historyApiFallback: true,
    static: {
      directory: path.join(process.cwd(), 'app'),
      publicPath: '/',
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    compress: true,
  },

  // Emit a source map for easier debugging
  // See https://webpack.js.org/configuration/devtool/#devtool
  devtool: 'eval-source-map',
  performance: {
    hints: false,
  },
});

{"name": "react-boilerplate", "private": true, "version": "4.0.0", "description": "A highly scalable, offline-first foundation with the best DX and a focus on performance and best practices", "repository": {"type": "git", "url": "git://github.com/react-boilerplate/react-boilerplate.git"}, "engines": {"npm": ">=5", "node": ">=8.15.1"}, "author": "<PERSON>", "license": "MIT", "scripts": {"build": "cross-env NODE_ENV=production ASSET_PATH=//sandbox-st.antsomi.com/gen2 node ./internals/scripts/build.staging.js", "build:production": "cross-env NODE_ENV=production ASSET_PATH=//st.antsomi.com/gen2 node ./internals/scripts/build.production.js", "build:aws": "cross-env NODE_ENV=production ASSET_PATH=//st.antsomi.com/gen2 node ./internals/scripts/build.production.js", "build:staging": "cross-env NODE_ENV=production ASSET_PATH=//staging-st.antsomi.com/gen2 node ./internals/scripts/build.production.js", "build:analyze": "node ./internals/scripts/build.analyze.js", "build:clean": "rimraf ./build", "start": "cross-env NODE_ENV=development webpack serve --config internals/webpack/webpack.dev.babel.js", "start:express": "cross-env NODE_ENV=development node server", "start:demo": "cross-env NODE_ENV=production node server", "translate": "node ./internals/scripts/translate.js", "test:clean": "rimraf ./coverage", "test": "cross-env NODE_ENV=test jest", "test:coverage": "cross-env NODE_ENV=test jest --coverage", "test:watch": "cross-env NODE_ENV=test jest --watchAll --silent=false", "functions": "node ./internals/scripts/formular-functions.js", "postinstall": "husky install", "prepare": "husky install", "debug": "nothing here", "storybook": "cross-env NODE_ENV=storybook start-storybook -p 6006", "build-storybook": "NODE_ENV=storybook build-storybook"}, "resolutions": {"babel-core": "7.0.0-bridge.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@antscorp/account-permission-role": "1.0.7", "@antscorp/ants-account-sharing": "1.0.46", "@antscorp/ants-account-support": "^1.0.13", "@antscorp/ants-app-inbox": "1.0.65", "@antscorp/antsomi-charts": "1.0.12", "@antscorp/antsomi-ui": "1.3.2-beta.5", "@antscorp/calendar-table": "0.0.50", "@antscorp/email-template-editor": "1.0.17", "@antscorp/form-design": "^0.0.79-beta.27", "@babel/polyfill": "7.4.3", "@codemirror/autocomplete": "^6.4.2", "@codemirror/lang-xml": "^6.0.2", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.9.1", "@date-io/date-fns": "1.x", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "3.2.0", "@testing-library/jest-dom": "^5.16.5", "@tinymce/tinymce-react": "^3.7.0", "@uiw/react-codemirror": "^4.19.9", "@upsetjs/venn.js": "^1.4.2", "@xlab-team/ui-components": "1.0.17-beta.37", "@xlab-team/workflows": "0.0.93", "axios": "^0.19.2", "beautiful-react-diagrams": "^0.5.1", "cdp-explore-package": "1.0.13-beta.11", "classnames": "^2.3.2", "codemirror": "^5.58.2", "colord": "^2.8.0", "connected-react-router": "6.4.0", "date-fns": "^2.15.0", "date-fns-tz": "^1.0.10", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.1.3", "fontfaceobserver": "2.1.0", "highlight.js": "^11.8.0", "history": "4.9.0", "hoist-non-react-statics": "3.3.0", "html-entities": "^2.0.2", "html-react-parser": "^0.13.0", "immer": "3.0.0", "intl": "1.2.5", "invariant": "2.2.4", "ip": "1.1.5", "js-beautify": "^1.13.0", "lodash": "4.17.11", "material-ui-popup-state": "^1.6.1", "moment": "^2.29.1", "moment-timezone": "^0.5.39", "network-side-nav": "1.0.21", "node-sass": "^4.14.1", "polished": "^3.6.5", "prop-types": "15.7.2", "re-resizable": "^6.5.5", "react": "16.14.0", "react-beautiful-dnd": "^13.0.0", "react-codemirror2": "^7.2.1", "react-colorful": "^5.5.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "16.14.0", "react-helmet": "6.0.0-beta", "react-input-autosize": "^2.2.2", "react-intersection-observer": "^8.26.2", "react-json-view": "^1.21.3", "react-perfect-scrollbar": "^1.5.8", "react-redux": "7.2.1", "react-router-dom": "5.1.0", "react-router-redux": "^4.0.8", "react-scrollbars-custom": "^4.0.25", "react-selectable-fast": "^3.4.0", "react-slick": "^0.26.1", "react-sortablejs": "^6.0.0", "react-table": "^7.2.2", "react-table-sticky": "^1.1.2", "reactstrap": "^8.5.1", "recharts": "^1.8.5", "redux": "4.0.1", "redux-immutable": "^4.0.0", "redux-saga": "1.0.2", "reselect": "4.0.0", "sanitize.css": "8.0.0", "sass-loader": "7.1.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.5.4", "sortablejs": "^1.13.0", "string-replace-to-array": "^2.1.0", "styled-components": "^5.2.1", "uniqid": "^5.2.0", "use-immer": "^0.4.1", "uuid": "^9.0.0"}, "devDependencies": {"@babel/cli": "7.4.3", "@babel/core": "7.4.3", "@babel/plugin-proposal-class-properties": "7.4.0", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-modules-commonjs": "7.4.3", "@babel/plugin-transform-react-constant-elements": "7.2.0", "@babel/plugin-transform-react-inline-elements": "7.2.0", "@babel/preset-env": "7.4.3", "@babel/preset-react": "7.0.0", "@babel/register": "7.4.0", "@commitlint/cli": "^17.5.0", "@commitlint/config-conventional": "^17.4.4", "@storybook/addon-actions": "^6.5.16", "@storybook/addon-essentials": "^6.5.16", "@storybook/addon-interactions": "^6.5.16", "@storybook/addon-links": "^6.5.16", "@storybook/builder-webpack4": "^6.5.16", "@storybook/manager-webpack4": "^6.5.16", "@storybook/react": "^6.5.16", "@storybook/testing-library": "^0.0.13", "@testing-library/dom": "^8.11.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "add-asset-html-webpack-plugin": "3.1.3", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-loader": "8.0.5", "babel-plugin-dynamic-import-node": "2.2.0", "babel-plugin-lodash": "3.3.4", "babel-plugin-react-intl": "3.0.1", "babel-plugin-styled-components": "1.10.0", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "chalk": "2.4.2", "circular-dependency-plugin": "5.0.2", "compare-versions": "3.4.0", "compression": "^1.7.4", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "coveralls": "3.0.3", "cross-env": "^7.0.3", "css-loader": "2.1.1", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-airbnb-base": "13.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-webpack": "0.11.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-prettier": "3.0.1", "eslint-plugin-react": "7.12.4", "eslint-plugin-react-hooks": "1.6.0", "eslint-plugin-redux-saga": "1.0.0", "eslint-plugin-storybook": "^0.6.11", "express": "^4.17.1", "file-loader": "3.0.1", "googleapis": "^52.1.0", "html-loader": "0.5.5", "html-webpack-plugin": "^5.5.0", "husky": "^7.0.4", "image-webpack-loader": "4.6.0", "imports-loader": "0.8.0", "jest": "^29.5.0", "jest-cli": "24.7.1", "jest-dom": "3.1.3", "jest-environment-jsdom-sixteen": "^2.0.0", "jest-styled-components": "6.3.1", "lint-staged": "8.1.5", "minimist": "^1.2.5", "node-plop": "0.18.0", "null-loader": "0.1.1", "offline-plugin": "5.0.6", "plop": "2.3.0", "prettier": "1.17.0", "react-app-polyfill": "0.2.2", "redux-logger": "^3.0.6", "rimraf": "2.6.3", "shelljs": "0.8.3", "style-loader": "0.23.1", "stylelint": "10.0.1", "stylelint-config-recommended": "2.2.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.6.0", "svg-url-loader": "2.3.2", "terser-webpack-plugin": "^5.3.0", "url-loader": "1.1.2", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0", "webpack-deadcode-plugin": "^0.1.12", "webpack-dev-middleware": "3.6.2", "webpack-hot-middleware": "2.24.3", "webpack-pwa-manifest": "4.0.0", "whatwg-fetch": "3.0.0", "path-browserify": "^1.0.1", "crypto-browserify": "^3.12.0", "stream-browserify": "^3.0.0", "buffer": "^6.0.3"}}
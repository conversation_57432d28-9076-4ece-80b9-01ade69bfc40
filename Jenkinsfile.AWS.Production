#!/usr/bin/env groovy

import groovy.json.JsonOutput
import java.util.Optional
import hudson.tasks.test.AbstractTestResultAction
import hudson.model.Actionable
import hudson.tasks.junit.CaseResult

def isResultGoodForPublishing = { ->
    return currentBuild.result == null
}

////////////////////////////
def author = "";

def getGitAuthor = {
    def commit = sh(returnStdout: true, script: 'git rev-parse HEAD')
    author = sh(returnStdout: true, script: "git --no-pager show -s --format='%an' ${commit}").trim()
}

////////////////////////////
def message = "";

def getLastCommitMessage = {
    message = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
}

def branch = "";
def getbranchname = {
	branch = sh(returnStdout: true, script: 'git name-rev --name-only HEAD').trim()
}
//////////////////////////////
def slackNotificationChannel = 'cdp-fe-ci-cd'     // ex: = "builds"

def notifySlack(text, channel, attachments) {
    def slackURL = '*******************************************************************************'
    def jenkinsIcon = 'https://wiki.jenkins-ci.org/download/attachments/2916393/logo.png'

    def payload = JsonOutput.toJson([text: text,
        channel: channel,
        username: "Jenkins",
        icon_url: jenkinsIcon,
        attachments: attachments
    ])

    sh "curl -X POST --data-urlencode \'payload=${payload}\' ${slackURL}"
}

///////////////////////////

def populateGlobalVariables = {
    getLastCommitMessage()
    getGitAuthor()
    getbranchname()
}
def getlogindocker(){
  withCredentials([usernamePassword(credentialsId: 'registry.adx.vn.', usernameVariable: 'registry_user', passwordVariable: 'registry_pwd')]) {
    sh "echo ${registry_pwd} | docker login registry.adx.vn. --username ${registry_user} --password-stdin "
      }
}
// build image
def buildimage(docker_image, docker_tag){
  sh "docker pull ${docker_image}:latest || true"
  sh "DOCKER_BUILDKIT=1 docker build --cache-from ${docker_image}:latest --file Dockerfile.AWS.Production --tag ${docker_image}:${docker_tag} . "
  sh "echo done"
}
// push images
def pushdockerimage(docker_image, docker_tag){
  sh "docker tag ${docker_image}:${docker_tag} ${docker_image}:latest"
  sh "docker image ls | grep ${docker_image}"
  sh "docker push ${docker_image}:${docker_tag}"
  sh "docker push ${docker_image}:latest"
}
// clean image
def cleanimages(docker_image, docker_tag){
  sh "docker image rm ${docker_image}:${docker_tag}"
  sh "docker image rm ${docker_image}:latest"
}

///////////////////
node {
    try {
        stage('Checkout') {
            scmSkip(deleteBuild: true, skipPattern:'.*\\[on-prem\\].*')
            checkout scm
            populateGlobalVariables()
            def buildColor = currentBuild.result == null ? "good" : "warning"
            notifySlack("", slackNotificationChannel, [
                    [
                        title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                        title_link: "${env.BUILD_URL}",
                        color: "${buildColor}",
                        author_name: "${author}",
                        fields: [
                            [
                                title: "Pipeline is starting",
                                value: "${branch}",
                                short: true
                            ],
                            [
                                title: "Last Commit",
                                value: "${message}",
                                short: false
                            ]
                        ]
                    ]
                ])
            
        }
        withEnv(['DOCKER_IMAGE=registry.adx.vn/cdp-frontend-gen2', 'DOCKER_TAG=aws-production']){
	    stage("build") {
            getlogindocker()
            // //build image
            buildimage(DOCKER_IMAGE, DOCKER_TAG)
            // //pushimage
            pushdockerimage(DOCKER_IMAGE, DOCKER_TAG)
            // //clean to save disk
            cleanimages(DOCKER_IMAGE, DOCKER_TAG)
            populateGlobalVariables()
            def buildColor = currentBuild.result == null ? "good" : "warning"
            def buildStatus = currentBuild.result == null ? "Success" : currentBuild.result
            notifySlack("", slackNotificationChannel, [
                [
                    title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                    title_link: "${env.BUILD_URL}",
                    color: "${buildColor}",
                    author_name: "${author}",
                    text: "${buildStatus}\n${author}",
                    fields: [
                        [
                            title: "STAGE BUILD",
                            value: "${branch}",
                            short: true
                        ],
                        [
                            title: "Last Commit",
                            value: "${message}",
                            short: false
                        ]
                    ]
                ]
            ])
        }
        }
	stage("deploy") {
            populateGlobalVariables()
            def buildColor = currentBuild.result == null ? "good" : "warning"
            notifySlack("", slackNotificationChannel, [
                    [
                        title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                        title_link: "${env.BUILD_URL}",
                        color: "${buildColor}",
                        author_name: "${author}",
                        fields: [
                            [
                                title: "STAGE DEPLOY",
                                value: "${branch}",
                                short: true
                            ],
                            [
                                title: "Last Commit",
                                value: "${message}",
                                short: false
                            ]
                        ]
                    ]
                ])
            sshagent(['sandbox_server']) {
            script{
            //ssh and run script
        sh '''
            ssh -o StrictHostKeyChecking=no -l ${SSH_USER} ${SSH_SERVER} -p22222 \
            "cd ${PATH_CDP} && ./changename.sh"
        '''

        sh '''
            ssh -o StrictHostKeyChecking=no -l ${SSH_USER} ${SSH_SERVER_02} -p22222 \
            "cd ${PATH_CDP} && ./changename.sh"
        '''
            }
        }
        }
    stage("Update Static") {
            populateGlobalVariables()
            def buildColor = currentBuild.result == null ? "good" : "warning"
            notifySlack("", slackNotificationChannel, [
                    [
                        title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                        title_link: "${env.BUILD_URL}",
                        color: "${buildColor}",
                        author_name: "${author}",
                        fields: [
                            [
                                title: "STAGE RESTART CDP-FE GEN2",
                                value: "${branch}",
                                short: true
                            ],
                            [
                                title: "Last Commit",
                                value: "${message}",
                                short: false
                            ]
                        ]
                    ]
                ])
            sshagent(['sandbox_server']) {
            script{
            //ssh and run script
        sh '''
            ssh -o StrictHostKeyChecking=no -l ${SSH_USER} ${SSH_SERVER} -p22222 \
            "sudo -u cdpfe NODE_ENV=production pm2 restart /data/app.cdp.asia/fe-gen2-cdp-antsomi.com/app.coffee -i 2 -n fe-gen2-cdp.antsomi.com"
        '''

        sh '''
            ssh -o StrictHostKeyChecking=no -l ${SSH_USER} ${SSH_SERVER_02} -p22222 \
            "sudo -u cdpfe NODE_ENV=production pm2 restart /data/app.cdp.asia/fe-gen2-cdp-antsomi.com/app.coffee -i 2 -n fe-gen2-cdp.antsomi.com"
        '''
            }
        }
        }
    }  catch (e) {
        def buildStatus = "Failed"

        notifySlack("", slackNotificationChannel, [
            [
                title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                title_link: "${env.BUILD_URL}",
                color: "danger",
                author_name: "${author}",
                text: "${buildStatus}",
                fields: [
                    [
                        title: "Branch",
                        value: "${env.BRANCH_NAME}",
                        short: true
                    ],
                    [
                        title: "Last Commit",
                        value: "${message}",
                        short: false
                    ],
                    [
                        title: "Error",
                        value: "${e}",
                        short: false
                    ]
                ]
            ]
        ])

        throw e
    }
}